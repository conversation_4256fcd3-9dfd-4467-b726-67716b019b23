<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456

[circleci-url]: https://circleci.com/gh/nestjs/nest

  <p align="center">A progressive <a href="http://nodejs.org" target="_blank">Node.js</a> framework for building efficient and scalable server-side applications.</p>
    <p align="center">
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/dm/@nestjs/common.svg" alt="NPM Downloads" /></a>
<a href="https://circleci.com/gh/nestjs/nest" target="_blank"><img src="https://img.shields.io/circleci/build/github/nestjs/nest/master" alt="CircleCI" /></a>
<a href="https://coveralls.io/github/nestjs/nest?branch=master" target="_blank"><img src="https://coveralls.io/repos/github/nestjs/nest/badge.svg?branch=master#9" alt="Coverage" /></a>
<a href="https://discord.gg/G7Qnnhy" target="_blank"><img src="https://img.shields.io/badge/discord-online-brightgreen.svg" alt="Discord"/></a>
<a href="https://opencollective.com/nest#backer" target="_blank"><img src="https://opencollective.com/nest/backers/badge.svg" alt="Backers on Open Collective" /></a>
<a href="https://opencollective.com/nest#sponsor" target="_blank"><img src="https://opencollective.com/nest/sponsors/badge.svg" alt="Sponsors on Open Collective" /></a>
  <a href="https://paypal.me/kamilmysliwiec" target="_blank"><img src="https://img.shields.io/badge/Donate-PayPal-ff3f59.svg" alt="Donate us"/></a>
    <a href="https://opencollective.com/nest#sponsor"  target="_blank"><img src="https://img.shields.io/badge/Support%20us-Open%20Collective-41B883.svg" alt="Support us"></a>
  <a href="https://twitter.com/nestframework" target="_blank"><img src="https://img.shields.io/twitter/follow/nestframework.svg?style=social&label=Follow" alt="Follow us on Twitter"></a>
</p>
  <!--[![Backers on Open Collective](https://opencollective.com/nest/backers/badge.svg)](https://opencollective.com/nest#backer)
  [![Sponsors on Open Collective](https://opencollective.com/nest/sponsors/badge.svg)](https://opencollective.com/nest#sponsor)-->

## Description

[Nest](https://github.com/nestjs/nest) framework TypeScript starter repository.

## Project setup

```bash
$ npm install
```

## Compile and run the project

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Run tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## Deployment

When you're ready to deploy your NestJS application to production, there are some key steps you can take to ensure it
runs as efficiently as possible. Check out the [deployment documentation](https://docs.nestjs.com/deployment) for more
information.

If you are looking for a cloud-based platform to deploy your NestJS application, check out [Mau](https://mau.nestjs.com)
, our official platform for deploying NestJS applications on AWS. Mau makes deployment straightforward and fast,
requiring just a few simple steps:

```bash
$ npm install -g mau
$ mau deploy
```

With Mau, you can deploy your application in just a few clicks, allowing you to focus on building features rather than
managing infrastructure.

## Resources

Check out a few resources that may come in handy when working with NestJS:

- Visit the [NestJS Documentation](https://docs.nestjs.com) to learn more about the framework.
- For questions and support, please visit our [Discord channel](https://discord.gg/G7Qnnhy).
- To dive deeper and get more hands-on experience, check out our official video [courses](https://courses.nestjs.com/).
- Deploy your application to AWS with the help of [NestJS Mau](https://mau.nestjs.com) in just a few clicks.
- Visualize your application graph and interact with the NestJS application in real-time
  using [NestJS Devtools](https://devtools.nestjs.com).
- Need help with your project (part-time to full-time)? Check out our
  official [enterprise support](https://enterprise.nestjs.com).
- To stay in the loop and get updates, follow us on [X](https://x.com/nestframework)
  and [LinkedIn](https://linkedin.com/company/nestjs).
- Looking for a job, or have a job to offer? Check out our official [Jobs board](https://jobs.nestjs.com).

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If
you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://twitter.com/kammysliwiec)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](https://github.com/nestjs/nest/blob/master/LICENSE).

# Database

- docker exec -it <container-id> sqlplus SYSTEM/asdf@XE
- docker exec -it <container_id> sqlplus SYS/<your_sys_password>@XE as sysdba
- docker exec -it 1669dad966c4 sqlplus SYS/asdf@XE as sysdba
- docker exec -it oraclecobradb /bin/bash

- CREATE USER cobra IDENTIFIED BY asdf;
- GRANT ALL PRIVILEGES TO cobra;
  [//]: # (- sqlplus sys/asdf@XE as sysdba)

## Docker commands
echo "<YOUR_GITHUB_PERSONAL_ACCESS_TOKEN>" | docker login ghcr.io -u <YOUR_GITHUB_USERNAME> --password-stdin

pull images from repository:
docker compose up -d --force-recreate --pull always
docker compose up -d --force-recreate --pull always cobra-enrollment-service


CREATE DATABASE LINK "ECRS"
CONNECT TO "ECRS" IDENTIFIED BY VALUES ':1'
USING 'ecrstest';

CREATE DATABASE LINK my_dblink
CONNECT TO "ECRS" IDENTIFIED BY "***password***"
USING '(DESCRIPTION=
(ADDRESS=(PROTOCOL=TCP)(HOST=*********)(PORT=1522))
(CONNECT_DATA=(SERVICE_NAME=ecrstest))
)';

CREATE VIEW TBL_CRS_LEGACY_DATA AS
SELECT PIN, LASTNAME, SEX, EMPLOYER_CODE, OTHERNAME, FIRSTNAME, PHONE, DOB, PFA
FROM TBL_CRS_LEGACY_DATA@ECRS;

CREATE VIEW TBL_CONTRIBUTOR_BIODATA AS
SELECT RSAPIN, SURNAME, GENDER, PFACODE, EMP_EMPLOYER_CODE, MIDDLENAME, FIRSTNAME, PHONE_NO, DATE_OF_BIRTH
FROM TBL_CONTRIBUTOR_BIODATA@ECRS;

docker-compose up -d --build
docker compose --env-file .env up -d --build
docker compose -f docker-compose-local.yml up -d --build cobra-login-service

docker compose -f docker-compose-local.yml up -d --build cobra-notification-service
docker compose -f docker-compose.yml up -d --build cobra-user-service
docker compose -f docker-compose-local.yml build --no-cache
docker compose -f docker-compose.java.yml up -d
docker compose up -f docker-compose.java.yml -d --force-recreate --pull always cobra-notification-service


docker-compose -f docker-compose.oracle.yml up -d --build
docker-compose -f docker-compose.utilities.yml up -d --build 
docker compose -f docker-compose.monitoring.yml up -d --build jaeger

nest generate module ldap -p shared
nest generate module database -p shared
nest generate module dto -p shared
nest generate module health -p shared
nest generate module logger -p shared
nest generate module cache -p shared
nest generate module enums -p shared

nest g app pdf-generator-ms
nest g app user-ms
nest g app notification-ms

## start specific service

nest start setting-ms --watch
nest start notification-ms --watch

nest g resource setting-ms
nest g resource notification-ms

# Documentation

http://<IP>>:<PORT>/swagger-api

# Run locally
docker compose -f docker-compose-local.yml up -d --build cobra-cron-service
docker compose -f docker-compose-local.yml up -d cobra-login-service

docker rmi ghcr.io/keanhive/cobra-login-service --force

### pull just one service:
docker pull ghcr.io/keanhive/cobra-core:latest
docker pull ghcr.io/keanhive/cobra-login-service:latest
docker pull ghcr.io/keanhive/cobra-user-service:latest
docker pull ghcr.io/keanhive/cobra-setting-service:latest
docker pull ghcr.io/keanhive/cobra-notification-service:latest
docker pull ghcr.io/keanhive/cobra-ecrs-service:latest
docker pull ghcr.io/keanhive/cobra-enrollment-service:latest
docker pull ghcr.io/keanhive/cobra-enrollment-engine-service:latest
docker pull ghcr.io/keanhive/cobra-web:latest

docker run -d -p 3000:3000 ghcr.io/keanhive/cobra-web
docker run -d -p 3000:3000 --env-file .env ghcr.io/keanhive/cobra-web


docker pull ghcr.io/keanhive/cobra-web:latest
docker stop cobra-web
docker rm cobra-web
docker run -d -p 3000:3000 --env-file .env --name cobra-web ghcr.io/keanhive/cobra-web


docker compose -f docker-compose.yml up -d --build cobra-user-service
docker compose -f docker-compose.yml up -d cobra-login-service
docker compose -f docker-compose.yml up --build cobra-login-service
docker compose up --force-recreate -d


docker exec -it a472be32e05d env

## prune images
docker image prune -a

rm -rf node_modules package-lock.json                                            
npm cache clean --force
npm install

lsof -i :9229    
kill -9 5822

docker run --name my-openldap -p 389:389 -e LDAP_ORGANISATION="PENCOM" -e LDAP_DOMAIN="pencom.com" -e LDAP_ADMIN_PASSWORD="adminpassword" --detach osixia/openldap                                

Portainer
docker run -d -p 3001:9000 -v /var/run/docker.sock:/var/run/docker.sock portainer/portainer-ce

Dozzle:
docker run -d -p 3020:8080 -v /var/run/docker.sock:/var/run/docker.sock amir20/dozzle

docker run -d --name uptime-kuma -p 3033:3001 louislam/uptime-kuma

ssh -p 22 admin@10.1.18.202
ssh -p 22 admin@10.1.18.201

docker pull dbeaver/cloudbeaver:latest
docker run --name cloudbeaver -d --restart unless-stopped -p 3018:8978 -v /var/cloudbeaver/workspace:/opt/cloudbeaver/workspace dbeaver/cloudbeaver:latest
docker run -d -p 3004:8000 docker.io/linuxserver/gateone

### speedtest
curl -s https://raw.githubusercontent.com/sivel/speedtest-cli/master/speedtest.py | python3

http://************:3006/wetty/ssh/admin
# Give metabase permission to write to the volume
chmod -R 777 ./plugins
chmod -R 777 ./logs

#folder structure
tree -I "node_modules|dist|coverage" > structure.md

docker run --name html2pdf --detach -p=3022:3000 \
--shm-size 1G --sysctl net.ipv6.conf.all.disable_ipv6=1 \
ghcr.io/ccjmne/puppeteer-html2pdf:latest

[//]: # (requestfields :::::::::::::C11: 1989-01-11 ,C12: 1990-04-02 ,C13:  F ,C14:  2559780 ,C15:  2005-04-02 ,C16:  0.05)

# drop unused columns
ALTER TABLE "EMPLOYMENT_DETAIL" DROP UNUSED COLUMNS;


ALTER TABLE COBRA.TBL_BATCH ADD PAYMENT_CONFIRMED_PFA VARCHAR2(1000) NULL;

SELECT
acc.table_name AS child_table,
acc.column_name AS child_column,
con.constraint_name,
con.r_constraint_name AS parent_constraint_name,
par.table_name AS parent_table
FROM
user_cons_columns acc
JOIN user_constraints con
ON acc.constraint_name = con.constraint_name
JOIN user_tables par
ON par.table_name = con.table_name
WHERE
con.constraint_name = 'FK_251712d6735d4f5d514180dd77f';


# Resolve issue with pk constraint
INSERT INTO TBL_PFA (
PFACODE, PFANAME,ADDRESS1,EMAIL_ADDRESS,PFCCODE
)
SELECT
LEVEL,
'Temp PFA',
'ADDRESS1',
'EMAIL_ADDRESS',
'001'
FROM dual
CONNECT BY LEVEL <= 295;

DELETE FROM tbl_pfa WHERE address1 = 'ADDRESS1';