-- COBRA.TBL_CONTRIBUTOR_BIODATA_COBRA definition

CREATE TABLE "TBL_CONTRIBUTOR_BIODATA_COBRA"
   (	"pk" NUMBER GENERATED BY DEFAULT AS IDENTITY MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE  NOKEEP  NOSCALE  NOT NULL ENABLE,
	"ACTIVE" NUMBER(1,0) DEFAULT 1 NOT NULL ENABLE,
	"CREATE_DATE" TIMESTAMP (6) DEFAULT CURRENT_TIMESTAMP NOT NULL ENABLE,
	"LAST_MODIFIED" TIMESTAMP (6) DEFAULT CURRENT_TIMESTAMP NOT NULL ENABLE,
	"RSAPIN" VARCHAR2(255) NOT NULL ENABLE,
	"NIN" VARCHAR2(255) NOT NULL ENABLE,
	"SURNAME" VARCHAR2(255) NOT NULL ENABLE,
	"GENDER" VARCHAR2(255) NOT NULL ENABLE,
	"PFACODE" VARCHAR2(255) NOT NULL ENABLE,
	"EMP_EMPLOYER_CODE" VARCHAR2(255) NOT NULL ENABLE,
	"MIDDLENAME" VARCHAR2(255),
	"FIRSTNAME" VARCHAR2(255) NOT NULL ENABLE,
	"PHONE_NO" VARCHAR2(255) NOT NULL ENABLE,
	"DATE_OF_BIRTH" DATE NOT NULL ENABLE,
	 CONSTRAINT "PK_7e46ae21c45f19421de2f7e4355" PRIMARY KEY ("pk")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"  ENABLE,
	 CONSTRAINT "UQ_5916f450d15fae50ad06bcaf4ef" UNIQUE ("RSAPIN")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS"  ENABLE
   ) SEGMENT CREATION IMMEDIATE
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "USERS" ;

  -- COBRA.TBL_CRS_LEGACY_DATA_COBRA definition

  CREATE TABLE "COBRA"."TBL_CRS_LEGACY_DATA_COBRA"
     (	"pk" NUMBER GENERATED BY DEFAULT AS IDENTITY MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE  NOKEEP  NOSCALE  NOT NULL ENABLE,
  	"ACTIVE" NUMBER(1,0) DEFAULT 1 NOT NULL ENABLE,
  	"CREATE_DATE" TIMESTAMP (6) DEFAULT CURRENT_TIMESTAMP NOT NULL ENABLE,
  	"LAST_MODIFIED" TIMESTAMP (6) DEFAULT CURRENT_TIMESTAMP NOT NULL ENABLE,
  	"PIN" VARCHAR2(255) NOT NULL ENABLE,
  	"LASTNAME" VARCHAR2(255) NOT NULL ENABLE,
  	"SEX" VARCHAR2(255) NOT NULL ENABLE,
  	"PFACODE" VARCHAR2(255) NOT NULL ENABLE,
  	"EMPLOYER_CODE" VARCHAR2(255) NOT NULL ENABLE,
  	"OTHERNAME" VARCHAR2(255) NOT NULL ENABLE,
  	"FIRSTNAME" VARCHAR2(255) NOT NULL ENABLE,
  	"PHONE" VARCHAR2(255),
  	"DOB" DATE NOT NULL ENABLE,
  	 CONSTRAINT "PK_163b8ac71ca613457cacab42eff" PRIMARY KEY ("pk")
    USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
    STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "USERS"  ENABLE
     ) SEGMENT CREATION IMMEDIATE
    PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
   NOCOMPRESS LOGGING
    STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
    PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
    BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
    TABLESPACE "USERS" ;

-- for local 
CREATE OR REPLACE FORCE EDITIONABLE VIEW "TBL_CRS_LEGACY_DATA" ("PIN", "LASTNAME", "SEX", "EMPLOYER_CODE",
"OTHERNAME", "FIRSTNAME", "PHONE", "DOB", "PFA") AS 
  SELECT PIN, LASTNAME, SEX, EMPLOYER_CODE, MIDDLENAME, FIRSTNAME, PHONE_NO, DATE_OF_BIRTH, PFACODE
FROM TBL_CONTRIBUTOR_BIODATA_COBRA;

CREATE OR REPLACE FORCE EDITIONABLE VIEW "TBL_CRS_LEGACY_DATA" ("PIN", "LASTNAME", "SEX", "EMPLOYER_CODE", "OTHERNAME", "FIRSTNAME", "PHONE", "DOB", "PFA") AS
  SELECT PIN, LASTNAME, SEX, EMPLOYER_CODE, OTHERNAME, FIRSTNAME, PHONE, DOB, PFACODE
FROM TBL_CRS_LEGACY_DATA_COBRA;


CREATE OR REPLACE FORCE EDITIONABLE VIEW "COBRA"."TBL_TRANSITIONALPIN_MST" ("TPIN", "SURNAME", "GENDER", "PFACODE", "EMP_EMPLOYER_CODE", "MIDDLENAME", "FIRSTNAME", "PHONE_NO", "DATE_OF_BIRTH") AS
  SELECT TPIN, SURNAME, GENDER, PFACODE, EMP_EMPLOYER_CODE, MIDDLENAME, FIRSTNAME, PHONE_NO, DATE_OF_BIRTH
FROM TBL_TRANSITIONALPIN_MST_DATA;

-- for staging

-- COBRA.TBL_CONTRIBUTOR_BIODATA source
CREATE OR REPLACE FORCE EDITIONABLE VIEW "TBL_CONTRIBUTOR_BIODATA" ("RSAPIN", "SURNAME", "GENDER", "PFACODE", "EMP_EMPLOYER_CODE", "MIDDLENAME", "FIRSTNAME", "PHONE_NO", "DATE_OF_BIRTH", "NIN") AS
  SELECT RSAPIN, SURNAME, GENDER, PFACODE, EMP_EMPLOYER_CODE, MIDDLENAME, FIRSTNAME, PHONE_NO, DATE_OF_BIRTH, NIN
FROM TBL_CONTRIBUTOR_BIODATA_COBRA;

-- COBRA.TBL_CRS_LEGACY_DATA source

CREATE OR REPLACE FORCE EDITIONABLE VIEW "TBL_CRS_LEGACY_DATA" ("PIN", "LASTNAME", "SEX", "EMPLOYER_CODE", "OTHERNAME", "FIRSTNAME", "PHONE", "DOB", "PFA") AS
  SELECT PIN, LASTNAME, SEX, EMPLOYER_CODE, OTHERNAME, FIRSTNAME, PHONE, DOB, PFA
FROM TBL_CONTRIBUTOR_BIODATA_COBRA;

-- COBRA.TBL_TRANSITIONALPIN_MST source

CREATE OR REPLACE FORCE EDITIONABLE VIEW "COBRA"."TBL_TRANSITIONALPIN_MST" ("TPIN", "SURNAME", "GENDER", "PFACODE", "EMP_EMPLOYER_CODE", "MIDDLENAME", "FIRSTNAME", "PHONE_NO", "DATE_OF_BIRTH") AS
  SELECT TPIN, SURNAME, GENDER, PFACODE, EMP_EMPLOYER_CODE, MIDDLENAME, FIRSTNAME, PHONE_NO, DATE_OF_BIRTH
FROM TBL_TRANSITIONALPIN_MST@ECRS;