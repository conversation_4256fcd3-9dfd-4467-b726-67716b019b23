services:
  cobra-redis-master:
    image: redis:6.0.7
    container_name: cobra-redis-master
    restart: always
    volumes:
      - cobra_redis_master:/data
    ports:
      - 6379:6379
    networks:
      - microservices-network

  cobra_redis_insight:
    image: redis/redisinsight:latest
    container_name: cobra_redis_insight
    restart: always
    ports:
      - 5540:5540
    volumes:
      - redis_insight_volume_data:/db
    depends_on:
      - cobra-redis-master
    networks:
      - microservices-network

  cobra-rabbitmq:
    image: rabbitmq:3-management
    container_name: cobra-rabbitmq
    restart: always
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: password
    ports:
      - 5672:5672  # RabbitMQ AMQP port
      - 15672:15672  # RabbitMQ Management UI port
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - microservices-network

networks:
  microservices-network:
    driver: bridge

volumes:
  cobra_redis_master:
  rabbitmq_data:
  redis_insight_volume_data:
