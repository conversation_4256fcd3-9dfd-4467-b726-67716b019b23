services:
  cobra-audit-service:
    image: ghcr.io/keanhive/cobra-audit-service:latest
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    build:
      context: .
      dockerfile: Dockerfile.audit
    ports:
      - '${AUDIT_SERVICE_PORT}:${AUDIT_SERVICE_PORT}'
    volumes:
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - AUDIT_SERVICE_PORT=${AUDIT_SERVICE_PORT}
      - RABBIT_MQ_URL=${RABBIT_MQ_URL}
      - AUDIT_QUEUE_NAME=${AUDIT_QUEUE_NAME}
      - JWT_SECRET=${JWT_SECRET}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
    networks:
      - microservices-network

  cobra-login-service:
    image: ghcr.io/keanhive/cobra-login-service:latest
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    build:
      context: .
      dockerfile: Dockerfile.login
    ports:
      - '${LOGIN_SERVICE_PORT}:${LOGIN_SERVICE_PORT}'
    volumes:
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - LOGIN_SERVICE_PORT=${LOGIN_SERVICE_PORT}
      - USER_SERVICE_HOST=${USER_SERVICE_HOST}
      - USER_SERVICE_PORT=${USER_SERVICE_PORT}
      - USER_SERVICE_TCP_PORT=${USER_SERVICE_TCP_PORT}
      - JWT_SECRET=${JWT_SECRET}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
      - CLIENT_TIME_TOLERANCE_MINUTES=${CLIENT_TIME_TOLERANCE_MINUTES}
      - ECRS_SERVICE_HOST=${ECRS_SERVICE_HOST}
      - ECRS_SERVICE_TCP_PORT=${ECRS_SERVICE_TCP_PORT}
      - NOTIFICATION_SERVICE_TCP_PORT=${NOTIFICATION_SERVICE_TCP_PORT}
      - NOTIFICATION_SERVICE_HOST=${NOTIFICATION_SERVICE_HOST}
    depends_on:
      - cobra-user-service
    networks:
      - microservices-network

  cobra-user-service:
    image: ghcr.io/keanhive/cobra-user-service:latest
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    build:
      context: .
      dockerfile: Dockerfile.user
    ports:
      - '${USER_SERVICE_PORT}:${USER_SERVICE_PORT}'
      - '${USER_SERVICE_TCP_PORT}:${USER_SERVICE_TCP_PORT}'
    volumes:
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - USER_SERVICE_PORT=${USER_SERVICE_PORT}
      - USER_SERVICE_TCP_PORT=${USER_SERVICE_TCP_PORT}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
      - ECRS_SERVICE_HOST=${ECRS_SERVICE_HOST}
      - ECRS_SERVICE_TCP_PORT=${ECRS_SERVICE_TCP_PORT}
      - NOTIFICATION_SERVICE_TCP_PORT=${NOTIFICATION_SERVICE_TCP_PORT}
      - NOTIFICATION_SERVICE_HOST=${NOTIFICATION_SERVICE_HOST}
    networks:
      - microservices-network

  cobra-setting-service:
    image: ghcr.io/keanhive/cobra-setting-service:latest
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    build:
      context: .
      dockerfile: Dockerfile.setting
    ports:
      - '${SETTINGS_SERVICE_PORT}:${SETTINGS_SERVICE_PORT}'
    volumes:
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - SETTINGS_SERVICE_PORT=${SETTINGS_SERVICE_PORT}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
    networks:
      - microservices-network

  cobra-notification-service:
    image: ghcr.io/keanhive/cobra-notification-service:latest
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    build:
      context: .
      dockerfile: Dockerfile.notification
    ports:
      - '${NOTIFICATION_SERVICE_PORT}:${NOTIFICATION_SERVICE_PORT}'
      - '${NOTIFICATION_SERVICE_TCP_PORT}:${NOTIFICATION_SERVICE_TCP_PORT}'
    volumes:
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - NOTIFICATION_SERVICE_PORT=${NOTIFICATION_SERVICE_PORT}
      - NOTIFICATION_SERVICE_TCP_PORT=${NOTIFICATION_SERVICE_TCP_PORT}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
    networks:
      - microservices-network

  cobra-ecrs-service:
    image: ghcr.io/keanhive/cobra-ecrs-service:latest
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    build:
      context: .
      dockerfile: Dockerfile.ecrs
    ports:
      - '${ECRS_SERVICE_PORT}:${ECRS_SERVICE_PORT}'
      - '${ECRS_SERVICE_TCP_PORT}:${ECRS_SERVICE_TCP_PORT}'
    volumes:
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - ECRS_SERVICE_PORT=${ECRS_SERVICE_PORT}
      - ECRS_SERVICE_TCP_PORT=${ECRS_SERVICE_TCP_PORT}
      - ECRS_SERVICE_HOST=${ECRS_SERVICE_HOST}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
    networks:
      - microservices-network

  cobra-enrollment-service:
    image: ghcr.io/keanhive/cobra-enrollment-service:latest
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    build:
      context: .
      dockerfile: Dockerfile.enrollmentms
    ports:
      - '${ENROLLMENT_SERVICE_HTTP_PORT}:${ENROLLMENT_SERVICE_HTTP_PORT}'
      - '${ENROLLMENT_SERVICE_TCP_PORT}:${ENROLLMENT_SERVICE_TCP_PORT}'
    volumes:
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - ENROLLMENT_SERVICE_HTTP_PORT=${ENROLLMENT_SERVICE_HTTP_PORT}
      - ENROLLMENT_SERVICE_TCP_PORT=${ENROLLMENT_SERVICE_TCP_PORT}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
      - RABBIT_MQ_URL=${RABBIT_MQ_URL}
      - ECRS_SERVICE_HOST=${ECRS_SERVICE_HOST}
      - ECRS_SERVICE_TCP_PORT=${ECRS_SERVICE_TCP_PORT}
      - NOTIFICATION_SERVICE_TCP_PORT=${NOTIFICATION_SERVICE_TCP_PORT}
      - NOTIFICATION_SERVICE_HOST=${NOTIFICATION_SERVICE_HOST}
    networks:
      - microservices-network

  cobra-enrollment-engine-service:
    image: ghcr.io/keanhive/cobra-enrollment-engine-service:latest
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    build:
      context: .
      dockerfile: Dockerfile.enrollmentengine
    ports:
      - '${ENROLLMENT_ENGINE_SERVICE_PORT}:${ENROLLMENT_ENGINE_SERVICE_PORT}'
    volumes:
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - ENROLLMENT_ENGINE_SERVICE_PORT=${ENROLLMENT_ENGINE_SERVICE_PORT}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
      - RABBIT_MQ_URL=${RABBIT_MQ_URL}
      - ECRS_SERVICE_HOST=${ECRS_SERVICE_HOST}
      - ECRS_SERVICE_TCP_PORT=${ECRS_SERVICE_TCP_PORT}
      - ENROLLMENT_SERVICE_TCP_PORT=${ENROLLMENT_SERVICE_TCP_PORT}
      - ENROLLMENT_SERVICE_HOST=${ENROLLMENT_SERVICE_HOST}
    networks:
      - microservices-network

  cobra-cron-service:
    image: ghcr.io/keanhive/cobra-cron-service:latest
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    build:
      context: .
      dockerfile: Dockerfile.cron
    ports:
      - '${CRON_SERVICE_PORT}:${CRON_SERVICE_PORT}'
    volumes:
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - CRON_SERVICE_PORT=${CRON_SERVICE_PORT}
      - NOTIFICATION_SERVICE_TCP_PORT=${NOTIFICATION_SERVICE_TCP_PORT}
      - NOTIFICATION_SERVICE_HOST=${NOTIFICATION_SERVICE_HOST}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - RABBIT_MQ_URL=${RABBIT_MQ_URL}
    networks:
      - microservices-network

#  pdf-service:
#    image: ghcr.io/keanhive/pdf-generator-service:latest
#    container_name: pdf-generator
#    restart: always
#    logging:
#      driver: "json-file"
#      options:
#        max-size: "10m"
#        max-file: "3"
#    build:
#      context: .
#      dockerfile: Dockerfile.pdfgen
#    ports:
#      - '${PDF_SERVICE_PORT}:${PDF_SERVICE_PORT}'
#      - '9248:9248'
#    volumes:
#      - ./pdf-service/logs:/app/logs
#    working_dir: /usr/src/app
#    environment:
#      - TZ=Africa/Lagos
#      - PDF_SERVICE_PORT=${PDF_SERVICE_PORT}
#    networks:
#      - microservices-network

networks:
  microservices-network:
    driver: bridge
