import js from '@eslint/js';
import ts from '@typescript-eslint/eslint-plugin';
import tsParser from '@typescript-eslint/parser';
import prettier from 'eslint-config-prettier';
import prettierPlugin from 'eslint-plugin-prettier';

export default [
    js.configs.recommended,
    {
        ignores: ["node_modules", "dist"],
    },
    {
        files: ["**/*.ts", "**/*.tsx"],
        languageOptions: {
            parser: tsParser,
            parserOptions: {
                project: "./tsconfig.json",
                tsconfigRootDir: process.cwd(),
                sourceType: "module",
            },
        },
        plugins: {
            "@typescript-eslint": ts,
            prettier: prettierPlugin,
        },
        rules: {
            // ⚠️ Strict TypeScript Rules
            "@typescript-eslint/explicit-function-return-type": "error",
            "@typescript-eslint/no-explicit-any": "error",
            "@typescript-eslint/no-unused-vars": ["error", { argsIgnorePattern: "^_" }],
            "@typescript-eslint/strict-boolean-expressions": "error",
            "@typescript-eslint/no-inferrable-types": "error",
            "@typescript-eslint/naming-convention": [
                "error",
                {
                    "selector": "interface",
                    "format": ["PascalCase"],
                    "custom": {
                        "regex": "^I[A-Z]",
                        "match": true
                    }
                }
            ],
            '@typescript-eslint/explicit-module-boundary-types': 'error',

            // ✅ Code Quality
            "no-console": "warn",
            "no-debugger": "error",
            "prefer-const": "error",
            "eqeqeq": ["error", "always"],

            // ✨ Prettier Formatting
            "prettier/prettier": "error",
        },
    },
    prettier,
];
