{"compilerOptions": {"strict": false, "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "declaration": true, "removeComments": false, "strictFunctionTypes": false, "strictNullChecks": false, "strictPropertyInitialization": false, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@app/shared": ["libs/shared/src"], "@app/shared/*": ["libs/shared/src/*"]}}}