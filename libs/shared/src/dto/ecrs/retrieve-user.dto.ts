import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';
import { RsaPinValidator } from '@app/shared/decorators/rsapin-validator.decorator';

export class RetrieveUserDto {
  @ApiProperty({
    example: 'PEN123456789012',
    description: 'RSA PIN of the user, must be 15 characters long and start with PEN',
  })
  @IsString()
  @IsNotEmpty()
  @RsaPinValidator()
  @ApiProperty({
    example: '12345678',
    description: 'RSA PIN of the user',
  })
  @IsString()
  @IsNotEmpty()
  rsaPin: string;

  @ApiProperty({
    example: 'Smith',
    description: 'Surname of the user',
  })
  @IsString()
  @IsNotEmpty()
  surname: string;

  @ApiProperty({
    example: 'retiree',
    description: 'Type of user (deceased or retiree)',
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(RetireeUserTypeEnum, {
    message: 'User status must be RETIREE or DECEASED',
  })
  userType: RetireeUserTypeEnum;

  skipSurnameCheck?: boolean;

  checkCrsStatus?: boolean;
}

export class RetrieveUserWithPinDto {
  @ApiProperty({
    example: 'PEN123456789012',
    description: 'RSA PIN of the user, must be 15 characters long and start with PEN',
  })
  @IsString()
  @IsNotEmpty()
  @RsaPinValidator()
  @ApiProperty({
    example: '12345678',
    description: 'RSA PIN of the user',
  })
  @IsString()
  @IsNotEmpty()
  rsaPin: string;
}
