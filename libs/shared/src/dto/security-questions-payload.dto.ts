import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ArrayMinSize, IsArray, IsEmail, IsNotEmpty, IsString, ValidateNested } from 'class-validator';

export class SaveSecurityQuestionsRequestDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ValidateNested({ each: true })
  @ArrayMinSize(1, {
    message: 'At least one security question must be provided.',
  })
  @Type(() => SecurityQuestionDto)
  securityQuestions: SecurityQuestionDto[];
}

class SecurityQuestionDto {
  @IsNotEmpty()
  @IsString()
  question: string;

  @IsNotEmpty()
  @IsString()
  answer: string;
}

export class VerifySecurityQuestionsRequestDto {
  @IsString()
  @IsNotEmpty()
  emailAddress: string;

  @IsArray({ message: 'Questions must be an array.' })
  @ArrayMinSize(1, { message: 'At least one question must be provided.' })
  @ValidateNested({ each: true })
  @Type(() => VerifiedSecurityQuestionDto)
  questions: VerifiedSecurityQuestionDto[];
}

export class VerifiedSecurityQuestionDto {
  @ApiProperty({
    description: 'The security question',
    example: 'What is the return policy?',
  })
  @Expose()
  @IsString()
  @IsNotEmpty()
  question: string;

  @ApiProperty({
    description: 'The answer to the security question (if it exists)',
    example: 'Returns are accepted within 30 days with a receipt.',
    required: false,
  })
  @Expose()
  @IsString()
  @IsNotEmpty()
  answer: string;
}

export class GetSecurityQuestionsResponseDto extends BaseResponseDto {
  @ApiProperty({
    description: 'An array of security questions',
    type: [String],
    example: [
      'What was the name of your first pet?',
      'What is your mother’s maiden name?',
      'What city were you born in?',
    ],
  })
  @Expose()
  questions: string[];
}

export class GetUserSecurityQuestionRequestDto {
  @IsEmail()
  email: string;
}
