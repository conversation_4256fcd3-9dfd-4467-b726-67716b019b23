import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';
import { UploadProcessTypeEnum } from '@app/shared/enums/upload-process-type-enum';
import { ApiProperty } from '@nestjs/swagger';
import { BatchUploadProcessTypeEnum } from '@app/shared/enums/batch-upload-process-type-enum';

export class FileUploadRequestDto {
  @ApiProperty({
    description: 'Process type for the file upload',
    example: UploadProcessTypeEnum.MDA_RETIREE_DATA_UPLOAD,
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(UploadProcessTypeEnum, {
    message: `Process Type must be one of: ${Object.values(UploadProcessTypeEnum).join(', ')}`,
  })
  processType: UploadProcessTypeEnum;
}

export class BatchFileUploadRequestDto {
  @IsNotEmpty()
  @IsEnum(BatchUploadProcessTypeEnum, {
    message: `Process Type must be one of: ${Object.values(BatchUploadProcessTypeEnum).join(', ')}`,
  })
  processType: BatchUploadProcessTypeEnum;

  @IsNotEmpty()
  @IsString()
  batchName: string;
}

export class BatchPaymentConfirmationUploadRequestDto {
  @IsNotEmpty()
  @IsString()
  batchName: string;
}
