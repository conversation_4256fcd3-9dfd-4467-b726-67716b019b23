import { Is<PERSON>rray, IsEmail, <PERSON>Not<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';

export class SendNotificationDto {
  @IsString()
  @IsNotEmpty()
  subject: string;

  @IsArray()
  @IsEmail({}, { each: true })
  email: string[];

  @IsString()
  @IsNotEmpty()
  content: string;

  @IsArray()
  @IsOptional()
  attachments: { filename: string; content: Buffer; contentType: string }[];
}
