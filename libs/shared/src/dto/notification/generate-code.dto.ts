import { IsEmail, IsIn, IsObject, Validate } from 'class-validator';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { IsRecordOfStrings } from '@app/shared/decorators/is-record-strings.decorator';

export class GenerateCodeDto {
  @IsIn(Object.values(NotificatonTypeEnum))
  otpRecordType: string;

  @IsEmail()
  email: string;

  @IsObject()
  @Validate(IsRecordOfStrings)
  placeholders: Record<string, string>;
}
