import { IsEmail, IsIn, IsNotEmpty, IsString } from 'class-validator';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { ApiProperty } from '@nestjs/swagger';

export class VerifyOtpDto {
  @ApiProperty({
    description: 'Process type for the OTP verification',
    example: NotificatonTypeEnum.REGISTER,
    required: true,
  })
  @IsNotEmpty()
  @IsIn(Object.values(NotificatonTypeEnum))
  processType: string;

  @ApiProperty({
    description: "User's email address",
    example: '<EMAIL>',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'otp to be verified',
    example: '123445',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  otp: string;
}
