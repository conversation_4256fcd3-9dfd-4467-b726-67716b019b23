import { Is<PERSON>rray, IsEmail, IsEnum, IsObject, IsOptional, IsString, Validate } from 'class-validator';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { IsRecordOfStrings } from '@app/shared/decorators/is-record-strings.decorator';

export class SendNotificationTemplateDto {
  @IsString()
  @IsEnum(NotificatonTypeEnum)
  notificationType: NotificatonTypeEnum;

  @IsArray()
  @IsEmail({}, { each: true })
  emailRecipients: string[];

  @IsObject()
  @Validate(IsRecordOfStrings)
  placeholders: Record<string, string>;

  @IsArray()
  @IsOptional()
  attachments: { filename: string; content: Buffer; contentType: string }[];
}
