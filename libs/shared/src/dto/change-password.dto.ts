import { IsE<PERSON>, IsIn, <PERSON>NotEmpty, IsOptional, IsString } from 'class-validator';
import { PasswordResetTypeEnum } from '@app/shared/enums/PasswordResetTypeEnum';
import { Match } from '@app/shared/decorators';

export class ChangePasswordDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsIn(Object.values(PasswordResetTypeEnum))
  resetType: string;

  @IsString()
  @IsNotEmpty()
  newPassword: string;

  @IsString()
  @IsNotEmpty()
  @Match('newPassword', { message: 'Passwords do not match' })
  confirmNewPassword: string;

  @IsString()
  @IsOptional()
  otp?: string;
}

export class ChangeOldPasswordDto {
  @IsString()
  @IsNotEmpty()
  oldPassword: string;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  password: string;
}

export class ResetPasswordDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  userType: string;
}
