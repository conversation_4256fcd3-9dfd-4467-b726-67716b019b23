import { Expose } from 'class-transformer';

export class CobraUserProfileDto {
  @Expose()
  rsaPin: string;

  @Expose()
  firstName: string;

  @Expose()
  surname: string;

  @Expose()
  emailAddress: string;

  @Expose()
  staffId: string;

  @Expose()
  mdaCode: string;

  @Expose()
  mdaName: string;

  @Expose()
  pfaCode: string;

  @Expose()
  pfaName: string;

  @Expose()
  userType: string;

  @Expose()
  roleName: string;

  @Expose()
  rolePk: string;

  @Expose()
  createdByFirstName: string;

  @Expose()
  createdByLastName: string;

  signature: string;
  avatar: string;
  gender: string;
  blacklisted: boolean;
  active: boolean;
  phoneNumber: string;
  location: string;
  isValidator: boolean;
  isAuditor: boolean;
  pendingRecordCount: number;
}
