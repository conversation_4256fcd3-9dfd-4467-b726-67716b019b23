import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { HelpResourceType } from './resource-type.enum';

export class CreateHelpTemplateDTO {
  @ApiProperty({ description: 'Template title', example: 'Password Management' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'Template description', example: 'Resources for managing your password' })
  @IsString()
  description: string;

  @ApiProperty({ description: 'Section name', example: 'Account Management' })
  @IsString()
  @IsNotEmpty()
  section: string;

  @ApiProperty({ description: 'Resource type', enum: HelpResourceType, example: HelpResourceType.PDF })
  @IsEnum(HelpResourceType)
  @IsNotEmpty()
  type: HelpResourceType;

  @ApiProperty({ description: 'Resource link', example: 'https://example.com/guide.pdf' })
  @IsString()
  @IsNotEmpty()
  link: string;

  @ApiProperty({ description: 'User type', enum: UserTypeEnum, example: UserTypeEnum.PFA })
  @IsEnum(UserTypeEnum)
  @IsNotEmpty()
  userType: UserTypeEnum;
}
