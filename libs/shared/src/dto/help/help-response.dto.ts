import { ApiProperty } from '@nestjs/swagger';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { HelpResourceType } from '@app/shared/dto/help/resource-type.enum';

export class HelpItemDTO {
  @ApiProperty({ description: 'Resource title', example: 'MDA USER MANUAL' })
  title: string;

  @ApiProperty({ description: 'Resource description', example: 'MDA USER MANUAL DESCRIPTION' })
  description: string;

  @ApiProperty({ description: 'Resource type', enum: HelpResourceType, example: 'PDF' })
  type: HelpResourceType;

  @ApiProperty({ description: 'Resource link', example: 'https://example.com/manual.pdf' })
  link: string;
}

export class HelpResponseDTO extends BaseResponseDto {
  @ApiProperty({
    description: 'Map of help items by section',
    example: {
      'Getting Started': [
        {
          title: 'MDA USER MANUAL',
          description: 'MDA USER MANUAL DESCRIPTION',
          type: 'PDF',
          link: 'https://example.com/manual.pdf',
        },
      ],
    },
  })
  content: Record<string, HelpItemDTO[]>;
}
