import { IsNumber, IsObject, IsOptional, Validate } from 'class-validator';
import { IsRecordOfStrings } from '@app/shared/decorators/is-record-strings.decorator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class PaginatedSearchDto {
  @ApiProperty({ required: false, description: 'Search query' })
  @IsOptional()
  @IsObject()
  @Validate(IsRecordOfStrings)
  filters?: Record<string, string>;

  @ApiProperty({ required: false, default: 1 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({ required: false, default: 10 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number = 10;
}
