/* eslint-disable */
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  Validate,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { UserTypeEnum } from '../enums/UserTypeEnum';
import { Expose } from 'class-transformer';
import { Injectable } from '@nestjs/common';
import { IsValidClientTime } from '../decorators/valid-client-time.decorator';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ApiProperty } from '@nestjs/swagger';

@ValidatorConstraint({ name: 'customUsername', async: false })
@Injectable()
export class CustomUsernameValidator implements ValidatorConstraintInterface {
  validate(username: string, args: ValidationArguments): boolean {
    const { object } = args;
    const userType = (object as any).userType;

    if (userType === UserTypeEnum.RETIREE) {
      return /^PEN\d{12}$/.test(username);
    }

    if (userType === UserTypeEnum.MDA || userType === UserTypeEnum.PFA) {
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(username);
    }

    if (userType === UserTypeEnum.PENCOM) {
      return typeof username === 'string' && username.length > 0;
    }

    return false;
  }

  defaultMessage(args: ValidationArguments): string {
    const { object } = args;
    const userType = (object as any).userType;

    if (userType === UserTypeEnum.RETIREE) {
      return 'Username must start with "PEN" followed by 12 digits for RETIREE users';
    }
    if (userType === UserTypeEnum.MDA || userType === UserTypeEnum.PFA) {
      return 'Username must be a valid email for MDA and PFA users';
    }
    return 'Invalid username format';
  }
}

export class LoginAccessDto {
  @ApiProperty({
    description: 'Username can be email address or PENCOM ID',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @Validate(CustomUsernameValidator, ['userType'])
  username: string;

  @ApiProperty({
    description: 'userType can be MDA, PFA, RETIREE, PENCOM',
    example: 'MDA',
  })
  @IsEnum(UserTypeEnum)
  userType: UserTypeEnum;

  @ApiProperty({
    description: 'Password for the user',
    example: 'password',
  })
  @IsNotEmpty()
  @IsString()
  password: string;

  @ApiProperty({
    description: 'Client time for the login',
    example: new Date().toISOString(),
  })
  @IsDateString()
  @IsNotEmpty()
  //@IsValidClientTime()
  clientTime: Date;

  @ApiProperty({
    description: 'Captcha token',
    example: 'token',
  })
  @IsOptional()
  @IsString()
  captcha: string;
}

export class LoginAccessResponseDto extends BaseResponseDto {
  @Expose()
  firstTimeLogin: boolean;

  @Expose()
  updatePassword: boolean;

  @Expose()
  securityQuestions: boolean;

  @Expose()
  token: string;

  @Expose()
  twoFactorAuthRequired: boolean;

  @Expose()
  twoFactorAuthOnboardingRequired: boolean;
}
