/* eslint-disable */
import { Expose } from 'class-transformer';
import { ResponseCodeEnum, ResponseCodeEnumDescription } from '@app/shared/enums';
import { ApiProperty } from '@nestjs/swagger';

export class BaseResponseDto {
  @ApiProperty({
    description: 'Response code ',
    enum: ResponseCodeEnum,
  })
  @Expose()
  code: number;

  @ApiProperty({
    description: 'Response description ',
    enum: ResponseCodeEnumDescription,
  })
  @Expose()
  description: string;

  constructor(responseCodeEnum?: ResponseCodeEnum) {
    if (responseCodeEnum) {
      this.setResponseCode(responseCodeEnum);
    }
  }

  setResponseCode(responseCodeEnum: ResponseCodeEnum) {
    this.code = responseCodeEnum;
    this.description = ResponseCodeEnumDescription[responseCodeEnum];
  }

  setDescription(description: string) {
    this.description = description;
  }
}
