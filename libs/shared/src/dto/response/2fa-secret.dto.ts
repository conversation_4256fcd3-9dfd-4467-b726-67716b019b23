import { LoginAccessDto } from '@app/shared/dto/login-access.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString } from 'class-validator';

export class Generate2FASecretResponse {
  otpAuthUrl: string;
  qrCodeDataUrl: string;
  base32: string;
}

export class Verify2FACode extends LoginAccessDto {
  @ApiProperty({
    description: '2FA code to be verified',
    example: '123445',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  twoFactorToken: string;
}

export class Reset2FaSecret {
  @ApiProperty({
    description: "User's email address",
    example: '<EMAIL>',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;
}
