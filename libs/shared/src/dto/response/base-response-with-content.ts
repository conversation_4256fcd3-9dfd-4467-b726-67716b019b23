import { BaseResponseDto } from './base-response.dto';
import { ResponseCodeEnum } from '@app/shared/enums';

export class BaseResponseWithContent<T> extends BaseResponseDto {
  content: T[];
  total?: number;
  page?: number;
  limit?: number;
}

export class BaseResponseWithNoCountInfo<T> extends BaseResponseDto {
  content: T[];
  hasPrevious: boolean;
  hasNext: boolean;
}
export class BaseResponseWithContentNoPagination<T> extends BaseResponseDto {
  content: T;

  constructor(responseCode: ResponseCodeEnum, content?: T) {
    super(responseCode);
    this.content = content ?? (null as T); // Ensure content is initialized properly
  }
}

export class BaseResponseListWithContentNoPagination<T> extends BaseResponseDto {
  content: T | T[];

  constructor(responseCode: ResponseCodeEnum, content?: T) {
    super(responseCode);
    this.content = content ?? (null as T[]); // Ensure content is initialized properly
  }
}
