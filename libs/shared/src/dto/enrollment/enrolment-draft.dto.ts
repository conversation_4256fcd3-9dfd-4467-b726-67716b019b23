import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsObject, IsOptional, IsPositive, IsString, Matches } from 'class-validator';
import { IsNotEmptyObjectCustom } from '@app/shared/decorators/is-not-empty-object.decorator';
import { Transform } from 'class-transformer';

export class SaveEnrolmentDraftDto {
  @IsPositive()
  @IsNumber()
  stepNumber: number;

  @IsNotEmpty()
  @IsObject()
  @IsNotEmptyObjectCustom({ message: 'formData cannot be empty and must contain at least one key' })
  formData: Record<string, any>;
}

export class GetEnrolmentDraftDto {
  @IsNotEmpty()
  @IsString()
  rsaPin: string;
}

export class RetrieveYearDetailsDto {
  @IsNotEmpty()
  @IsString()
  year: string;
}

export class RetrieveYearSectorDetailsDto extends RetrieveYearDetailsDto {
  @IsNotEmpty()
  @IsString()
  organizationSector: string;
}

export class RetrieveSectorEmployerDetailsDto extends RetrieveYearSectorDetailsDto {
  @IsNotEmpty()
  @IsString()
  employerCode: string;
}

export class RetrieveOrganisationDetailsDto {
  @IsNotEmpty()
  @IsString()
  employerCode: string;
}

export class RetrieveCompleteEmployerDetailsDto extends RetrieveSectorEmployerDetailsDto {
  salaryStructure: string;
  gradelevel: string;
  step: string;
  ippisDate: string;
}

export class BandCalculation {
  @IsString()
  @IsOptional()
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Leave Start date must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  @Transform(({ value }) => String(value))
  startDate: string;

  @IsString()
  @IsOptional()
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Leave End date must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  @Transform(({ value }) => String(value))
  endDate: string;

  @IsString()
  @IsOptional()
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'IPPIS date must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  @Transform(({ value }) => String(value))
  ippisDate: string;
}
