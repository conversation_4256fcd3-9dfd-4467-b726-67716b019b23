import { IsNotEmpty, IsString, Matches } from 'class-validator';
import { Transform } from 'class-transformer';
import { MdaBiodataDto } from '@app/shared/dto/enrollment/mda-data-upload.dto';
import { IMdaBaseBioData } from '@app/shared/dto/enrollment/interfaces/mda-base-bio-data.interface';

export class MdaDeceasedDataUploadDto extends MdaBiodataDto implements IMdaBaseBioData {
  @IsString()
  @IsNotEmpty()
  @Matches(/^([0-2][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/, {
    message: 'Date of Death must be in DD/MM/YYYY format (e.g., 04/04/2025)',
  })
  @Transform(({ value }) => String(value))
  dateOfDeath: string;
}
