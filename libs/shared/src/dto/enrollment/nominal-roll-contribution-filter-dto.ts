import { <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsString, Matches, Validate } from 'class-validator';
import { Transform } from 'class-transformer';
import { NrDateRangeValidator } from '@app/shared/decorators/validate-nr-computation-start-end-dates.decorator';

export class NominalRollContributionFilterDto {
  @IsNotEmpty()
  @IsString()
  filterName: string;

  @IsString()
  @IsOptional()
  rsaPin?: string;

  @IsString()
  @IsOptional()
  mdaCode?: string;

  @IsString()
  @IsOptional()
  pfaCode?: string;

  @IsNumber()
  @IsOptional()
  totalAmount?: number;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => {
    if (typeof value !== 'string') return undefined;
    const trimmed = value.trim();
    return trimmed === '' ? undefined : trimmed;
  })
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Upload Start date must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  uploadStartDate: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => {
    if (typeof value !== 'string') return undefined;
    const trimmed = value.trim();
    return trimmed === '' ? undefined : trimmed;
  })
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Upload End date must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  uploadEndDate: string;

  @Validate(NrDateRangeValidator)
  validateDates: boolean;
}
