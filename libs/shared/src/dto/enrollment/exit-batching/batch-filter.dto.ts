import { IsIn, IsNotEmpty, <PERSON>N<PERSON>ber, IsOptional, IsString, Matches, Validate } from 'class-validator';
import { Transform } from 'class-transformer';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';
import { DateRangeValidator } from '@app/shared/decorators/validate-batching-start-end-dates.decorator';

export class StampScheduleDto {
  @IsString()
  batchName: string;

  @IsString()
  actorEmailAddress: string;

  @IsNotEmpty()
  stampType: 'EXCO_MEMO' | 'AUDIT_SCHEDULE';

  processType: 'NOMINAL_ROLL' | 'EXIT_RECORD' = 'EXIT_RECORD';
}
export class BatchMemoGenerationDto {
  @IsString()
  batchName: string;

  @IsNumber()
  rbbrfBalance: number;
}

export class BatchDocumentDto {
  @IsString()
  batchName: string;
}

export class BatchFilterDto {
  @IsString()
  batchName: string;

  @IsNumber()
  @IsOptional()
  totalAmount?: number;

  @IsIn([RetireeUserTypeEnum.DECEASED, RetireeUserTypeEnum.RETIREE], {
    message: 'Retiree user type must be either "RETIREE" or "DECEASED"',
  })
  rsaHolderType: string;

  @IsIn(['Accrued Rights', 'Contributions'])
  batchType: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => {
    if (typeof value !== 'string') return undefined;
    const trimmed = value.trim();
    return trimmed === '' ? undefined : trimmed;
  })
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Enrolment Start date must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  enrollmentStartDate: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => {
    if (typeof value !== 'string') return undefined;
    const trimmed = value.trim();
    return trimmed === '' ? undefined : trimmed;
  })
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Enrolment End date must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  enrollmentEndDate: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => {
    if (typeof value !== 'string') return undefined;
    const trimmed = value.trim();
    return trimmed === '' ? undefined : trimmed;
  })
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Retirement Start date must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  retirementStartDate: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => {
    if (typeof value !== 'string') return undefined;
    const trimmed = value.trim();
    return trimmed === '' ? undefined : trimmed;
  })
  @Matches(/^([0-2][0-9]|3[01]|[1-9])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Retirement End date must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  retirementEndDate: string;

  @Validate(DateRangeValidator)
  validateDates: boolean;
}
