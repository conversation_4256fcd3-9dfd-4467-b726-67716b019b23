import { IsDateString, IsInt, IsNotEmpty, IsOptional, IsString, Min, ValidateNested } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class BandDataDto {
  @IsInt()
  year: number;

  @IsString()
  employer: string;

  @IsString()
  salaryStructure: string;

  @IsString()
  gl: string;

  @IsString()
  step: string;
}

export class EmployeeDataDto {
  @IsString()
  rsaPin: string;

  @IsString()
  surname: string;

  @IsString()
  dateOfFirstAppointment: string;

  @IsOptional()
  @IsString()
  dateOfExit?: Date;

  @ValidateNested({ each: true })
  @Type(() => BandDataDto)
  bandData: BandDataDto[];
}

export class NominalRollDetailSearchDto {
  @IsNotEmpty()
  @IsString()
  rsaPin: string;
}
export class NominalRollSearchDto {
  @IsOptional()
  @IsString()
  searchTerm?: string;

  @IsOptional()
  @IsString()
  rsaPin?: string;

  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  surname?: string;

  @IsOptional()
  @IsString()
  otherName?: string;

  @IsOptional()
  @IsDateString()
  dateOfFirstAppointment: string;

  @IsOptional()
  @IsDateString()
  dateOfExit: string;

  @IsOptional()
  @IsString()
  mdaCode: string;

  @IsOptional()
  @IsString()
  pfaCode: string;

  @IsOptional()
  @IsDateString()
  createDate?: string;

  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10), { toClassOnly: true })
  @IsInt()
  @Min(1)
  page?: number;

  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10), { toClassOnly: true })
  @IsInt()
  @Min(1)
  limit?: number;
}
