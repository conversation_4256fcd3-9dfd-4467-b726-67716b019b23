import { IsIn, IsNotEmpty, IsObject, IsString, ValidateIf } from 'class-validator';
import { IsNotEmptyObjectCustom } from '@app/shared/decorators/is-not-empty-object.decorator';
import { EnrolmentMachineEventType, eventValues } from '@app/shared/workflow/workflow/exit-ar-cr.workflow';
import { RsaPinValidator } from '@app/shared/decorators/rsapin-validator.decorator';

export class ProcessEnrolmentDto {
  @IsNotEmpty()
  @IsString()
  @IsIn(eventValues)
  action: EnrolmentMachineEventType;

  @IsNotEmpty()
  @IsString()
  @RsaPinValidator()
  rsaPin: string;

  @ValidateIf(
    (dto: ProcessEnrolmentDto) =>
      dto.action === 'PENCOM_VALIDATOR_REJECTION' ||
      dto.action === 'AUDIT_VALIDATOR_REJECTION' ||
      dto.action === 'PENCOM_VALIDATOR_APPROVE_AMENDMENT_REQUEST' ||
      dto.action === 'AUDIT_SUPERVISOR_REJECTION'
  )
  @IsNotEmpty()
  @IsString()
  comment: string;

  @ValidateIf(
    (dto: ProcessEnrolmentDto) =>
      dto.action === 'ENROLMENT' || dto.action === 'PFA_APPROVAL' || dto.action === 'PENCOM_VALIDATOR_APPROVAL'
  )
  @IsNotEmpty()
  @IsObject()
  @IsNotEmptyObjectCustom({ message: 'Employee information must be provided.' })
  formData?: Record<string, any>;
}

export class ProcessBulkEnrolmentDto {
  @IsNotEmpty()
  @IsString()
  @IsIn(eventValues)
  action: EnrolmentMachineEventType;

  @IsNotEmpty()
  @IsObject()
  @IsNotEmptyObjectCustom({ message: 'formData cannot be empty and must contain at least one key' })
  formData?: Record<string, any>;
}
