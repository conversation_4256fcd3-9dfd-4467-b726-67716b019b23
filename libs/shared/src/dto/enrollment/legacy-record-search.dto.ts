import { PaginatedSearchDto } from '../paginated-search.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsNumber } from 'class-validator';

export class LegacyRecordSearchDto extends PaginatedSearchDto {
  rsaPin?: string;
  nin?: string;
  name?: string;
}

export class LegacyRecordDocumentDto {
  @ApiProperty({ description: 'Legacy record ID' })
  @IsNumber()
  legacyRecordId: number;
}
