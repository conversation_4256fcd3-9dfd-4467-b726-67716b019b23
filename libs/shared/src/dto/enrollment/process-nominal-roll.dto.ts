import { IsIn, <PERSON>NotEmpty, IsObject, IsString } from 'class-validator';
import { IsNotEmptyObjectCustom } from '@app/shared/decorators/is-not-empty-object.decorator';
import {
  eventValues,
  NominalRollMachineEventType,
} from '@app/shared/workflow/workflow/norminal-roll-contribution.workflow';

export class ProcessNominalRollDto {
  @IsNotEmpty()
  @IsString()
  @IsIn(eventValues)
  action: NominalRollMachineEventType;

  @IsNotEmpty()
  @IsObject()
  @IsNotEmptyObjectCustom({ message: 'Options cannot be empty and must contain at least one key' })
  options?: Record<string, any>;
}
