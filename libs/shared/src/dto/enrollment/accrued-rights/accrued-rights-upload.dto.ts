import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsPositive, IsString, Matches } from 'class-validator';
import { Transform } from 'class-transformer';
import { RsaPinValidator } from '@app/shared/decorators/rsapin-validator.decorator';

export class AccruedRightsUploadDto {
  @IsString()
  @IsNotEmpty()
  @RsaPinValidator()
  rsaPin: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^([0-2][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/, {
    message: 'Date of Retirement must be in DD/MM/YYYY format (e.g., 04/04/2025)',
  })
  @Transform(({ value }) => String(value))
  dateOfBirth: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^([0-2][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/, {
    message: 'Date of Retirement must be in DD/MM/YYYY format (e.g., 04/04/2025)',
  })
  @Transform(({ value }) => String(value))
  dateOfRetirement: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^([0-2][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/, {
    message: 'Date of First Appointment must be in DD/MM/YYYY format (e.g., 04/04/2025)',
  })
  @Transform(({ value }) => String(value))
  dateOfFirstAppointment: string;

  @IsString()
  @IsNotEmpty()
  gender: string;

  @IsString()
  @IsNotEmpty()
  retireType: string;

  @IsNumber()
  @IsPositive()
  apaValue: string;
}
