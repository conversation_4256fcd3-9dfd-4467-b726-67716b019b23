import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject, IsString } from 'class-validator';

export class ReassignmentDto {
  @ApiProperty({
    description: 'Mapping of user email to count of records to be assigned',
    example: {
      '<EMAIL>': 10,
      '<EMAIL>': 20,
    },
  })
  @IsNotEmpty()
  @IsObject()
  userCountMapping: Record<string, number> = {};

  @ApiProperty({
    description: 'Type of reassignment',
    example: 'ENROLLED',
  })
  @IsNotEmpty()
  @IsString()
  type: 'COMPUTED' | 'ENROLLED';

  @ApiProperty({
    description: 'Email of the task owner',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsString()
  taskOwnerEmail: string;
}

export class ReassignmentUsersDto {
  @ApiProperty({
    description: 'Action to perform (e.g., validator, auditor)',
    example: 'validator',
  })
  @IsNotEmpty()
  @IsString()
  action: 'validator' | 'auditor';

  @ApiProperty({
    description: 'Name of the user role',
    example: 'PENCOM_VALIDATOR',
  })
  @IsNotEmpty()
  @IsString()
  roleName: string;

  @ApiProperty({
    description: 'Email of the task owner',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsString()
  taskOwnerEmail: string;
}

export class ReassignmentRolesDto {
  @ApiProperty({
    description: 'Name of the user role',
    example: 'PENCOM_VALIDATOR',
  })
  @IsNotEmpty()
  @IsString()
  userRoleName: string;
}
