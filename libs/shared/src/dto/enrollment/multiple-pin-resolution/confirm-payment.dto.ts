import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString, Length } from 'class-validator';

export class ConfirmPaymentDto {
  @ApiProperty({
    description: 'The RSA PIN for which payment is being confirmed',
    example: '**************',
  })
  @IsNotEmpty()
  @IsString()
  pin: string;

  @ApiProperty({
    description: 'Optional comment about the payment',
    example: 'Payment processed via bank transfer',
    required: false,
  })
  @Length(0, 20)
  @IsOptional()
  @IsString()
  paymentRef: string;

  @ApiProperty({
    description: 'Valid Pin Payment Reference',
    example: 'HDETI-21',
  })
  @IsOptional()
  @Length(0, 20)
  @IsString()
  validPinPaymentRef: string;
}

export class ReconcilePaymentDto {
  @ApiProperty({
    description: 'The batch ID for which payment is being reconciled',
    example: '**************',
  })
  @IsNotEmpty()
  @IsNumber()
  pk: number;
}
