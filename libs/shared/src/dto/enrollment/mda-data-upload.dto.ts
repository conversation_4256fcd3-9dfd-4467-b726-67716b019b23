/* eslint-disable */
import { IsNotEmpty, <PERSON>N<PERSON>ber, IsOptional, IsPositive, IsString, Length, Matches } from 'class-validator';
import { Transform } from 'class-transformer';
import { IMdaBaseBioData } from '@app/shared/dto/enrollment/interfaces/mda-base-bio-data.interface';
import { RsaPinValidator } from '@app/shared/decorators/rsapin-validator.decorator';

export class MdaBiodataDto implements IMdaBaseBioData {
  @IsString()
  @IsNotEmpty()
  @RsaPinValidator()
  rsaPin: string;

  @IsString()
  @IsNotEmpty()
  surname: string;

  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  @Length(3, 15, { message: 'Staff Id must be between 3 to 15 characters' })
  @Transform(({ value }) => String(value).trim()) // Ensure it's always a string
  staffId: string;

  @IsString()
  @IsOptional()
  employerJune2004: string;

  @IsString()
  @IsOptional()
  salaryStructureJune2004: string;

  @Transform(({ value }) => (value === '' ? undefined : value))
  @IsNumber({ allowNaN: false }, { message: 'Grade level for June 2004 must be a number' })
  @IsPositive({ message: 'Grade level for June 2004 must be a positive number' })
  @IsOptional()
  gradeLevelJune2004: string;

  @Transform(({ value }) => (value === '' ? undefined : value))
  @IsNumber({ allowNaN: false }, { message: 'Step for June 2004 must be a number' })
  @IsPositive({ message: 'Step for June 2004 must be a positive number' })
  @IsOptional()
  stepJune2004: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^([0-2][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/, {
    message: 'EDOR must be in DD/MM/YYYY format (e.g., 04/04/2025)',
  })
  @Transform(({ value }) => String(value))
  edor: string;

  @IsString()
  @IsOptional()
  @Matches(/^([0-2][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/, {
    message: 'DTS must be in DD/MM/YYYY format (e.g., 04/04/2025)',
  })
  @Transform(({ value }) => (value ? String(value).trim() || undefined : undefined))
  dts: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^([0-2][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/, {
    message: 'DOFA must be in DD/MM/YYYY format (e.g., 04/04/2025)',
  })
  @Transform(({ value }) => String(value))
  dofa: string;
}
