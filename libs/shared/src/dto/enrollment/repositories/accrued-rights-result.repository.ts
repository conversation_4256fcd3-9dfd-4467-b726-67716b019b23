import { AbstractRepository } from '@app/shared/database';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { AccruedRightsResult } from '@app/shared/dto/enrollment/entities/accrued-rights-result.entity';
import { PinoLogger } from 'nestjs-pino';

Injectable();
export class AccruedRightsResultRepository extends AbstractRepository<AccruedRightsResult> {
  constructor(
    @InjectRepository(AccruedRightsResult)
    accruedRightsResultRepository: Repository<AccruedRightsResult>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(accruedRightsResultRepository, entityManager);
  }
}
