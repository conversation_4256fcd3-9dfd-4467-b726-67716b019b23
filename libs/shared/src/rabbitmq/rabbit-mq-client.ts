import { Injectable } from '@nestjs/common';
import { ClientProxy, ClientProxyFactory, Transport } from '@nestjs/microservices';

@Injectable()
export class RabbitMqClient {
  private readonly client: ClientProxy;

  constructor(queue: string, rabbitMqUrl: string) {
    this.client = ClientProxyFactory.create({
      transport: Transport.RMQ,
      options: {
        urls: [rabbitMqUrl],
        queue,
        queueOptions: { durable: true },
      },
    });
  }

  getClient(): ClientProxy {
    return this.client;
  }
}
