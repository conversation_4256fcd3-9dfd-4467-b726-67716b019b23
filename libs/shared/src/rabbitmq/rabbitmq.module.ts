/* eslint-disable */
import { DynamicModule, Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RabbitMqClient } from '@app/shared/rabbitmq/rabbit-mq-client';

@Global()
@Module({
  imports: [ConfigModule.forRoot({ isGlobal: true })],
})
export class RabbitmqModule {
  static register(queue: string): DynamicModule {
    return {
      module: RabbitmqModule,
      providers: [
        {
          provide: RabbitMqClient,
          useFactory: (configService: ConfigService) => {
            const rabbitMqUrl = configService.get<string>('RABBIT_MQ_URL', 'amqp://admin:password@localhost:5672');
            return new RabbitMqClient(queue, rabbitMqUrl);
          },
          inject: [ConfigService],
        },
      ],
      exports: [RabbitMqClient],
    };
  }
}
