/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AuthLibService {
  private readonly BLACKLIST_PREFIX = 'token:blacklist:';

  constructor(
    private readonly redisCacheService: RedisCacheService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService
  ) {}

  generateToken(payload: any): string {
    const expiresIn = this.configService.get<string>('JWT_EXPIRES_IN', '1h');
    return this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: expiresIn,
    });
  }

  verifyToken(token: string): any {
    return this.jwtService.verify(token, {
      secret: this.configService.get<string>('JWT_SECRET'),
    });
  }

  async isTokenInvalidated(token: string): Promise<boolean> {
    try {
      const decoded = this.jwtService.verify(token, {
        secret: this.configService.get<string>('JWT_SECRET'),
      });

      if (!decoded || typeof decoded !== 'object') {
        return true;
      }

      const blacklisted = await this.redisCacheService.get(`${this.BLACKLIST_PREFIX}${token}`);
      return !!blacklisted;
    } catch {
      return true;
    }
  }

  async invalidateToken(token: string): Promise<void> {
    try {
      const verified = this.jwtService.verify(token, {
        secret: this.configService.get<string>('JWT_SECRET'),
      });

      if (!verified || typeof verified !== 'object' || !('exp' in verified)) {
        throw new Error('Invalid token: missing expiration claim');
      }

      const expirationTime = verified.exp * 1000;
      const now = Date.now();
      const ttl = Math.max(0, Math.ceil((expirationTime - now) / 1000));

      if (ttl > 0) {
        await this.redisCacheService.set(`${this.BLACKLIST_PREFIX}${token}`, 'true', ttl);
      }
    } catch (error) {
      throw error;
    }
  }
}
