import { Module } from '@nestjs/common';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { LoggerModule, RedisCacheModule } from '@app/shared';

import { JwtModule, JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AuthLibService } from './services/auth-lib.service';
import { PrivilegeGuard } from './guards/privilege.guard';

@Module({
  imports: [
    LoggerModule,
    RedisCacheModule.register(),
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN', '1h'),
        },
      }),
    }),
  ],
  providers: [AuthLibService, RedisCacheService, JwtService, PrivilegeGuard],
  exports: [AuthLibService, JwtService, PrivilegeGuard],
})
export class AuthModule {}
