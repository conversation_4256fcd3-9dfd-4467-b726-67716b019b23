import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import {
  CobraPrivilegeKey,
  CobraPrivileges,
  getPrivilegeDescriptionsByCodes,
} from '../../user-service/entities/enums/cobra-privilege-enum';
import { REQUIRED_PRIVILEGES_KEY } from '@app/shared/decorators/require-privileges.decorator';
import { CustomException } from '@app/shared/filters/exception.dto';
import { UserStatusAction } from '@app/shared/enums/UserStatusActionEnum';
import { UploadProcessTypeEnum } from '@app/shared/enums/upload-process-type-enum';
import { PinoLogger } from 'nestjs-pino';
import { AmendmentRequestStatus } from '@app/shared/enrollment-service/entities/amendment-request.entity';

@Injectable()
export class PrivilegeGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private logger: PinoLogger
  ) {}

  canActivate(context: ExecutionContext): boolean {
    let requiredPrivileges = this.reflector.getAllAndOverride<CobraPrivilegeKey[]>(REQUIRED_PRIVILEGES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const { user, body } = context.switchToHttp().getRequest();

    if (!user || !user.privileges) {
      throw new CustomException('User privileges not found');
    }

    // path specific privilege check
    if (context.getHandler().name === 'updateUserStatus') {
      requiredPrivileges = this.getRequestPrivilegeForUserUpdate(body);
    } else if (context.getHandler().name === 'handleMdaBiodataUpload') {
      requiredPrivileges = this.getRequestPrivilegeForMdaUpload(body);
    } else if (context.getHandler().name === 'updateRequest') {
      requiredPrivileges = this.getRequestPrivilegeForAmendmentRequestUpdate(body);
    }

    const userPrivilegesMap = new Map(user.privileges.map((userPriv) => [userPriv, userPriv]));

    if (!requiredPrivileges.length) {
      this.logger.info('No required privileges found for this request');
      return true;
    }
    const hasAtLeastOneRequiredPrivilege = requiredPrivileges.some((privilege) => {
      const userPriv = userPrivilegesMap.get(privilege);
      return userPriv;
    });

    if (!hasAtLeastOneRequiredPrivilege) {
      // throw new CustomException(
      //   `Your account does not have any of the required privileges: ${getPrivilegeDescriptionsByCodes(requiredPrivileges)} to access this resource`
      // );
    }

    return true;
  }

  getRequestPrivilegeForUserUpdate(body: any): CobraPrivilegeKey[] {
    if (!body || !body.action) {
      return [];
    }
    // Map actions to required privileges
    const actionPrivilegeMap = {
      [UserStatusAction.ACTIVATE]: CobraPrivileges.USER_MANAGEMENT_ACTIVATION,
      [UserStatusAction.DEACTIVATE]: CobraPrivileges.USER_MANAGEMENT_DEACTIVATION,
      [UserStatusAction.BLACKLIST]: CobraPrivileges.USER_MANAGEMENT_BLACKLIST,
      [UserStatusAction.WHITELIST]: CobraPrivileges.USER_MANAGEMENT_WHITELIST,
    };

    const requiredPrivilege = actionPrivilegeMap[body.action];

    if (!requiredPrivilege) {
      // new action will not be blocked
      return [];
    }

    return [requiredPrivilege.code];
  }
  getRequestPrivilegeForMdaUpload(body: any): CobraPrivilegeKey[] {
    if (!body || !body.processType) {
      // new action will not be blocked
      return [];
    }
    // Map actions to required privileges
    const actionPrivilegeMap = {
      [UploadProcessTypeEnum.MDA_RETIREE_DATA_UPLOAD]: CobraPrivileges.MDA_RETIREE_DATA_UPLOAD,
      [UploadProcessTypeEnum.MDA_DECEASED_DATA_UPLOAD]: CobraPrivileges.MDA_DECEASED_DATA_UPLOAD,
      [UploadProcessTypeEnum.MDA_NOMINAL_ROLL_UPLOAD]: CobraPrivileges.NOMINAL_ROLL_UPLOAD,
      [UploadProcessTypeEnum.ACCRUED_RIGHTS]: CobraPrivileges.ACCRUED_RIGHTS_UPLOAD,
    };

    const requiredPrivilege = actionPrivilegeMap[body.processType];

    if (!requiredPrivilege) {
      return [];
    }

    return [requiredPrivilege.code];
  }
  getRequestPrivilegeForAmendmentRequestUpdate(body: any): CobraPrivilegeKey[] {
    if (!body || !body.status) {
      // new action will not be blocked
      return [];
    }
    // Map actions to required privileges
    const actionPrivilegeMap = {
      [AmendmentRequestStatus.APPROVED]: CobraPrivileges.APPROVE_AMENDMENT_REQUEST,
      [AmendmentRequestStatus.REJECTED]: CobraPrivileges.REJECT_AMENDMENT_REQUEST,
      [AmendmentRequestStatus.CANCELLED]: CobraPrivileges.CANCEL_AMENDMENT_REQUEST,
    };

    const requiredPrivilege = actionPrivilegeMap[body.status];

    if (!requiredPrivilege) {
      return [];
    }

    return [requiredPrivilege.code];
  }
}
