/* eslint-disable */
import { Inject, Injectable, Optional } from '@nestjs/common';
import { HealthCheck, HealthCheckService, HealthCheckStatus, TypeOrmHealthIndicator } from '@nestjs/terminus';
import { RedisHealthIndicator } from '@app/shared/health/indicators/redis-health-indicator';
import { Public } from '@app/shared/decorators/public.decorator';
import { TcpHealthIndicator } from '@app/shared/health/indicators/tcp-health-indicator';
import { TcpHealthConfig } from '@app/shared/health/health.module';
import { RabbitMqHealthIndicator } from '@app/shared/health/indicators/rabbit-mq-health-indicator';

@Injectable()
export class HealthService {
  constructor(
    private health: HealthCheckService,
    private redisHealthIndicator: RedisHealthIndicator,
    private db: TypeOrmHealthIndicator,
    private tcpHealthIndicator: TcpHealthIndicator,
    private rabbitMqHealthIndicator: RabbitMqHealthIndicator,
    @Inject('TCP_HEALTH_CONFIG') private config: TcpHealthConfig,
    @Optional() @Inject('RABBITMQ_QUEUE_NAME') private readonly queueName?: string
  ) {}

  @Public()
  @HealthCheck()
  async checkHealth() {
    const checks = [
      async () => this.db.pingCheck('oracle-db'),
      async () => this.redisHealthIndicator.isHealthy('redis'),
    ];

    for (const service of this.config.services) {
      checks.push(async () => this.tcpHealthIndicator.isHealthy(service.name, service.host, service.port));
    }

    if (this.queueName) {
      checks.push(async () => this.rabbitMqHealthIndicator.isHealthy());
    }

    try {
      return await this.health.check(checks);
    } catch (error: any) {
      return {
        status: (error.response?.status as HealthCheckStatus) ?? ('error' as HealthCheckStatus), // Must match HealthCheckStatus type
        info: error.response?.info || {},
        error: error.response?.error || {},
        details: error.response?.details || {},
      };
    }
  }
}
