import { DynamicModule, Module } from '@nestjs/common';
import { HealthController } from '@app/shared/health/health.controller';
import { HealthService } from '@app/shared/health/health.service';
import { DatabaseModule } from '@app/shared';
import { TerminusModule } from '@nestjs/terminus';
import { RedisHealthIndicator } from '@app/shared/health/indicators/redis-health-indicator';
import { RedisCacheModule } from '@app/shared/cache';
import { TcpHealthIndicator } from '@app/shared/health/indicators/tcp-health-indicator';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RabbitMqHealthIndicator } from '@app/shared/health/indicators/rabbit-mq-health-indicator';
import { RabbitMqClient } from '@app/shared/rabbitmq/rabbit-mq-client';

export interface TcpServiceConfig {
  name: string;
  host: string;
  port: number;
}

export interface TcpHealthConfig {
  services?: TcpServiceConfig[];
}

@Module({})
export class HealthModule {
  static registerAsync(
    services?: { name: string; host: string; port: string }[],
    queueName?: string // Accept queue name as argument
  ): DynamicModule {
    return {
      module: HealthModule,
      imports: [ConfigModule, DatabaseModule, TerminusModule, RedisCacheModule.register()],
      controllers: [HealthController],
      providers: [
        HealthService,
        RedisHealthIndicator,
        TcpHealthIndicator,
        {
          provide: 'TCP_HEALTH_CONFIG',
          useFactory: (configService: ConfigService): TcpHealthConfig => ({
            services: services
              ? services.map((service) => ({
                  name: service.name,
                  host: configService.get<string>(service.host, '127.0.0.1'),
                  port: configService.get<number>(service.port, 1000),
                }))
              : [],
          }),
          inject: [ConfigService],
        },
        {
          provide: 'RABBITMQ_QUEUE_NAME',
          useValue: queueName,
        },
        {
          provide: RabbitMqClient,
          useFactory: (configService: ConfigService) => {
            const rabbitMqUrl = configService.get<string>('RABBIT_MQ_URL', 'amqp://admin:password@localhost:5672');
            return new RabbitMqClient(queueName, rabbitMqUrl);
          },
          inject: [ConfigService],
        },
        {
          provide: RabbitMqHealthIndicator,
          useFactory: (rabbitMqClient: RabbitMqClient, queueName: string) =>
            new RabbitMqHealthIndicator(rabbitMqClient, queueName),
          inject: [RabbitMqClient, 'RABBITMQ_QUEUE_NAME'],
        },
      ],
      exports: [HealthService, TerminusModule, RedisHealthIndicator, TcpHealthIndicator],
    };
  }
}
