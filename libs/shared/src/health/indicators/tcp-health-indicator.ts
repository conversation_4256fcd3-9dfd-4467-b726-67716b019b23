import { Injectable } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult } from '@nestjs/terminus';
import { Socket } from 'net';

@Injectable()
export class TcpHealthIndicator extends HealthIndicator {
  async isHealthy(key: string, host: string, port: number): Promise<HealthIndicatorResult> {
    return new Promise<HealthIndicatorResult>((resolve) => {
      const socket = new Socket();
      const startTime = Date.now();

      socket.setTimeout(3000); // 3-second timeout

      socket.on('connect', () => {
        const responseTime = Date.now() - startTime;
        socket.destroy();
        resolve(this.getStatus(key, true, { responseTime }));
      });

      socket.on('error', (err) => {
        socket.destroy();
        resolve(this.getStatus(key, false, { error: err.message }));
      });

      socket.on('timeout', () => {
        socket.destroy();
        resolve(this.getStatus(key, false, { error: 'Connection timeout' }));
      });

      socket.connect(port, host);
    });
  }
}
