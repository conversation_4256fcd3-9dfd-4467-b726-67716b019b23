import { Inject, Injectable, Optional } from '@nestjs/common';
import { HealthCheckError, HealthIndicator, HealthIndicatorResult } from '@nestjs/terminus';
import { RabbitMqClient } from '@app/shared/rabbitmq/rabbit-mq-client';

@Injectable()
export class RabbitMqHealthIndicator extends HealthIndicator {
  constructor(
    private readonly rabbitMqClient: RabbitMqClient,
    @Optional() @Inject('RABBITMQ_QUEUE_NAME') private readonly queueName?: string
  ) {
    super();
  }

  async isHealthy(): Promise<HealthIndicatorResult> {
    try {
      const client = this.rabbitMqClient.getClient();
      await client.connect();
      return this.getStatus(`rabbitmq-${this.queueName}`, true);
    } catch (error) {
      throw new HealthCheckError(
        `RabbitMQ ${this.queueName} check failed`,
        this.getStatus(`rabbitmq-${this.queueName}`, false)
      );
    }
  }
}
