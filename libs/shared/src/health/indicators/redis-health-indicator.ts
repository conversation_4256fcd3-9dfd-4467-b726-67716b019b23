import { Injectable } from '@nestjs/common';
import { HealthCheckError, HealthIndicator, HealthIndicatorResult } from '@nestjs/terminus';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class RedisHealthIndicator extends HealthIndicator {
  constructor(
    private readonly redisCacheService: RedisCacheService,
    private readonly logger: PinoLogger
  ) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const pingResponse = await this.redisCacheService.pingWithTimeout(2000); // Ping Redis to check if it's alive
      this.logger.debug('Redis ping response:', pingResponse);
      return this.getStatus(key, true);
    } catch (error) {
      this.logger.error('Redis health check failed:', error);
      throw new HealthCheckError('Redis connection failed', this.getStatus(key, false, { message: error }));
    }
  }
}
