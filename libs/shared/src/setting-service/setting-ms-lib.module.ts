import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseModule, LoggerModule } from '@app/shared';
import { ConfigModule } from '@nestjs/config';
import { RedisCacheModule } from '@app/shared/cache';
import { Setting } from './entities/setting.entity';
import { SettingMsLibRepository } from '@app/shared/setting-service/setting-ms-lib.repository';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';

@Module({
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([Setting]),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    LoggerModule,
    RedisCacheModule.register(),
  ],
  providers: [SettingMsLibService, SettingMsLibRepository, RedisCacheService],
  exports: [SettingMsLibService, SettingMsLibRepository],
})
export class SettingMsLibModule {}
