import { Column, Entity } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';

@Entity('SETTING')
export class Setting extends AbstractEntity<Setting> {
  @Column({ name: 'NAME', nullable: false, length: 256, unique: true })
  name: string;

  @Column({ name: 'DESCRIPTION', nullable: true, length: 512 })
  description: string;

  @Column({ name: 'VALUE', nullable: false, length: 512 })
  value: string;
}
