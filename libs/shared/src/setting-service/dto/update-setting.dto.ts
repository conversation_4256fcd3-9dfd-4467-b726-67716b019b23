import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class UpdateSettingDto {
  @ApiProperty({ description: 'Setting name' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Setting value' })
  @IsNotEmpty()
  @IsString()
  value: string;

  @IsOptional()
  @ApiProperty({ description: 'Setting description', required: false })
  @IsString()
  description?: string;
}
