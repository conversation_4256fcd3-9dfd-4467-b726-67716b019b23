import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ResponseCodeEnum } from '@app/shared/enums';

export class SettingsResponse extends BaseResponseDto {
  settings: {
    supportEmailAddress: string;
    passwordRegex: string;
    enrolmentYearBands: string;
    leaveTypes: string;
    maxNumberOfAdmins: number;
    specimenFileMaxSize: number;
    livenessEvents: string;
    livenessVarianceThreshold: string;
    livenessIndicatorColor: string;
    livenessIndicatorLineWidth: string;
    livenessBlinkThreshold: string;
    livenessEyeBrowDynamicThreshold: string;
    livenessTimer: string;
    livenessChallengeCount: string;
    retireExcelTemplateUrl: string;
    deaceasedExcelTemplateUrl: string;
    nominalRollExcelTemplateUrl: string;
    multiplePinExcelTemplateUrl: string;
    paymentConfirmationExcelTemplateUrl: string;
    captchaSiteKey: string;
    enableCaptcha: string;
  } = {
    supportEmailAddress: '',
    passwordRegex: '',
    enrolmentYearBands: '',
    leaveTypes: '',
    maxNumberOfAdmins: 3,
    specimenFileMaxSize: 2,
    livenessEvents: '',
    livenessVarianceThreshold: '',
    livenessIndicatorColor: '',
    livenessIndicatorLineWidth: '',
    livenessBlinkThreshold: '',
    livenessEyeBrowDynamicThreshold: '',
    livenessTimer: '',
    livenessChallengeCount: '',
    retireExcelTemplateUrl: '',
    deaceasedExcelTemplateUrl: '',
    nominalRollExcelTemplateUrl: '',
    multiplePinExcelTemplateUrl: '',
    captchaSiteKey: '',
    enableCaptcha: '',
    paymentConfirmationExcelTemplateUrl: '',
  };

  constructor(responseCodeEnum?: ResponseCodeEnum) {
    super(responseCodeEnum);
  }
}
