/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { ResponseCodeEnum, SettingsEnum, SettingsEnumKey } from '@app/shared/enums';
import { CreateSettingDto } from './dto/create-setting.dto';
import { Setting } from './entities/setting.entity';
import { GetSettingDto } from './dto/get-setting.dto';
import { SettingsResponse } from './dto/settings-response.dto';
import { SettingMsLibRepository } from '@app/shared/setting-service/setting-ms-lib.repository';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { UpdateSettingDto } from '@app/shared/setting-service/dto/update-setting.dto';
import { CustomException } from '@app/shared/filters/exception.dto';
import {
  BaseResponseWithContentNoPagination,
  BaseResponseWithNoCountInfo,
} from '../dto/response/base-response-with-content';
import { PinoLogger } from 'nestjs-pino';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';

@Injectable()
export class SettingMsLibService {
  constructor(
    private readonly redisCacheService: RedisCacheService,
    private readonly settingsRepository: SettingMsLibRepository,
    private readonly logger: PinoLogger
  ) {}

  async create(createSettingDto: CreateSettingDto): Promise<Setting> {
    const setting = new Setting({ ...createSettingDto });
    return await this.settingsRepository.saveEntity(setting);
  }

  async getGlobalSettings(getSettingDto: GetSettingDto): Promise<SettingsResponse> {
    this.logger.debug(`getGlobalSettings appversion: ${getSettingDto.appVersion}`);
    const settingsResponse = new SettingsResponse(ResponseCodeEnum.SUCCESS);
    settingsResponse.settings.passwordRegex = await this.getSetting(SettingsEnumKey.PASSWORD_REGEX, settingsResponse);
    settingsResponse.settings.supportEmailAddress = await this.getSetting(
      SettingsEnumKey.SUPPORT_EMAIL_ADDRESS,
      settingsResponse
    );
    settingsResponse.settings.leaveTypes = await this.getSetting(SettingsEnumKey.LEAVE_TYPES, settingsResponse);
    settingsResponse.settings.livenessEvents = await this.getSetting(SettingsEnumKey.LIVENESS_EVENTS, settingsResponse);
    settingsResponse.settings.livenessVarianceThreshold = await this.getSetting(
      SettingsEnumKey.LIVENESS_VARIANCE_THRESHOLD,
      settingsResponse
    );
    settingsResponse.settings.livenessIndicatorColor = await this.getSetting(
      SettingsEnumKey.LIVENESS_INDICATOR_COLOR,
      settingsResponse
    );
    settingsResponse.settings.livenessIndicatorLineWidth = await this.getSetting(
      SettingsEnumKey.LIVENESS_INDICATOR_LINE_WIDTH,
      settingsResponse
    );
    settingsResponse.settings.livenessBlinkThreshold = await this.getSetting(
      SettingsEnumKey.LIVENESS_BLINK_THRESHOLD,
      settingsResponse
    );
    settingsResponse.settings.livenessEyeBrowDynamicThreshold = await this.getSetting(
      SettingsEnumKey.LIVENESS_EYE_BROW_DYNAMIC_THRESHOLD,
      settingsResponse
    );
    settingsResponse.settings.livenessTimer = await this.getSetting(SettingsEnumKey.LIVENESS_TIMER, settingsResponse);
    settingsResponse.settings.livenessChallengeCount = await this.getSetting(
      SettingsEnumKey.LIVENESS_CHALLENGE_COUNT,
      settingsResponse
    );
    settingsResponse.settings.enrolmentYearBands = await this.getSetting(
      SettingsEnumKey.ENROLMENT_YEAR_BANDS,
      settingsResponse
    );
    settingsResponse.settings.maxNumberOfAdmins = await this.getSettingInt(
      SettingsEnumKey.MAX_NUMBER_OF_ADMINS,
      settingsResponse
    );
    settingsResponse.settings.specimenFileMaxSize = await this.getSettingInt(
      SettingsEnumKey.SPECIMEN_FILE_MAX_SIZE,
      settingsResponse
    );
    settingsResponse.settings.retireExcelTemplateUrl = await this.getSetting(
      SettingsEnumKey.RETIRE_EXCEL_TEMPLATE_URL,
      settingsResponse
    );
    settingsResponse.settings.deaceasedExcelTemplateUrl = await this.getSetting(
      SettingsEnumKey.DEACEASED_EXCEL_TEMPLATE_URL,
      settingsResponse
    );
    settingsResponse.settings.nominalRollExcelTemplateUrl = await this.getSetting(
      SettingsEnumKey.NOMINAL_ROLL_EXCEL_TEMPLATE_URL,
      settingsResponse
    );
    settingsResponse.settings.multiplePinExcelTemplateUrl = await this.getSetting(
      SettingsEnumKey.MULTIPLE_PIN_EXCEL_TEMPLATE_URL,
      settingsResponse
    );
    settingsResponse.settings.paymentConfirmationExcelTemplateUrl = await this.getSetting(
      SettingsEnumKey.PAYMENT_CONFIRMATION_EXCEL_TEMPLATE_URL,
      settingsResponse
    );
    settingsResponse.settings.captchaSiteKey = await this.getSetting(
      SettingsEnumKey.CAPTCHA_SITE_KEY,
      settingsResponse
    );
    settingsResponse.settings.enableCaptcha = await this.getSetting(SettingsEnumKey.ENABLE_CAPTCHA, settingsResponse);

    return settingsResponse;
  }

  async getSettingInt(settingKey: SettingsEnumKey, settingsResponse?: SettingsResponse): Promise<number> {
    const settingValue = await this.getSetting(settingKey, settingsResponse);
    return Number(settingValue);
  }

  async getSettingList(settingKey: SettingsEnumKey, settingsResponse?: SettingsResponse): Promise<string[]> {
    const settingValue = await this.getSetting(settingKey, settingsResponse);
    return settingValue.split(',');
  }

  async getSettingJSONList(settingKey: SettingsEnumKey, settingsResponse?: SettingsResponse): Promise<string[]> {
    const settingValue = await this.getSetting(settingKey, settingsResponse);
    const cleanedString = settingValue.replace(/'/g, '"');
    return JSON.parse(cleanedString);
  }

  async getSettingBoolean(settingKey: SettingsEnumKey, settingsResponse?: SettingsResponse): Promise<boolean> {
    const settingValue = await this.getSetting(settingKey, settingsResponse);
    return settingValue?.trim().toLowerCase() === 'true';
  }

  async getSettingJson(settingKey: SettingsEnumKey, settingsResponse?: SettingsResponse): Promise<any> {
    const settingValue = await this.getSetting(settingKey, settingsResponse);
    return JSON.parse(settingValue);
  }

  async getSetting(settingKey: SettingsEnumKey, settingsResponse?: SettingsResponse): Promise<string> {
    const setting = SettingsEnum[settingKey];
    let finalVal = setting.value;

    if (!setting) {
      this.logger.debug(`Unable to get setting Key <<<<<<<<<<<<<>>>>>>>>>>>>>>>> ${settingKey}`);
      return finalVal;
    }
    try {
      finalVal = await this.getSettingValue(setting.name, setting.value, setting.description);
    } catch (error) {
      this.logger.error(`Exception occurred on retrieving setting: ${setting.name}: \\n ${error}`);
      if (settingsResponse) {
        settingsResponse.setResponseCode(ResponseCodeEnum.ERROR);
      }
    }
    return finalVal;
  }

  async getSettingValue(settingName: string, defaultValue: string, description: string): Promise<string> {
    let settingValue: string | undefined;

    // 2️⃣ Check in cache
    settingValue = await this.redisCacheService.get(settingName);
    if (settingValue) {
      this.logger.debug(
        `returning setting from cache for setting with name <<<<<<<<<<<<<>>>>>>>>>>>>>>>> ${settingName}`
      );
      return settingValue;
    }

    // 3️⃣ Check in database
    const setting = await this.getSettingByName(settingName);

    if (setting) {
      settingValue = setting.value?.trim() || defaultValue;
    } else {
      this.logger.debug(`about to save setting with name <<<<<<<<<<<<<>>>>>>>>>>>>>>>> ${settingName}`);
      settingValue = defaultValue?.trim() || ' ';
      const setting = new Setting({
        name: settingName,
        value: defaultValue,
        description,
      });
      await this.settingsRepository.saveEntity(setting);
    }

    // 4️⃣ Store in cache
    await this.redisCacheService.set(settingName, settingValue);
    return settingValue;
  }

  async updateSetting(updateSettingDto: UpdateSettingDto): Promise<BaseResponseWithContentNoPagination<Setting>> {
    const { name, value, description } = updateSettingDto;

    // Find existing setting or create new one
    let setting = await this.getSettingByName(name);

    if (!setting) {
      throw new CustomException('Setting not found');
    }

    setting.value = value;
    if (description) {
      setting.description = description;
    }

    // Save to database
    const savedSetting = await this.settingsRepository.saveEntity(setting);

    // Update cache
    await this.redisCacheService.set(name, value);

    const resp = new BaseResponseWithContentNoPagination<Setting>(ResponseCodeEnum.SUCCESS);
    resp.content = savedSetting;
    resp.setDescription(`${setting.name} updated successfully`);
    return resp;
  }

  private async getSettingByName(settingName: string): Promise<Setting> {
    try {
      return await this.settingsRepository.findOne({ name: settingName });
    } catch (error) {
      this.logger.error(
        `error occured while getting setting with name: ${settingName} <<<<<<<<<<<<<>>>>>>>>>>>>>>>> ${error instanceof Error ? error.stack : error}`
      );
      return Promise.reject(error);
    }
  }

  async getSettings(filter: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<Setting>> {
    return await this.settingsRepository.findWithPagination(filter);
  }
}
