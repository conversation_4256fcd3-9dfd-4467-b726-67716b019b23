import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { Setting } from './entities/setting.entity';
import { PinoLogger } from 'nestjs-pino';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { BaseResponseWithNoCountInfo } from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums';

@Injectable()
export class SettingMsLibRepository extends AbstractRepository<Setting> {
  constructor(
    @InjectRepository(Setting)
    settingRepository: Repository<Setting>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(settingRepository, entityManager);
  }

  async findWithPagination(filter: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<Setting>> {
    const queryBuilder = this.entityRepository.createQueryBuilder('setting');

    // Apply custom filters if provided
    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryBuilder.andWhere(`UPPER(setting.${key}) LIKE :${key}`, { [key]: `%${value.toString().toUpperCase()}%` });
        }
      });
    }

    // Apply pagination
    const limit = Math.max(filter.limit || 10, 1);
    const page = Math.max(filter.page || 1, 1);
    const offset = (page - 1) * limit;

    queryBuilder.skip(offset).take(limit);

    // Order by name by default
    queryBuilder.orderBy('setting.name', 'ASC');

    const data = await queryBuilder.getMany();

    const response = new BaseResponseWithNoCountInfo<Setting>(ResponseCodeEnum.SUCCESS);
    response.content = data;
    response.hasNext = data.length === limit;
    response.hasPrevious = page > 1;

    this.logger.debug(`Found ${data.length} settings`);
    return response;
  }
}
