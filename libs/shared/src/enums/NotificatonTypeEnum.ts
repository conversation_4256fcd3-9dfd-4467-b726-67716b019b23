export enum NotificatonTypeEnum {
  RESET_PASSWORD = 'RESET_PASSWORD',
  REGISTER = 'REGISTER',
  EXTERNAL_USER_CREATION = 'EXTERNAL_USER_CREATION',
  CHANGE_PASSWORD = 'CHAN<PERSON>_PASSWORD',
  ANSWERED_SECURITY_QUESTIONS = 'ANSWERED_SECURITY_QUESTIONS',
  ROLE_CHANGE_APPROVAL = 'ROLE_CHANGE_APPROVAL',
  WHITELIST_APPROVAL = 'WHITELIST_APPROVAL',
  WHITELIST_REQUEST_CONFIRMATION = 'WHITELIST_REQUEST_CONFIRMATION',
  ROLE_CHANGE_REQUEST_ACKNOWLEDGMENT = 'ROLE_CHANGE_REQUEST_ACKNOWLEDGMENT',
  WHITELIST_REQUEST_STATUS = 'WHITELIST_REQUEST_STATUS',
  R<PERSON><PERSON>_CHANGE_STATUS = 'ROLE_CHANGE_STATUS',
  R<PERSON>E_ADMIN_UPGRADE = 'ROLE_ADMIN_UPGRADE',
  ROLE_CHANGE_STATUS_APPROVAL = 'ROLE_CHANGE_STATUS_APPROVAL',
  ROLE_CHANGE_STATUS_REJECTION = 'ROLE_CHANGE_STATUS_REJECTION',
  WHITELIST_REQUEST_APPROVED = 'WHITELIST_REQUEST_APPROVED',
  WHITELIST_REQUEST_REJECTED = 'WHITELIST_REQUEST_REJECTED',
  WHITELIST_REQUEST_USER_APPROVED = 'WHITELIST_REQUEST_USER_APPROVED',
  WHITELIST_REQUEST_USER_REJECTED = 'WHITELIST_REQUEST_USER_REJECTED',
  RETIREE_ENROLMENT_NOTIFICATION = 'RETIREE_ENROLMENT_NOTIFICATION',
  SUPERVISOR_REVIEW_REQUEST = 'SUPERVISOR_REVIEW_REQUEST',
  VALIDATOR_REVIEW_REQUEST = 'VALIDATOR_REVIEW_REQUEST',
  AUDIT_VALIDATOR_REVIEW_REQUEST = 'AUDIT_VALIDATOR_REVIEW_REQUEST',
  AUDIT_SUPERVISOR_REVIEW_REQUEST = 'AUDIT_SUPERVISOR_REVIEW_REQUEST',
  AUDIT_VALIDATOR_NR_REVIEW_REQUEST = 'AUDIT_VALIDATOR_NR_REVIEW_REQUEST',
  AUDIT_SUPERVISOR_NR_REVIEW_REQUEST = 'AUDIT_SUPERVISOR_NR_REVIEW_REQUEST',
  VALIDATOR_REQUEST_REJECTION = 'VALIDATOR_REQUEST_REJECTION',
  AUDIT_VALIDATOR_REQUEST_REJECTION = 'AUDIT_VALIDATOR_REQUEST_REJECTION',
  AUDIT_VALIDATOR_CONTRIBUTION_REQUEST_REJECTION = 'AUDIT_VALIDATOR_CONTRIBUTION_REQUEST_REJECTION',
  VALIDATOR_REQUEST_REJECTION_RETIREE_NOTIFICATION = 'VALIDATOR_REQUEST_REJECTION_RETIREE_NOTIFICATION',
  NDMD_RSA_PIN_REVIEW = 'NDMD_RSA_PIN_REVIEW',
  NDMD_RSA_PIN_REVIEW_COMPLETED = 'NDMD_RSA_PIN_REVIEW_COMPLETED',
  NDMD_RSA_PIN_REVIEW_REJECTED = 'NDMD_RSA_PIN_REVIEW_REJECTED',
  AMENDMENT_REQUEST_REVIEW = 'AMENDMENT_REQUEST_REVIEW',
  AMENDMENT_REQUEST_INITIATED_PFA = 'AMENDMENT_REQUEST_INITIATED_PFA',
  AMENDMENT_REQUEST_INITIATED_RETIREE = 'AMENDMENT_REQUEST_INITIATED_RETIREE',
  AMENDMENT_REQUEST_APPROVAL_RETIREE = 'AMENDMENT_REQUEST_APPROVAL_RETIREE',
  AMENDMENT_REQUEST_APPROVAL_PFA = 'AMENDMENT_REQUEST_APPROVAL_PFA',
  AMENDMENT_REQUEST_REJECTION_RETIREE = 'AMENDMENT_REQUEST_REJECTION_RETIREE',
  AMENDMENT_REQUEST_REJECTION_PFA = 'AMENDMENT_REQUEST_REJECTION_PFA',
  AMENDMENT_REQUEST_CANCELLATION_RETIREE = 'AMENDMENT_REQUEST_CANCELLATION_RETIREE',
  AMENDMENT_REQUEST_CANCELLATION_PENCOM = 'AMENDMENT_REQUEST_CANCELLATION_PENCOM',
  AMENDMENT_REQUEST_CANCELLATION_PFA = 'AMENDMENT_REQUEST_CANCELLATION_PFA',
  MULTIPLE_PIN_RESOLUTION_TRANSACTION_HISTORY = 'MULTIPLE_PIN_RESOLUTION_TRANSACTION_HISTORY',
  TRANSACTION_HISTORY_MEMO_NOTIFICATION = 'TRANSACTION_HISTORY_MEMO_NOTIFICATION',
  MULTIPLE_PIN_RESOLUTION_COMPUTATION = 'MULTIPLE_PIN_RESOLUTION_COMPUTATION',
  MPR_PAYMENT_REQUEST_PFA = 'MPR_PAYMENT_REQUEST_PFA',
  MULTIPLE_PIN_PAYMENT_CONFIRMATION_PENCOM = 'MULTIPLE_PIN_PAYMENT_CONFIRMATION_PENCOM',
  MULTIPLE_PIN_VALID_PIN_PAYMENT_CONFIRMATION = 'MULTIPLE_PIN_VALID_PIN_PAYMENT_CONFIRMATION',
  EXCO_APPROVAL_REQUEST = 'EXCO_APPROVAL_REQUEST',
  EXCO_APPROVAL_GRANTED = 'EXCO_APPROVAL_GRANTED',
  ACCOUNT_PAYMENT_REQUESTED = 'ACCOUNT_PAYMENT_REQUESTED',
  ACCOUNT_PAYMENT_CONFIRMED = 'ACCOUNT_PAYMENT_CONFIRMED',
  PFA_PAYMENT_CONFIRMED = 'PFA_PAYMENT_CONFIRMED',
  PFA_PAYMENT_MADE = 'PFA_PAYMENT_MADE',
  PFC_PAYMENT_MADE = 'PFC_PAYMENT_MADE',
  PFA_NON_PAYMENT_CONFIRMATION = 'PFA_NON_PAYMENT_CONFIRMATION',
  HOD_CERTIFICATION_NOTIFICATION = 'HOD_CERTIFICATION_NOTIFICATION',
  MULTIPLE_PIN_TRANSACTION_HISTORY_REJECTION = 'MULTIPLE_PIN_TRANSACTION_HISTORY_REJECTION',
}
