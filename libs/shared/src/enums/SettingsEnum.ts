import { LeaveTypeEnum } from '@app/shared/enums/LeaveTypeEnum';
import { LivenessEventTypeEnum } from '@app/shared/enums/LivenessEventTypeEnum';

export enum SettingsEnumKey {
  PASSWORD_REGEX = 'PASSWORD_REGEX',
  FROM_EMAIL = 'FROM_EMAIL',
  OTP_LENGTH = 'OTP_LENGTH',
  OTP_TYPE = 'OTP_TYPE',
  OTP_EXPIRATION_TIME_IN_MINUTES = 'OTP_EXPIRATION_TIME_IN_MINUTES',
  EXCEL_SHEET_FILE_MAX_SIZE = 'EXCEL_SHEET_FILE_MAX_SIZE',
  EXCEL_SHEET_MAX_ROW_COUNT = 'EXCEL_SHEET_MAX_ROW_COUNT',
  STABLE_UPLOAD_PROCESSING_TIMER_RANGE = 'STABLE_UPLOAD_PROCESSING_TIMER_RANGE',
  NOMINAL_ROLL_SHEET_BAND_CONFIG = 'NOMINAL_ROLL_SHEET_BAND_CONFIG',
  AD_OID_URL_SUFFIX = 'AD_OID_URL_SUFFIX',
  AD_OID_URL = 'AD_OID_URL',
  AD_OID_DOMAIN_NAME = 'AD_OID_DOMAIN_NAME',
  PENCOM_USER_LOGIN_MODE = 'PENCOM_USER_LOGIN_MODE',
  COBRA_BASE_URL = 'COBRA_BASE_URL',
  NOTIFICATION_BASE_URL = 'NOTIFICATION_BASE_URL',
  ENROLMENT_YEAR_BANDS = 'ENROLMENT_YEAR_BANDS',
  LEAVE_TYPES = 'LEAVE_TYPES',
  SUPPORT_EMAIL_ADDRESS = 'SUPPORT_EMAIL_ADDRESS',
  MAX_NUMBER_OF_ADMINS = 'MAX_NUMBER_OF_ADMINS',
  WHITELIST_APPROVER_ROLES = 'WHITELIST_APPROVER_ROLES',
  ROLE_CHANGE_APPROVER_ROLES = 'ROLE_CHANGE_APPROVER_ROLES',
  CRON_INACTIVE_USERS_BATCH_SIZE = 'CRON_INACTIVE_USERS_BATCH_SIZE',
  DAYS_TO_DEACTIVATE_INACTIVE_USERS = 'DAYS_TO_DEACTIVATE_INACTIVE_USERS',
  SPECIMEN_FILE_MAX_SIZE = 'SPECIMEN_FILE_MAX_SIZE',
  METABASE_BASE_URL = 'METABASE_BASE_URL',
  METABASE_SECRET_KEY = 'METABASE_SECRET_KEY',
  METABASE_TOKEN_DURATION = 'METABASE_TOKEN_DURATION',
  PFA_SUPERVISOR_ROLE_NAME = 'PFA_SUPERVISOR_ROLE_NAME',
  PENCOM_VALIDATOR_ROLE_NAME = 'PENCOM_VALIDATOR_ROLE_NAME',
  PENCOM_AUDIT_VALIDATOR_ROLE_NAME = 'PENCOM_AUDIT_VALIDATOR_ROLE_NAME',
  PENCOM_AUDIT_SUPERVISOR_ROLE_NAME = 'PENCOM_AUDIT_SUPERVISOR_ROLE_NAME',
  PENCOM_EXCO_ROLE_NAME = 'PENCOM_EXCO_ROLE_NAME',
  PDF_SERVICE_URL = 'PDF_SERVICE_URL',
  PDF_SERVICE_LANDSCAPE_URL = 'PDF_SERVICE_LANDSCAPE_URL',
  ALWAYS_REGENERATE_ACCOUNT_MEMO = 'ALWAYS_REGENERATE_ACCOUNT_MEMO',
  ALWAYS_REGENERATE_PFA_MEMO = 'ALWAYS_REGENERATE_PFA_MEMO',
  MDA_ADMIN_ROLE_NAME = 'MDA-ADMIN-ROLE-NAME',
  PFA_ADMIN_ROLE_NAME = 'PFA-ADMIN-ROLE-NAME',
  LIVENESS_EVENTS = 'LIVENESS_EVENTS',
  LIVENESS_VARIANCE_THRESHOLD = 'LIVENESS_VARIANCE_THRESHOLD',
  LIVENESS_INDICATOR_COLOR = 'LIVENESS_INDICATOR_COLOR',
  LIVENESS_INDICATOR_LINE_WIDTH = 'LIVENESS_INDICATOR_LINE_WIDTH',
  LIVENESS_BLINK_THRESHOLD = 'LIVENESS_BLINK_THRESHOLD',
  LIVENESS_EYE_BROW_DYNAMIC_THRESHOLD = 'LIVENESS_EYE_BROW_DYNAMIC_THRESHOLD',
  LIVENESS_TIMER = 'LIVENESS_TIMER',
  LIVENESS_CHALLENGE_COUNT = 'LIVENESS_CHALLENGE_COUNT',
  NDMD_ROLE_NAME = 'NDMD_ROLE_NAME',
  HOD_ROLE_NAME = 'HOD_ROLE_NAME',
  AUDIT_HOD_ROLE_NAME = 'AUDIT_HOD_ROLE_NAME',
  ACCOUNT_ROLE_NAME = 'ACCOUNT_ROLE_NAME',
  RETIRE_EXCEL_TEMPLATE_URL = 'RETIRE_EXCEL_TEMPLATE_URL',
  DEACEASED_EXCEL_TEMPLATE_URL = 'DEACEASED_EXCEL_TEMPLATE_URL',
  NOMINAL_ROLL_EXCEL_TEMPLATE_URL = 'NOMINAL_ROLL_EXCEL_TEMPLATE_URL',
  MULTIPLE_PIN_EXCEL_TEMPLATE_URL = 'MULTIPLE_PIN_EXCEL_TEMPLATE_URL',
  PAYMENT_CONFIRMATION_EXCEL_TEMPLATE_URL = 'PAYMENT_CONFIRMATION_EXCEL_TEMPLATE_URL',
  EXIT_CONTRIBUTION_INTEREST_RATE = 'EXIT_CONTRIBUTION_INTEREST_RATE',
  NOMINAL_ROLL_UPLOAD_STATE = 'NOMINAL_ROLL_UPLOAD_STATE',
  MDA_EXCEL_RESULT_TIMEOUT = 'MDA_EXCEL_RESULT_TIMEOUT',
  MULTIPLE_PIN_CONTRIBUTION_THRESHOLD_PERCENT = 'MULTIPLE_PIN_CONTRIBUTION_THRESHOLD_PERCENT',
  MULTIPLE_PIN_CPA_INTEREST_RATE_PERCENT = 'MULTIPLE_PIN_CPA_INTEREST_RATE_PERCENT',
  PFA_AWAITING_CONFIRMATION_WAIT_PERIOD = 'PFA_AWAITING_CONFIRMATION_WAIT_PERIOD',
  CAPTCHA_SECRET_KEY = 'CAPTCHA_SECRET_KEY',
  CAPTCHA_SITE_KEY = 'CAPTCHA_SITE_KEY',
  CAPTCHA_VALID_THRESHOLD = 'CAPTCHA_VALID_THRESHOLD',
  CAPTCHA_VALIDATION_URL = 'CAPTCHA_VALIDATION_URL',
  ENABLE_CAPTCHA = 'ENABLE_CAPTCHA',
  ENABLE_2FA_FOR_PENCOM_USERS = 'ENABLE_2FA_FOR_PENCOM_USERS',
  TWO_FACTOR_WINDOW = 'TWO_FACTOR_WINDOW',
  APP_NAME = 'APP_NAME',
  ACCRUED_RIGHTS_INTEREST_RATE = 'ACCRUED_RIGHTS_INTEREST_RATE',
}

export const SettingsEnum: Record<SettingsEnumKey, { name: string; value: string; description: string }> = {
  [SettingsEnumKey.PASSWORD_REGEX]: {
    name: 'PASSWORD-REGEX',
    value: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\W).{12,}$',
    description: 'REGEX for validating user password',
  },
  [SettingsEnumKey.FROM_EMAIL]: {
    name: 'FROM-EMAIL',
    value: 'PenCom COBRA <<EMAIL>>',
    description: 'FROM_EMAIL',
  },
  [SettingsEnumKey.OTP_LENGTH]: {
    name: 'OTP-LENGTH',
    value: '6',
    description: 'OTP-LENGTH',
  },
  [SettingsEnumKey.OTP_TYPE]: {
    name: 'OTP-TYPE',
    value: 'DIGITS',
    description: 'OTP-TYPE',
  },
  [SettingsEnumKey.OTP_EXPIRATION_TIME_IN_MINUTES]: {
    name: 'OTP-EXPIRATION-TIME-IN-MINUTES',
    value: '5',
    description: 'OTP-EXPIRATION-TIME-IN-MINUTES',
  },
  [SettingsEnumKey.EXCEL_SHEET_FILE_MAX_SIZE]: {
    name: 'EXCEL-SHEET-FILE-MAX-SIZE',
    value: '1',
    description: 'Maximum file size allowed to be uploaded in MB',
  },
  [SettingsEnumKey.EXCEL_SHEET_MAX_ROW_COUNT]: {
    name: 'EXCEL-SHEET-MAX-ROW-COUNT',
    value: '2000',
    description: 'Number or records an excel sheet is allowed to capture',
  },
  [SettingsEnumKey.STABLE_UPLOAD_PROCESSING_TIMER_RANGE]: {
    name: 'STABLE-UPLOAD-PROCESSING-TIMER-RANGE',
    value: '5',
    description: 'Time in which we would generate thesame unique number',
  },
  [SettingsEnumKey.AD_OID_URL_SUFFIX]: {
    name: 'AD-OID-URL-SUFFIX',
    value: '@PenCom.Local',
    description: 'Active Directory OID URL SUFFIX',
  },
  [SettingsEnumKey.AD_OID_URL]: {
    name: 'AD-OID-URL',
    value: 'ldap://**********:389',
    description: 'Active Directory OID URL',
  },
  [SettingsEnumKey.AD_OID_DOMAIN_NAME]: {
    name: 'AD-OID-DOMAIN-NAME',
    value: 'DC=PENCOM,DC=LOCAL',
    description: 'Active Directory Domain name',
  },
  [SettingsEnumKey.PENCOM_USER_LOGIN_MODE]: {
    name: 'PENCOM-USER-LOGIN-MODE',
    value: 'ACTIVE_DIRECTORY',
    description: 'LOGIN modes for pencom users : ACTIVE_DIRECTORY | PASSWORD',
  },
  [SettingsEnumKey.COBRA_BASE_URL]: {
    name: 'COBRA-BASE-URL',
    value: 'http://************:3000/',
    description: 'COBRA BASE URL',
  },
  [SettingsEnumKey.NOTIFICATION_BASE_URL]: {
    name: 'NOTIFICATION-BASE-URL',
    value: 'http://************:3009',
    description: 'COBRA NOTIFICATION BASE URL',
  },
  [SettingsEnumKey.ENROLMENT_YEAR_BANDS]: {
    name: 'ENROLMENT-YEAR-BANDS',
    value: `['AS AT 30TH JUNE, 2004','2004', '2007', '2010', '2013', '2016', '2019', '2022', '2025']`,
    description: 'ENROLMENT YEAR BANDS',
  },
  [SettingsEnumKey.LEAVE_TYPES]: {
    name: 'LEAVE-TYPES',
    value: `[${Object.values(LeaveTypeEnum).join(', ')}]`,
    description: 'LEAVE TYPES',
  },

  [SettingsEnumKey.MAX_NUMBER_OF_ADMINS]: {
    name: 'MAX-NUMBER-OF-ADMINS',
    value: '1',
    description: 'Maximum number of admins allowed per organization',
  },
  [SettingsEnumKey.WHITELIST_APPROVER_ROLES]: {
    name: 'WHITELIST-APPROVER-ROLES',
    value: 'CBRD_VALIDATORS, HOD, HOU',
    description: 'Roles that can approve whitelist requests',
  },
  [SettingsEnumKey.ROLE_CHANGE_APPROVER_ROLES]: {
    name: 'ROLE-CHANGE-APPROVER-ROLES',
    value: 'CBRD_VALIDATORS, HOD, HOU',
    description: 'Roles that can approve role change requests',
  },
  [SettingsEnumKey.SUPPORT_EMAIL_ADDRESS]: {
    name: 'SUPPORT-EMAIL-ADDRESS',
    value: '<EMAIL>',
    description: 'SUPPORT EMAIL ADDRESS',
  },
  [SettingsEnumKey.CRON_INACTIVE_USERS_BATCH_SIZE]: {
    name: 'CRON-INACTIVE-USERS-BATCH-SIZE',
    value: '100',
    description: 'Number of inactive users to process in each batch',
  },
  [SettingsEnumKey.DAYS_TO_DEACTIVATE_INACTIVE_USERS]: {
    name: 'DAYS-TO-DEACTIVATE-INACTIVE-USERS',
    value: '90', // Default value of 90 days
    description: 'Number of days after which inactive users will be deactivated',
  },
  [SettingsEnumKey.SPECIMEN_FILE_MAX_SIZE]: {
    name: 'SPECIMEN-FILE-MAX-SIZE',
    value: '2',
    description: 'Maximum file size allowed for specimen documents in MB',
  },
  [SettingsEnumKey.METABASE_BASE_URL]: {
    name: 'METABASE-BASE-URL',
    value: 'http://metabase.default.url',
    description: 'Base URL for Metabase dashboard',
  },
  [SettingsEnumKey.METABASE_SECRET_KEY]: {
    name: 'METABASE-SECRET-KEY',
    value: 'your-default-secret-key',
    description: 'Secret key for Metabase token generation',
  },
  [SettingsEnumKey.METABASE_TOKEN_DURATION]: {
    name: 'METABASE-TOKEN-DURATION',
    value: '10',
    description: 'Duration in minutes for Metabase token validity',
  },
  [SettingsEnumKey.PFA_SUPERVISOR_ROLE_NAME]: {
    name: 'PFA-SUPERVISOR-ROLE-NAME',
    value: 'PFA_SUPERVISOR',
    description: 'Role name for PFA Supervisor',
  },
  [SettingsEnumKey.PENCOM_VALIDATOR_ROLE_NAME]: {
    name: 'PENCOM-VALIDATOR-ROLE-NAME',
    value: 'PENCOM_VALIDATOR',
    description: 'Role name for PENCOM Validator',
  },
  [SettingsEnumKey.PENCOM_AUDIT_VALIDATOR_ROLE_NAME]: {
    name: 'PENCOM-AUDIT-VALIDATOR-ROLE-NAME',
    value: 'PENCOM_AUDIT_VALIDATOR',
    description: 'Role name for PENCOM AUDIT Validator',
  },
  [SettingsEnumKey.PENCOM_AUDIT_SUPERVISOR_ROLE_NAME]: {
    name: 'PENCOM-AUDIT-SUPERVISOR-ROLE-NAME',
    value: 'PENCOM_AUDIT_SUPERVISOR',
    description: 'Role name for PENCOM AUDIT SUPERVISOR',
  },
  [SettingsEnumKey.PENCOM_EXCO_ROLE_NAME]: {
    name: 'PENCOM-EXCO-ROLE-NAME',
    value: 'PENCOM_EXCO_ROLE',
    description: 'Role name for PENCOM AUDIT SUPERVISOR',
  },
  [SettingsEnumKey.PDF_SERVICE_URL]: {
    name: 'PDF-SERVICE-URL',
    value: 'http://localhost:3040?format=legal&landscape=false',
    description: 'PDF_SERVICE_URL',
  },
  [SettingsEnumKey.PDF_SERVICE_LANDSCAPE_URL]: {
    name: 'PDF-SERVICE-LANDSCAPE-URL',
    value: 'http://localhost:3040?format=legal&landscape=true',
    description: 'PDF_SERVICE_LANDSCAPE_URL',
  },
  [SettingsEnumKey.MDA_ADMIN_ROLE_NAME]: {
    name: 'MDA-ADMIN-ROLE-NAME',
    value: 'MDA ADMIN',
    description: 'Role name for MDA admin users',
  },
  [SettingsEnumKey.PFA_ADMIN_ROLE_NAME]: {
    name: 'PFA-ADMIN-ROLE-NAME',
    value: 'PFA ADMIN',
    description: 'Role name for PFA admin users',
  },
  [SettingsEnumKey.LIVENESS_EVENTS]: {
    name: 'LIVENESS-EVENTS',
    value: `[${Object.values(LivenessEventTypeEnum).join(', ')}]`,
    description: 'LIVENESS EVENT TYPES',
  },
  [SettingsEnumKey.LIVENESS_VARIANCE_THRESHOLD]: {
    name: 'LIVENESS-VARIANCE-THRESHOLD',
    value: '7000',
    description: 'LIVENESS VARIANCE THRESHOLD',
  },
  [SettingsEnumKey.LIVENESS_INDICATOR_COLOR]: {
    name: 'LIVENESS-INDICATOR-COLOR',
    value: 'red',
    description: 'LIVENESS INDICATOR COLOR',
  },
  [SettingsEnumKey.LIVENESS_INDICATOR_LINE_WIDTH]: {
    name: 'LIVENESS-INDICATOR-LINE-WIDTH',
    value: '1',
    description: 'LIVENESS INDICATOR LINE WIDTH',
  },
  [SettingsEnumKey.LIVENESS_BLINK_THRESHOLD]: {
    name: 'LIVENESS-BLINK-THRESHOLD',
    value: '0.27',
    description: 'LIVENESS BLINK THRESHOLD',
  },
  [SettingsEnumKey.LIVENESS_EYE_BROW_DYNAMIC_THRESHOLD]: {
    name: 'LIVENESS-EYE-BROW-DYNAMIC-THRESHOLD',
    value: '5',
    description: 'LIVENESS EYE BROW DYNAMIC THRESHOLD',
  },
  [SettingsEnumKey.LIVENESS_TIMER]: {
    name: 'LIVENESS-TIMER',
    value: '20',
    description: 'LIVENESS TIMER',
  },
  [SettingsEnumKey.LIVENESS_CHALLENGE_COUNT]: {
    name: 'LIVENESS-CHALLENGE-COUNT',
    value: '2',
    description: 'LIVENESS CHALLENGE COUNT',
  },
  [SettingsEnumKey.NDMD_ROLE_NAME]: {
    name: 'NDMD-ROLE-NAME',
    value: 'NDMD',
    description: 'Role name for NDMD users',
  },
  [SettingsEnumKey.HOD_ROLE_NAME]: {
    name: 'HOD-ROLE-NAME',
    value: 'HOD',
    description: 'Role name for HOD users',
  },
  [SettingsEnumKey.AUDIT_HOD_ROLE_NAME]: {
    name: 'AUDIT-HOD-ROLE-NAME',
    value: 'AUDIT-HOD',
    description: 'Role name for AUDIT HOD users',
  },
  [SettingsEnumKey.ACCOUNT_ROLE_NAME]: {
    name: 'ACCOUNT-ROLE-NAME',
    value: 'ACCOUNT, FINANCIAL_PLANNING',
    description: 'Role name for ACCOUNT users',
  },
  [SettingsEnumKey.ALWAYS_REGENERATE_ACCOUNT_MEMO]: {
    name: 'ALWAYS-REGENERATE-ACCOUNT-MEMO',
    value: 'false',
    description: 'Determines whether to always generate account memo or not',
  },
  [SettingsEnumKey.ALWAYS_REGENERATE_PFA_MEMO]: {
    name: 'ALWAYS-REGENERATE-PFA-MEMO',
    value: 'false',
    description: 'Determines whether to always generate PFA memo or not',
  },
  [SettingsEnumKey.RETIRE_EXCEL_TEMPLATE_URL]: {
    name: 'RETIRE-EXCEL-TEMPLATE-URL',
    value:
      'https://docs.google.com/spreadsheets/d/1hpb-vb_Ftedst7aU5JH87nMXv8mMKwz6/edit?usp=sharing&ouid=111864015008020044556&rtpof=true&sd=true',
    description: 'Excel template url for RETIREE',
  },
  [SettingsEnumKey.DEACEASED_EXCEL_TEMPLATE_URL]: {
    name: 'DEACEASED-EXCEL-TEMPLATE-URL',
    value:
      'https://docs.google.com/spreadsheets/d/1p_aG-w8QZawRSeCmR8Yp6_3bQjfakgoA/edit?usp=sharing&ouid=111864015008020044556&rtpof=true&sd=true',
    description: 'Excel template url for RETIREE',
  },
  [SettingsEnumKey.NOMINAL_ROLL_EXCEL_TEMPLATE_URL]: {
    name: 'NOMINAL-ROLL-EXCEL-TEMPLATE-URL',
    value:
      'https://docs.google.com/spreadsheets/d/1W7rJrPS8uUeQZ-GwUTx3MJ6AWD8Wnn-l/edit?usp=sharing&ouid=111864015008020044556&rtpof=true&sd=true',
    description: 'Excel template url for RETIREE',
  },
  [SettingsEnumKey.MULTIPLE_PIN_EXCEL_TEMPLATE_URL]: {
    name: 'MULTIPLE-PIN-EXCEL-TEMPLATE-URL',
    value:
      'https://docs.google.com/spreadsheets/d/1-lzpfiVeRbgBNFGwz-j7D4CJJ4LjzjyP/edit?usp=sharing&ouid=111864015008020044556&rtpof=true&sd=true',
    description: 'Excel template url for MULTIPLE PIN',
  },
  [SettingsEnumKey.PAYMENT_CONFIRMATION_EXCEL_TEMPLATE_URL]: {
    name: 'PAYMENT-CONFIRMATION-EXCEL-TEMPLATE-URL',
    value: 'https://drive.google.com/file/d/1Xlyt9-g_vtc4mASX0Cw5CkQeM4DCUrL9/view?usp=sharing',
    description: 'Excel template url for MULTIPLE PIN',
  },
  [SettingsEnumKey.EXIT_CONTRIBUTION_INTEREST_RATE]: {
    name: 'EXIT-CONTRIBUTION-INTEREST-RATE',
    value: '7',
    description: 'Interest rate for exit contribution',
  },
  [SettingsEnumKey.NOMINAL_ROLL_SHEET_BAND_CONFIG]: {
    name: 'NOMINAL-ROLL-SHEET-BAND-CONFIG',
    value:
      '[{"year":2004,"start":3},{"year":2007,"start":7},{"year":2010,"start":11},{"year":2013,"start":15},{"year":2016,"start":19},{"year":2019,"start":23},{"year":2022,"start":27},{"year":2025,"start":31}]',
    description: 'Band configuration for nominal roll excel sheet',
  },
  [SettingsEnumKey.NOMINAL_ROLL_UPLOAD_STATE]: {
    name: 'NOMINAL-ROLL-UPLOAD-STATE',
    value: 'UPLOADED, CR_FINALISED',
    description: 'Band configuration for nominal roll excel sheet',
  },
  [SettingsEnumKey.MDA_EXCEL_RESULT_TIMEOUT]: {
    name: 'MDA-EXCEL-RESULT-TIMEOUT',
    value: '2000',
    description: 'Interest rate for exit contribution',
  },
  [SettingsEnumKey.MULTIPLE_PIN_CONTRIBUTION_THRESHOLD_PERCENT]: {
    name: 'MULTIPLE-PIN-CONTRIBUTION-THRESHOLD-PERCENT',
    value: '50',
    description: 'Threshold percentage for invalid PIN contributions compared to valid PIN',
  },
  [SettingsEnumKey.MULTIPLE_PIN_CPA_INTEREST_RATE_PERCENT]: {
    name: 'MULTIPLE-PIN-CPA-INTEREST-RATE-PERCENT',
    value: '7',
    description: 'Percentage of investment income allocated to CPA balance',
  },
  [SettingsEnumKey.PFA_AWAITING_CONFIRMATION_WAIT_PERIOD]: {
    name: 'PFA-AWAITING-CONFIRMATION-WAIT-PERIOD',
    value: '10',
    description: 'PFA AWAITING CONFIRMATION WAIT PERIOD IN DAYS (WORKING DAYS ONLY)',
  },

  [SettingsEnumKey.CAPTCHA_SECRET_KEY]: {
    name: 'CAPTCHA-SECRET-KEY',
    value: 'captcha-secret-key',
    description: 'Secret key for captcha validation',
  },
  [SettingsEnumKey.CAPTCHA_VALID_THRESHOLD]: {
    name: 'CAPTCHA-VALID-THRESHOLD',
    value: '0.5',
    description: 'Threshold for captcha validation',
  },
  [SettingsEnumKey.CAPTCHA_VALIDATION_URL]: {
    name: 'CAPTCHA-VALIDATION-URL',
    value: 'https://www.example.com/',
    description: 'URL for captcha validation',
  },
  [SettingsEnumKey.CAPTCHA_SITE_KEY]: {
    name: 'CAPTCHA-SITE-KEY',
    value: 'site-key',
    description: 'Site key for captcha validation',
  },
  [SettingsEnumKey.ENABLE_CAPTCHA]: {
    name: 'ENABLE-CAPTCHA',
    value: 'false',
    description: 'Enable captcha validation',
  },
  [SettingsEnumKey.ENABLE_2FA_FOR_PENCOM_USERS]: {
    name: 'ENABLE-2FA-FOR-PENCOM-USERS',
    value: 'false',
    description: 'Enable 2FA for pencom users',
  },
  [SettingsEnumKey.TWO_FACTOR_WINDOW]: {
    name: 'TWO-FACTOR-WINDOW',
    value: '1',
    description: 'Number of intervals before or after which the token is considered valid',
  },
  [SettingsEnumKey.APP_NAME]: {
    name: 'APP-NAME',
    value: 'COBRA',
    description: 'Name of the application',
  },
  [SettingsEnumKey.ACCRUED_RIGHTS_INTEREST_RATE]: {
    name: 'ACCRUED-RIGHTS-INTEREST-RATE',
    value: '0.5',
    description: 'Interest rate for accrued rights calculations',
  },
};
