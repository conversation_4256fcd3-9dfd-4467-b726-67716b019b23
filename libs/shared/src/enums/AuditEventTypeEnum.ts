export enum AuditEventTypeEnum {
  LOGIN_REGISTER_ATTEMPT = 'LOGIN_REGISTER_ATTEMPT',
  LOGIN_REGISTER_SUCCESS = 'LOGIN_REGISTER_SUCCESS',

  LOGIN_ATTEMPT = 'LOGIN_ATTEMPT',

  SAVE_SECURITY_QUESTIONS_ATTEMPT = 'SAVE_SECURITY_QUESTIONS_ATTEMPT',
  SAVE_SECURITY_QUESTIONS_SUCCESS = 'SAVE_SECURITY_QUESTIONS_SUCCESS',

  VERIFY_SECURITY_QUESTIONS_ATTEMPT = 'VERIFY_SECURITY_QUESTIONS_ATTEMPT',
  VERIFY_SECURITY_QUESTIONS_SUCCESS = 'VERIFY_SECURITY_QUESTIONS_SUCCESS',

  GET_SECURITY_QUESTIONS_ATTEMPT = 'GET_SECURITY_QUESTIONS_ATTEMPT',
  GET_SECURITY_QUESTIONS_SUCCESS = 'GET_SECURITY_QUESTIONS_SUCCESS',

  GET_USER_SECURITY_QUESTIONS_ATTEMPT = 'GET_USER_SECURITY_QUESTIONS_ATTEMPT',
  GET_USER_SECURITY_QUESTIONS_SUCCESS = 'GET_USER_SECURITY_QUESTIONS_SUCCESS',

  CHANGE_PASSWORD_ATTEMPT = 'CHANGE_PASSWORD_ATTEMPT',
  CHANGE_PASSWORD_SUCCESS = 'CHANGE_PASSWORD_SUCCESS',

  CHANGE_OLD_PASSWORD_ATTEMPT = 'CHANGE_OLD_PASSWORD_ATTEMPT',
  CHANGE_OLD_PASSWORD_SUCCESS = 'CHANGE_OLD_PASSWORD_SUCCESS',

  RESET_PASSWORD_ATTEMPT = 'RESET_PASSWORD_ATTEMPT',
  RESET_PASSWORD_SUCCESS = 'RESET_PASSWORD_SUCCESS',

  SIGNOUT_ATTEMPT = 'SIGNOUT_ATTEMPT',
  SIGNOUT_SUCCESS = 'SIGNOUT_SUCCESS',

  CONTRIBUTION_LEDGER_INSERT = 'CONTRIBUTION_LEDGER_INSERT',
  ACCRUED_RIGHTS_LEDGER_INSERT = 'ACCRUED_RIGHTS_LEDGER_INSERT',

  FLAG_RSA_HOLDER = 'FLAG_RSA_HOLDER',
  EDIT_FLAG_RSA_HOLDER = 'EDIT_FLAG_RSA_HOLDER',

  CREATE_USER_ATTEMPT = 'CREATE_USER_ATTEMPT',
  CREATE_USER_SUCCESS = 'CREATE_USER_SUCCESS',

  CREATE_USER_WITHOUT_PASSWORD_ATTEMPT = 'CREATE_USER_WITHOUT_PASSWORD_ATTEMPT',
  CREATE_USER_WITHOUT_PASSWORD_SUCCESS = 'CREATE_USER_WITHOUT_PASSWORD_SUCCESS',

  GET_USER_BY_EMAIL_ATTEMPT = 'GET_USER_BY_EMAIL_ATTEMPT',
  GET_USER_BY_EMAIL_SUCCESS = 'GET_USER_BY_EMAIL_SUCCESS',

  UPLOAD_PROFILE_PICTURE_ATTEMPT = 'UPLOAD_PROFILE_PICTURE_ATTEMPT',
  UPLOAD_PROFILE_PICTURE_SUCCESS = 'UPLOAD_PROFILE_PICTURE_SUCCESS',

  SAVE_SIGNATURE_ATTEMPT = 'SAVE_SIGNATURE_ATTEMPT',
  SAVE_SIGNATURE_SUCCESS = 'SAVE_SIGNATURE_SUCCESS',

  UPDATE_ROLE_CHANGE_REQUEST = 'UPDATE_ROLE_CHANGE_REQUEST',
  CREATE_ROLE_CHANGE_REQUEST = 'CREATE_ROLE_CHANGE_REQUEST',
  EDIT_USER_ATTEMPT = 'EDIT_USER_ATTEMPT',
  UPDATE_USER_WHITELIST_REQUEST = 'UPDATE_USER_WHITELIST_REQUEST',
  CREATE_USER_WHITELIST_REQUEST = 'CREATE_USER_WHITELIST_REQUEST',
  UPDATE_SETTING_ATTEMPT = 'UPDATE_SETTING_ATTEMPT',
  CREATE_HELP_TEMPLATE = 'CREATE_HELP_TEMPLATE',
  CREATE_HELP_RESOURCE = 'CREATE_HELP_RESOURCE',
  DEACTIVATE_ROLE_ATTEMPT = 'DEACTIVATE_ROLE_ATTEMPT',

  CREATE_ROLE_ATTEMPT = 'CREATE_ROLE_ATTEMPT',
  EDIT_ROLE_ATTEMPT = 'EDIT_ROLE_ATTEMPT',
  UPDATE_USER_STATUS = 'UPDATE_USER_STATUS',
  CREATE_MULTIPLE_PIN_RESOLUTION = 'CREATE_MULTIPLE_PIN_RESOLUTION',
  MULTIPLE_PIN_PAYMENT_CONFIRMATION_PENCOM = 'MULTIPLE_PIN_PAYMENT_CONFIRMATION_PENCOM',
  MULTIPLE_PIN_MEMO_GENERATION = 'MULTIPLE_PIN_MEMO_GENERATION',
  CREATE_AMENDMENT_REQUEST = 'CREATE_AMENDMENT_REQUEST',
  DELETE_MULTIPLE_PIN_REQUEST_TRANSACTION_HISTORY = 'DELETE_MULTIPLE_PIN_REQUEST_TRANSACTION_HISTORY',
  CREATE_SETTING_ATTEMPT = 'CREATE_SETTING_ATTEMPT',
  BATCH_MEMO_GENERATION = 'BATCH_MEMO_GENERATION',
  BATCH_ACCOUNT_MEMO_GENERATION = 'BATCH_ACCOUNT_MEMO_GENERATION',
  BATCH_STAMP_SCHEDULE = 'BATCH_STAMP_SCHEDULE',
  BATCH_DOCUMENT_UPLOAD = 'BATCH_DOCUMENT_UPLOAD',
  MULTIPLE_PIN_TRANSACTION_HISTORY_UPLOAD = 'MULTIPLE_PIN_TRANSACTION_HISTORY_UPLOAD',

  REASSIGN_TASK = 'REASSIGN_TASK',
  UPDATE_AMENDMENT_REQUEST = 'UPDATE_AMENDMENT_REQUEST',
  CREATE_PFA = 'CREATE_PFA',
  DELETE_PFA = 'DELETE_PFA',
  EDIT_PFA = 'EDIT_PFA',
  CREATE_PFC = 'CREATE_PFC',
  DELETE_PFC = 'DELETE_PFC',
  EDIT_PFC = 'EDIT_PFC',
  CREATE_MDA = 'CREATE_MDA',
  DELETE_MDA = 'DELETE_MDA',
  EDIT_MDA = 'EDIT_MDA',
  MAIL_HISTORY_RESEND = 'MAIL_HISTORY_RESEND',
  VERIFY_OTP_ATTEMPT = 'VERIFY_OTP_ATTEMPT',
  RESET_2FA_SECRET_ATTEMPT = 'RESET_2FA_SECRET_ATTEMPT',
}
