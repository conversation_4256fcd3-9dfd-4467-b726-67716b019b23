/* eslint-disable */
import { Injectable, NotFoundException } from '@nestjs/common';
import * as ldap from 'ldapjs';
import { SearchOptions } from 'ldapjs';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { SettingsEnumKey } from '@app/shared/enums';

@Injectable()
export class LdapAuthService {
  constructor(private readonly settingsService: SettingMsLibService) {}

  async login(username: string, password: string): Promise<{ staffName: string; department: string }> {
    try {
      const adSuffix = await this.settingsService.getSetting(SettingsEnumKey.AD_OID_URL_SUFFIX);
      const adminName = `${username}${adSuffix}`;
      const adminPassword = password;
      const ldapURL = await this.settingsService.getSetting(SettingsEnumKey.AD_OID_URL);

      const env = {
        url: ldapURL,
        bindDN: adminName,
        bindCredentials: adminPassword,
      };

      const client = ldap.createClient(env);
      const oidDn = await this.settingsService.getSetting(SettingsEnumKey.AD_OID_DOMAIN_NAME);

      return new Promise<{ staffName: string; department: string }>((resolve, reject) => {
        // Handle LDAP client errors
        client.on('error', (err) => {
          client.destroy();
          reject(new NotFoundException('LDAP connection error: ' + err.message));
        });

        // Bind to the LDAP server
        client.bind(adminName, adminPassword, (bindErr) => {
          if (bindErr) {
            client.destroy();
            reject(new NotFoundException('Invalid credentials or user not found'));
            return;
          }

          // Search for the user
          const searchOptions: SearchOptions = {
            filter: `(&(mailNickname=${username.trim()})(mail=*))`,
            scope: 'sub',
            attributes: ['cn', 'description'], // Only fetch required attributes
          };

          client.search(oidDn, searchOptions, (searchErr, res) => {
            if (searchErr) {
              client.destroy();
              reject(new NotFoundException('User not found'));
              return;
            }

            let staffName = '';
            let department = '';
            let userFound = false;

            res.on('searchEntry', (entry) => {
              userFound = true;
              if (entry.attributes) {
                staffName = entry.attributes.find((attr) => attr.type === 'cn')?.values[0] || '';
                department = entry.attributes.find((attr) => attr.type === 'description')?.values[0] || '';
              }
            });

            res.on('error', (error) => {
              client.destroy();
              reject(new NotFoundException('Error searching LDAP'));
            });

            res.on('end', () => {
              client.destroy();
              if (!userFound) {
                reject(new NotFoundException('User not found'));
              } else {
                resolve({ staffName, department });
              }
            });
          });
        });
      });
    } catch (error) {
      // Handle unexpected errors
      console.log('LdapAuthService login error:', error);
      throw new NotFoundException('An error occurred during the LDAP authentication process');
    }
  }
}
