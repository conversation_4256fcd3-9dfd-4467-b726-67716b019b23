import * as speakeasy from 'speakeasy';
import * as qrcode from 'qrcode';
import { Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { Generate2FASecretResponse } from '@app/shared/dto/response/2fa-secret.dto';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { SettingsEnumKey } from '@app/shared/enums';

@Injectable()
export class TwoFactorAuthenticationService {
  constructor(
    private readonly logger: PinoLogger,
    private readonly settingMsLibService: SettingMsLibService
  ) {}

  async generate2FASecret(username: string): Promise<Generate2FASecretResponse> {
    const secret = speakeasy.generateSecret({
      name: `${await this.settingMsLibService.getSetting(SettingsEnumKey.APP_NAME)}: ${username}`,
    });

    const qrCodeDataUrl = await qrcode.toDataURL(secret.otpauth_url);

    return {
      otpAuthUrl: secret.otpauth_url,
      base32: secret.base32,
      qrCodeDataUrl,
    };
  }

  async verify2FACode(userSecret: string, userToken: string, clientTime: Date): Promise<boolean> {
    const window = await this.settingMsLibService.getSettingInt(SettingsEnumKey.TWO_FACTOR_WINDOW);
    return speakeasy.totp.verify({
      secret: userSecret,
      encoding: 'base32',
      token: userToken,
      time: Math.floor(new Date(clientTime).getTime() / 1000),
      window: window, // allows +/- 1 interval
    });
  }
}
