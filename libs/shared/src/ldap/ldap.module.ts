import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseModule, LoggerModule } from '@app/shared';
import { Setting } from '@app/shared/setting-service/entities/setting.entity';
import { TwoFactorAuthenticationService } from '@app/shared/ldap/services/2fa-lib.service';
import { LdapAuthService } from '@app/shared/ldap/services/ldap-auth.service';
import { SettingMsLibRepository } from '@app/shared/setting-service/setting-ms-lib.repository';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';

@Module({
  imports: [DatabaseModule, DatabaseModule.forFeature([Setting]), LoggerModule],
  providers: [TwoFactorAuthenticationService, LdapAuthService, SettingMsLibRepository, SettingMsLibService],
  exports: [TwoFactorAuthenticationService, LdapAuthService],
})
export class LdapModule {}
