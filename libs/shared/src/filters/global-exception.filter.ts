/* eslint-disable */
import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import { QueryFailedError } from 'typeorm';
import { ValidationError } from 'class-validator';
import { CustomException } from './exception.dto';
import { ResponseCodeEnum } from '../enums';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let errors = [];

    // Handle different types of exceptions
    if (exception instanceof CustomException) {
      status = 200; // return 200 status code for all custom exceptions
      message = exception.message;
    } else if (exception instanceof HttpException) {
      status = exception.getStatus();
      const response = exception.getResponse();
      message = typeof response === 'string' ? response : response['message'];
      errors = typeof response === 'string' ? [response] : response['errors'] || [];
    } else if (exception instanceof QueryFailedError) {
      status = HttpStatus.BAD_REQUEST;
      message = 'An error occurred while processing your request. Please try again.';
      errors = [];
    } else if (Array.isArray(exception) && exception[0] instanceof ValidationError) {
      status = HttpStatus.BAD_REQUEST;
      message = 'Validation failed';
      errors = this.formatValidationErrors(exception as ValidationError[]);
    } else if (exception instanceof Error) {
      errors = [exception.message as never];
    }

    if (!(exception instanceof CustomException)) {
      console.error('Exception:', {
        path: request.url,
        timestamp: new Date().toISOString(),
        exception: exception instanceof Error ? exception.stack : exception,
      });
    }

    // Send the error response
    response.status(status).json({
      code: ResponseCodeEnum.ERROR,
      success: false,
      description: message,
      content: errors,
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }

  private formatValidationErrors(validationErrors: ValidationError[]): string[] {
    const errors: string[] = [];

    validationErrors.forEach((error) => {
      if (error.constraints) {
        Object.values(error.constraints).forEach((constraint) => {
          errors.push(constraint);
        });
      }

      // Handle nested validation errors
      if (error.children && error.children.length > 0) {
        errors.push(...this.formatValidationErrors(error.children));
      }
    });

    return errors;
  }
}
