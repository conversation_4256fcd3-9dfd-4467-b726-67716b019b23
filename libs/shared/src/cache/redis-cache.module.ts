/* eslint-disable */
import { DynamicModule, Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisCacheService } from './redis-cache.service';

@Global()
@Module({})
export class RedisCacheModule {
  static register(): DynamicModule {
    return {
      module: RedisCacheModule,
      imports: [ConfigModule], // Ensure ConfigModule is available
      providers: [
        {
          provide: 'REDIS_URL',
          useFactory: (configService: ConfigService) => {
            configService.get<string>('REDIS_URL', 'redis://localhost:6379');
            configService.get<number>('REDIS_TTL', 300000);
          },
          inject: [ConfigService],
        },
        RedisCacheService,
      ],
      exports: [RedisCacheService],
    };
  }
}
