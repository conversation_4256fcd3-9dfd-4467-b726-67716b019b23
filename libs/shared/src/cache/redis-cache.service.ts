/* eslint-disable */
import { Injectable, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis, { Result } from 'ioredis';
import { ChainableCommander } from 'ioredis/built/utils/RedisCommander';
import { PinoLogger } from 'nestjs-pino/PinoLogger';

@Injectable()
export class RedisCacheService implements OnModuleInit, OnModuleDestroy {
  private redisClient: Redis;
  private defaultTTL: number;

  constructor(
    private configService: ConfigService,
    private readonly logger: PinoLogger
  ) {}

  onModuleInit() {
    const redisUrl = this.configService.get<string>('REDIS_URL', 'redis://localhost:6379');
    this.redisClient = new Redis(redisUrl);
    this.defaultTTL = this.configService.get<number>('REDIS_TTL', 1500);
    console.log('🚀 Redis connected:', redisUrl);
  }

  async get(key: string): Promise<any> {
    try {
      return this.redisClient.get(key);
    } catch (error) {
      this.logger.error('error getting item from cache', error);
    }
    return null;
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      const expiration = ttl ?? this.defaultTTL;
      await this.redisClient.set(key, value, 'EX', expiration);
    } catch (error) {
      this.logger.error('error setting item in cache', error);
    }
  }

  async setNx(key: string, value: any, ttl?: number): Promise<Result<'OK' | null, any>> {
    try {
      const expiration = ttl ?? this.defaultTTL;
      return this.redisClient.set(key, value, 'EX', expiration, 'NX');
    } catch (error) {
      this.logger.error('error setting item in cache', error);
      return null;
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.redisClient.del(key);
    } catch (error) {
      this.logger.error('error deleting item from cache', error);
    }
  }

  async deleteKeysWithPattern(pattern: string): Promise<void> {
    const client = this.redisClient;
    const stream = client.scanStream({ match: pattern });

    stream.on('data', async (keys: string[]) => {
      if (keys.length) {
        await client.del(...keys);
      }
    });

    stream.on('end', () => {
      console.log(`Deleted keys matching pattern: ${pattern}`);
    });
  }

  async incr(key: string): Promise<void> {
    await this.redisClient.incr(key);
  }

  multi(): ChainableCommander {
    return this.redisClient.multi();
  }

  ping() {
    return this.redisClient.ping();
  }

  pingWithTimeout(timeout: number): Promise<string> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('Redis ping timed out'));
      }, timeout);

      this.redisClient
        .ping()
        .then((res) => {
          clearTimeout(timer);
          resolve(res);
        })
        .catch((err) => {
          clearTimeout(timer);
          reject(err);
        });
    });
  }

  async keys(pattern = '*'): Promise<string[]> {
    return this.redisClient.keys(pattern);
  }

  async flushAll(): Promise<void> {
    await this.redisClient.flushall();
  }

  onModuleDestroy() {
    this.redisClient.disconnect();
    console.log('🛑 Redis disconnected.');
  }
}
