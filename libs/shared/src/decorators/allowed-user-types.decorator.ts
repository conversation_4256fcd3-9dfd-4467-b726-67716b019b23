import { SetMetadata, createParamDecorator, ExecutionContext, UseGuards } from '@nestjs/common';
import { UserTypeEnum } from '../enums/UserTypeEnum';
import { AllowedUserTypesGuard } from '@app/shared/guards/allowed-user-types.guard';

export const ALLOWED_USER_TYPES_KEY = 'allowedUserTypes';

export const AllowedUserTypes = (...userTypes: UserTypeEnum[]) => {
  return (target: any, key?: string, descriptor?: PropertyDescriptor) => {
    SetMetadata(ALLOWED_USER_TYPES_KEY, userTypes)(target, key, descriptor);
    UseGuards(AllowedUserTypesGuard)(target, key, descriptor);
  };
};
