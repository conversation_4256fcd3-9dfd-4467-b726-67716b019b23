import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

// Custom validator to check if an object is non-empty
@ValidatorConstraint({ name: 'IsNotEmptyObjectCustom', async: false })
export class IsNotEmptyObjectConstraint implements ValidatorConstraintInterface {
  validate(value: any) {
    return typeof value === 'object' && value !== null && Object.keys(value).length > 0;
  }

  defaultMessage() {
    return 'formData must not be empty and should contain at least one field';
  }
}

// Decorator function
export function IsNotEmptyObjectCustom(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsNotEmptyObjectConstraint,
    });
  };
}
