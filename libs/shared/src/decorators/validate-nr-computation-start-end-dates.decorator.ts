import { ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';
import { isAfter, isValid, parse } from 'date-fns';

@ValidatorConstraint({ name: 'NrDateRangeValidator', async: false })
export class NrDateRangeValidator implements ValidatorConstraintInterface {
  validate(_: any, args: ValidationArguments): boolean {
    const obj = args.object as any;

    const parseDate = (dateStr: string) => parse(dateStr, 'dd-MM-yyyy', new Date());

    const hasUploadDates = obj.uploadStartDate && obj.uploadEndDate;

    // Rule 1: If one upload date is provided, both must be present, valid, and in correct order
    if (obj.uploadStartDate || obj.uploadEndDate) {
      if (!hasUploadDates) return false;
      const start = parseDate(obj.uploadStartDate);
      const end = parseDate(obj.uploadEndDate);
      if (!isValid(start) || !isValid(end)) return false;
      if (!isAfter(end, start) && end.getTime() !== start.getTime()) return false;
    }

    return true;
  }

  defaultMessage(args: ValidationArguments): string {
    return 'You must provide a valid pair of Upload Start and End Date, the end date must be after start date.';
  }
}
