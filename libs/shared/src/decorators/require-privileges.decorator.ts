import { SetMetadata, UseGuards } from '@nestjs/common';
import { CobraPrivilege, CobraPrivilegeKey } from '../user-service/entities/enums/cobra-privilege-enum';
import { PrivilegeGuard } from '@app/shared/auth/guards/privilege.guard';

export const REQUIRED_PRIVILEGES_KEY = 'requiredPrivileges';
export const RequirePrivileges = (...privileges: (CobraPrivilegeKey | CobraPrivilege)[]) => {
  return (target: any, key?: string, descriptor?: PropertyDescriptor) => {
    SetMetadata(
      REQUIRED_PRIVILEGES_KEY,
      privileges.map((priv) => (typeof priv === 'string' ? priv : priv.code))
    )(target, key, descriptor);
    UseGuards(PrivilegeGuard)(target, key, descriptor);
  };
};
