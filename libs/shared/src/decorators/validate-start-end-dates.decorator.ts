import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

export function IsStartDateBeforeEndDate(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'IsStartDateBeforeEndDate',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(_: any, args: ValidationArguments) {
          const obj = args.object as any;
          if (!obj.leaveStartDate?.trim() || !obj.leaveEndDate?.trim()) return true; // Ignore empty values

          const parseDate = (dateStr: string): Date | null => {
            const [day, month, year] = dateStr.split('-').map(Number);
            return day && month && year ? new Date(year, month - 1, day) : null;
          };

          const startDate = parseDate(obj.leaveStartDate);
          const endDate = parseDate(obj.leaveEndDate);

          return startDate !== null && endDate !== null && startDate < endDate;
        },
        defaultMessage(validationArguments?: ValidationArguments): string {
          const obj = validationArguments?.object as any;
          return `Leave Start date: ${obj?.leaveStartDate} must be before Leave End date: ${obj?.leaveEndDate}`;
        },
      },
    });
  };
}
