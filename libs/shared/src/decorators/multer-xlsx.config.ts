import { Options as MulterOptions } from 'multer';
import { BadRequestException } from '@nestjs/common';

export const multerExcelOptions: MulterOptions = {
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB TODO read from setting
  },
  fileFilter: (req, file, cb: (error: Error | null, acceptFile: boolean) => void) => {
    const allowedMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'application/vnd.ms-excel.sheet.macroEnabled.12', // .xlsm
      'application/vnd.ms-excel.sheet.macroenabled.12', // .xlsm
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      return cb(new BadRequestException('Only Excel files (.xlsx, .xls, .xlsm) are allowed'), false);
    }
    cb(null, true);
  },
};
