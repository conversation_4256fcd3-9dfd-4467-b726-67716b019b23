import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';
import { AS_AT_JUNE_30TH_BAND } from '@app/shared/constants/enrolment-services-contants';

export function IsValidBand(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'IsValidBand',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (value?.toUpperCase() === AS_AT_JUNE_30TH_BAND) {
            return true;
          }
          const year = typeof value === 'number' ? value : parseInt(value, 10);
          return year >= 2004;
        },
        defaultMessage(args: ValidationArguments) {
          return `'${args.value}' is not a valid band year. Must be a year >= 2004 or '${AS_AT_JUNE_30TH_BAND}'`;
        },
      },
    });
  };
}
