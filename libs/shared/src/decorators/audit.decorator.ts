/* eslint-disable */
import { AuditEventTypeEnum } from '../enums/AuditEventTypeEnum';

export const AUDIT_EVENT_KEY = 'auditEventType';
export const AUDIT_METADATA_EXTRACTOR_KEY = 'auditMetadataExtractor';

export type AuditMetadataExtractor = (
  req: any,
  params: any
) => {
  extra?: Record<string, any>;
};

export const Audit = (eventType: AuditEventTypeEnum, metadataExtractor?: AuditMetadataExtractor) => {
  return (target: any, key?: string, descriptor?: PropertyDescriptor) => {
    Reflect.defineMetadata(AUDIT_EVENT_KEY, eventType, descriptor.value);
    if (metadataExtractor) {
      Reflect.defineMetadata(AUDIT_METADATA_EXTRACTOR_KEY, metadataExtractor, descriptor.value);
    }
    return descriptor;
  };
};
