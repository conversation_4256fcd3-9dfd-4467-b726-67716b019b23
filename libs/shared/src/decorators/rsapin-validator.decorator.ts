import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

export function RsaPinValidator(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'ConditionalLength',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') return false;
          if (value.startsWith('PEN')) {
            return value.length === 15;
          } else if (value.startsWith('TPEN')) {
            return value.length === 15;
          }
          return false;
        },
        defaultMessage(args: ValidationArguments) {
          const value = args.value;
          if (typeof value !== 'string') return `RSA Pin must be a string`;
          if (value.startsWith('PEN')) {
            return `RSA Pin must be exactly 15 characters when starting with PEN`;
          } else if (value.startsWith('TPEN')) {
            return `RSA Pin must be exactly 15 characters when starting with TPEN`;
          }
          return `RSA Pin must start with PEN or TPEN`;
        },
      },
    });
  };
}
