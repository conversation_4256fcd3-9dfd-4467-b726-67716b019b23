/* eslint-disable */
import { registerDecorator, ValidationOptions } from 'class-validator';
import { ConfigService } from '@nestjs/config';

export function IsValidClientTime(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    const configService = new ConfigService(); // Initialize ConfigService
    const toleranceMinutes = parseInt(configService.get('CLIENT_TIME_TOLERANCE_MINUTES', '15'), 10);
    const fiveMinutesInMs = toleranceMinutes * 60 * 1000;

    registerDecorator({
      name: 'IsValidClientTime',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          if (!value) return false;

          const clientDate = new Date(value);
          if (isNaN(clientDate.getTime())) return false; // Ensure valid date

          const serverDate = new Date();
          console.log(`clientTime: ${value}, serverDate: ${serverDate}`);

          const timeDifference = Math.abs(serverDate.getTime() - clientDate.getTime());
          return timeDifference <= fiveMinutesInMs;
        },
        defaultMessage() {
          return `clientTime must be within ±${Math.floor(fiveMinutesInMs / 60000)} minutes of the server time`;
        },
      },
    });
  };
}
