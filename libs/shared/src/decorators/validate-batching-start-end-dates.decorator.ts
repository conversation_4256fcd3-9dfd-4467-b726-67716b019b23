import { ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';
import { isAfter, isValid, parse } from 'date-fns';

@ValidatorConstraint({ name: 'DateRangeValidator', async: false })
export class DateRangeValidator implements ValidatorConstraintInterface {
  validate(_: any, args: ValidationArguments): boolean {
    const obj = args.object as any;

    const parseDate = (dateStr: string) => parse(dateStr, 'dd-MM-yyyy', new Date());

    const hasEnrollmentDates = obj.enrollmentStartDate && obj.enrollmentEndDate;
    const hasRetirementDates = obj.retirementStartDate && obj.retirementEndDate;

    // Rule 3: Must provide at least one valid date pair
    if (!hasEnrollmentDates && !hasRetirementDates) {
      return false;
    }

    // Rule 1: If one enrollment date is provided, both must be present, valid, and in correct order
    if (obj.enrollmentStartDate || obj.enrollmentEndDate) {
      if (!hasEnrollmentDates) return false;
      const start = parseDate(obj.enrollmentStartDate);
      const end = parseDate(obj.enrollmentEndDate);
      if (!isValid(start) || !isValid(end)) return false;
      if (!isAfter(end, start) && end.getTime() !== start.getTime()) return false;
    }

    // Rule 2: If one retirement date is provided, both must be present, valid, and in correct order
    if (obj.retirementStartDate || obj.retirementEndDate) {
      if (!hasRetirementDates) return false;
      const start = parseDate(obj.retirementStartDate);
      const end = parseDate(obj.retirementEndDate);
      if (!isValid(start) || !isValid(end)) return false;
      if (!isAfter(end, start) && end.getTime() !== start.getTime()) return false;
    }

    return true;
  }

  defaultMessage(args: ValidationArguments): string {
    return 'You must provide a valid pair of either (Enrollment Start Date and Enrollment End Date) or (Retirement Start Date and Retirement End Date), and end dates must be after start dates.';
  }
}
