/* eslint-disable */
import { ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';

@ValidatorConstraint({ name: 'isRecordOfStrings', async: false })
export class IsRecordOfStrings implements ValidatorConstraintInterface {
  validate(placeholders: Record<string, any>, args: ValidationArguments) {
    return typeof placeholders === 'object' && Object.values(placeholders).every((value) => typeof value === 'string');
  }

  defaultMessage(_: ValidationArguments) {
    return 'placeholders must be an object where all values are strings';
  }
}
