import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

export function RequireAllLeaveFields(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'RequireAllLeaveFields',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(_: any, args: ValidationArguments) {
          const obj = args.object as any;
          const isNotEmpty = (value: any) => value !== undefined && value !== null && value !== '';

          const hasAny = isNotEmpty(obj.leaveType) || isNotEmpty(obj.leaveStartDate) || isNotEmpty(obj.leaveEndDate);
          const hasAll = isNotEmpty(obj.leaveType) && isNotEmpty(obj.leaveStartDate) && isNotEmpty(obj.leaveEndDate);

          return !hasAny || hasAll; // If one is set, all must be set
        },
        defaultMessage(): string {
          return 'If one of leaveType, leaveStartDate, or leaveEndDate is provided, then all three must be provided.';
        },
      },
    });
  };
}
