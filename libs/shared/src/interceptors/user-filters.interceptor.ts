import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';

export const AppendUserFilters = createParamDecorator((data: string[], ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();

  if (!request.body.filters) {
    request.body.filters = {};
  }

  if (request.user?.userType === UserTypeEnum.MDA) {
    request.body.filters.mdaCode = request.user.mdaCode;
  }

  if (request.user?.userType === UserTypeEnum.PFA) {
    request.body.filters.pfaCode = request.user.pfaCode;
  }

  if (data?.includes('userType') && request.user?.userType === UserTypeEnum.MDA) {
    request.body.filters.userType = UserTypeEnum.MDA;
  }

  if (data?.includes('userType') && request.user?.userType === UserTypeEnum.PFA) {
    request.body.filters.userType = UserTypeEnum.PFA;
  }

  return request.body;
});
