import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { format } from 'date-fns';

@Injectable()
export class BufferToBase64Interceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(map((data) => this.convertBuffersToBase64(data)));
  }

  private convertBuffersToBase64(obj: any): any {
    if (!obj || typeof obj !== 'object') return obj;

    if (Buffer.isBuffer(obj)) {
      return obj.toString('base64');
    }

    // ✅ Exclude Date objects from processing
    if (obj instanceof Date) {
      return format(obj, 'dd-MM-yyyy HH:mm:ss');
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.convertBuffersToBase64(item));
    }

    return Object.keys(obj).reduce((acc, key) => {
      acc[key] = this.convertBuffersToBase64(obj[key]);
      return acc;
    }, {} as any);
  }
}
