import { plainToInstance } from 'class-transformer';
import { validate, ValidationError } from 'class-validator';
import { EnrolmentBiodata } from '@app/shared/enrollment-service/entities/enrolment-biodata.entity';
import { EmploymentDocument } from '@app/shared/enrollment-service/entities/employment-document.entity';
import { EmploymentDetail } from '@app/shared/enrollment-service/entities/employment-detail.entity';
import { HYPHEN_DATE_FORMAT, ISO_HYPHEN_DATE_FORMAT } from '@app/shared/constants';
import { convertDateFormatWithoutParsing } from '@app/shared/utils/input-field-validator-utils';
import { parseISO } from 'date-fns';

export class WorkflowUtils {}
export function mapToEnrolmentBiodata(input: Record<string, any>): EnrolmentBiodata {
  const enrolmentBiodata = new EnrolmentBiodata({});
  enrolmentBiodata.rsaPin = input.rsaPin;
  enrolmentBiodata.nin = input.nin;
  enrolmentBiodata.pfaCode = input.pfaCode;
  enrolmentBiodata.pfaName = input.pfaName;
  enrolmentBiodata.employerCode = input.employerCode;
  enrolmentBiodata.employerName = input.employerName;
  enrolmentBiodata.firstName = input.firstName;
  enrolmentBiodata.surname = input.surname;
  enrolmentBiodata.otherName = input.otherName;
  enrolmentBiodata.gender = input.gender;
  enrolmentBiodata.contactAddress = input.contactAddress;
  enrolmentBiodata.phoneNumber = input.phoneNumber;
  enrolmentBiodata.alternatePhoneNumber = input.alternatePhoneNumber;
  enrolmentBiodata.retireeUserType = input.retireeUserType;
  enrolmentBiodata.emailAddress = input.email;
  enrolmentBiodata.modeOfRetirement = input.modeOfRetirement;
  enrolmentBiodata.reasonForExiting = input.reasonForVoluntaryRetirement;
  enrolmentBiodata.numberOfEmployment = input.numberOfEmployment;
  enrolmentBiodata.criticalHealthChallenge = input.criticalHealthChallenge;
  enrolmentBiodata.alreadyPaid = input.alreadyPaid;
  enrolmentBiodata.staffId = input.staffId;
  enrolmentBiodata.portraitOverrideReason = input.overrideVerificationReason;
  enrolmentBiodata.portraitOverride = input.overrideVerification;

  enrolmentBiodata.dateOfDeath = convertHypenToIsoDate(input.dateOfDeath, HYPHEN_DATE_FORMAT);
  enrolmentBiodata.dateOfBirth = convertHypenToIsoDate(input.dateOfBirth, HYPHEN_DATE_FORMAT);
  enrolmentBiodata.dateOfFirstAppointment = convertHypenToIsoDate(input.dofa, HYPHEN_DATE_FORMAT);
  enrolmentBiodata.dateOfTransferService = convertHypenToIsoDate(input.dts, HYPHEN_DATE_FORMAT);
  enrolmentBiodata.dateOfRetirement = convertHypenToIsoDate(input.edor, HYPHEN_DATE_FORMAT);

  if (input.pdoDetails) {
    enrolmentBiodata.pdoUserId = input.pdoDetails.userId;
    enrolmentBiodata.pdoFirstName = input.pdoDetails.firstName;
    enrolmentBiodata.pdoSurname = input.pdoDetails.surname;
    enrolmentBiodata.pdoEmailAddress = input.pdoDetails.emailAddress;
  }

  if (input.nokDetails) {
    enrolmentBiodata.nokFirstName = input.nokDetails.firstName;
    enrolmentBiodata.nokFirstName = input.nokDetails.surname;
    enrolmentBiodata.nokEmailAddress = input.nokDetails.emailAddress;
    enrolmentBiodata.nokPhoneNumber = input.nokDetails.phoneNumber;
  }

  const employmentDocument = new EmploymentDocument({});
  employmentDocument.supportingDocument = decodeBase64(input.supportingDocument);
  employmentDocument.letterOfFirstAppointment = decodeBase64(input.letterOfFirstAppointment);
  employmentDocument.letterOfIntroduction = decodeBase64(input.letterOfIntroduction);
  employmentDocument.medicalReport = decodeBase64(input.medicalReport);
  employmentDocument.recordOfService = decodeBase64(input.recordOfService);
  employmentDocument.portrait = decodeBase64(input.portrait);
  employmentDocument.signature = decodeBase64(input.signature);

  enrolmentBiodata.documents = employmentDocument;
  employmentDocument.retiree = enrolmentBiodata;

  enrolmentBiodata.employmentDetails = input.otherEmploymentDetails.map((detail: any) => {
    const employmentDetail = new EmploymentDetail({});
    employmentDetail.employmentYear = detail.year;
    employmentDetail.effectivePromotionYear = convertHypenToIsoDate(detail.promotionYear, HYPHEN_DATE_FORMAT);
    employmentDetail.sector = detail.organizationSector;
    employmentDetail.employerCode = detail.employerCode;
    employmentDetail.employerName = detail.employerName;
    employmentDetail.designation = detail.designation;
    employmentDetail.staffId = detail.staffId;
    employmentDetail.salaryStructure = detail.salaryStructure;
    employmentDetail.gradeLevel = detail.gradeLevel;
    employmentDetail.step = detail.step;
    employmentDetail.leaveType = detail.leaveType || null;
    employmentDetail.leaveStartDate = convertHypenToIsoDate(detail.leaveStartDate, HYPHEN_DATE_FORMAT);
    employmentDetail.leaveEndDate = convertHypenToIsoDate(detail.leaveEndDate, HYPHEN_DATE_FORMAT);
    employmentDetail.ippisDate = convertHypenToIsoDate(detail.ippisDate, HYPHEN_DATE_FORMAT);
    employmentDetail.enrolmentBiodata = enrolmentBiodata;
    return employmentDetail;
  });

  return enrolmentBiodata;
}

export async function validateFormData<T extends object>(
  formData: Record<string, any>,
  dtoClass: new () => T
): Promise<{ dto: T | null; errors: Record<string, string[]> | null }> {
  const dto = plainToInstance(dtoClass, formData, {
    enableImplicitConversion: true, // ✅ Ensures automatic type conversion
    exposeDefaultValues: true, // ✅ Ensures default values in DTOs are respected
  });

  if (!dto) {
    return { dto: null, errors: { general: ['Invalid form data input provided'] } };
  }

  const validationErrors = await validate(dto);
  if (validationErrors.length > 0) {
    const errors = flattenValidationErrors(validationErrors);
    return { dto: null, errors };
  }

  return { dto, errors: null };
}

function flattenValidationErrors(errors: ValidationError[]): Record<string, string[]> {
  const formattedErrors: Record<string, string[]> = {};

  const extractErrors = (error: ValidationError, parentKey = '') => {
    const key = parentKey ? `${parentKey}.${error.property}` : error.property;

    if (error.constraints) {
      formattedErrors[key] = Object.values(error.constraints);
    }

    if (error.children?.length) {
      for (const child of error.children) {
        extractErrors(child, key);
      }
    }
  };

  for (const error of errors) {
    extractErrors(error);
  }

  return formattedErrors;
}

export function decodeBase64(base64Str: string): Buffer | null {
  return base64Str ? Buffer.from(base64Str, 'base64') : null;
}

export const convertHypenToIsoDate = (dateString: string, formatString: string): Date | null => {
  if (!dateString?.trim() || !formatString) {
    console.log('dts', dateString, 'formatString', formatString);
    return null;
  }

  let finalDateString = dateString;
  if (formatString !== ISO_HYPHEN_DATE_FORMAT) {
    finalDateString = convertDateFormatWithoutParsing(dateString, formatString, ISO_HYPHEN_DATE_FORMAT);
  }

  return parseISO(finalDateString);
};

export const calculateRecordAmount = (record: EnrolmentBiodata, type: string): number => {
  if (type === 'Accrued Rights') {
    return parseFloat(record.accruedRightsAmount || '0');
  } else if (type === 'Contributions') {
    return parseFloat(record.contributionAmount || '0');
  }
  return 0;
};

export const calculateTotalContributionBatchAmount = (records): { refunds: number; contributions: number } => {
  return records.reduce(
    (totals, record) => {
      const amount = parseFloat(record.totalPensionPlusInterest || '0');

      if (amount < 0) {
        totals.refunds += amount;
      } else {
        totals.contributions += amount;
      }
      return totals;
    },
    { refunds: 0, contributions: 0 }
  );
};
