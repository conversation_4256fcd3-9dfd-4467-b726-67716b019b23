import { Inject, Injectable } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { PinoLogger } from 'nestjs-pino';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { SettingsEnumKey } from '@app/shared/enums';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { SendNotificationTemplateDto } from '@app/shared/dto/notification/send-notification-template.dto';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { firstValueFrom } from 'rxjs';
import { NotificationServiceClientConstant } from '@app/shared/constants';

@Injectable()
export class BaseWorkflowService {
  constructor(
    @Inject('NOTIFICATION_SERVICE_CLIENT') protected readonly notificationClient: ClientProxy,
    protected readonly cobraUserRepository: CobraUserRepository,
    protected readonly settingMsService: SettingMsLibService,
    protected readonly logger: PinoLogger
  ) {}

  async sendBulkNotification(
    role: SettingsEnumKey,
    placeholders: Record<string, any>,
    notificationType: NotificatonTypeEnum
  ): Promise<void> {
    try {
      // Get Pencom role from settings
      const roleString = await this.settingMsService.getSetting(role);
      if (!roleString) {
        this.logger.warn('Pencom role setting not found');
        return;
      }

      const actorRoles = roleString.split(',').map((role) => role.trim());
      const eligibleValidators = await this.cobraUserRepository.getUsersByRoleNameAndUserType(
        actorRoles,
        UserTypeEnum.PENCOM
      );

      if (!eligibleValidators || eligibleValidators.length === 0) {
        this.logger.error(`No users found with role name: ${roleString}`);
        return;
      }

      // Send notification to each Pencom user
      const notificationPromises = eligibleValidators.map((user) => {
        placeholders['username'] = user.firstName;
        const sendNotificationTemplateDto = new SendNotificationTemplateDto();
        sendNotificationTemplateDto.emailRecipients = [user.emailAddress];
        sendNotificationTemplateDto.notificationType = notificationType;
        sendNotificationTemplateDto.placeholders = placeholders;

        return firstValueFrom(
          this.notificationClient.send(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            sendNotificationTemplateDto
          )
        ).catch((error) => {
          this.logger.error(
            `Failed to send notification to Pencom user ${user.emailAddress}: ${error instanceof Error ? error.message : error}`
          );
        });
      });

      await Promise.all(notificationPromises);
      this.logger.info(`Successfully sent notifications to ${eligibleValidators.length} Pencom users`);
    } catch (error) {
      this.logger.error(`Failed to process Pencom notifications : ${error instanceof Error ? error.message : error}`);
    }
  }

  async sendBulkRecipientNotification(
    emailRecipients: string[],
    placeholders: Record<string, any>,
    notificationType: NotificatonTypeEnum,
    attachments?: { filename: string; content: Buffer; contentType: string }[]
  ): Promise<void> {
    const sendNotificationTemplateDto = new SendNotificationTemplateDto();
    sendNotificationTemplateDto.emailRecipients = emailRecipients;
    sendNotificationTemplateDto.notificationType = notificationType;
    sendNotificationTemplateDto.placeholders = placeholders;
    sendNotificationTemplateDto.attachments = attachments;

    try {
      await firstValueFrom(
        this.notificationClient.send(
          NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
          sendNotificationTemplateDto
        )
      );
    } catch (error) {
      console.log(error);
      this.logger.error({ err: error }, 'Failed to send sendBulkRecipientNotification');
    }
  }
}
