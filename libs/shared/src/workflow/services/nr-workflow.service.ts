import { Injectable } from '@nestjs/common';
import { createActor, Snapshot, transition } from 'xstate';
import { WorkflowStateRepository } from '@app/shared/workflow/repository/workflow-state.repository';
import { WorkflowState } from '@app/shared/workflow/entities/workflow-state.entity';
import { Actor } from 'xstate/dist/declarations/src/createActor';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ResponseCodeEnum, SettingsEnumKey } from '@app/shared/enums';
import { PinoLogger } from 'nestjs-pino';
import { ProcessTypeEnum } from '@app/shared/workflow/enums/ProcessTypeEnum';
import { QueryRunner } from 'typeorm';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import {
  machine,
  NominalRollMachineEvent,
  NrActiveSnapshot,
} from '@app/shared/workflow/workflow/norminal-roll-contribution.workflow';
import { NominalRollWorkflowService } from '@app/shared/workflow/services/nominal-roll-workflow.service';
import { NominalRollStatusEnum } from '@app/shared/enums/nominal-roll-status.enum';
import { NominalRollBatchRepository } from '@app/shared/enrollment-service/repository/nominal-roll-batch.repository';
import { TbcNominalRollBatch } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch';
import { TbcNominalRollBatchRecord } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch-record';
import { TbcNominalRollBatchRecordRepository } from '@app/shared/enrollment-service/repository/tbc-nominal-roll-batch-record.repository';
import { calculateTotalContributionBatchAmount } from '@app/shared/workflow/utils/workflow-utils';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { TbcBatchNrAccountMemoDetailsRepository } from '@app/shared/enrollment-service/repository/tbc-batch-nr-account-memo-details.repository';
import { WorkflowHelperService } from '@app/shared/workflow/services/workflow-helper.service';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { TbcNominalRollPfaMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-nominal-roll-pfa-memo-document-repository.service';
import { TbcNominalRollPfcMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-nominal-roll-pfc-memo-document-repository.service';
import { PfcRepository } from '@app/shared/user-service/repositories/pfc.repository';
import { CronJobNamesConstant, EnrollmentEngineServiceClientConstant } from '@app/shared/constants';
import { ClientProxy } from '@nestjs/microservices';
import { RabbitMqClient } from '@app/shared/rabbitmq/rabbit-mq-client';
import { StampScheduleDto } from '@app/shared/dto/enrollment/exit-batching/batch-filter.dto';
import { MemoService } from '@app/shared/enrollment-service/services/memo.service';

@Injectable()
export class NrWorkflowService {
  private rmqClient: ClientProxy;
  constructor(
    private readonly workflowStateRepository: WorkflowStateRepository,
    private readonly nominalRollWorkflowService: NominalRollWorkflowService,
    private readonly nominalRollBatchRepository: NominalRollBatchRepository,
    private readonly tbcNominalRollBatchRecordRepository: TbcNominalRollBatchRecordRepository,
    private readonly tbcBatchNrAccountMemoDetailsRepository: TbcBatchNrAccountMemoDetailsRepository,
    private readonly tbcNominalRollPfaMemoDetailRepository: TbcNominalRollPfaMemoDocumentRepository,
    private readonly tbcNominalRollPfcMemoDocumentRepository: TbcNominalRollPfcMemoDocumentRepository,
    private readonly pfaRepository: PfaRepository,
    private readonly pfcRepository: PfcRepository,
    private readonly logger: PinoLogger,
    private readonly workflowHelperService: WorkflowHelperService,
    private redisService: RedisCacheService,
    private readonly rabbitMqClient: RabbitMqClient,
    private readonly memoService: MemoService
  ) {
    this.rmqClient = this.rabbitMqClient.getClient();
  }

  async validateBatchTransitions(
    rsaPins: string[],
    event: NominalRollMachineEvent,
    processType: ProcessTypeEnum
  ): Promise<{ rsaPin: string; valid: boolean; reason?: string }[]> {
    const results: { rsaPin: string; valid: boolean; reason?: string }[] = [];

    for (const rsaPin of rsaPins) {
      try {
        // Get actor and workflow state
        event.options.rsaPin = rsaPin;
        event.options.nominalRollWorkflowService = this.nominalRollWorkflowService;
        const { actor } = await this.getActor(rsaPin, processType, event.options);
        const currentSnapshot = actor.getSnapshot();
        const nextState = transition(machine, currentSnapshot, event);

        const isValid = nextState[0] !== currentSnapshot;
        if (!isValid) {
          results.push({
            rsaPin,
            valid: false,
            reason: `Invalid event: ${event.type} for current state: ${this.getRelevantState(currentSnapshot.value, event.type)}`,
          });
        } else {
          results.push({ rsaPin, valid: true });
        }

        actor.stop(); // stop actor after use
      } catch (err) {
        this.logger.error(`Validation failed for ${rsaPin}: ${err}`);
        results.push({
          rsaPin,
          valid: false,
          reason: `Unexpected error during transition validation: ${err instanceof Error ? err.message : String(err)}`,
        });
      }
    }

    return results;
  }

  /**
   * Process an event for a given RSA PIN.
   */
  async processEvent(
    event: NominalRollMachineEvent,
    rsaPin: string,
    actor: Actor<any>,
    workflowState: WorkflowState
  ): Promise<BaseResponseDto> {
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
    const FINITE_STATES = new Set(Object.values(NominalRollStatusEnum));

    try {
      console.log('about to send NR event:::: ', event.type);
      actor.send(event);
      const state = await new Promise<Snapshot<unknown>>((resolve, reject) => {
        const subscription = actor.subscribe({
          next: async (snapshot) => {
            console.log('snapshot.value ::::: ', snapshot.value);
            const snapshotValue = snapshot.value;
            let relevantState = this.getRelevantState(snapshotValue, event.type);
            console.log('relevantState ::::: ', relevantState);

            if (relevantState && FINITE_STATES.has(relevantState as NominalRollStatusEnum)) {
              await this.updateWorkflowState(actor, workflowState, event);
              subscription.unsubscribe();
              resolve(snapshot);
            }
          },
          error: (err) => {
            console.error('Error in actor:', err);
            reject(err);
          },
        });
      });

      const output = (state as any).context?.output;
      if (output) {
        response.setResponseCode(output.code);
        response.setDescription(output.description);
      } else {
        response.setDescription('No output from workflow.');
      }

      return response;
    } catch (error) {
      this.logger.error(`Error processing event ${event.type} for record ${rsaPin}:`, error);
      response.setDescription(`Error processing event ${event.type} for record ${rsaPin}`);
      return response;
    }
  }

  async processBatchEvent(
    rsaPins: string[],
    event: NominalRollMachineEvent,
    processType: ProcessTypeEnum
  ): Promise<BaseResponseWithContentNoPagination<any>> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const action = event.type;
    const queryRunner: QueryRunner = event.options.queryRunner;

    if ('CR_BATCHING' === action) {
      const crBatchResult = await this.createNominalRollBatch(event.options.batchName, queryRunner);
      console.log('crBatchResult:::: ', crBatchResult);
      if (crBatchResult.code !== ResponseCodeEnum.SUCCESS) {
        response.setDescription(`Batch creation failed: ${crBatchResult.description}`);
        return response;
      }
    } else if ('CR_DELETE_BATCH' === action) {
      const crBatchResult = await this.deleteBatch(event.options.batchName, queryRunner);
      if (crBatchResult.code !== ResponseCodeEnum.SUCCESS) {
        response.setDescription(`Batch deletion failed: ${crBatchResult.description}`);
        return response;
      }
    } else if (['CR_EXCO_PAYMENT_APPROVAL'].includes(action)) {
      const stampScheduleDto = new StampScheduleDto();
      stampScheduleDto.batchName = event.options.batchName;
      stampScheduleDto.actorEmailAddress = event.options.actorEmailAddress;
      stampScheduleDto.stampType = 'EXCO_MEMO';
      stampScheduleDto.processType = 'NOMINAL_ROLL';
      const result = await this.memoService.stampDocument(stampScheduleDto, queryRunner);
      if (result.code !== ResponseCodeEnum.SUCCESS) {
        response.setDescription(`Unable to process request: ${result.description}`);
        return response;
      }
    }

    try {
      let atLeast1Failed = false;
      let relevantState;
      for (const rsaPin of rsaPins) {
        event.options.rsaPin = rsaPin;
        event.options.nominalRollWorkflowService = this.nominalRollWorkflowService;
        const { actor, workflowState } = await this.getActor(rsaPin, processType, event.options);

        const result = await this.processEvent(event, rsaPin, actor, workflowState);
        console.log('NR- processEvent result:::: ', result);
        if (result.code !== ResponseCodeEnum.SUCCESS) {
          atLeast1Failed = true;
          if (!Array.isArray(response.content)) {
            response.content = [];
          }
          (response.content as string[]).push(`Failed for rsaPin ${rsaPin}: ${result.description}`);
        } else if (!relevantState) {
          relevantState = this.getRelevantState(actor.getSnapshot().value, event.type);
          console.log('NR- relevantState:::: ', relevantState);
          console.log('NR- actor.getSnapshot():::: ', actor.getSnapshot().value);
        }

        if (result.code === ResponseCodeEnum.SUCCESS && event.type === 'REQUEST_COMPUTATION') {
          const cacheKey = `${CronJobNamesConstant.NOMINAL_ROLL_CONTRIBUTION_JOB}:${rsaPin}`;
          // Check if the record exists in Redis cache
          const exists = await this.redisService.get(cacheKey);
          if (!exists) {
            // put in queue immediately after approval
            await this.rmqClient.emit(EnrollmentEngineServiceClientConstant.NOMINAL_ROLL_CONTRIBUTION_JOB, {
              rsaPin: rsaPin,
            });
            // Store in Redis to prevent re-processing
            await this.redisService.set(cacheKey, 'processing');
          }
        }
        actor.stop(); // Stop actor when done
      }

      if (atLeast1Failed) {
        response.setResponseCode(ResponseCodeEnum.ERROR);
        await queryRunner.rollbackTransaction();
      } else {
        if ('CR_NOTIFY_PFA_PFC' === action) {
          await this.nominalRollBatchRepository.findOneAndUpdate(
            { batchName: event.options.batchName },
            { status: relevantState, pfaNotificationDate: new Date() },
            queryRunner
          );
        } else {
          if (!['CR_DELETE_BATCH'].includes(action) && event.options.batchName) {
            await this.nominalRollBatchRepository.findOneAndUpdate(
              { batchName: event.options.batchName },
              { status: relevantState },
              queryRunner
            );
          }
        }

        await queryRunner.commitTransaction();
        response.setResponseCode(ResponseCodeEnum.SUCCESS);
        response.setDescription('Request was successfully processed.');
        await this.sendNotification(event);
      }
    } catch (err) {
      this.logger.error(`Batch transition failed. Rolling back. Reason: ${err}`);
      await queryRunner?.rollbackTransaction();
      response.setDescription(`Batch failed: ${err.message}`);
    }

    return response;
  }

  async sendNotification(event: NominalRollMachineEvent) {
    const actionMap: Record<
      string,
      {
        role: SettingsEnumKey;
        type: NotificatonTypeEnum;
        getPlaceholders?: (event: NominalRollMachineEvent) => Record<string, string>;
      }
    > = {
      CR_REQUEST_EXCO_APPROVAL: {
        role: SettingsEnumKey.PENCOM_EXCO_ROLE_NAME,
        type: NotificatonTypeEnum.EXCO_APPROVAL_REQUEST,
      },
      CR_EXCO_PAYMENT_APPROVAL: {
        role: SettingsEnumKey.HOD_ROLE_NAME,
        type: NotificatonTypeEnum.EXCO_APPROVAL_GRANTED,
      },
      CR_REQUEST_ACCOUNT_PAYMENT: {
        role: SettingsEnumKey.ACCOUNT_ROLE_NAME,
        type: NotificatonTypeEnum.ACCOUNT_PAYMENT_REQUESTED,
      },
      CR_PAYMENT_REMITTED: {
        role: SettingsEnumKey.HOD_ROLE_NAME,
        type: NotificatonTypeEnum.ACCOUNT_PAYMENT_CONFIRMED,
      },
      CR_CONFIRM_PAYMENT: {
        role: SettingsEnumKey.HOD_ROLE_NAME,
        type: NotificatonTypeEnum.PFA_PAYMENT_CONFIRMED,
      },
    };

    const config = actionMap[event.type];

    if (config) {
      const placeholders = config.getPlaceholders?.(event) ?? { batchId: event.options.batchName };

      await this.nominalRollWorkflowService.sendBulkNotification(config.role, placeholders, config.type);
    } else if (event.type === 'CR_NOTIFY_PFA_PFC') {
      const placeholders = {
        batchId: event.options.batchName,
        paymentType: 'NOMINAL ROLL',
      };

      const pfaDocuments = await this.tbcNominalRollPfaMemoDetailRepository.findBy({
        batch: { batchName: event.options.batchName },
      });

      if (!pfaDocuments || pfaDocuments.length === 0) {
        this.logger.warn(`No account memo details found for batch ${event.options.batchName}`);
        return;
      }

      const emailAttachment: any[] = [];
      // email to PFAs
      for (const pfaDocument of pfaDocuments) {
        const pfaDetail = await this.pfaRepository.getCachedPfaDetails(pfaDocument.pfaCode);
        if (!pfaDetail.emailAddress) {
          this.logger.warn(`Pfa email has not been configured for ${pfaDocument.pfaCode}`);
          continue;
        }
        placeholders['username'] = pfaDocument.pfaName;
        const emailRecipients = [pfaDetail.emailAddress];
        await this.nominalRollWorkflowService.sendBulkRecipientNotification(
          emailRecipients,
          placeholders,
          NotificatonTypeEnum.PFA_PAYMENT_MADE
        );

        let pfcEntry = emailAttachment.find((e) => e.pfcCode === pfaDocument.pfcCode);
        if (!pfcEntry) {
          pfcEntry = {
            pfcCode: pfaDocument.pfcCode,
            attachments: [],
          };
          emailAttachment.push(pfcEntry);
        }

        // Add this PFA's memo and schedule as a grouped attachment
        pfcEntry.attachments.push({
          pfaCode: pfaDocument.pfaCode,
          schedule: pfaDocument.pfaSchedule,
        });
      }

      // email to PFCs
      const pfcMemoDocuments = await this.tbcNominalRollPfcMemoDocumentRepository.findBy({
        batch: { batchName: event.options.batchName },
      });

      for (const pfcMemo of pfcMemoDocuments) {
        const pfcEntry = emailAttachment.find((entry) => entry.pfcCode === pfcMemo.pfcCode);
        if (!pfcEntry) {
          continue;
        }
        const pfcDetail = await this.pfcRepository.findOne({ pfcCode: pfcMemo.pfcCode });
        if (!pfcDetail?.emailAddress) {
          continue;
        }

        placeholders['username'] = pfcMemo.pfcName;
        const emailRecipients = [pfcDetail.emailAddress];
        pfcEntry.attachments.unshift({ pfcMemo: pfcMemo.pfcMemo }); // use unshift to place it before PFA-specific docs

        const attachments: { filename: string; content: Buffer; contentType: string }[] = [];
        for (const doc of pfcEntry.attachments) {
          if (doc.pfcMemo) {
            attachments.push({
              filename: `memo-${pfcMemo.pfcCode}.pdf`,
              content: doc.pfcMemo,
              contentType: 'application/pdf',
            });
          }

          if (doc.pfaCode && doc.schedule) {
            attachments.push({
              filename: `schedule-${doc.pfaCode}.pdf`,
              content: doc.schedule,
              contentType: 'application/pdf',
            });
          }
        }

        await this.nominalRollWorkflowService.sendBulkRecipientNotification(
          emailRecipients,
          placeholders,
          NotificatonTypeEnum.PFC_PAYMENT_MADE,
          attachments
        );
      }
    }
  }

  /**
   * Get an actor, either a new one or a rehydrated one.
   */
  private async getActor(
    rsaPin: string,
    processType: ProcessTypeEnum,
    options: Record<string, any>
  ): Promise<{ actor: Actor<any>; workflowState: WorkflowState }> {
    let workflowState = await this.workflowStateRepository.findOne({
      uniqueId: rsaPin,
      processType,
    });

    let actor;
    if (!workflowState) {
      console.log(`No existing workflow found for recordId ${rsaPin}. Creating new workflow...`);
      // Create a new actor
      actor = createActor(
        machine.provide({
          // No context here
        }),
        {
          input: {
            rsaPin,
            options,
          },
        }
      );

      const initialState = actor.getSnapshot();

      // Remove nominalRollWorkflowService before persisting
      const cleanState = this.removeNominalRollWorkflowService(initialState);

      workflowState = new WorkflowState({
        uniqueId: rsaPin,
        currentState: JSON.stringify(cleanState),
        eventLog: JSON.stringify([]),
        processType,
      });

      await this.workflowStateRepository.saveEntity(workflowState);
    } else {
      // Rehydrate the state from the database
      const restoredState: Snapshot<unknown> = JSON.parse(workflowState.currentState);

      // Add accruedBenefitsService back to the state
      const hydratedState = this.addNominalRollWorkflowService(restoredState, options);

      actor = createActor(
        machine.provide({
          // No context here
        }),
        {
          input: {
            rsaPin,
            options,
          },
          snapshot: hydratedState, // Restore previous state
        }
      );
    }
    actor.start();

    return { actor, workflowState };
  }

  /**
   * Update the workflow state based on the provided actor.
   */
  private async updateWorkflowState(
    actor: Actor<any>,
    workflowState: WorkflowState,
    event: NominalRollMachineEvent
  ): Promise<void> {
    const currentState = actor.getSnapshot();

    const queryRunner: QueryRunner = event.options.queryRunner;
    const cleanState = this.removeNominalRollWorkflowService(currentState);
    workflowState.currentState = JSON.stringify(cleanState); // Serialize the cleaned state

    const timestamp = new Date();
    const actorRole = event.options.actorRole || 'UNKNOWN_ROLE';
    const userId = event.options.actorEmailAddress;

    const persistEvent = this.sanitizeWorkflowEvent(event);
    const newEvent = { event: persistEvent, actorRole, userId, timestamp, currentState: cleanState };

    // Parse the existing event log or initialize an empty array
    const eventLog = JSON.parse(workflowState.eventLog || '[]');
    eventLog.push(newEvent);
    workflowState.eventLog = JSON.stringify(eventLog);

    await this.workflowStateRepository.saveEntity(workflowState, queryRunner);
    console.log(
      `NR-Event ${event.type} processed successfully for record ${workflowState.uniqueId}. New state: ${JSON.stringify(cleanState.value)}`
    );
  }

  sanitizeWorkflowEvent(event: NominalRollMachineEvent): NominalRollMachineEvent {
    if ('options' in event) {
      const { options, ...rest } = event;
      return rest as NominalRollMachineEvent;
    }
    return event;
  }

  /**
   * Remove accruedBenefitsService from the snapshot before persisting.
   */
  private removeNominalRollWorkflowService(snapshot: Snapshot<unknown>): any {
    if (snapshot.status !== 'active') {
      throw new Error('Cannot modify context of a non-active snapshot');
    }

    // Cast the snapshot to the "active" state type
    const activeSnapshot = snapshot as NrActiveSnapshot;

    // Ensure context is an object
    const cleanContext = { ...activeSnapshot.context };
    delete cleanContext.options.nominalRollWorkflowService; // Remove the nominalRollWorkflowService
    delete cleanContext.options.queryRunner; // Remove the queryRunner
    return {
      ...activeSnapshot,
      context: cleanContext,
    };
  }

  /**
   * Add accruedBenefitsService back to the snapshot when rehydrating.
   */
  private addNominalRollWorkflowService(snapshot: Snapshot<unknown>, options: Record<string, any>): any {
    if (snapshot.status !== 'active') {
      throw new Error('Cannot modify context of a non-active snapshot');
    }

    // Cast the snapshot to the "active" state type
    const activeSnapshot = snapshot as NrActiveSnapshot;

    return {
      ...activeSnapshot,
      context: {
        ...activeSnapshot.context, // Access context directly from snapshot
        options, // Add the service back
      },
    };
  }

  getRelevantState(snapshotValue, eventType: string) {
    const isParallel =
      typeof snapshotValue === 'object' && snapshotValue !== null && 'PARALLEL_PROCESSES' in snapshotValue;
    let relevantState: string | undefined;

    if (isParallel) {
      const processes = (snapshotValue as any).PARALLEL_PROCESSES;
      if (eventType.startsWith('AR_')) relevantState = processes.AR_PROCESS;
      else if (eventType.startsWith('CR_')) relevantState = processes.CR_PROCESS;
      else if (eventType === 'AUDIT_SUPERVISOR_APPROVAL') relevantState = processes.CR_PROCESS;
    } else if (typeof snapshotValue === 'string') {
      relevantState = snapshotValue;
    }
    return relevantState;
  }

  private async createNominalRollBatch(batchName: string, queryRunner?: QueryRunner) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const batchKey = `nr-cr-batch-filter:${batchName}`;
    const cachedRecords = await this.redisService.get(`${batchKey}`);
    if (!cachedRecords) {
      response.setDescription('Batch not found or expired, please try again');
      return response;
    }

    const savedBatch = await this.nominalRollBatchRepository.findOne({ batchName });
    if (savedBatch) {
      response.setDescription(`Batch with name ${batchName} already exists`);
      return response;
    }

    const batchData = JSON.parse(cachedRecords as string);
    const { records, filterDto } = batchData;

    try {
      const { refunds, contributions } = calculateTotalContributionBatchAmount(records);
      const batch = new TbcNominalRollBatch({
        batchName: batchName,
        totalCount: records.length,
        totalRefunds: refunds,
        totalContributions: contributions,
        uploadStartDate: filterDto.uploadStartDate ? new Date(filterDto.uploadStartDate) : null,
        uploadEndDate: filterDto.uploadEndDate ? new Date(filterDto.uploadEndDate) : null,
        mdaCode: filterDto.mdaCode,
        pfaCode: filterDto.pfaCode,
      });

      // Save the batch
      const savedBatch = await this.nominalRollBatchRepository.saveEntity(batch, queryRunner);

      // Create batch records
      const batchRecords = [];
      for (const record of records) {
        const totalPension = parseFloat(record.totalPensionPlusInterest || '0');
        const batchRecord = new TbcNominalRollBatchRecord({
          batch: savedBatch,
          nominalRoll: record,
          amount: `${totalPension}`,
          recordCategory: totalPension > 0 ? 'CONTRIBUTION' : 'REFUND',
        });
        batchRecords.push(batchRecord);
      }

      console.log('batchRecords:::: ', batchRecords);
      // Save all batch records at once for better performance
      await this.tbcNominalRollBatchRecordRepository.saveEntities(batchRecords, queryRunner);

      response.content = {
        success: true,
        message: `Batch '${batchName}' created successfully with ${records.length} records`,
      };
      response.setResponseCode(ResponseCodeEnum.SUCCESS);
      return response;
    } catch (error) {
      this.logger.error(
        `error occurred when saving Nominal roll Batch with name ${batchName}: ${error instanceof Error ? error.stack : error}`
      );
      response.content = { success: false, message: `Failed to create batch: ${error.message}` };
      return response;
    }
  }

  private async deleteBatch(batchName: any, queryRunner: QueryRunner) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const nominalRollBatch = await this.nominalRollBatchRepository.findOneWithRelations({ batchName }, ['records']);

    if (!nominalRollBatch) {
      response.setDescription(`Batch not found with the provided name ${batchName}`);
      return response;
    }

    await this.nominalRollBatchRepository.removeEntity(nominalRollBatch, queryRunner);
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    return response;
  }
}
