import { Inject, Injectable } from '@nestjs/common';
import { WorkflowStateRepository } from '@app/shared/workflow/repository/workflow-state.repository';
import { ClientProxy } from '@nestjs/microservices';
import { PinoLogger } from 'nestjs-pino';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { ResponseCodeEnum, SettingsEnumKey } from '@app/shared/enums';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { EnrolmentDraftRepository } from '@app/shared/enrollment-service/repository/enrolment-draft.repository';
import { FormValidatorService } from '@app/shared/enrollment-service/services/form-validator.service';
import { EnrolmentBiodataRepository } from '@app/shared/enrollment-service/repository/enrolment-biodata.repository';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { NominalRollRepository } from '@app/shared/enrollment-service/repository/nominal-roll.repository';
import { BaseWorkflowService } from '@app/shared/workflow/services/base-workflow.service';
import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { NominalRollStatusEnum } from '@app/shared/enums/nominal-roll-status.enum';
import { QueryRunner } from 'typeorm';
import { NominalRollComment } from '@app/shared/enrollment-service/entities/nominal-roll-comment.entity';
import { NominalRollCommentRepository } from '@app/shared/enrollment-service/repository/nominal-roll-comment.repository';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';

@Injectable()
export class NominalRollWorkflowService extends BaseWorkflowService {
  constructor(
    private readonly workflowStateRepository: WorkflowStateRepository,
    private readonly enrolmentSummaryRepository: EnrolmentSummaryRepository,
    private readonly enrolmentDraftRepository: EnrolmentDraftRepository,
    private readonly enrolmentBiodataRepository: EnrolmentBiodataRepository,
    private readonly nominalRollCommentRepository: NominalRollCommentRepository,
    private readonly formValidatorService: FormValidatorService,
    private readonly nominalRollRepository: NominalRollRepository,
    private readonly pfaRepository: PfaRepository,
    @Inject('NOTIFICATION_SERVICE_CLIENT') notificationClient: ClientProxy,
    cobraUserRepository: CobraUserRepository,
    settingsService: SettingMsLibService,
    logger: PinoLogger
  ) {
    super(notificationClient, cobraUserRepository, settingsService, logger);
  }

  /*
   * fetch supervisor role from settings
   * get all users with the supersor role
   * send email to all users with supervisor role requesting for approval
   * */
  async handleProcessing(
    rsaPin: string,
    options: Record<string, any>,
    targetState: string,
    triggeredBy: string
  ): Promise<BaseResponseWithContentNoPagination<any>> {
    let response: BaseResponseWithContentNoPagination<unknown> = new BaseResponseWithContentNoPagination<unknown>(
      ResponseCodeEnum.ERROR
    );

    let cobraActor;
    if (options.actorEmailAddress !== 'SYSTEM_COMPUTATION') {
      cobraActor = await this.cobraUserRepository.findOne({ emailAddress: options.actorEmailAddress });
      if (!cobraActor) {
        response.setDescription('Unable to validate provided initiator details');
        return response;
      }

      if (triggeredBy === 'CR_CONFIRM_PAYMENT') {
        if (cobraActor.userType !== UserTypeEnum.PFA) {
          response.setDescription('Initiator of this request must be a PFA user ');
          return response;
        }
      } else if (cobraActor.userType !== UserTypeEnum.PENCOM) {
        response.setDescription('Initiator of this request must be a PENCOM user ');
        return response;
      }
    }

    // todo update the relevant tables for computation PENCOM_AUDIT_VALIDATOR_ROLE_NAME

    const nominalRoll = await this.nominalRollRepository.findOneWithRelations({ rsaPin }, ['createdBy']);
    nominalRoll.status = targetState;

    if (triggeredBy === 'CR_BATCHING') {
      if (!options || !options.batchName) {
        response.setDescription('Please provide Batch Name');
        return response;
      }
      nominalRoll.batchId = options.batchName;
    }

    if (['AUDIT_VALIDATOR_REJECTION', 'AUDIT_SUPERVISOR_REJECTION'].includes(triggeredBy) && options.comment) {
      const comment = new NominalRollComment({
        commentText: options.comment,
        authorEmail: options.actorEmailAddress,
        authorName: `${cobraActor?.firstName} ${cobraActor?.surname}`,
        nominalRoll: nominalRoll,
      });

      try {
        await this.nominalRollCommentRepository.saveEntity(comment);
      } catch (error) {
        this.logger.error(
          `Error performing  ${triggeredBy} for RSAPIN: ${rsaPin} ${error instanceof Error ? error.stack : error}`
        );
        response.setDescription(
          'Unable to complete REJECTION process for provided RSA PIN, please check and try again'
        );
      }
    }

    const queryRunner: QueryRunner = options.queryRunner;
    try {
      await this.nominalRollRepository.saveEntity(nominalRoll, queryRunner);
    } catch (error) {
      this.logger.error(
        `Error updating  nominalRoll for RSA PIN: ${rsaPin} ${error instanceof Error ? error.stack : error}`
      );
      response.setDescription(
        'Unable to locate the Nominal Roll details of provided RSA PIN, please check and try again'
      );
      return response;
    }

    await this.handleNotifications(targetState, triggeredBy, nominalRoll, rsaPin);
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    return response;
  }

  async handleNotifications(
    targetState: string,
    triggerdBy: string,
    nominalRoll: NominalRoll,
    finalComment?: string
  ): Promise<void> {
    const placeholders: Record<string, string> = {};
    const createdBy = await nominalRoll.createdBy;

    switch (targetState) {
      case NominalRollStatusEnum.COMPUTED:
        placeholders['rsaPin'] = nominalRoll.rsaPin;
        placeholders['submittedBy'] = createdBy.surname;
        placeholders['submissionDate'] = `${nominalRoll.lastModified}`;
        await this.sendBulkNotification(
          SettingsEnumKey.PENCOM_AUDIT_VALIDATOR_ROLE_NAME,
          placeholders,
          NotificatonTypeEnum.AUDIT_VALIDATOR_NR_REVIEW_REQUEST
        );
        break;

      case NominalRollStatusEnum.UPLOADED:
        placeholders['userName'] = 'VALIDATOR'; //todo get the validator user name
        placeholders['retireeName'] = `${nominalRoll.firstName} ${nominalRoll.surname}`;
        placeholders['rsaPin'] = nominalRoll.rsaPin;
        placeholders['comment'] = finalComment;
        await this.sendBulkNotification(
          SettingsEnumKey.PENCOM_VALIDATOR_ROLE_NAME,
          placeholders,
          NotificatonTypeEnum.AUDIT_VALIDATOR_CONTRIBUTION_REQUEST_REJECTION
        );
        break;

      case NominalRollStatusEnum.PRE_CERTIFIED:
        placeholders['rsaPin'] = nominalRoll.rsaPin;
        placeholders['submittedBy'] = createdBy.surname;
        placeholders['submissionDate'] = `${nominalRoll.lastModified}`;
        await this.sendBulkNotification(
          SettingsEnumKey.PENCOM_AUDIT_SUPERVISOR_ROLE_NAME,
          placeholders,
          NotificatonTypeEnum.AUDIT_SUPERVISOR_NR_REVIEW_REQUEST
        );
        break;

      case NominalRollStatusEnum.CR_CERTIFIED:
        console.log('contemplating sending email to HOD upon certification');
        // placeholders['rsaPin'] = nominalRoll.rsaPin;
        // placeholders['submittedBy'] = createdBy.surname;
        // placeholders['submissionDate'] = `${nominalRoll.lastModified}`;
        // await this.sendBulkNotification(SettingsEnumKey.HOD_ROLE_NAME, placeholders, NotificatonTypeEnum.AUDIT_VALIDATOR_NR_REVIEW_REQUEST);
        break;

      default:
        break;
    }

    if (triggerdBy === 'CR_NON_CONFIRMATION') {
      //TODO send email to pfa to confirm
      const pfaDetail = await this.pfaRepository.getCachedPfaDetails(nominalRoll.pfaCode);
      if (!pfaDetail.emailAddress) {
        this.logger.warn(`Pfa email has not been configured for ${nominalRoll.pfaCode}`);
        return;
      }
      placeholders['rsaPin'] = nominalRoll.rsaPin;
      placeholders['batchId'] = nominalRoll.batchId;
      placeholders['username'] = pfaDetail.pfc.pfcName;
      const emailRecipients = [pfaDetail.emailAddress];
      await this.sendBulkRecipientNotification(
        emailRecipients,
        placeholders,
        NotificatonTypeEnum.PFA_NON_PAYMENT_CONFIRMATION
      );
    }
  }
}
