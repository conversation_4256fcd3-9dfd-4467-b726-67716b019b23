import { Injectable } from '@nestjs/common';
import { createActor, Snapshot, transition } from 'xstate';
import { WorkflowStateRepository } from '@app/shared/workflow/repository/workflow-state.repository';
import { WorkflowState } from '@app/shared/workflow/entities/workflow-state.entity';
import { AccruedBenefitsWorkflowService } from '@app/shared/workflow/services/accrued-benefits-workflow.service';
import { Actor } from 'xstate/dist/declarations/src/createActor';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ResponseCodeEnum, SettingsEnumKey } from '@app/shared/enums';
import { PinoLogger } from 'nestjs-pino';
import { ProcessTypeEnum } from '@app/shared/workflow/enums/ProcessTypeEnum';
import { TransitionResponseDto } from '@app/shared/workflow/dto/transition-response.dto';
import { RegistrationStatusEnum } from '@app/shared/enums/registration-status.enum';
import { DataSource, QueryRunner } from 'typeorm';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { TblBatch } from '@app/shared/enrollment-service/entities/tbl-batch';
import { BatchRecord } from '@app/shared/enrollment-service/entities/batch-record';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { BatchRepository } from '@app/shared/enrollment-service/repository/batch.repository';
import { BatchRecordRepository } from '@app/shared/enrollment-service/repository/batch-record.repository';
import { calculateRecordAmount, convertHypenToIsoDate } from '@app/shared/workflow/utils/workflow-utils';
import { ActiveSnapshot, EnrolmentMachineEvent, workflow } from '@app/shared/workflow/workflow/exit-ar-cr.workflow';
import { StampScheduleDto } from '@app/shared/dto/enrollment/exit-batching/batch-filter.dto';
import { MemoService } from '@app/shared/enrollment-service/services/memo.service';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { TbcEnrollmentPfaMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-enrollment-pfa-memo-document-repository.service';
import { TbcEnrollmentPfcMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-enrollment-pfc-memo-document-repository.service';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { PfcRepository } from '@app/shared/user-service/repositories/pfc.repository';
import { HYPHEN_DATE_FORMAT } from '@app/shared/constants';

@Injectable()
export class WorkflowService {
  constructor(
    private readonly workflowStateRepository: WorkflowStateRepository,
    private readonly accruedBenefitsService: AccruedBenefitsWorkflowService,
    private readonly logger: PinoLogger,
    private readonly dataSource: DataSource,
    private redisService: RedisCacheService,
    private readonly batchRepository: BatchRepository,
    private readonly pfaRepository: PfaRepository,
    private readonly pfcRepository: PfcRepository,
    private readonly batchRecordRepository: BatchRecordRepository,
    private readonly tbcEnrollmentPfaMemoDocumentRepository: TbcEnrollmentPfaMemoDocumentRepository,
    private readonly tbcEnrollmentPfcMemoDocumentRepository: TbcEnrollmentPfcMemoDocumentRepository,
    private readonly memoService: MemoService
  ) {}

  /**
   * Check if the event is a valid transition for the given RSA PIN.
   */
  async checkIfValidTransition(
    event: EnrolmentMachineEvent,
    rsaPin: string,
    additionalInfo: Record<string, any>,
    processType: ProcessTypeEnum
  ): Promise<TransitionResponseDto> {
    const response = new TransitionResponseDto(ResponseCodeEnum.ERROR);
    try {
      // Get the actor and workflow state
      const { actor, workflowState } = await this.getActor(rsaPin, processType);

      // Validate the event
      const nextState = transition(workflow, actor.getSnapshot(), event);
      console.log(`nextState: ${JSON.stringify(nextState[0].value)}`);
      console.log(`getSnapshot: ${JSON.stringify(actor.getSnapshot().value)}`);
      if (nextState[0] === actor.getSnapshot()) {
        const currentState = this.getRelevantState(actor.getSnapshot().value, event.type);
        this.logger.error(`Invalid event: ${event.type} for current state ${currentState}`);
        response.setDescription(
          `Invalid event: ${event.type} is not allowed to be submitted as the current state is ${currentState}`
        );
        actor.stop();
        return response;
      }

      // If the event is valid, return the actor and workflow state
      response.setResponseCode(ResponseCodeEnum.SUCCESS);
      response.workflowState = workflowState;
      response.actor = actor;
      return response;
    } catch (error) {
      this.logger.error(
        `Error checking valid transition for record ${rsaPin} \n error:  ${error instanceof Error ? error.stack : error}`
      );
      response.setDescription(`Error checking valid transition for record ${rsaPin}`);
      return response;
    }
  }

  async validateBatchTransitions(
    rsaPins: string[],
    event: EnrolmentMachineEvent,
    additionalInfo: Record<string, any>,
    processType: ProcessTypeEnum
  ): Promise<{ rsaPin: string; valid: boolean; reason?: string }[]> {
    const results: { rsaPin: string; valid: boolean; reason?: string }[] = [];

    for (const rsaPin of rsaPins) {
      try {
        // Get actor and workflow state
        const { actor } = await this.getActor(rsaPin, processType);
        const currentSnapshot = actor.getSnapshot();
        const nextState = transition(workflow, currentSnapshot, event);

        const isValid = nextState[0] !== currentSnapshot;
        if (!isValid) {
          results.push({
            rsaPin,
            valid: false,
            reason: `Invalid event: ${event.type} for current state: ${this.getRelevantState(currentSnapshot.value, event.type)}`,
          });
        } else {
          results.push({ rsaPin, valid: true });
        }

        actor.stop(); // stop actor after use
      } catch (err) {
        this.logger.error(`Validation failed for ${rsaPin}: ${err}`);
        results.push({
          rsaPin,
          valid: false,
          reason: `Unexpected error during transition validation: ${err instanceof Error ? err.message : String(err)}`,
        });
      }
    }

    return results;
  }

  /**
   * Process an event for a given RSA PIN.
   */
  async processEvent(
    event: EnrolmentMachineEvent,
    rsaPin: string,
    additionalInfo: Record<string, any>,
    actor: Actor<any>,
    workflowState: WorkflowState,
    queryRunner?: QueryRunner
  ): Promise<BaseResponseWithContentNoPagination<any>> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const FINITE_STATES = new Set(Object.values(RegistrationStatusEnum));

    try {
      console.log('about to send event:::: ', event.type);
      actor.send(event);

      const state = await new Promise<Snapshot<unknown>>((resolve, reject) => {
        const subscription = actor.subscribe({
          next: async (snapshot) => {
            console.log('snapshot.value ::::: ', snapshot.value);
            const snapshotValue = snapshot.value;
            let relevantState = this.getRelevantState(snapshotValue, event.type);
            console.log('relevantState ::::: ', relevantState);

            if (relevantState && FINITE_STATES.has(relevantState as RegistrationStatusEnum)) {
              await this.updateWorkflowState(actor, workflowState, event, additionalInfo, queryRunner);
              subscription.unsubscribe();
              resolve(snapshot);
            }
          },
          error: (err) => {
            console.error('Error in actor:', err);
            reject(err);
          },
        });
      });

      const output = (state as any).context?.output;
      if (output) {
        response.setResponseCode(output.code);
        response.setDescription(output.description);
        response.content = output.content;
      } else {
        response.setDescription('No output from workflow.');
      }

      return response;
    } catch (error) {
      this.logger.error(`Error processing event ${event.type} for record ${rsaPin}:`, error);
      response.setDescription(`Error processing event ${event.type} for record ${rsaPin}`);
      return response;
    }
  }

  async processBatchEvent(
    rsaPins: string[],
    event: EnrolmentMachineEvent,
    additionalInfo: Record<string, any>,
    processType: ProcessTypeEnum,
    queryRunner: QueryRunner
  ): Promise<BaseResponseDto> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const action = event.type;
    console.log('before crBatchResult:::: ', action);

    if (['AR_BATCHING', 'CR_BATCHING'].includes(action)) {
      const crBatchResult = await this.createBatch(additionalInfo.batchName, queryRunner);
      console.log('crBatchResult:::: ', crBatchResult);
      if (crBatchResult.code !== ResponseCodeEnum.SUCCESS) {
        response.setDescription(`Batch creation failed: ${crBatchResult.description}`);
        return response;
      }
    } else if (['AR_DELETE_BATCH', 'CR_DELETE_BATCH'].includes(action)) {
      const crBatchResult = await this.deleteBatch(additionalInfo.batchName, queryRunner);
      if (crBatchResult.code !== ResponseCodeEnum.SUCCESS) {
        response.setDescription(`Batch deletion failed: ${crBatchResult.description}`);
        return response;
      }
    } else if (['AR_EXCO_PAYMENT_APPROVAL', 'CR_EXCO_PAYMENT_APPROVAL'].includes(action)) {
      const stampScheduleDto = new StampScheduleDto();
      stampScheduleDto.batchName = additionalInfo.batchName;
      stampScheduleDto.actorEmailAddress = additionalInfo.actorEmailAddress;
      stampScheduleDto.stampType = 'EXCO_MEMO';
      const result = await this.memoService.stampDocument(stampScheduleDto, queryRunner);
      if (result.code !== ResponseCodeEnum.SUCCESS) {
        response.setDescription(`Unable to process request: ${result.description}`);
        return response;
      }
    }

    try {
      let atLeast1Failed = false;
      let relevantState;
      for (const rsaPin of rsaPins) {
        const { actor, workflowState } = await this.getActor(rsaPin, processType);

        const result = await this.processEvent(event, rsaPin, additionalInfo, actor, workflowState, queryRunner);
        console.log('processEvent result:::: ', result);
        if (result.code !== ResponseCodeEnum.SUCCESS) {
          atLeast1Failed = true;
          if (!Array.isArray(response.content)) {
            response.content = [];
          }
          (response.content as string[]).push(`Failed for rsaPin ${rsaPin}: ${result.description}`);
        } else if (!relevantState) {
          relevantState = this.getRelevantState(actor.getSnapshot().value, event.type);
          console.log('relevantState:::: ', relevantState);
          console.log('actor.getSnapshot():::: ', actor.getSnapshot().value);
        }
        actor.stop(); // Stop actor when done
      }

      if (atLeast1Failed) {
        response.setResponseCode(ResponseCodeEnum.ERROR);
        await queryRunner.rollbackTransaction();
      } else {
        if (!['AR_NOTIFY_PFA_PFC', 'CR_NOTIFY_PFA_PFC'].includes(action)) {
          await this.batchRepository.findOneAndUpdate(
            { batchName: additionalInfo.batchName },
            { status: relevantState, pfaNotificationDate: new Date() },
            queryRunner
          );
        } else {
          if (!['AR_DELETE_BATCH', 'CR_DELETE_BATCH'].includes(action)) {
            await this.batchRepository.findOneAndUpdate(
              { batchName: additionalInfo.batchName },
              { status: relevantState },
              queryRunner
            );
          }
        }
        await queryRunner.commitTransaction();
        response.setResponseCode(ResponseCodeEnum.SUCCESS);
        response.setDescription('Request was successfully processed.');
        await this.sendNotification(event, additionalInfo);
      }
    } catch (err) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Batch transition failed. Rolling back. Reason: ${err}`);
      response.setDescription(`Batch failed: ${err.message}`);
    }

    return response;
  }

  /**
   * Get an actor, either a new one or a rehydrated one.
   */
  private async getActor(
    rsaPin: string,
    processType: ProcessTypeEnum
  ): Promise<{ actor: Actor<any>; workflowState: WorkflowState }> {
    let workflowState = await this.workflowStateRepository.findOne({
      uniqueId: rsaPin,
      processType,
    });

    let actor;
    if (!workflowState) {
      console.log(`No existing workflow found for recordId ${rsaPin}. Creating new workflow...`);

      // Create a new actor
      actor = createActor(
        workflow.provide({
          // No context here
        }),
        {
          input: {
            rsaPin,
            accruedBenefitsService: this.accruedBenefitsService, // Pass the service via input
          },
        }
      );

      const initialState = actor.getSnapshot(); // Get the initial state

      // Remove accruedBenefitsService before persisting
      const cleanState = this.removeAccruedBenefitsService(initialState);

      workflowState = new WorkflowState({
        uniqueId: rsaPin,
        currentState: JSON.stringify(cleanState), // Serialize the cleaned state
        eventLog: JSON.stringify([]),
        processType,
      });

      await this.workflowStateRepository.saveEntity(workflowState);
    } else {
      // Rehydrate the state from the database
      const restoredState: Snapshot<unknown> = JSON.parse(workflowState.currentState);

      // Add accruedBenefitsService back to the state
      const hydratedState = this.addAccruedBenefitsService(restoredState, this.accruedBenefitsService);

      actor = createActor(
        workflow.provide({
          // No context here
        }),
        {
          input: {
            rsaPin,
            // actorEmailAddress: additionalInfo.actorEmailAddress,
            // pfaCode: additionalInfo.pfaCode,
            accruedBenefitsService: this.accruedBenefitsService, // Pass the service via input
          },
          snapshot: hydratedState, // Restore previous state
        }
      );
    }
    actor.start();

    return { actor, workflowState };
  }

  /**
   * Update the workflow state based on the provided actor.
   */
  private async updateWorkflowState(
    actor: Actor<any>,
    workflowState: WorkflowState,
    event: EnrolmentMachineEvent,
    additionalInfo: Record<string, any>,
    queryRunner?: QueryRunner
  ): Promise<void> {
    const currentState = actor.getSnapshot();

    // Remove accruedBenefitsService before persisting
    const cleanState = this.removeAccruedBenefitsService(currentState);
    workflowState.currentState = JSON.stringify(cleanState); // Serialize the cleaned state

    const timestamp = new Date();
    const actorRole = additionalInfo.pfaCode; //todo this is not accurate for pencom users
    const userId = additionalInfo.actorEmailAddress;

    const persistEvent = this.sanitizeWorkflowEvent(event);
    const newEvent = { event: persistEvent, actorRole, userId, timestamp, currentState: cleanState };

    // Parse the existing event log or initialize an empty array
    const eventLog = JSON.parse(workflowState.eventLog || '[]');
    eventLog.push(newEvent);
    workflowState.eventLog = JSON.stringify(eventLog);

    await this.workflowStateRepository.saveEntity(workflowState, queryRunner);
    console.log(
      `Event ${event.type} processed successfully for record ${workflowState.uniqueId}. New state: ${JSON.stringify(cleanState.value)}`
    );
  }

  sanitizeWorkflowEvent(event: EnrolmentMachineEvent): EnrolmentMachineEvent {
    if ('formData' in event) {
      const { formData, ...rest } = event;
      return rest as EnrolmentMachineEvent;
    }
    return event;
  }

  /**
   * Remove accruedBenefitsService from the snapshot before persisting.
   */
  private removeAccruedBenefitsService(snapshot: Snapshot<unknown>): any {
    if (snapshot.status !== 'active') {
      throw new Error('Cannot modify context of a non-active snapshot');
    }

    // Cast the snapshot to the "active" state type
    const activeSnapshot = snapshot as ActiveSnapshot;

    // Ensure context is an object
    const cleanContext = { ...activeSnapshot.context }; // Access context directly from snapshot
    delete cleanContext.accruedBenefitsService; // Remove the service
    delete cleanContext.formData; // Remove the service
    return {
      ...activeSnapshot,
      context: cleanContext,
    };
  }

  /**
   * Add accruedBenefitsService back to the snapshot when rehydrating.
   */
  private addAccruedBenefitsService(
    snapshot: Snapshot<unknown>,
    accruedBenefitsService: AccruedBenefitsWorkflowService
  ): any {
    if (snapshot.status !== 'active') {
      throw new Error('Cannot modify context of a non-active snapshot');
    }

    // Cast the snapshot to the "active" state type
    const activeSnapshot = snapshot as ActiveSnapshot;

    return {
      ...activeSnapshot,
      context: {
        ...activeSnapshot.context, // Access context directly from snapshot
        accruedBenefitsService, // Add the service back
      },
    };
  }

  async deleteBatch(
    batchName: string,
    queryRunner: QueryRunner
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const batch = await this.batchRepository.findOneWithRelations({ batchName }, ['records']);

    if (!batch) {
      response.setDescription(`Batch not found with the provided name ${batchName}`);
      return response;
    }

    if (batch.records?.length) {
      await this.batchRecordRepository.removeEntities(batch.records, queryRunner);
    }

    await this.batchRepository.removeEntity(batch, queryRunner);
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    return response;
  }

  async createBatch(
    batchName: string,
    queryRunner?: QueryRunner
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);

    const cachedData = await this.redisService.get(`batch:${batchName}`);
    if (!cachedData) {
      response.content = { success: false, message: 'Batch not found or expired' };
      return response;
    }

    const savedBatch = await this.batchRepository.findOne({ batchName });
    if (savedBatch) {
      response.setDescription(`Batch with name ${batchName} already exists`);
      return response;
    }

    const batchData = JSON.parse(cachedData as string);
    const { records, filterDto } = batchData;

    try {
      const batch = new TblBatch({
        batchName: batchName,
        totalCount: records.length,
        totalAmount: this.calculateTotalBatchAmount(records, filterDto.batchType),
        rsaHolder: filterDto.rsaHolderType,
        batchType: filterDto.batchType,
        enrollmentStartDate: convertHypenToIsoDate(filterDto.enrollmentStartDate, HYPHEN_DATE_FORMAT),
        enrollmentEndDate: convertHypenToIsoDate(filterDto.enrollmentEndDate, HYPHEN_DATE_FORMAT),
        retirementStartDate: convertHypenToIsoDate(filterDto.retirementStartDate, HYPHEN_DATE_FORMAT),
        retirementEndDate: convertHypenToIsoDate(filterDto.retirementEndDate, HYPHEN_DATE_FORMAT),
      });
      console.log('batch:::: ', batch);

      // Save the batch
      const savedBatch = await this.batchRepository.saveEntity(batch, queryRunner);

      // Create batch records
      const batchRecords = [];
      for (const record of records) {
        const amount = calculateRecordAmount(record, filterDto.batchType); // Calculate amount once
        const batchRecord = new BatchRecord({
          batch: savedBatch,
          enrolment: record,
          batchType: filterDto.batchType.toUpperCase(),
          amount: `${amount}`,
        });
        batchRecords.push(batchRecord);
      }

      console.log('batchRecords:::: ', batchRecords);
      // Save all batch records at once for better performance
      await this.batchRecordRepository.saveEntities(batchRecords, queryRunner);

      response.content = {
        success: true,
        message: `Batch '${batchName}' created successfully with ${records.length} records`,
      };
      response.setResponseCode(ResponseCodeEnum.SUCCESS);
      return response;
    } catch (error) {
      this.logger.error(
        `error occurred when saving Batch with name ${batchName}: ${error instanceof Error ? error.stack : error}`
      );
      response.content = { success: false, message: `Failed to create batch: ${error.message}` };
      return response;
    }
  }

  calculateTotalBatchAmount(records, batchType) {
    return records.reduce((total, record) => {
      return total + calculateRecordAmount(record, batchType);
    }, 0);
  }

  getRelevantState(snapshotValue, eventType: string) {
    const isParallel =
      typeof snapshotValue === 'object' && snapshotValue !== null && 'PARALLEL_PROCESSES' in snapshotValue;
    let relevantState: string | undefined;

    console.log('isParallel ::::: ', isParallel);

    if (isParallel) {
      const processes = (snapshotValue as any).PARALLEL_PROCESSES;
      if (eventType.startsWith('AR_')) relevantState = processes.AR_PROCESS;
      else if (eventType.startsWith('CR_')) relevantState = processes.CR_PROCESS;
      else if (eventType === 'AUDIT_SUPERVISOR_APPROVAL') relevantState = processes.CR_PROCESS;
      else relevantState = `${processes.AR_PROCESS} | ${processes.CR_PROCESS} `;
    } else if (typeof snapshotValue === 'string') {
      relevantState = snapshotValue;
    }
    return relevantState;
  }

  async sendNotification(event: EnrolmentMachineEvent, additionalInfo: Record<string, any>) {
    let role: SettingsEnumKey | undefined;
    let type: NotificatonTypeEnum | undefined;

    if (['CR_REQUEST_EXCO_APPROVAL', 'AR_REQUEST_EXCO_APPROVAL'].includes(event.type)) {
      role = SettingsEnumKey.PENCOM_EXCO_ROLE_NAME;
      type = NotificatonTypeEnum.EXCO_APPROVAL_REQUEST;
    } else if (['CR_EXCO_PAYMENT_APPROVAL', 'AR_EXCO_PAYMENT_APPROVAL'].includes(event.type)) {
      role = SettingsEnumKey.HOD_ROLE_NAME;
      type = NotificatonTypeEnum.EXCO_APPROVAL_GRANTED;
    } else if (['CR_REQUEST_ACCOUNT_PAYMENT', 'AR_REQUEST_ACCOUNT_PAYMENT'].includes(event.type)) {
      role = SettingsEnumKey.ACCOUNT_ROLE_NAME;
      type = NotificatonTypeEnum.ACCOUNT_PAYMENT_REQUESTED;
    } else if (['CR_PAYMENT_REMITTED', 'AR_PAYMENT_REMITTED'].includes(event.type)) {
      role = SettingsEnumKey.HOD_ROLE_NAME;
      type = NotificatonTypeEnum.ACCOUNT_PAYMENT_CONFIRMED;
    } else if (['CR_CONFIRM_PAYMENT', 'AR_CONFIRM_PAYMENT'].includes(event.type)) {
      role = SettingsEnumKey.HOD_ROLE_NAME;
      type = NotificatonTypeEnum.PFA_PAYMENT_CONFIRMED;
    }

    if (role && type) {
      const placeholders: Record<string, string> = {};
      placeholders['batchId'] = additionalInfo.batchName;
      await this.accruedBenefitsService.sendBulkNotification(role, placeholders, type);
    } else if (['AR_NOTIFY_PFA_PFC', 'CR_NOTIFY_PFA_PFC'].includes(event.type)) {
      const placeholders = {
        batchId: additionalInfo.batchName,
        paymentType: event.type.startsWith('AR_') ? 'ACCRUED RIGHTS' : 'CONTRIBUTIONS',
      };

      const pfaDocuments = await this.tbcEnrollmentPfaMemoDocumentRepository.findBy({
        batch: { batchName: additionalInfo.batchName },
      });

      if (!pfaDocuments || pfaDocuments.length === 0) {
        this.logger.warn(`No account memo details found for batch ${additionalInfo.batchName}`);
        return;
      }

      const emailAttachment: any[] = [];
      // email to PFAs
      for (const pfaDocument of pfaDocuments) {
        const pfaDetail = await this.pfaRepository.getCachedPfaDetails(pfaDocument.pfaCode);
        if (!pfaDetail.emailAddress) {
          this.logger.warn(`Pfa email has not been configured for ${pfaDocument.pfaCode}`);
          continue;
        }
        placeholders['username'] = pfaDocument.pfaName;
        const emailRecipients = [pfaDetail.emailAddress];
        await this.accruedBenefitsService.sendBulkRecipientNotification(
          emailRecipients,
          placeholders,
          NotificatonTypeEnum.PFA_PAYMENT_MADE
        );

        let pfcEntry = emailAttachment.find((e) => e.pfcCode === pfaDocument.pfcCode);
        if (!pfcEntry) {
          pfcEntry = {
            pfcCode: pfaDocument.pfcCode,
            attachments: [],
          };
          emailAttachment.push(pfcEntry);
        }

        // Add this PFA's memo and schedule as a grouped attachment
        pfcEntry.attachments.push({
          pfaCode: pfaDocument.pfaCode,
          schedule: pfaDocument.pfaSchedule,
        });
      }

      // email to PFCs
      const pfcMemoDocuments = await this.tbcEnrollmentPfcMemoDocumentRepository.findBy({
        batch: { batchName: additionalInfo.batchName },
      });

      for (const pfcMemo of pfcMemoDocuments) {
        const pfcEntry = emailAttachment.find((entry) => entry.pfcCode === pfcMemo.pfcCode);
        if (!pfcEntry) {
          continue;
        }
        const pfcDetail = await this.pfcRepository.findOne({ pfcCode: pfcMemo.pfcCode });
        if (!pfcDetail?.emailAddress) {
          continue;
        }

        placeholders['username'] = pfcMemo.pfcName;
        const emailRecipients = [pfcDetail.emailAddress];
        pfcEntry.attachments.unshift({ pfcMemo: pfcMemo.pfcMemo }); // use unshift to place it before PFA-specific docs

        const attachments: { filename: string; content: Buffer; contentType: string }[] = [];
        for (const doc of pfcEntry.attachments) {
          if (doc.pfcMemo) {
            attachments.push({
              filename: `memo-${pfcMemo.pfcCode}.pdf`,
              content: doc.pfcMemo,
              contentType: 'application/pdf',
            });
          }

          if (doc.pfaCode && doc.schedule) {
            attachments.push({
              filename: `schedule-${doc.pfaCode}.pdf`,
              content: doc.schedule,
              contentType: 'application/pdf',
            });
          }
        }

        await this.accruedBenefitsService.sendBulkRecipientNotification(
          emailRecipients,
          placeholders,
          NotificatonTypeEnum.PFC_PAYMENT_MADE,
          attachments
        );
      }
    }
  }
}
