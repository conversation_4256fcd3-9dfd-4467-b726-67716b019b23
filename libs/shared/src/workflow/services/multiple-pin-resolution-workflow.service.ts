import { Injectable } from '@nestjs/common';
import { createActor, Snapshot, transition } from 'xstate';
import {
  machine as workflow,
  MultiplePinResolutionMachineEvent,
} from '@app/shared/workflow/workflow/multi-pin-resolution.workflow';
import { WorkflowStateRepository } from '@app/shared/workflow/repository/workflow-state.repository';
import { WorkflowState } from '@app/shared/workflow/entities/workflow-state.entity';
import { Actor } from 'xstate/dist/declarations/src/createActor';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ResponseCodeEnum } from '@app/shared/enums';
import { PinoLogger } from 'nestjs-pino';
import { ProcessTypeEnum } from '@app/shared/workflow/enums/ProcessTypeEnum';
import { TransitionResponseDto } from '@app/shared/workflow/dto/transition-response.dto';
import { CustomException } from '@app/shared/filters/exception.dto';
import { MultiplePinResolutionService } from 'apps/enrollment-ms/src/services/multiple-pin-resolution.service';
import { MultiplePinResolutionStatusEnum } from '@app/shared/enrollment-service/enums/multiple-pin-resolution-status.enum';

@Injectable()
export class MultiplePinResolutionWorkFlowService {
  constructor(
    private readonly workflowStateRepository: WorkflowStateRepository,
    private readonly logger: PinoLogger
  ) {}

  /**
   * Check if the event is a valid transition for the given RSA PIN.
   */
  async checkIfValidTransition(
    event: MultiplePinResolutionMachineEvent,
    pk: number,
    multiplePinResolutionService: MultiplePinResolutionService,
    processType: ProcessTypeEnum
  ): Promise<TransitionResponseDto> {
    const response = new TransitionResponseDto(ResponseCodeEnum.ERROR);

    // Get the actor and workflow state
    const { actor, workflowState } = await this.getActor(pk, event.formData, processType, multiplePinResolutionService);

    // Validate the event
    const nextState = transition(workflow, actor.getSnapshot(), event);
    console.log(`nextState: ${JSON.stringify(nextState[0].value)}`);
    console.log(`getSnapshot: ${JSON.stringify(actor.getSnapshot().value)}`);

    if (nextState[0] === actor.getSnapshot()) {
      this.logger.error(`Invalid event: ${event.type} for current state ${actor.getSnapshot().value}`);
      throw new CustomException(`Invalid event: ${event.type} for current state ${actor.getSnapshot().value}`);
    }

    // If the event is valid, return the actor and workflow state
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.workflowState = workflowState;
    response.actor = actor;
    return response;
  }

  /**
   * Process an event for a given RSA PIN.
   */
  async processEvent(
    event: MultiplePinResolutionMachineEvent,
    actor: Actor<any>,
    workflowState: WorkflowState
  ): Promise<BaseResponseDto> {
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
    // Send the event to the actor

    actor.send(event);

    const finiteStates = Object.values(MultiplePinResolutionStatusEnum);

    const state = await new Promise<Snapshot<unknown>>((resolve, reject) => {
      const subscription = actor.subscribe({
        next: async (snapshot) => {
          console.log('snapshot.value ::::: ', snapshot.value);
          // Convert enum to array
          if (finiteStates.includes(snapshot.value)) {
            await this.updateWorkflowState(actor, workflowState, event, event.formData);
            subscription.unsubscribe(); // Stop listening
            resolve(snapshot);
          }
        },
        error: (err) => {
          console.error('Error in actor:', err);
          reject(err);
        },
      });
    });

    const output = (state as any).context?.output ?? null;

    if (!output) {
      throw new CustomException('No output from workflow.');
    }
    response.setResponseCode(output.code);
    response.setDescription(output.description);

    return response;
  }

  /**
   * Get an actor, either a new one or a rehydrated one.
   */
  private async getActor(
    pk: number,
    additionalInfo: Record<string, any>,
    processType: ProcessTypeEnum,
    multiplePinResolutionService: MultiplePinResolutionService
  ): Promise<{ actor: Actor<any>; workflowState: WorkflowState }> {
    let workflowState = await this.workflowStateRepository.findOne({
      uniqueId: additionalInfo.batchId,
      processType,
    });

    let actor;
    if (!workflowState) {
      console.log(`No existing workflow found for recordId ${pk}. Creating new workflow...`);

      // Create a new actor
      actor = createActor(
        workflow.provide({
          // No context here
        }),
        {
          input: {
            pk: pk,
            multiplePinResolutionService: multiplePinResolutionService,
            actorEmailAddress: additionalInfo.actorEmailAddress,
            formData: { ...additionalInfo },
            currentState: additionalInfo.status, // Pass the service via input
          },
        }
      );

      const initialState = actor.getSnapshot(); // Get the initial state

      // Remove accruedBenefitsService before persisting
      const cleanState = this.removeMultiplePinWorkflowService(initialState);

      workflowState = new WorkflowState({
        uniqueId: additionalInfo.batchId,
        currentState: JSON.stringify(cleanState), // Serialize the cleaned state
        eventLog: JSON.stringify([]),
        processType,
      });

      await this.workflowStateRepository.saveEntity(workflowState);
    } else {
      // Rehydrate the state from the database
      const restoredState: Snapshot<unknown> = JSON.parse(workflowState.currentState);

      // Add accruedBenefitsService back to the state
      const hydratedState = this.attachMultiplePinResolutionService(
        restoredState,
        multiplePinResolutionService,
        additionalInfo
      );

      actor = createActor(workflow.provide({}), {
        input: {
          pk,
          actorEmailAddress: additionalInfo.actorEmailAddress,
          multiplePinResolutionService: multiplePinResolutionService,
          formData: { ...additionalInfo },
          currentState: additionalInfo.status,
        },
        snapshot: hydratedState, // Restore previous state
      });
    }
    actor.start();

    return { actor, workflowState };
  }

  /**
   * Update the workflow state based on the provided actor.
   */
  private async updateWorkflowState(
    actor: Actor<any>,
    workflowState: WorkflowState,
    event: MultiplePinResolutionMachineEvent,
    additionalInfo: Record<string, any>
  ): Promise<void> {
    const currentState = actor.getSnapshot();

    // Create a clean version of the state for persistence
    const cleanState = {
      ...currentState,
      context: {
        ...currentState.context,
      },
    };

    // Remove services from context
    delete cleanState.context.multiplePinResolutionService;
    delete cleanState.context.formData;

    workflowState.currentState = JSON.stringify(cleanState);

    const timestamp = new Date();
    const actorRole = additionalInfo.pfaCode;
    const userId = additionalInfo.actorEmailAddress;

    const persistEvent = this.sanitizeWorkflowEvent(event);
    const newEvent = { event: persistEvent, actorRole, userId, timestamp, currentState: cleanState };

    // Parse the existing event log or initialize an empty array
    const eventLog = JSON.parse(workflowState.eventLog || '[]');
    eventLog.push(newEvent);
    workflowState.eventLog = JSON.stringify(eventLog);

    await this.workflowStateRepository.saveEntity(workflowState);
    console.log(
      `Event ${event.type} processed successfully for record ${workflowState.uniqueId}. New state: ${JSON.stringify(cleanState.value)}`
    );
  }

  sanitizeWorkflowEvent(event: MultiplePinResolutionMachineEvent): MultiplePinResolutionMachineEvent {
    if ('formData' in event) {
      const { formData, ...rest } = event;
      return rest as MultiplePinResolutionMachineEvent;
    }
    return event;
  }

  /**
   * Remove accruedBenefitsService from the snapshot before persisting.
   */
  private removeMultiplePinWorkflowService(snapshot: Snapshot<unknown>): any {
    if (snapshot.status !== 'active') {
      throw new Error('Cannot modify context of a non-active snapshot');
    }

    // Cast the snapshot to the "active" state type
    const activeSnapshot = snapshot as ActiveSnapshot;

    // Ensure context is an object
    const cleanContext = { ...activeSnapshot.context }; // Access context directly from snapshot
    delete cleanContext.multiplePinResolutionService; // Remove the service
    delete cleanContext.formData; // Remove the service
  
    const cleanState = {
      ...activeSnapshot,
      context: cleanContext,
    };
    return cleanState;
  }

  /**
   * Add accruedBenefitsService back to the snapshot when rehydrating.
   */
  private attachMultiplePinResolutionService(
    snapshot: Snapshot<unknown>,
    multiplePinResolutionService: MultiplePinResolutionService,
    formData: Record<string, any>
  ): any {
    if (snapshot.status !== 'active') {
      throw new Error('Cannot modify context of a non-active snapshot');
    }

    // Cast the snapshot to the "active" state type
    const activeSnapshot = snapshot as ActiveSnapshot;

    return {
      ...activeSnapshot,
      context: {
        ...activeSnapshot.context, // Access context directly from snapshot
        multiplePinResolutionService,
        formData, // Add the service back
      },
    };
  }

  /**
   * Maps workflow actions to status enums
   */
  getStatusFromAction(status: string): MultiplePinResolutionStatusEnum {
    const statusEnum = MultiplePinResolutionStatusEnum[status as keyof typeof MultiplePinResolutionStatusEnum];

    if (!statusEnum) {
      throw new CustomException(`Invalid status: ${status}`);
    }

    return statusEnum;
  }
}

// Define the type for the "active" state snapshot
type ActiveSnapshot = Snapshot<MultiplePinResolutionWorkflowInput> & {
  status: 'active';
  context: MultiplePinResolutionWorkflowInput;
};

// Add this type definition
type MultiplePinResolutionWorkflowInput = {
  pk: number;
  multiplePinResolutionService: MultiplePinResolutionService;
  actorEmailAddress?: string;
  comment?: string;
  formData?: Record<string, any>;
};
