import { Injectable } from '@nestjs/common';
import { BatchRecordRepository } from '@app/shared/enrollment-service/repository/batch-record.repository';
import { TbcNominalRollBatchRecordRepository } from '@app/shared/enrollment-service/repository/tbc-nominal-roll-batch-record.repository';
import { TbcNominalRollBatchRecord } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch-record';
import { BatchRecord } from '@app/shared/enrollment-service/entities/batch-record';

@Injectable()
export class WorkflowHelperService {
  constructor(
    private readonly tbcNominalRollBatchRecordRepository: TbcNominalRollBatchRecordRepository,
    private readonly batchRecordRepository: BatchRecordRepository
  ) {}

  async getBatchRecords(
    batchPk: number,
    requestType: 'NOMINAL_ROLL' | 'EXIT_RECORD'
  ): Promise<TbcNominalRollBatchRecord[] | BatchRecord[]> {
    if (requestType === 'NOMINAL_ROLL') {
      return await this.tbcNominalRollBatchRecordRepository.find({
        where: { batch: { pk: batchPk } },
        relations: ['nominalRoll'],
      });
    }

    return await this.batchRecordRepository.find({
      where: { batch: { pk: batchPk } },
      relations: ['enrolment'],
    });
  }

  async getBatchContributionRecords(batchPk: number, requestType: 'NOMINAL_ROLL' | 'EXIT_RECORD') {
    if (requestType === 'NOMINAL_ROLL') {
      return await this.tbcNominalRollBatchRecordRepository.find({
        where: { batch: { pk: batchPk }, recordCategory: 'CONTRIBUTION' },
        relations: ['nominalRoll'],
      });
    }

    return await this.batchRecordRepository.find({
      where: { batch: { pk: batchPk }, batchType: 'CONTRIBUTION' },
      relations: ['enrolment'],
    });
  }
}
