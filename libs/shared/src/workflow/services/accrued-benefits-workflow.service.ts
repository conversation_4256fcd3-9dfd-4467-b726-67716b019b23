import { Inject, Injectable } from '@nestjs/common';
import { WorkflowStateRepository } from '@app/shared/workflow/repository/workflow-state.repository';
import { ClientProxy } from '@nestjs/microservices';
import { PinoLogger } from 'nestjs-pino';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { ResponseCodeEnum, SettingsEnumKey } from '@app/shared/enums';
import { SendNotificationTemplateDto } from '@app/shared/dto/notification/send-notification-template.dto';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { firstValueFrom } from 'rxjs';
import { NotificationServiceClientConstant } from '@app/shared/constants/notification-service-client.constant';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { RegistrationStatusEnum } from '@app/shared/enums/registration-status.enum';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { EnrolmentDraft } from '@app/shared/enrollment-service/entities/enrolment-draft.entity';
import { EnrolmentDraftRepository } from '@app/shared/enrollment-service/repository/enrolment-draft.repository';
import { FormValidatorService } from '@app/shared/enrollment-service/services/form-validator.service';
import { mapToEnrolmentBiodata } from '@app/shared/workflow/utils/workflow-utils';
import { EnrolmentBiodata } from '@app/shared/enrollment-service/entities/enrolment-biodata.entity';
import { EnrolmentBiodataRepository } from '@app/shared/enrollment-service/repository/enrolment-biodata.repository';
import { EnrolmentCommentRepository } from '@app/shared/enrollment-service/repository/enrolment-comment.repository';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { EnrolmentComment } from '@app/shared/enrollment-service/entities/enrolment-comment.entity';
import { QueryRunner } from 'typeorm';
import { AmendmentRequestRepository } from '@app/shared/enrollment-service/repositories/amendment-request.repository';
import { AmendmentRequestStatus } from '@app/shared/enrollment-service/entities/amendment-request.entity';
import { BaseWorkflowService } from '@app/shared/workflow/services/base-workflow.service';
import { MdaEmployeeBiodataRepository } from '@app/shared/login-service/repositories/mda-employee-biodata.repository';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { EnrolmentSummary } from '@app/shared/enrollment-service/entities/enrolment-summary.entity';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { format } from 'date-fns';

@Injectable()
export class AccruedBenefitsWorkflowService extends BaseWorkflowService {
  constructor(
    private readonly workflowStateRepository: WorkflowStateRepository,
    private readonly enrolmentSummaryRepository: EnrolmentSummaryRepository,
    private readonly amendmentRequestRepository: AmendmentRequestRepository,
    private readonly enrolmentDraftRepository: EnrolmentDraftRepository,
    private readonly enrolmentBiodataRepository: EnrolmentBiodataRepository,
    private readonly enrolmentCommentRepository: EnrolmentCommentRepository,
    private readonly mdaEmployeeBiodataRepository: MdaEmployeeBiodataRepository,
    private readonly pfaRepository: PfaRepository,
    @Inject('NOTIFICATION_SERVICE_CLIENT') notificationClient: ClientProxy,
    private readonly formValidatorService: FormValidatorService,
    cobraUserRepository: CobraUserRepository,
    settingsService: SettingMsLibService,
    logger: PinoLogger
  ) {
    super(notificationClient, cobraUserRepository, settingsService, logger);
  }

  /*
   * fetch supervisor role from settings
   * get all users with the supersor role
   * send email to all users with supervisor role requesting for approval
   * */
  async handleEnrolmentProcess(
    rsaPin: string,
    actorEmailAddress: string,
    formData: Record<string, any>
  ): Promise<BaseResponseWithContentNoPagination<any>> {
    let response: BaseResponseWithContentNoPagination<unknown> = new BaseResponseWithContentNoPagination<unknown>(
      ResponseCodeEnum.ERROR
    );

    const cobraActor = await this.cobraUserRepository.findOne({ emailAddress: actorEmailAddress });
    if (!cobraActor) {
      response.setDescription('Unable to validate provided initiator details');
      return response;
    }

    if (cobraActor.userType !== UserTypeEnum.PFA || !cobraActor.pfaCode) {
      response.setDescription('Initiator of this request must be a PFA user ');
      return response;
    }

    const mdaEmployeeBiodataInfo = await this.mdaEmployeeBiodataRepository.findOne({ rsaPin });
    if (!mdaEmployeeBiodataInfo) {
      response.setDescription('Unable to validate user information, please verify user is registered and try again');
      return response;
    }

    const pfaDetails = await this.pfaRepository.findPfaByCodeAndAssociatedCode(mdaEmployeeBiodataInfo.pfaCode);
    if (cobraActor.pfaCode !== pfaDetails.pfaCode) {
      response.setDescription(
        `Initiator of this request must belong to PFA: ${pfaDetails.pfaName} - ${pfaDetails.pfaCode}`
      );
      return response;
    }

    if (pfaDetails.pfaCode !== formData.pfaCode) {
      response.setDescription('Provided PFA code does not match the record PFA CODE, please check and try again');
      return response;
    }

    const pfaCode = cobraActor.pfaCode;

    const validators = [
      this.formValidatorService.validateBiodata,
      this.formValidatorService.validateEmploymentDetails,
      this.formValidatorService.validateOtherEmploymentDetails,
      this.formValidatorService.validateDocumentUpload,
      this.formValidatorService.validatePortrait,
      this.formValidatorService.validateSignature,
      this.formValidatorService.finalReviewAndSubmit,
    ];

    for (const validate of validators) {
      const response = await validate.call(this.formValidatorService, formData);
      if (response.code !== ResponseCodeEnum.SUCCESS) {
        return response; // Return immediately if validation fails
      }
    }

    const record = new EnrolmentDraft({
      rsaPin: formData.rsaPin,
      stepNumber: 7,
      formData: JSON.stringify(formData),
    });

    try {
      await this.enrolmentDraftRepository.upsertEntity(record, ['rsaPin']);
    } catch (error) {
      this.logger.error(
        `Error updating  enrolmentDraft for RSAPIN: ${rsaPin} ${error instanceof Error ? error.stack : error}`
      );
      response.setDescription('Unable to update Enrolment details of provided RSA PIN, please try again');
      return response;
    }

    try {
      await this.enrolmentSummaryRepository.findOneAndUpdate(
        { rsaPin },
        { status: RegistrationStatusEnum.PENDING_PFA_REVIEW, submittedBy: cobraActor, submissionDate: new Date() }
      );
    } catch (error) {
      this.logger.error(
        `Error updating  enrolmentSummary for RSAPIN: ${rsaPin} ${error instanceof Error ? error.stack : error}`
      );
      response.setDescription(
        'Unable to locate the Enrolment summary details of provided RSA PIN, please check and try again'
      );
      return response;
    }

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Request has been successfully submitted for review');

    const pfaSuperVisorRoleName = await this.settingMsService.getSetting(SettingsEnumKey.PFA_SUPERVISOR_ROLE_NAME);
    const cobraUsers = await this.cobraUserRepository.getUsersByRoleNameAndPfaCode(pfaSuperVisorRoleName, pfaCode);

    //TODO update enrolment_summary to PENDING
    if (!cobraUsers || cobraUsers.length === 0) {
      this.logger.error(`No users found with role name: ${pfaSuperVisorRoleName}, pfaCode: ${pfaCode} `);
      return response;
    }

    const placeholders: Record<string, string> = {};
    placeholders['rsaPin'] = rsaPin;
    placeholders['submittedBy'] = actorEmailAddress;
    placeholders['submissionDate'] = `${new Date()}`;

    const sendNotificationTemplateDto = new SendNotificationTemplateDto();
    sendNotificationTemplateDto.emailRecipients = cobraUsers;
    sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.SUPERVISOR_REVIEW_REQUEST;
    sendNotificationTemplateDto.placeholders = placeholders;

    try {
      await firstValueFrom(
        this.notificationClient.send(
          NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
          sendNotificationTemplateDto
        )
      );
    } catch (error) {
      this.logger.error(
        `Failed to send SEND_EMAIL_NOTIFICATION_TEMPLATE notification to approver error: \n error:  ${error instanceof Error ? error.stack : error}`
      );
    }
    return response;
  }

  async handleRecordApproval(
    rsaPin: string,
    actorEmailAddress: string,
    formData: Record<string, any>,
    targetState: string,
    triggeredBy: string
  ) {
    let response: BaseResponseWithContentNoPagination<unknown> = new BaseResponseWithContentNoPagination<unknown>(
      ResponseCodeEnum.ERROR
    );

    const cobraActor = await this.cobraUserRepository.findOne({ emailAddress: actorEmailAddress });
    if (!cobraActor) {
      response.setDescription('Unable to validate provided initiator details');
      return response;
    }

    if (triggeredBy === 'PENCOM_VALIDATOR_APPROVAL' && cobraActor.userType !== UserTypeEnum.PENCOM) {
      response.setDescription('Initiator of this request must be a PENCOM user');
      return response;
    }

    if (
      triggeredBy !== 'PENCOM_VALIDATOR_APPROVAL' &&
      (cobraActor.userType !== UserTypeEnum.PFA || !cobraActor.pfaCode)
    ) {
      response.setDescription('Initiator of this request must be a PFA user');
      return response;
    }

    const enrollmentSummary = await this.enrolmentSummaryRepository.findOne({ rsaPin });
    if (!enrollmentSummary) {
      response.setDescription('Unable to locate the Enrolment summary details of provided RSA PIN');
      return response;
    }
    const submittedBy = await enrollmentSummary.submittedBy;
    if (submittedBy && submittedBy.pk === cobraActor.pk) {
      response.setDescription('You are not allowed to approve the request you submitted');
      return response;
    }

    let pfaDetailsPfaCode;
    let pfaDetailsPfaName;
    if (triggeredBy === 'PENCOM_VALIDATOR_APPROVAL') {
      const pendingAmendmentRequest = await this.amendmentRequestRepository.findOne({
        rsaPin: rsaPin,
        status: AmendmentRequestStatus.PENDING,
      });
      if (pendingAmendmentRequest) {
        response.setDescription(
          'There is a pending amendment request for this pin, kindly go to the amendment module to resolve this before proceeding with this action.'
        );
        return response;
      }

      const assignedTo = await enrollmentSummary.assignedTo;
      if (!assignedTo || assignedTo.pk !== cobraActor.pk) {
        !assignedTo
          ? response.setDescription('Record is not assigned to you, please check and try again')
          : response.setDescription(
              `Record is not assigned to you as it is assigned to ${assignedTo.surname} ${assignedTo.firstName}, please check and try again`
            );
        return response;
      }
    } else {
      const mdaEmployeeBiodataInfo = await this.mdaEmployeeBiodataRepository.findOne({ rsaPin });
      if (!mdaEmployeeBiodataInfo) {
        response.setDescription('Unable to validate user information, please verify user is registered and try again');
        return response;
      }

      const pfaDetails = await this.pfaRepository.findPfaByCodeAndAssociatedCode(mdaEmployeeBiodataInfo.pfaCode);
      if (cobraActor.pfaCode !== pfaDetails.pfaCode) {
        response.setDescription(
          `Initiator of this request must belong to PFA: ${pfaDetails.pfaName} - ${pfaDetails.pfaCode}`
        );
        return response;
      }

      if (pfaDetails.pfaCode !== formData.pfaCode) {
        response.setDescription('Provided PFA code does not match the record PFA CODE, please check and try again');
        return response;
      }

      pfaDetailsPfaCode = pfaDetails.pfaCode;
      pfaDetailsPfaName = pfaDetails.pfaName;
    }

    const validators = [
      this.formValidatorService.validateBiodata,
      this.formValidatorService.validateEmploymentDetails,
      this.formValidatorService.validateOtherEmploymentDetails,
      this.formValidatorService.validateDocumentUpload,
      this.formValidatorService.validatePortrait,
      this.formValidatorService.validateSignature,
      this.formValidatorService.finalReviewAndSubmit,
    ];

    for (const validate of validators) {
      const response = await validate.call(this.formValidatorService, formData);
      if (response.code !== ResponseCodeEnum.SUCCESS) {
        return response; // Return immediately if validation fails
      }
    }

    let enrolmentBiodata: EnrolmentBiodata;
    try {
      const existingBiodata = await this.enrolmentBiodataRepository.findOneWithRelations({ rsaPin }, ['comments']);
      const savedComments = existingBiodata?.comments || null;
      if (existingBiodata) {
        pfaDetailsPfaCode = existingBiodata.pfaCode;
        pfaDetailsPfaName = existingBiodata.pfaName;
      }

      await this.enrolmentBiodataRepository.findOneAndDelete({ rsaPin });
      enrolmentBiodata = mapToEnrolmentBiodata(formData);
      if (savedComments && savedComments.length > 0) {
        savedComments.forEach((comment) => {
          comment.biodata = enrolmentBiodata; // Updating FK reference
        });
        enrolmentBiodata.comments = savedComments;
      }
      enrolmentBiodata.pfaCode = pfaDetailsPfaCode;
      enrolmentBiodata.pfaName = pfaDetailsPfaName;
      await this.enrolmentBiodataRepository.saveEntity(enrolmentBiodata);
      response.setDescription('Record successfully submitted for review by supervisor');
    } catch (error) {
      console.log(error);
      this.logger.error(
        `Error updating  mapToEnrolmentBiodata and save to db for RSAPIN: ${rsaPin} ${error instanceof Error ? error.stack : error}`
      );
      response.setDescription(
        'Error occurred while trying to save Enrolment Details of provided RSA PIN, please check and try again'
      );
      return response;
    }

    if (triggeredBy === 'PENCOM_VALIDATOR_APPROVAL') {
      enrollmentSummary.validationDate = new Date();
      enrollmentSummary.status = targetState;
      await this.enrolmentSummaryRepository.saveEntity(enrollmentSummary);
    } else {
      try {
        await this.enrolmentSummaryRepository.findOneAndUpdate(
          { rsaPin },
          { status: RegistrationStatusEnum.ENROLLED, enrolledBy: cobraActor, enrolmentDate: new Date() }
        );
      } catch (error) {
        this.logger.error(
          `Error updating  enrolmentSummary for RSAPIN: ${rsaPin} ${error instanceof Error ? error.stack : error}`
        );
        response.setDescription(
          'Unable to locate the Enrolment summary details of provided RSA PIN, please check and try again'
        );
        return response;
      }

      //  send email to retiree
      const placeholders: Record<string, string> = {};
      placeholders['userName'] = enrolmentBiodata.firstName;

      const sendNotificationTemplateDto = new SendNotificationTemplateDto();
      sendNotificationTemplateDto.emailRecipients = [enrolmentBiodata.emailAddress];
      sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.RETIREE_ENROLMENT_NOTIFICATION;
      sendNotificationTemplateDto.placeholders = placeholders;

      try {
        await firstValueFrom(
          this.notificationClient.send(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            sendNotificationTemplateDto
          )
        );
      } catch (error) {
        this.logger.error(
          `Failed to send SEND_EMAIL_NOTIFICATION_TEMPLATE notification to approver error: \n error:  ${error instanceof Error ? error.stack : error}`
        );
      }

      // send to PENCOM VALIDATOR if it is a resubmission
      const assignedTo = await enrollmentSummary.assignedTo;
      if (assignedTo) {
        const enrolledBy = await enrollmentSummary.enrolledBy;
        const placeholders: Record<string, string> = {};
        placeholders['userName'] = assignedTo.firstName;
        placeholders['rsaPin'] = rsaPin;
        placeholders['submittedBy'] = `${enrolledBy.surname} ${enrolledBy.firstName}`;
        placeholders['submissionDate'] = enrollmentSummary.enrolmentDate
          ? format(enrollmentSummary.enrolmentDate, 'yyyy-MM-dd HH:mm:ss')
          : 'N/A';
        const sendNotificationTemplateDto = new SendNotificationTemplateDto();
        sendNotificationTemplateDto.emailRecipients = [assignedTo.emailAddress];
        sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.VALIDATOR_REVIEW_REQUEST;
        sendNotificationTemplateDto.placeholders = placeholders;

        try {
          await firstValueFrom(
            this.notificationClient.send(
              NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
              sendNotificationTemplateDto
            )
          );
        } catch (error) {
          this.logger.error(
            `Failed to send SEND_EMAIL_NOTIFICATION_TEMPLATE notification to approver error: \n error:  ${error instanceof Error ? error.stack : error}`
          );
        }
      }
    }

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription(`Request has been successfully ${targetState}`);
    return response;
  }

  async handlePencomApproval(
    rsaPin: string,
    actorEmailAddress: string,
    targetState: string,
    triggeredBy: string
  ): Promise<BaseResponseWithContentNoPagination<any>> {
    let response: BaseResponseWithContentNoPagination<unknown> = new BaseResponseWithContentNoPagination<unknown>(
      ResponseCodeEnum.ERROR
    );

    const cobraActor = await this.cobraUserRepository.findOne({ emailAddress: actorEmailAddress });
    if (!cobraActor) {
      response.setDescription('Unable to validate provided initiator details');
      return response;
    }

    if (cobraActor.userType !== UserTypeEnum.PENCOM) {
      response.setDescription('Initiator of this request must be a PENCOM user ');
      return response;
    }

    let enrolmentSummary;
    try {
      if (targetState === RegistrationStatusEnum.CERTIFIED) {
        // this is the event coming after supervisor has approved request
        enrolmentSummary = await this.enrolmentSummaryRepository.findOneAndUpdate(
          { rsaPin },
          {
            status: targetState,
            contributionStatus: RegistrationStatusEnum.CR_CERTIFIED,
            accruedRightsStatus: RegistrationStatusEnum.AR_CERTIFIED,
            certifiedBy: cobraActor,
            auditCertificationDate: new Date(),
          }
        );
      } else if (triggeredBy === 'AUDIT_VALIDATOR_APPROVED') {
        enrolmentSummary = await this.enrolmentSummaryRepository.findOne({ rsaPin });
        if (!enrolmentSummary) {
          response.setDescription('Unable to locate the Enrolment summary details of provided RSA PIN');
          return response;
        }

        if (
          !enrolmentSummary.auditorAssignedTo ||
          enrolmentSummary.auditorAssignedTo.toLowerCase() !== cobraActor.emailAddress.toLowerCase()
        ) {
          !enrolmentSummary.auditorAssignedTo
            ? response.setDescription('Record is not assigned to you, please check and try again')
            : response.setDescription(
                `Record is not assigned to you as it is assigned to ${enrolmentSummary.auditorAssignedTo}, please check and try again`
              );
          return response;
        }

        enrolmentSummary.auditorValidationDate = new Date();
        enrolmentSummary.status = targetState;
        await this.enrolmentSummaryRepository.saveEntity(enrolmentSummary);
      } else {
        enrolmentSummary = await this.enrolmentSummaryRepository.findOneAndUpdate({ rsaPin }, { status: targetState });
      }
      response = new BaseResponseWithContentNoPagination<unknown>(ResponseCodeEnum.SUCCESS);
      response.setDescription('Record successfully processed');
    } catch (error) {
      this.logger.error(
        `Error updating  enrolmentSummary for RSAPIN: ${rsaPin} ${error instanceof Error ? error.stack : error}`
      );
      response.setDescription(
        'Unable to updated the Enrolment summary details of provided RSA PIN, please check and try again'
      );
      return response;
    }

    await this.handleNotifications(targetState, triggeredBy, enrolmentSummary, rsaPin, cobraActor);
    return response;
  }

  async handleDeleteBatch(
    rsaPin: string,
    actorEmailAddress: string,
    targetState: string,
    triggeredBy: string,
    formData: Record<string, any>
  ): Promise<BaseResponseWithContentNoPagination<any>> {
    let response: BaseResponseWithContentNoPagination<unknown> = new BaseResponseWithContentNoPagination<unknown>(
      ResponseCodeEnum.ERROR
    );

    if (!formData) {
      response.setDescription('Batch Name is required to perform this action');
      return response;
    }

    const queryRunner: QueryRunner = formData.queryRunner;
    if (!queryRunner) {
      response.setDescription('Unable to process request, please try again');
      return response;
    }

    try {
      const enrolmentSummary = await this.enrolmentSummaryRepository.findOne({ rsaPin });
      if (!enrolmentSummary) {
        response.setDescription('Unable to locate the Enrolment summary details of provided RSA PIN');
        return response;
      }

      const enrolmenBiodata = await this.enrolmentBiodataRepository.findOne({ rsaPin });
      if (!enrolmenBiodata) {
        response.setDescription('Enrolment details not found for provided RSA PIN');
        return response;
      }

      if (triggeredBy.startsWith('AR')) {
        enrolmentSummary.accruedRightsStatus = RegistrationStatusEnum.AR_CERTIFIED;
        enrolmenBiodata.accruedRightsBatchId = null;
      } else if (triggeredBy.startsWith('CR')) {
        enrolmentSummary.contributionStatus = RegistrationStatusEnum.CR_CERTIFIED;
        enrolmenBiodata.contributionBatchId = null;
      } else {
        response.setDescription('Unknown action type provided, please check and try again');
        return response;
      }

      enrolmentSummary.status = targetState;

      await this.enrolmentSummaryRepository.saveEntity(enrolmentSummary, queryRunner);
      await this.enrolmentBiodataRepository.saveEntity(enrolmenBiodata, queryRunner);
      response = new BaseResponseWithContentNoPagination<unknown>(ResponseCodeEnum.SUCCESS);
      response.setDescription('Batch successfully deleted');
    } catch (error) {
      this.logger.error(
        `Error updating  enrolmentSummary for RSAPIN: ${rsaPin} ${error instanceof Error ? error.stack : error}`
      );
      response.setDescription(
        'Unable to updated the Enrolment summary details of provided RSA PIN, please check and try again'
      );
    }
    return response;
  }

  async handlePencomBatchApproval(
    rsaPin: string,
    actorEmailAddress: string,
    targetState: string,
    formData: Record<string, any>
  ): Promise<BaseResponseWithContentNoPagination<any>> {
    let response: BaseResponseWithContentNoPagination<unknown> = new BaseResponseWithContentNoPagination<unknown>(
      ResponseCodeEnum.ERROR
    );
    if (!formData || !formData.batchName) {
      response.setDescription('Batch Name is required to perform this action');
      return response;
    }

    const queryRunner: QueryRunner = formData.queryRunner;
    if (!queryRunner) {
      response.setDescription('Unable to process request, please try again');
      return response;
    }

    try {
      const enrolmentSummary = await this.enrolmentSummaryRepository.findOne({ rsaPin });
      if (!enrolmentSummary) {
        response.setDescription('Unable to locate the Enrolment summary details of provided RSA PIN');
        return response;
      }

      const enrolmenBiodata = await this.enrolmentBiodataRepository.findOne({ rsaPin });
      if (!enrolmenBiodata) {
        response.setDescription('Enrolment details not found for provided RSA PIN');
        return response;
      }

      if (targetState.startsWith('AR')) {
        enrolmentSummary.accruedRightsStatus = targetState;
        enrolmenBiodata.accruedRightsBatchId = formData.batchName;
      } else if (targetState.startsWith('CR')) {
        enrolmentSummary.contributionStatus = targetState;
        enrolmenBiodata.contributionBatchId = formData.batchName;
      } else {
        response.setDescription('Unknown action type provided, please check and try again');
        return response;
      }

      if (enrolmenBiodata.accruedRightsBatchId && enrolmenBiodata.contributionBatchId) {
        enrolmentSummary.status = RegistrationStatusEnum.BATCHED;
      } else {
        enrolmentSummary.status = RegistrationStatusEnum.CERTIFIED;
      }

      await this.enrolmentSummaryRepository.saveEntity(enrolmentSummary, queryRunner);
      await this.enrolmentBiodataRepository.saveEntity(enrolmenBiodata, queryRunner);
      response = new BaseResponseWithContentNoPagination<unknown>(ResponseCodeEnum.SUCCESS);
      response.setDescription('Record successfully processed');
    } catch (error) {
      this.logger.error(
        `Error updating  enrolmentSummary for RSAPIN: ${rsaPin} ${error instanceof Error ? error.stack : error}`
      );
      response.setDescription(
        'Unable to updated the Enrolment summary details of provided RSA PIN, please check and try again'
      );
      return response;
    }

    // TODO, DETERMINE WHO TO SEND MAIL TO

    return response;
  }

  async handlePencomBatchApproval2(
    rsaPin: string,
    actorEmailAddress: string,
    targetState: string,
    triggeredBy: string,
    formData: Record<string, any>
  ): Promise<BaseResponseWithContentNoPagination<any>> {
    let response: BaseResponseWithContentNoPagination<unknown> = new BaseResponseWithContentNoPagination<unknown>(
      ResponseCodeEnum.ERROR
    );

    //TODO perform cleanup if targetState is FINALISED

    if (!formData || !formData.batchName) {
      response.setDescription('Batch Name is required to perform this action');
      return response;
    }

    const queryRunner: QueryRunner = formData.queryRunner;
    if (!queryRunner) {
      response.setDescription('Unable to process request, please try again');
      return response;
    }

    try {
      const enrolmentSummary = await this.enrolmentSummaryRepository.findOne({ rsaPin });
      if (!enrolmentSummary) {
        response.setDescription('Unable to retrieve the Enrolment summary details of provided RSA PIN');
        return response;
      }

      if (targetState.startsWith('AR')) {
        enrolmentSummary.accruedRightsStatus = targetState;
      } else if (targetState.startsWith('CR')) {
        enrolmentSummary.contributionStatus = targetState;
      } else {
        response.setDescription('Unknown action type provided, please check and try again');
        return response;
      }

      await this.enrolmentSummaryRepository.saveEntity(enrolmentSummary, queryRunner);
      response = new BaseResponseWithContentNoPagination<unknown>(ResponseCodeEnum.SUCCESS);
      response.setDescription('Record successfully processed');
    } catch (error) {
      this.logger.error(
        `Error updating  enrolmentSummary for RSAPIN: ${rsaPin} ${error instanceof Error ? error.stack : error}`
      );
      response.setDescription(
        'Unable to updated the Enrolment summary details of provided RSA PIN, please check and try again'
      );
      return response;
    }

    // TODO, DETERMINE WHO TO SEND MAIL TO

    return response;
  }

  async handlePencomRejection(
    rsaPin: string,
    actorEmailAddress: string,
    commentText: string,
    targetState: string,
    triggeredBy: string
  ): Promise<BaseResponseWithContentNoPagination<any>> {
    let response: BaseResponseWithContentNoPagination<unknown> = new BaseResponseWithContentNoPagination<unknown>(
      ResponseCodeEnum.ERROR
    );

    if (!commentText) {
      response.setDescription('Comment is required to perform this action, please check and try again');
      return response;
    }

    const cobraActor = await this.cobraUserRepository.findOne({ emailAddress: actorEmailAddress });
    if (!cobraActor) {
      response.setDescription('Unable to validate provided initiator details');
      return response;
    }

    if (cobraActor.userType !== UserTypeEnum.PENCOM) {
      response.setDescription('Initiator of this request must be a PENCOM user ');
      return response;
    }

    if (triggeredBy === 'PENCOM_VALIDATOR_REJECTION') {
      const enrolmentSummary = await this.enrolmentSummaryRepository.findOne({ rsaPin });
      const assignedTo = await enrolmentSummary.assignedTo;
      if (!assignedTo || assignedTo.pk !== cobraActor.pk) {
        !assignedTo
          ? response.setDescription('Record is not assigned to you, please check and try again')
          : response.setDescription(
              `Record is not assigned to you as it is assigned to ${assignedTo.surname} ${assignedTo.firstName}, please check and try again`
            );
        return response;
      }
    }

    if (triggeredBy === 'AUDIT_VALIDATOR_REJECTION') {
      const enrolmentSummary = await this.enrolmentSummaryRepository.findOne({ rsaPin });
      if (
        !enrolmentSummary.auditorAssignedTo ||
        enrolmentSummary.auditorAssignedTo.toLowerCase() !== cobraActor.emailAddress.toLowerCase()
      ) {
        !enrolmentSummary.auditorAssignedTo
          ? response.setDescription('Record is not assigned to you, please check and try again')
          : response.setDescription(
              `Record is not assigned to you as it is assigned to ${enrolmentSummary.auditorAssignedTo}, please check and try again`
            );
        return response;
      }
    }

    const enrolmenBiodata = await this.enrolmentBiodataRepository.findOne({ rsaPin });
    if (!enrolmenBiodata) {
      response.setDescription('Enrolment details not found for provided RSA PIN');
      return response;
    }

    let finalComment = '';
    if (triggeredBy === 'PENCOM_VALIDATOR_APPROVE_AMENDMENT_REQUEST') {
      finalComment = `AMENDMENT REASON: ${commentText}`;
    } else {
      finalComment = commentText;
    }

    const comment = new EnrolmentComment({
      commentText: finalComment,
      authorEmail: actorEmailAddress,
      authorName: `${cobraActor.firstName} ${cobraActor.surname}`,
      biodata: enrolmenBiodata,
    });

    try {
      await this.enrolmentCommentRepository.saveEntity(comment);
    } catch (error) {
      this.logger.error(
        `Error performing  handlePencomRejection for RSAPIN: ${rsaPin} ${error instanceof Error ? error.stack : error}`
      );
      response.setDescription('Unable to complete REJECTION process for provided RSA PIN, please check and try again');
    }

    try {
      await this.enrolmentSummaryRepository.findOneAndUpdate({ rsaPin }, { status: targetState });
    } catch (error) {
      this.logger.error(
        `Error updating  enrolmentSummary for RSAPIN: ${rsaPin} ${error instanceof Error ? error.stack : error}`
      );
      response.setDescription(
        'Unable to updated the Enrolment summary details of provided RSA PIN, please check and try again'
      );
    }

    if (triggeredBy === 'PENCOM_VALIDATOR_APPROVE_AMENDMENT_REQUEST') {
      response.setDescription('Amendment request successfully approved');
    } else {
      if (RegistrationStatusEnum.PENDING_PFA_REVIEW === targetState) {
        const officerDetails = await this.enrolmentSummaryRepository.getEnrollmentOfficerDetails(rsaPin);
        const placeholders: Record<string, string> = {};
        placeholders['userName'] = officerDetails.firstName;
        placeholders['rsaPin'] = enrolmenBiodata.rsaPin;
        placeholders['comment'] = finalComment;

        const sendNotificationTemplateDto = new SendNotificationTemplateDto();
        sendNotificationTemplateDto.emailRecipients = [officerDetails.emailAddress];
        sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.VALIDATOR_REQUEST_REJECTION;
        sendNotificationTemplateDto.placeholders = placeholders;

        try {
          await firstValueFrom(
            this.notificationClient.send(
              NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
              sendNotificationTemplateDto
            )
          );
        } catch (error) {
          this.logger.error(
            `Failed to send VALIDATOR_REQUEST_REJECTION notification to PFA error: \n error:  ${error instanceof Error ? error.stack : error}`
          );
        }

        const retireePlaceholders: Record<string, string> = {};
        retireePlaceholders['userName'] = enrolmenBiodata.firstName;
        retireePlaceholders['comment'] = finalComment;

        const sendRetireeNotificationTemplateDto = new SendNotificationTemplateDto();
        sendRetireeNotificationTemplateDto.emailRecipients = [enrolmenBiodata.emailAddress];
        sendRetireeNotificationTemplateDto.notificationType =
          NotificatonTypeEnum.VALIDATOR_REQUEST_REJECTION_RETIREE_NOTIFICATION;
        sendRetireeNotificationTemplateDto.placeholders = placeholders;

        try {
          await firstValueFrom(
            this.notificationClient.send(
              NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
              sendRetireeNotificationTemplateDto
            )
          );
        } catch (error) {
          this.logger.error(
            `Failed to send VALIDATOR_REQUEST_REJECTION_RETIREE_NOTIFICATION notification to retireee error: \n error:  ${error instanceof Error ? error.stack : error}`
          );
        }
      } else if (RegistrationStatusEnum.ENROLLED === targetState) {
        // send email to Validator
        const officerDetails = await this.enrolmentSummaryRepository.getEnrollmentOfficerDetails(rsaPin);
        const placeholders: Record<string, string> = {};
        placeholders['userName'] = officerDetails.firstName;
        placeholders['retireeName'] = `${enrolmenBiodata.firstName} ${enrolmenBiodata.surname}`;
        placeholders['rsaPin'] = enrolmenBiodata.rsaPin;
        placeholders['comment'] = finalComment;

        const sendNotificationTemplateDto = new SendNotificationTemplateDto();
        sendNotificationTemplateDto.emailRecipients = [officerDetails.emailAddress];
        sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.AUDIT_VALIDATOR_REQUEST_REJECTION;
        sendNotificationTemplateDto.placeholders = placeholders;
        try {
          await firstValueFrom(
            this.notificationClient.send(
              NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
              sendNotificationTemplateDto
            )
          );
        } catch (error) {
          this.logger.error(
            `Failed to send VALIDATOR_REQUEST_REJECTION notification to PFA error: \n error:  ${error instanceof Error ? error.stack : error}`
          );
        }
      }
      response.setResponseCode(ResponseCodeEnum.SUCCESS);
      response.setDescription('Record successfully rejected');
    }

    return response;
  }

  /*
   * receives the accruedRights computation and contribution for rsaPin and
   * updates EnrollmentSummary/EnrollmentSummary table
   * */
  async handleComputationAnalysis(
    rsaPin: string,
    actorEmailAddress: string,
    formData: Record<string, any>
  ): Promise<BaseResponseWithContentNoPagination<any>> {
    let response: BaseResponseWithContentNoPagination<unknown> = new BaseResponseWithContentNoPagination<unknown>(
      ResponseCodeEnum.ERROR
    );

    const enrolmentSummary = await this.enrolmentSummaryRepository.findOne({ rsaPin });
    enrolmentSummary.accruedRightsAmount = formData.accruedBenefit;
    enrolmentSummary.contributionAmount = formData.contribution;
    enrolmentSummary.retirementMode = formData.retirementType;
    enrolmentSummary.apaValue2004 = formData.apaValue;
    enrolmentSummary.status = RegistrationStatusEnum.COMPUTED;

    await this.enrolmentSummaryRepository.upsertEntity(enrolmentSummary, ['rsaPin']);

    const enrolmentBiodata = await this.enrolmentBiodataRepository.findOne({ rsaPin });
    enrolmentBiodata.accruedRightsAmount = formData.accruedBenefit;
    enrolmentBiodata.contributionAmount = formData.contribution;
    enrolmentBiodata.retirementMode = formData.retirementType;
    enrolmentBiodata.apaValue2004 = formData.apaValue;

    await this.enrolmentBiodataRepository.upsertEntity(enrolmentBiodata, ['rsaPin']);

    // send email to audit validators
    response.setResponseCode(ResponseCodeEnum.SUCCESS);

    if (enrolmentSummary.auditorAssignedTo) {
      const enrolledBy = await enrolmentSummary.enrolledBy;
      const targetAuditor = await this.cobraUserRepository.findOne({
        emailAddress: enrolmentSummary.auditorAssignedTo,
      });
      if (targetAuditor) {
        const placeholders: Record<string, string> = {};
        placeholders['userName'] = targetAuditor.firstName;
        placeholders['rsaPin'] = rsaPin;
        placeholders['submittedBy'] = `${enrolledBy.surname} ${enrolledBy.firstName}`;
        placeholders['submissionDate'] = `${enrolmentSummary.enrolmentDate}`
          ? format(enrolmentSummary.enrolmentDate, 'yyyy-MM-dd HH:mm:ss')
          : 'N/A';

        const sendNotificationTemplateDto = new SendNotificationTemplateDto();
        sendNotificationTemplateDto.emailRecipients = [targetAuditor.emailAddress];
        sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.AUDIT_VALIDATOR_REVIEW_REQUEST;
        sendNotificationTemplateDto.placeholders = placeholders;

        try {
          await firstValueFrom(
            this.notificationClient.send(
              NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
              sendNotificationTemplateDto
            )
          );
        } catch (error) {
          console.log('error', error);
          this.logger.error(
            `Failed to send AUDIT_VALIDATOR_REVIEW_REQUEST notification to auditor error: \n error:  ${error instanceof Error ? error.stack : error.message}`
          );
        }
      }
    }

    return response;
  }

  async handleNotifications(
    targetState: string,
    triggerdBy: string,
    enrolmentSummary: EnrolmentSummary,
    rsaPin: string,
    cobraActor: CobraUser
  ): Promise<void> {
    const placeholders: Record<string, string> = {};
    switch (targetState) {
      case RegistrationStatusEnum.PRE_CERTIFIED: {
        const enrolledBy = await enrolmentSummary.enrolledBy;
        placeholders['rsaPin'] = rsaPin;
        placeholders['submittedBy'] = `${enrolledBy.surname} ${enrolledBy.firstName}`;
        placeholders['submissionDate'] = `${enrolmentSummary.enrolmentDate}`;
        placeholders['reviewedBy'] = `${cobraActor.surname} ${cobraActor.firstName}`;
        placeholders['reviewDate'] = `${enrolmentSummary.auditorValidationDate}`;
        await this.sendBulkNotification(
          SettingsEnumKey.PENCOM_AUDIT_SUPERVISOR_ROLE_NAME,
          placeholders,
          NotificatonTypeEnum.AUDIT_SUPERVISOR_REVIEW_REQUEST
        );
        break;
      }
      case RegistrationStatusEnum.CERTIFIED: {
        placeholders['rsaPin'] = rsaPin;
        placeholders['certificationDate'] = `${enrolmentSummary.enrolmentDate}`;
        placeholders['certifiedBy'] = `${cobraActor.surname} ${cobraActor.firstName}`;
        await this.sendBulkNotification(
          SettingsEnumKey.HOD_ROLE_NAME,
          placeholders,
          NotificatonTypeEnum.HOD_CERTIFICATION_NOTIFICATION
        );
        break;
      }
      default:
        break;
    }
  }
}
