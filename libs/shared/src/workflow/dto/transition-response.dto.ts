import { ResponseCodeEnum } from '@app/shared/enums';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { Actor } from 'xstate/dist/declarations/src/createActor';
import { WorkflowState } from '@app/shared/workflow/entities/workflow-state.entity';

export class TransitionResponseDto extends BaseResponseDto {
  actor?: Actor<any>;
  workflowState?: WorkflowState;

  constructor(responseCode?: ResponseCodeEnum) {
    super(responseCode);
  }
}
