import { assign, fromPromise, setup, Snapshot } from 'xstate';
import { AccruedBenefitsWorkflowService } from '@app/shared/workflow/services/accrued-benefits-workflow.service';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums';
import { RegistrationStatusEnum } from '@app/shared/enums/registration-status.enum';

// Define the type for the "active" state snapshot
export type ActiveSnapshot = Snapshot<WorkflowInput> & {
  status: 'active';
  context: WorkflowInput;
};

export type EnrolmentMachineEvent =
  | { type: 'ENROLMENT'; rsaPin: string; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'PFA_APPROVAL'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'PENCOM_VALIDATOR_APPROVAL'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'PENCOM_VALIDATOR_REJECTION'; actorEmailAddress: string; comment: string }
  | { type: 'PENCOM_VALIDATOR_APPROVE_AMENDMENT_REQUEST'; actorEmailAddress: string; comment: string }
  | { type: 'COMPUTATION_ANALYSIS'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'COMPUTATION'; actorEmailAddress: string }
  | { type: 'AR_BATCHING'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'CR_BATCHING'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'AUDIT_VALIDATOR_REJECTION'; actorEmailAddress: string; comment: string }
  | { type: 'AUDIT_VALIDATOR_APPROVED'; actorEmailAddress: string }
  | { type: 'AUDIT_SUPERVISOR_APPROVAL'; actorEmailAddress: string }
  | { type: 'AUDIT_SUPERVISOR_REJECTION'; actorEmailAddress: string; comment: string }
  | { type: 'REQUEST_DOCUMENT_REVIEW'; actorEmailAddress: string; comment: string }
  | { type: 'AR_DELETE_BATCH'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'CR_DELETE_BATCH'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'AR_REQUEST_EXCO_APPROVAL'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'CR_REQUEST_EXCO_APPROVAL'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'AR_EXCO_PAYMENT_APPROVAL'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'CR_EXCO_PAYMENT_APPROVAL'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'CR_REQUEST_ACCOUNT_PAYMENT'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'AR_REQUEST_ACCOUNT_PAYMENT'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'CR_PAYMENT_REMITTED'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'AR_PAYMENT_REMITTED'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'CR_NOTIFY_PFA_PFC'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'AR_NOTIFY_PFA_PFC'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'CR_NON_CONFIRMATION'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'AR_NON_CONFIRMATION'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'AR_CONFIRM_PAYMENT'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'CR_CONFIRM_PAYMENT'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'AR_COMPLETED'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'CR_COMPLETED'; actorEmailAddress: string; formData: Record<string, any> };

export const eventValues = [
  'ENROLMENT',
  'PFA_APPROVAL',
  'PENCOM_VALIDATOR_APPROVAL',
  'PENCOM_VALIDATOR_REJECTION',
  'PENCOM_VALIDATOR_APPROVE_AMENDMENT_REQUEST',
  'COMPUTATION_ANALYSIS',
  'COMPUTATION',
  'AR_BATCHING',
  'CR_BATCHING',
  'AUDIT_VALIDATOR_REJECTION',
  'AUDIT_VALIDATOR_APPROVED',
  'AUDIT_SUPERVISOR_APPROVAL',
  'AUDIT_SUPERVISOR_REJECTION',
  'REQUEST_DOCUMENT_REVIEW',
  'AR_DELETE_BATCH',
  'CR_DELETE_BATCH',
  'AR_REQUEST_EXCO_APPROVAL',
  'CR_REQUEST_EXCO_APPROVAL',
  'AR_EXCO_PAYMENT_APPROVAL',
  'CR_EXCO_PAYMENT_APPROVAL',
  'AR_NON_CONFIRMATION',
  'AR_COMPLETED',
  'CR_COMPLETED',
  'AR_REQUEST_ACCOUNT_PAYMENT',
  'AR_PAYMENT_REMITTED',
  'AR_NOTIFY_PFA_PFC',
  'AR_CONFIRM_PAYMENT',
  'CR_REQUEST_ACCOUNT_PAYMENT',
  'CR_PAYMENT_REMITTED',
  'CR_NOTIFY_PFA_PFC',
  'CR_CONFIRM_PAYMENT',
  'CR_NON_CONFIRMATION',
] as const;

export type EnrolmentMachineEventType = (typeof eventValues)[number];

export type WorkflowInput = {
  rsaPin?: string;
  accruedBenefitsService: AccruedBenefitsWorkflowService;
  actorEmailAddress?: string;
  comment?: string;
  formData?: Record<string, any>;
};

export const workflow = setup({
  types: {
    context: {} as {
      rsaPin?: string;
      accruedBenefitsService: AccruedBenefitsWorkflowService;
      actorEmailAddress?: string;
      comment?: string;
      formData?: Record<string, any>;
      output?: BaseResponseWithContentNoPagination<any>;
    },
    events: {} as EnrolmentMachineEvent,
    input: {} as WorkflowInput,
  },
  actions: {
    logEventTrigger: ({ event }) => {
      console.log(`${event.type} event triggered <<<<<<<<<>>>>>>>>>>>>`);
    },
    updateContextFromEnrolment: assign({
      rsaPin: ({ context, event }) => {
        if (event.type === 'ENROLMENT') {
          return event.rsaPin;
        }
        return context.rsaPin;
      },
      formData: ({ event }) => {
        if (event.type === 'ENROLMENT' || event.type === 'PFA_APPROVAL') return event.formData;
        return undefined;
      },
      actorEmailAddress: ({ event }) => {
        if ('actorEmailAddress' in event) return event.actorEmailAddress;
        return undefined;
      },
      comment: ({ event }) => {
        if (
          event.type === 'PENCOM_VALIDATOR_REJECTION' ||
          event.type === 'AUDIT_VALIDATOR_REJECTION' ||
          event.type === 'PENCOM_VALIDATOR_APPROVE_AMENDMENT_REQUEST'
        )
          return event.comment;
        return undefined;
      },
    }),
  },
  actors: {
    handleEnrolmentProcess: fromPromise(async ({ input }: { input: WorkflowInput }) => {
      let result: BaseResponseWithContentNoPagination<any> = new BaseResponseWithContentNoPagination<any>(
        ResponseCodeEnum.ERROR
      );
      try {
        const { rsaPin, accruedBenefitsService, actorEmailAddress, formData } = input;
        result = await accruedBenefitsService.handleEnrolmentProcess(rsaPin, actorEmailAddress, formData);
        return result;
      } catch (error) {
        console.error('handleEnrolmentProcess encountered an error:', error);
        return result;
      }
      return result;
    }),
    handleRecordApproval: fromPromise(
      async ({ input }: { input: WorkflowInput & { targetState?: string; triggeredBy?: string } }) => {
        console.log('handleRecordApproval invoked with input:', input);
        let result: BaseResponseWithContentNoPagination<any> = new BaseResponseWithContentNoPagination<any>(
          ResponseCodeEnum.ERROR
        );
        try {
          const { rsaPin, accruedBenefitsService, actorEmailAddress, formData, targetState, triggeredBy } = input;
          const result = await accruedBenefitsService.handleRecordApproval(
            rsaPin,
            actorEmailAddress,
            formData,
            targetState,
            triggeredBy
          );
          return result;
        } catch (error) {
          console.error('handleEnrolmentProcess encountered an error:', error);
          return result;
        }
        return result;
      }
    ),
    handlePencomApproval: fromPromise(
      async ({ input }: { input: WorkflowInput & { targetState?: string; triggeredBy?: string } }) => {
        const { rsaPin, accruedBenefitsService, actorEmailAddress, targetState, triggeredBy } = input;
        const result: BaseResponseWithContentNoPagination<any> = await accruedBenefitsService.handlePencomApproval(
          rsaPin,
          actorEmailAddress,
          targetState,
          triggeredBy
        );

        return result;
      }
    ),
    handlePencomRejection: fromPromise(
      async ({ input }: { input: WorkflowInput & { targetState?: string; triggeredBy?: string } }) => {
        const { rsaPin, actorEmailAddress, accruedBenefitsService, comment, targetState, triggeredBy } = input;
        const result: BaseResponseWithContentNoPagination<any> = await accruedBenefitsService.handlePencomRejection(
          rsaPin,
          actorEmailAddress,
          comment,
          targetState,
          triggeredBy
        );
        return result;
      }
    ),
    handlePencomBatchApproval: fromPromise(async ({ input }: { input: WorkflowInput & { targetState?: string } }) => {
      const { rsaPin, actorEmailAddress, accruedBenefitsService, formData, targetState } = input;
      const result: BaseResponseWithContentNoPagination<any> = await accruedBenefitsService.handlePencomBatchApproval(
        rsaPin,
        actorEmailAddress,
        targetState,
        formData
      );
      return result;
    }),
    handlePencomBatchApproval2: fromPromise(
      async ({ input }: { input: WorkflowInput & { targetState?: string; triggeredBy?: string } }) => {
        const { rsaPin, actorEmailAddress, accruedBenefitsService, formData, targetState, triggeredBy } = input;
        const result: BaseResponseWithContentNoPagination<any> =
          await accruedBenefitsService.handlePencomBatchApproval2(
            rsaPin,
            actorEmailAddress,
            targetState,
            triggeredBy,
            formData
          );
        return result;
      }
    ),
    handleDeleteBatch: fromPromise(
      async ({ input }: { input: WorkflowInput & { targetState?: string; triggeredBy?: string } }) => {
        const { rsaPin, actorEmailAddress, accruedBenefitsService, formData, targetState, triggeredBy } = input;
        const result: BaseResponseWithContentNoPagination<any> = await accruedBenefitsService.handleDeleteBatch(
          rsaPin,
          actorEmailAddress,
          targetState,
          triggeredBy,
          formData
        );
        return result;
      }
    ),
    handleComputationAnalysis: fromPromise(async ({ input }: { input: WorkflowInput }) => {
      const { rsaPin, actorEmailAddress, accruedBenefitsService, formData } = input;
      const result: BaseResponseWithContentNoPagination<any> = await accruedBenefitsService.handleComputationAnalysis(
        rsaPin,
        actorEmailAddress,
        formData
      );
      return result;
    }),
  },
}).createMachine({
  context: ({ input }: { input: WorkflowInput }) => ({
    rsaPin: input.rsaPin,
    accruedBenefitsService: input.accruedBenefitsService,
    actorEmailAddress: input.actorEmailAddress,
    comment: input.comment,
    formData: input.formData,
  }),
  id: 'contributionAccruedRightsWorkflowMachine',
  initial: 'REGISTERED',
  states: {
    REGISTERED: {
      on: {
        ENROLMENT: {
          actions: ['logEventTrigger', 'updateContextFromEnrolment'],
          target: 'PROCESSING_ENROLMENT',
        },
      },
    },
    PROCESSING_ENROLMENT: {
      invoke: {
        src: 'handleEnrolmentProcess',
        input: ({ context }) => ({
          rsaPin: context.rsaPin,
          actorEmailAddress: context.actorEmailAddress,
          accruedBenefitsService: context.accruedBenefitsService,
          formData: context.formData,
        }),
        onDone: [
          {
            target: 'PENDING_PFA_REVIEW',
            guard: ({ event }) => event.output?.code === 1,
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
          {
            target: 'REGISTERED',
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        ],
        onError: {
          target: 'REGISTERED',
          actions: assign({
            output: ({ event }) =>
              new BaseResponseWithContentNoPagination<any>(
                ResponseCodeEnum.ERROR,
                `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
              ),
          }),
        },
      },
      target: 'PENDING_PFA_REVIEW',
    },
    PENDING_PFA_REVIEW: {
      description: 'Record has been captured by ENROLL-ER and submitted to PFA Supervisor to review',
      on: {
        PFA_APPROVAL: {
          actions: ['logEventTrigger', 'updateContextFromEnrolment'],
          target: 'PROCESSING_PFA_APPROVAL',
        },
      },
    },
    PROCESSING_PFA_APPROVAL: {
      invoke: {
        src: 'handleRecordApproval',
        input: ({ context, event }) => ({
          rsaPin: context.rsaPin,
          actorEmailAddress: context.actorEmailAddress,
          accruedBenefitsService: context.accruedBenefitsService,
          formData: context.formData,
          targetState: RegistrationStatusEnum.ENROLLED,
          triggeredBy: event.type,
        }),
        onDone: [
          {
            target: 'ENROLLED',
            guard: ({ event }) => event.output?.code === 1,
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
          {
            target: 'PENDING_PFA_REVIEW',
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        ],
        onError: {
          target: 'PENDING_PFA_REVIEW',
          actions: assign({
            output: ({ event }) =>
              new BaseResponseWithContentNoPagination<any>(
                ResponseCodeEnum.ERROR,
                `Error in handleRecordApproval: ${event.error || 'Unknown error'}`
              ),
          }),
        },
      },
      target: 'ENROLLED',
    },
    ENROLLED: {
      on: {
        PENCOM_VALIDATOR_APPROVAL: {
          target: 'PROCESSING_PENCOM_VALIDATOR_APPROVAL',
          actions: {
            type: 'logEventTrigger',
          },
        },
        PENCOM_VALIDATOR_REJECTION: {
          target: 'PROCESSING_PENCOM_VALIDATOR_REJECTION',
          actions: {
            type: 'logEventTrigger',
          },
        },
        PENCOM_VALIDATOR_APPROVE_AMENDMENT_REQUEST: {
          target: 'PROCESSING_PENCOM_VALIDATOR_APPROVE_AMENDMENT_REQUEST',
          actions: {
            type: 'logEventTrigger',
          },
        },
      },
      description:
        'This is the state in which PFA has completed the enrolment cycle,\n\nsecuring approval from the Supervisor',
    },
    PROCESSING_PENCOM_VALIDATOR_APPROVAL: {
      invoke: {
        src: 'handleRecordApproval',
        input: ({ context, event }) => ({
          rsaPin: context.rsaPin,
          actorEmailAddress: event.actorEmailAddress,
          accruedBenefitsService: context.accruedBenefitsService,
          ...(event.type === 'PENCOM_VALIDATOR_APPROVAL' ? { formData: event.formData } : {}),
          targetState: RegistrationStatusEnum.VALIDATED,
          triggeredBy: event.type,
        }),
        onDone: [
          {
            target: 'VALIDATED',
            guard: ({ event }) => event.output?.code === 1,
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
          {
            target: 'ENROLLED',
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        ],
        onError: {
          target: 'ENROLLED',
          actions: assign({
            output: ({ event }) =>
              new BaseResponseWithContentNoPagination<any>(
                ResponseCodeEnum.ERROR,
                `Error in handlePencomApproval: ${event.error || 'Unknown error'}`
              ),
          }),
        },
      },
      target: 'VALIDATED',
    },
    PROCESSING_PENCOM_VALIDATOR_REJECTION: {
      invoke: {
        src: 'handlePencomRejection',
        input: ({ context, event }) => ({
          rsaPin: context.rsaPin,
          actorEmailAddress: event.actorEmailAddress,
          ...(event.type === 'PENCOM_VALIDATOR_REJECTION' ? { comment: event.comment } : {}),
          targetState: RegistrationStatusEnum.PENDING_PFA_REVIEW,
          accruedBenefitsService: context.accruedBenefitsService,
          triggeredBy: event.type,
        }),
        onDone: [
          {
            target: 'PENDING_PFA_REVIEW',
            guard: ({ event }) => event.output?.code === 1,
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
          {
            target: 'ENROLLED',
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        ],
        onError: {
          target: 'ENROLLED',
          actions: assign({
            output: ({ event }) =>
              new BaseResponseWithContentNoPagination<any>(
                ResponseCodeEnum.ERROR,
                `Error in handlePencomRejection: ${event.error || 'Unknown error'}`
              ),
          }),
        },
      },
      target: 'PENDING_PFA_REVIEW',
    },
    PROCESSING_PENCOM_VALIDATOR_APPROVE_AMENDMENT_REQUEST: {
      invoke: {
        src: 'handlePencomRejection',
        input: ({ context, event }) => ({
          rsaPin: context.rsaPin,
          actorEmailAddress: event.actorEmailAddress,
          targetState: RegistrationStatusEnum.PENDING_PFA_REVIEW,
          accruedBenefitsService: context.accruedBenefitsService,
          triggeredBy: event.type,
          ...(event.type === 'PENCOM_VALIDATOR_APPROVE_AMENDMENT_REQUEST' ? { comment: event.comment } : {}),
        }),
        onDone: [
          {
            target: 'PENDING_PFA_REVIEW',
            guard: ({ event }) => event.output?.code === 1,
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
          {
            target: 'ENROLLED',
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        ],
        onError: {
          target: 'ENROLLED',
          actions: assign({
            output: ({ event }) =>
              new BaseResponseWithContentNoPagination<any>(
                ResponseCodeEnum.ERROR,
                `Error in handlePencomRejection: ${event.error || 'Unknown error'}`
              ),
          }),
        },
      },
      target: 'PENDING_PFA_REVIEW',
    },
    VALIDATED: {
      on: {
        COMPUTATION_ANALYSIS: {
          target: 'PROCESSING_COMPUTATION',
          actions: {
            type: 'logEventTrigger',
          },
        },
      },
      description: 'The state after the PENCOM Validator\\\nhas approved the record coming from PFA',
    },
    PROCESSING_COMPUTATION: {
      invoke: {
        src: 'handleComputationAnalysis',
        input: ({ context, event }) => ({
          rsaPin: context.rsaPin,
          ...(event.type === 'COMPUTATION_ANALYSIS' ? { formData: event.formData } : {}),
          accruedBenefitsService: context.accruedBenefitsService,
        }),
        onDone: [
          {
            target: 'COMPUTED',
            guard: ({ event }) => event.output?.code === 1,
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
          {
            target: 'VALIDATED',
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        ],
        onError: {
          target: 'VALIDATED',
          actions: assign({
            output: ({ event }) =>
              new BaseResponseWithContentNoPagination<any>(
                ResponseCodeEnum.ERROR,
                `Error in handleComputationAnalysis: ${event.error || 'Unknown error'}`
              ),
          }),
        },
      },
      target: 'COMPUTED',
    },
    COMPUTED: {
      on: {
        AUDIT_VALIDATOR_REJECTION: {
          target: 'PROCESSING_AUDIT_VALIDATOR_REJECTION',
          actions: ['logEventTrigger', 'updateContextFromEnrolment'],
        },
        AUDIT_VALIDATOR_APPROVED: {
          target: 'PROCESSING_AUDIT_VALIDATOR_APPROVED',
          actions: { type: 'logEventTrigger' },
        },
      },
      description: 'At this point, the Audit Department\\\ngets to review the record and either \\\nAPPROVE or REJECT',
    },
    PROCESSING_AUDIT_VALIDATOR_APPROVED: {
      invoke: {
        src: 'handlePencomApproval',
        input: ({ context, event }) => ({
          rsaPin: context.rsaPin,
          actorEmailAddress: event.actorEmailAddress,
          targetState: RegistrationStatusEnum.PRE_CERTIFIED,
          accruedBenefitsService: context.accruedBenefitsService,
          triggeredBy: event.type,
        }),
        onDone: [
          {
            target: 'PRE_CERTIFIED',
            guard: ({ event }) => event.output?.code === 1,
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
          {
            target: 'COMPUTED',
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        ],
        onError: {
          target: 'COMPUTED',
          actions: assign({
            output: ({ event }) =>
              new BaseResponseWithContentNoPagination<any>(
                ResponseCodeEnum.ERROR,
                `Error in handlePencomApproval: ${event.error || 'Unknown error'}`
              ),
          }),
        },
      },
      target: 'PRE_CERTIFIED',
    },
    PROCESSING_AUDIT_VALIDATOR_REJECTION: {
      invoke: {
        src: 'handlePencomRejection',
        input: ({ context, event }) => ({
          rsaPin: context.rsaPin,
          actorEmailAddress: event.actorEmailAddress,
          targetState: RegistrationStatusEnum.ENROLLED,
          ...(event.type === 'AUDIT_VALIDATOR_REJECTION' ? { comment: event.comment } : {}),
          accruedBenefitsService: context.accruedBenefitsService,
          triggeredBy: event.type,
        }),
        onDone: [
          {
            target: 'ENROLLED',
            guard: ({ event }) => event.output?.code === 1,
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
          {
            target: 'COMPUTED',
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        ],
        onError: {
          target: 'COMPUTED',
          actions: assign({
            output: ({ event }) =>
              new BaseResponseWithContentNoPagination<any>(
                ResponseCodeEnum.ERROR,
                `Error in handlePencomRejection: ${event.error || 'Unknown error'}`
              ),
          }),
        },
      },
      target: 'ENROLLED',
    },
    PRE_CERTIFIED: {
      on: {
        AUDIT_SUPERVISOR_APPROVAL: {
          target: 'PROCESSING_AUDIT_SUPERVISOR_APPROVAL',
          actions: { type: 'logEventTrigger' },
        },
        AUDIT_SUPERVISOR_REJECTION: {
          target: 'PROCESSING_AUDIT_SUPERVISOR_REJECTION',
          actions: ['logEventTrigger', 'updateContextFromEnrolment'],
        },
      },
    },
    PROCESSING_AUDIT_SUPERVISOR_REJECTION: {
      invoke: {
        src: 'handlePencomRejection',
        input: ({ context, event }) => ({
          rsaPin: context.rsaPin,
          actorEmailAddress: event.actorEmailAddress,
          targetState: RegistrationStatusEnum.ENROLLED,
          ...(event.type === 'AUDIT_SUPERVISOR_REJECTION' ? { comment: event.comment } : {}),
          accruedBenefitsService: context.accruedBenefitsService,
          triggeredBy: event.type,
        }),
        onDone: [
          {
            target: 'ENROLLED',
            guard: ({ event }) => event.output?.code === 1,
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
          {
            target: 'PRE_CERTIFIED',
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        ],
        onError: {
          target: 'PRE_CERTIFIED',
          actions: assign({
            output: ({ event }) =>
              new BaseResponseWithContentNoPagination<any>(
                ResponseCodeEnum.ERROR,
                `Error in handlePencomRejection: ${event.error || 'Unknown error'}`
              ),
          }),
        },
      },
      target: 'ENROLLED',
    },
    PROCESSING_AUDIT_SUPERVISOR_APPROVAL: {
      invoke: {
        src: 'handlePencomApproval',
        input: ({ context, event }) => ({
          rsaPin: context.rsaPin,
          actorEmailAddress: event.actorEmailAddress,
          targetState: RegistrationStatusEnum.CERTIFIED,
          accruedBenefitsService: context.accruedBenefitsService,
          triggeredBy: event.type,
        }),
        onDone: [
          {
            target: 'PARALLEL_PROCESSES',
            guard: ({ event }) => event.output?.code === 1,
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
          {
            target: 'PRE_CERTIFIED',
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        ],
        onError: {
          target: 'PRE_CERTIFIED',
          actions: assign({
            output: ({ event }) =>
              new BaseResponseWithContentNoPagination<any>(
                ResponseCodeEnum.ERROR,
                `Error in handlePencomApproval: ${event.error || 'Unknown error'}`
              ),
          }),
        },
      },
      target: 'PARALLEL_PROCESSES',
    },
    PARALLEL_PROCESSES: {
      type: 'parallel',
      on: {
        REQUEST_DOCUMENT_REVIEW: {
          target: 'ENROLLED',
        },
      },
      onDone: {
        target: 'FINALISED',
      },
      states: {
        AR_PROCESS: {
          initial: 'AR_CERTIFIED',
          states: {
            AR_CERTIFIED: {
              on: {
                AR_BATCHING: {
                  target: 'PROCESSING_AR_BATCHING',
                  actions: {
                    type: 'logEventTrigger',
                  },
                },
              },
            },
            PROCESSING_AR_BATCHING: {
              invoke: {
                src: 'handlePencomBatchApproval',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.AR_BATCHED,
                  ...(event.type === 'AR_BATCHING' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'AR_BATCHED',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'AR_CERTIFIED',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'AR_CERTIFIED',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'AR_BATCHED',
            },
            AR_BATCHED: {
              on: {
                AR_REQUEST_EXCO_APPROVAL: {
                  target: 'PROCESSING_AR_REQUEST_EXCO_APPROVAL',
                },
                AR_DELETE_BATCH: {
                  target: 'PROCESSING_AR_DELETE_BATCH',
                },
              },
            },
            PROCESSING_AR_DELETE_BATCH: {
              invoke: {
                src: 'handleDeleteBatch',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.CERTIFIED,
                  triggeredBy: 'AR_DELETE_BATCH',
                  ...(event.type === 'AR_DELETE_BATCH' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'AR_CERTIFIED',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'AR_BATCHED',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'AR_BATCHED',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'AR_CERTIFIED',
            },
            PROCESSING_AR_REQUEST_EXCO_APPROVAL: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.AR_AWAITING_EXCO_APPROVAL,
                  triggeredBy: event.type,
                  ...(event.type === 'AR_REQUEST_EXCO_APPROVAL' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'AR_AWAITING_EXCO_APPROVAL',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'AR_BATCHED',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'AR_BATCHED',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'AR_AWAITING_EXCO_APPROVAL',
            },
            AR_AWAITING_EXCO_APPROVAL: {
              on: {
                AR_EXCO_PAYMENT_APPROVAL: {
                  target: 'PROCESSING_AR_EXCO_PAYMENT_APPROVAL',
                },
              },
            },
            PROCESSING_AR_EXCO_PAYMENT_APPROVAL: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.AR_PAYMENT_APPROVED,
                  triggeredBy: event.type,
                  ...(event.type === 'AR_EXCO_PAYMENT_APPROVAL' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'AR_PAYMENT_APPROVED',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'AR_AWAITING_EXCO_APPROVAL',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'AR_AWAITING_EXCO_APPROVAL',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'AR_PAYMENT_APPROVED',
            },
            AR_PAYMENT_APPROVED: {
              on: {
                AR_REQUEST_ACCOUNT_PAYMENT: {
                  target: 'PROCESSING_AR_REQUEST_ACCOUNT_PAYMENT',
                },
              },
              description: 'State at which exco has approved and his signature has\n\nbeen appended to document',
            },
            PROCESSING_AR_REQUEST_ACCOUNT_PAYMENT: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.AR_PAYMENT_REQUESTED,
                  triggeredBy: event.type,
                  ...(event.type === 'AR_REQUEST_ACCOUNT_PAYMENT' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'AR_PAYMENT_REQUESTED',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'AR_PAYMENT_APPROVED',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'AR_PAYMENT_APPROVED',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'AR_PAYMENT_REQUESTED',
            },
            AR_PAYMENT_REQUESTED: {
              on: {
                AR_PAYMENT_REMITTED: {
                  target: 'PROCESSING_AR_PAYMENT_REMITTED',
                },
              },
              description:
                'State at which account and financial Planning\n\nreceive the request and then get to pay into PFA/PFC',
            },
            PROCESSING_AR_PAYMENT_REMITTED: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.AR_ACCOUNT_REMITTED,
                  triggeredBy: event.type,
                  ...(event.type === 'AR_PAYMENT_REMITTED' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'AR_ACCOUNT_REMITTED',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'AR_PAYMENT_REQUESTED',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'AR_PAYMENT_REQUESTED',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'AR_ACCOUNT_REMITTED',
            },
            AR_ACCOUNT_REMITTED: {
              on: {
                AR_NOTIFY_PFA_PFC: {
                  target: 'PROCESSING_AR_NOTIFY_PFA_PFC',
                },
              },
              description: 'State at which Account/Financial Planning dept \n\nhave made remittance to PFA/PFC',
            },
            PROCESSING_AR_NOTIFY_PFA_PFC: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.AR_AWAITING_PFA_PAYMENT_CONFIRMATION,
                  triggeredBy: event.type,
                  ...(event.type === 'AR_NOTIFY_PFA_PFC' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'AR_AWAITING_PFA_PAYMENT_CONFIRMATION',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'AR_ACCOUNT_REMITTED',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'AR_ACCOUNT_REMITTED',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'AR_AWAITING_PFA_PAYMENT_CONFIRMATION',
            },
            AR_AWAITING_PFA_PAYMENT_CONFIRMATION: {
              on: {
                AR_CONFIRM_PAYMENT: {
                  target: 'PROCESSING_AR_CONFIRM_PAYMENT',
                },
                AR_NON_CONFIRMATION: {
                  target: 'PROCESSING_AR_NON_CONFIRMATION',
                },
              },
            },
            PROCESSING_AR_CONFIRM_PAYMENT: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.AR_PAID,
                  triggeredBy: event.type,
                  ...(event.type === 'AR_CONFIRM_PAYMENT' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'AR_PAID',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'AR_AWAITING_PFA_PAYMENT_CONFIRMATION',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'AR_AWAITING_PFA_PAYMENT_CONFIRMATION',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'AR_PAID',
            },
            PROCESSING_AR_NON_CONFIRMATION: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.AR_AWAITING_PFA_PAYMENT_CONFIRMATION,
                  triggeredBy: event.type,
                  ...(event.type === 'AR_NON_CONFIRMATION' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'AR_AWAITING_PFA_PAYMENT_CONFIRMATION',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'AR_AWAITING_PFA_PAYMENT_CONFIRMATION',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'AR_AWAITING_PFA_PAYMENT_CONFIRMATION',
            },
            AR_PAID: {
              on: {
                AR_COMPLETED: {
                  target: 'PROCESSING_AR_COMPLETED',
                },
              },
            },
            PROCESSING_AR_COMPLETED: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.AR_FINALISED,
                  triggeredBy: event.type,
                  ...(event.type === 'AR_COMPLETED' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'AR_FINALISED',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'AR_PAID',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'AR_PAID',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'AR_FINALISED',
            },
            AR_FINALISED: {
              type: 'final',
            },
          },
        },
        CR_PROCESS: {
          initial: 'CR_CERTIFIED',
          states: {
            CR_CERTIFIED: {
              on: {
                CR_BATCHING: {
                  target: 'PROCESSING_CR_BATCHING',
                  actions: {
                    type: 'logEventTrigger',
                  },
                },
              },
            },
            PROCESSING_CR_BATCHING: {
              invoke: {
                src: 'handlePencomBatchApproval',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.CR_BATCHED,
                  ...(event.type === 'CR_BATCHING' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'CR_BATCHED',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'CR_CERTIFIED',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'CR_CERTIFIED',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'CR_BATCHED',
            },
            CR_BATCHED: {
              on: {
                CR_REQUEST_EXCO_APPROVAL: {
                  target: 'PROCESSING_CR_REQUEST_EXCO_APPROVAL',
                },
                CR_DELETE_BATCH: {
                  target: 'PROCESSING_CR_DELETE_BATCH',
                },
              },
            },
            PROCESSING_CR_DELETE_BATCH: {
              invoke: {
                src: 'handleDeleteBatch',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.CERTIFIED,
                  triggeredBy: 'CR_DELETE_BATCH',
                  ...(event.type === 'CR_DELETE_BATCH' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'CR_CERTIFIED',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'CR_BATCHED',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'CR_BATCHED',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'CR_CERTIFIED',
            },
            PROCESSING_CR_REQUEST_EXCO_APPROVAL: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.CR_AWAITING_EXCO_APPROVAL,
                  triggeredBy: event.type,
                  ...(event.type === 'CR_REQUEST_EXCO_APPROVAL' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'CR_AWAITING_EXCO_APPROVAL',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'CR_BATCHED',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'CR_BATCHED',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'CR_AWAITING_EXCO_APPROVAL',
            },
            CR_AWAITING_EXCO_APPROVAL: {
              on: {
                CR_EXCO_PAYMENT_APPROVAL: {
                  target: 'PROCESSING_CR_EXCO_PAYMENT_APPROVAL',
                },
              },
            },
            PROCESSING_CR_EXCO_PAYMENT_APPROVAL: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.CR_PAYMENT_APPROVED,
                  triggeredBy: event.type,
                  ...(event.type === 'CR_EXCO_PAYMENT_APPROVAL' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'CR_PAYMENT_APPROVED',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'CR_AWAITING_EXCO_APPROVAL',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'CR_AWAITING_EXCO_APPROVAL',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'CR_PAYMENT_APPROVED',
            },
            CR_PAYMENT_APPROVED: {
              on: {
                CR_REQUEST_ACCOUNT_PAYMENT: {
                  target: 'PROCESSING_CR_REQUEST_ACCOUNT_PAYMENT',
                },
              },
              description: 'State at which exco has approved and his signature has\n\nbeen appended to document',
            },
            PROCESSING_CR_REQUEST_ACCOUNT_PAYMENT: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.CR_PAYMENT_REQUESTED,
                  triggeredBy: event.type,
                  ...(event.type === 'CR_REQUEST_ACCOUNT_PAYMENT' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'CR_PAYMENT_REQUESTED',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'CR_PAYMENT_APPROVED',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'CR_PAYMENT_APPROVED',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'CR_PAYMENT_REQUESTED',
            },
            CR_PAYMENT_REQUESTED: {
              on: {
                CR_PAYMENT_REMITTED: {
                  target: 'PROCESSING_CR_PAYMENT_REMITTED',
                },
              },
              description:
                'State at which account and financial Planning\n\nreceive the request and then get to pay into PFA/PFC',
            },
            PROCESSING_CR_PAYMENT_REMITTED: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.CR_ACCOUNT_REMITTED,
                  triggeredBy: event.type,
                  ...(event.type === 'CR_PAYMENT_REMITTED' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'CR_ACCOUNT_REMITTED',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'CR_PAYMENT_REQUESTED',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'CR_PAYMENT_REQUESTED',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'CR_ACCOUNT_REMITTED',
            },
            CR_ACCOUNT_REMITTED: {
              on: {
                CR_NOTIFY_PFA_PFC: {
                  target: 'PROCESSING_CR_NOTIFY_PFA_PFC',
                },
              },
              description: 'State at which Account/Financial Planning dept \n\nhave made remittance to PFA/PFC',
            },
            PROCESSING_CR_NOTIFY_PFA_PFC: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.CR_AWAITING_PFA_PAYMENT_CONFIRMATION,
                  triggeredBy: event.type,
                  ...(event.type === 'CR_NOTIFY_PFA_PFC' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'CR_AWAITING_PFA_PAYMENT_CONFIRMATION',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'CR_ACCOUNT_REMITTED',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'CR_ACCOUNT_REMITTED',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'CR_AWAITING_PFA_PAYMENT_CONFIRMATION',
            },
            CR_AWAITING_PFA_PAYMENT_CONFIRMATION: {
              on: {
                CR_CONFIRM_PAYMENT: {
                  target: 'PROCESSING_CR_CONFIRM_PAYMENT',
                },
                CR_NON_CONFIRMATION: {
                  target: 'PROCESSING_CR_NON_CONFIRMATION',
                },
              },
            },
            PROCESSING_CR_CONFIRM_PAYMENT: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.CR_PAID,
                  triggeredBy: event.type,
                  ...(event.type === 'CR_CONFIRM_PAYMENT' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'CR_PAID',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'CR_AWAITING_PFA_PAYMENT_CONFIRMATION',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'CR_AWAITING_PFA_PAYMENT_CONFIRMATION',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'CR_PAID',
            },
            PROCESSING_CR_NON_CONFIRMATION: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.CR_AWAITING_PFA_PAYMENT_CONFIRMATION,
                  triggeredBy: event.type,
                  ...(event.type === 'CR_NON_CONFIRMATION' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'CR_AWAITING_PFA_PAYMENT_CONFIRMATION',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'CR_AWAITING_PFA_PAYMENT_CONFIRMATION',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'CR_AWAITING_PFA_PAYMENT_CONFIRMATION',
            },
            CR_PAID: {
              on: {
                CR_COMPLETED: {
                  target: 'PROCESSING_CR_COMPLETED',
                },
              },
            },
            PROCESSING_CR_COMPLETED: {
              invoke: {
                src: 'handlePencomBatchApproval2',
                input: ({ context, event }) => ({
                  rsaPin: context.rsaPin,
                  actorEmailAddress: context.actorEmailAddress,
                  accruedBenefitsService: context.accruedBenefitsService,
                  targetState: RegistrationStatusEnum.CR_FINALISED,
                  triggeredBy: event.type,
                  ...(event.type === 'CR_COMPLETED' ? { formData: event.formData } : {}),
                }),
                onDone: [
                  {
                    target: 'CR_FINALISED',
                    guard: ({ event }) => event.output?.code === 1,
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                  {
                    target: 'CR_PAID',
                    actions: assign({
                      output: ({ event }) => event.output,
                    }),
                  },
                ],
                onError: {
                  target: 'CR_PAID',
                  actions: assign({
                    output: ({ event }) =>
                      new BaseResponseWithContentNoPagination<any>(
                        ResponseCodeEnum.ERROR,
                        `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
                      ),
                  }),
                },
              },
              target: 'CR_FINALISED',
            },
            CR_FINALISED: {
              type: 'final',
            },
          },
        },
      },
    },
    FINALISED: {
      exit: {
        type: 'logEventTrigger',
      },
      invoke: {
        src: 'handlePencomBatchApproval2',
        input: ({ context, event }) => ({
          rsaPin: context.rsaPin,
          actorEmailAddress: context.actorEmailAddress,
          accruedBenefitsService: context.accruedBenefitsService,
          targetState: RegistrationStatusEnum.FINALISED,
          triggeredBy: event.type,
          ...(event.type === 'CR_COMPLETED' || event.type === 'AR_COMPLETED' ? { formData: event.formData } : {}),
        }),
        onDone: {
          guard: ({ event }) => event.output?.code === 1,
          actions: assign({
            output: ({ event }) => event.output,
          }),
        },
        onError: {
          actions: assign({
            output: ({ event }) =>
              new BaseResponseWithContentNoPagination<any>(
                ResponseCodeEnum.ERROR,
                `Error in handleEnrolmentProcess: ${event.error || 'Unknown error'}`
              ),
          }),
        },
      },
    },
  },
});
