import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums/ResponseCodeEnum';
import { MultiplePinResolutionService } from 'apps/enrollment-ms/src/services/multiple-pin-resolution.service';
import { assign, fromPromise, setup } from 'xstate';

export type MultiplePinResolutionMachineEvent =
  | { type: 'NDMD_APPROVED'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'INITIATE'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'UPLOAD_TRANSACTION_HISTORY'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'AWAITING_COMPUTATION'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'REQUEST_PAYMENT'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'COMPUTE'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'PAYMENT_CONFIRMED'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'RECONCILE_PAYMENT'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'CANCEL_REQUEST'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'REJECT'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'MEMO_GENERATED'; actorEmailAddress: string; formData: Record<string, any> }
  | { type: 'REJECT_TRANSACTION_HISTORY'; actorEmailAddress: string; formData: Record<string, any> };

export type WorkflowInput = {
  pk: number;
  actorEmailAddress?: string;
  comment?: string;
  formData?: Record<string, any>;
  multiplePinResolutionService: MultiplePinResolutionService;
  currentState: string;
};

export const machine = setup({
  types: {
    context: {} as {
      pk: number;
      multiplePinResolutionService: MultiplePinResolutionService;
      actorEmailAddress?: string;
      comment?: string;
      formData?: Record<string, any>;
      output?: BaseResponseWithContentNoPagination<any>;
    },
    events: {} as MultiplePinResolutionMachineEvent,
    input: {} as WorkflowInput,
  },
  actions: {
    logEventTrigger: ({ event }) => {
      console.log(`${event.type} event triggered <<<<<<<<<>>>>>>>>>>>>`);
    },
  },
  actors: {
    sendEmailToNDMD: fromPromise(async ({ input }: { input: WorkflowInput }) => {
      const { multiplePinResolutionService, formData } = input;
      await multiplePinResolutionService.notifyNDMDUsers(formData);
      return new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS, null);
    }),
    notifyHODOfApproval: fromPromise(async ({ input }: { input: WorkflowInput }) => {
      const { multiplePinResolutionService, formData } = input;
      await multiplePinResolutionService.notifyHODOfApproval(formData);
      return new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
    }),
    notifyHODOfRejection: fromPromise(async ({ input }: { input: WorkflowInput }) => {
      const { multiplePinResolutionService, formData } = input;
      await multiplePinResolutionService.notifyHODOfRejection(formData);
      return new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
    }),
    notifyHodOfTransactionHistoryUpload: fromPromise(async ({ input }: { input: WorkflowInput }) => {
      const { multiplePinResolutionService, formData } = input;
      await multiplePinResolutionService.notifyHODOfTransactionHistoryUpload(formData);
      return new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
    }),
    requestTransactionHistory: fromPromise(async ({ input }: { input: WorkflowInput }) => {
      const { multiplePinResolutionService, formData } = input;
      await multiplePinResolutionService.notifyPfaOfMemoGeneration(formData);
      return new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
    }),
    notifyPencomOfComputation: fromPromise(async ({ input }: { input: WorkflowInput }) => {
      const { multiplePinResolutionService, formData } = input;
      await multiplePinResolutionService.notifyPencomOfComputation(formData);
      return new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
    }),
    notifyPencomOfAllPaymentsConfirmed: fromPromise(async ({ input }: { input: WorkflowInput }) => {
      const { multiplePinResolutionService, formData } = input;
      await multiplePinResolutionService.notifyPencomOfAllPaymentsConfirmed(formData);
      return new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
    }),
    notifyPfaOfPaymentRequest: fromPromise(async ({ input }: { input: WorkflowInput }) => {
      const { multiplePinResolutionService, formData } = input;
      await multiplePinResolutionService.notifyPfaOfPaymentRequest(formData);
      return new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
    }),
    updateStatus: fromPromise(async ({ input }: { input: WorkflowInput }) => {
      const { multiplePinResolutionService } = input;
      const pk = input.pk;

      await multiplePinResolutionService.updateStatus(pk, input.currentState, {
        validPin: input.formData?.req?.validPin,
        comment: input.comment,
      });

      return new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS, { status: input.currentState });
    }),
    uploadDocumentAndStatus: fromPromise(async ({ input }: { input: WorkflowInput }) => {
      /* ... */
      console.log('uploadDocumentAndStatus invoked with input:', input);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }),
    rejectTransactionHistory: fromPromise(async ({ input }: { input: WorkflowInput }) => {
      const { multiplePinResolutionService, formData } = input;
      await multiplePinResolutionService.rejectTransactionHistory(formData);
      return new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
    }),
  },
}).createMachine({
  context: ({ input }: { input: WorkflowInput }) => ({
    pk: input.pk,
    actorEmailAddress: input.actorEmailAddress,
    multiplePinResolutionService: input.multiplePinResolutionService,
    comment: input.comment,
    formData: input.formData,
  }),
  id: 'Multiple PIN  resolution',
  initial: 'INITIATED',
  states: {
    INITIATED: {
      on: {
        INITIATE: {
          target: 'PENDING_NDMD',
          actions: ['logEventTrigger'],
        },
      },
    },
    PENDING_NDMD: {
      on: {
        NDMD_APPROVED: {
          target: 'REVIEWED',
          actions: ['logEventTrigger'],
        },
        CANCEL_REQUEST: {
          target: 'CANCELLING_REQUEST',
          actions: ['logEventTrigger'],
        },
        REJECT: {
          target: 'NOTIFYING_HOD_OF_REJECTION',
          actions: ['logEventTrigger'], // Direct reference to the action
        },
      },
      invoke: [
        {
          src: 'updateStatus',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'PENDING_NDMD',
          }),
          onDone: {
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        },
        {
          src: 'sendEmailToNDMD',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'PENDING_NDMD',
          }),
          onDone: {
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        },
      ],
    },
    REVIEWED: {
      on: {
        MEMO_GENERATED: {
          target: 'PENDING_PFA_UPLOAD',
          actions: ['logEventTrigger'],
        },
      },
      invoke: [
        {
          src: 'notifyHODOfApproval',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'REVIEWED',
          }),
        },
        {
          src: 'updateStatus',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'REVIEWED',
          }),
          onDone: {
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        },
      ],
    },
    PENDING_PFA_UPLOAD: {
      entry: ['logEventTrigger'],
      on: {
        UPLOAD_TRANSACTION_HISTORY: {
          target: 'AWAITING_COMPUTATION',
          actions: ['logEventTrigger'],
        },
      },
      invoke: [
        {
          src: 'requestTransactionHistory',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'PENDING_PFA_UPLOAD',
          }),
        },
        {
          src: 'updateStatus',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'PENDING_PFA_UPLOAD',
          }),
          onDone: {
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        },
      ],
    },
    CANCELLING_REQUEST: {
      entry: ['logEventTrigger'],
      invoke: [
        {
          src: 'updateStatus',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'CANCELLED',
          }),
          onDone: {
            target: 'CANCELLED',
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        },
      ],
    },
    CANCELLED: {
      type: 'final',
      guard: ({ context }) => context.output?.code === 1,
      actions: ['logEventTrigger'],
    },
    NOTIFYING_HOD_OF_REJECTION: {
      entry: ['logEventTrigger'],
      invoke: [
        {
          src: 'notifyHODOfRejection',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'NDMD_REJECTED',
          }),
          onDone: {
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        },
        {
          src: 'updateStatus',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'NDMD_REJECTED',
          }),
          onDone: {
            target: 'NDMD_REJECTED',
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        },
      ],
    },
    NDMD_REJECTED: {
      type: 'final',
      guard: ({ context }) => context.output?.code === 1,
      actions: ['logEventTrigger'],
    },
    AWAITING_COMPUTATION: {
      on: {
        COMPUTE: {
          target: 'COMPUTED',
          actions: ['logEventTrigger'],
        },
      },
      invoke: [
        {
          src: 'notifyHodOfTransactionHistoryUpload',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'AWAITING_COMPUTATION',
          }),
        },
        {
          src: 'updateStatus',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'AWAITING_COMPUTATION',
          }),
          onDone: {
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        },
      ],
    },
    COMPUTED: {
      on: {
        REJECT_TRANSACTION_HISTORY: {
          target: 'REJECTING_TRANSACTION_HISTORY',
          actions: ['logEventTrigger'],
        },
        REQUEST_PAYMENT: {
          target: 'PENDING_PAYMENT_CONFIRMATION',
          actions: ['logEventTrigger'],
        },
      },
      invoke: [
        {
          src: 'updateStatus',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'COMPUTED',
          }),
          onDone: {
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        },
        {
          src: 'notifyPencomOfComputation',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'COMPUTED',
          }),
        },
      ],
    },
    REJECTING_TRANSACTION_HISTORY: {
      entry: ['logEventTrigger'],
      invoke: [
        {
          src: 'updateStatus',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'PENDING_PFA_UPLOAD',
          }),
          onDone: {
            target: 'PENDING_PFA_UPLOAD',
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        },
        {
          src: 'rejectTransactionHistory',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'PENDING_PFA_UPLOAD',
          }),
        },
      ],
    },
    REJECTED_TRANSACTION_HISTORY: {
      type: 'final',
      guard: ({ context }) => context.output?.code === 1,
      actions: ['logEventTrigger'],
    },
    PENDING_PAYMENT_CONFIRMATION: {
      on: {
        PAYMENT_CONFIRMED: {
          target: 'PROCESSING_PAYMENT_CONFIRMATION',
          actions: ['logEventTrigger'],
        },
      },
      invoke: [
        {
          src: 'notifyPfaOfPaymentRequest',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'PENDING_PAYMENT_CONFIRMATION',
          }),
        },
        {
          src: 'updateStatus',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'PENDING_PAYMENT_CONFIRMATION',
          }),
          onDone: {
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        },
      ],
    },
    PROCESSING_PAYMENT_CONFIRMATION: {
      on: {
        RECONCILE_PAYMENT: {
          target: 'RECONCILIATION_COMPLETED',
          actions: ['logEventTrigger'],
        },
      },
      invoke: [
        {
          src: 'notifyPencomOfAllPaymentsConfirmed',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'PENDING_RECONCILIATION',
          }),
        },
        {
          src: 'updateStatus',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'PENDING_RECONCILIATION',
          }),
        },
      ],
    },
    RECONCILIATION_COMPLETED: {
      entry: ['logEventTrigger'],
      invoke: [
        {
          src: 'updateStatus',
          input: ({ context }) => ({
            pk: context.pk,
            actorEmailAddress: context.actorEmailAddress,
            multiplePinResolutionService: context.multiplePinResolutionService,
            formData: context.formData,
            comment: context.comment,
            currentState: 'RECONCILED',
          }),
          onDone: {
            target: 'RECONCILED',
            actions: assign({
              output: ({ event }) => event.output,
            }),
          },
        },
      ],
    },
    RECONCILED: {
      type: 'final',
      guard: ({ context }) => context.output?.code === 1,
      actions: ['logEventTrigger'],
    },
  },
});
