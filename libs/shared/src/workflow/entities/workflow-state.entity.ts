import { Column, Entity, Unique } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';

@Entity('WORKFLOW_STATE')
@Unique(['uniqueId', 'processType'])
export class WorkflowState extends AbstractEntity<WorkflowState> {
  @Column({ name: 'UNIQUE_ID', nullable: false })
  uniqueId: string;

  @Column({ name: 'PROCESS_TYPE', nullable: false })
  processType: string;

  @Column({ name: 'EVENT_LOG', type: 'clob', nullable: true })
  eventLog: string;

  @Column({ name: 'CURRENT_STATE', nullable: false, type: 'clob' })
  currentState: string;
}
