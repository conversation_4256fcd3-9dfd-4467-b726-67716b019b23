import { Module } from '@nestjs/common';
import { DatabaseModule, LoggerModule } from '@app/shared';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisCacheModule } from '@app/shared/cache';
import { SettingMsLibRepository } from '@app/shared/setting-service/setting-ms-lib.repository';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { Setting } from '@app/shared/setting-service/entities/setting.entity';
import { WorkflowService } from '@app/shared/workflow/services/workflow.service';
import { WorkflowStateRepository } from '@app/shared/workflow/repository/workflow-state.repository';
import { WorkflowState } from '@app/shared/workflow/entities/workflow-state.entity';
import { AccruedBenefitsWorkflowService } from '@app/shared/workflow/services/accrued-benefits-workflow.service';
import { ClientProxyFactory, Transport } from '@nestjs/microservices';
import { SettingMsLibModule } from '@app/shared/setting-service/setting-ms-lib.module';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { EnrolmentSummary } from '@app/shared/enrollment-service/entities/enrolment-summary.entity';
import { EnrolmentDraftRepository } from '@app/shared/enrollment-service/repository/enrolment-draft.repository';
import { EnrolmentDraft } from '@app/shared/enrollment-service/entities/enrolment-draft.entity';
import { FormValidatorService } from '@app/shared/enrollment-service/services/form-validator.service';
import { TbeOrganisationsRepository } from '@app/shared/enrollment-service/repository/tbe-organisations.repository';
import { TbeOrganisations } from '@app/shared/enrollment-service/entities/tbe-organisations.entity';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { Pfa } from '@app/shared/user-service/entities/pfa.entity';
import { MdaRepository } from '@app/shared/user-service/repositories/mda.repository';
import { Mda } from '@app/shared/user-service/entities/mda.entity';
import { EnrolmentBiodataRepository } from '@app/shared/enrollment-service/repository/enrolment-biodata.repository';
import { EnrolmentBiodata } from '@app/shared/enrollment-service/entities/enrolment-biodata.entity';
import { EnrolmentComment } from '@app/shared/enrollment-service/entities/enrolment-comment.entity';
import { EnrolmentCommentRepository } from '@app/shared/enrollment-service/repository/enrolment-comment.repository';
import { MultiplePinResolutionService } from 'apps/enrollment-ms/src/services/multiple-pin-resolution.service';
import { MultiplePinResolutionRequestRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-req.repository';
import { MultiplePinResolutionWorkFlowService } from '@app/shared/workflow/services/multiple-pin-resolution-workflow.service';
import { MultiplePinResolutionRequest } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-req.entity';
import { WorkflowUtils } from '@app/shared/workflow/utils/workflow-utils';
import { BatchRepository } from '@app/shared/enrollment-service/repository/batch.repository';
import { BatchRecordRepository } from '@app/shared/enrollment-service/repository/batch-record.repository';
import { TblBatch } from '@app/shared/enrollment-service/entities/tbl-batch';
import { BatchRecord } from '@app/shared/enrollment-service/entities/batch-record';
import { MemoService } from '@app/shared/enrollment-service/services/memo.service';
import { SlipService } from '@app/shared/enrollment-service/services/slip.service';
import { SerialNumberService } from '@app/shared/enrollment-service/services/serial-number.service';
import { SerialNumber } from '@app/shared/enrollment-service/entities/serial-number.entity';
import { BatchDocumentRepository } from '@app/shared/enrollment-service/repository/batch-document.repository';
import { AccruedRightsResultRepository } from '@app/shared/dto/enrollment/repositories/accrued-rights-result.repository';
import { BatchDocuments } from '@app/shared/enrollment-service/entities/batch-documents.entity';
import { EnrolmentDetailRepository } from '@app/shared/enrollment-service/repository/enrolment-detail.repository';
import { EmploymentDetail } from '@app/shared/enrollment-service/entities/employment-detail.entity';
import { AccruedRightsResult } from '@app/shared/dto/enrollment/entities/accrued-rights-result.entity';
import { MultiplePinResolutionDocumentRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-document.repository';
import { MultiplePinResolutionDocument } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-document.entity';
import { TbcBatchAccountMemoDetailsRepository } from '@app/shared/enrollment-service/repository/tbc-batch-account-memo-details.repository';
import { TbcBatchAccountMemoDetails } from '@app/shared/enrollment-service/entities/tbc-batch-account-memo-details.entity';
import { Pfc } from '@app/shared/user-service/entities/pfc.entity';
import { PfcRepository } from '@app/shared/user-service/repositories/pfc.repository';
import { MultiplePinResolutionPinRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-pin.repository';
import { MultiplePinResolutionPin } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-pin.entity';
import { MultiplePinResolutionTransactionHistoryRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-transaction.repository';
import { MultiplePinResolutionTransactionHistory } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-transaction.entity';
import { NrWorkflowService } from '@app/shared/workflow/services/nr-workflow.service';
import { NominalRollWorkflowService } from '@app/shared/workflow/services/nominal-roll-workflow.service';
import { NominalRollRepository } from '@app/shared/enrollment-service/repository/nominal-roll.repository';
import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';
import { NominalRollBatchRepository } from '@app/shared/enrollment-service/repository/nominal-roll-batch.repository';
import { TbcNominalRollBatch } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch';
import { TbcNominalRollBatchRecord } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch-record';
import { NominalRollBatchDocuments } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch-documents.entity';
import { TbcNominalRollBatchRecordRepository } from '@app/shared/enrollment-service/repository/tbc-nominal-roll-batch-record.repository';
import { NrBatchDocumentRepository } from '@app/shared/enrollment-service/repository/nr-batch-document.repository';
import { TbcBatchNrAccountMemoDetailsRepository } from '@app/shared/enrollment-service/repository/tbc-batch-nr-account-memo-details.repository';
import { TbcBatchNrAccountMemoDetails } from '@app/shared/enrollment-service/entities/tbc-batch-nr-account-memo-details.entity';
import { NominalRollCommentRepository } from '@app/shared/enrollment-service/repository/nominal-roll-comment.repository';
import { NominalRollComment } from '@app/shared/enrollment-service/entities/nominal-roll-comment.entity';
import { NominalRollBandRepository } from '@app/shared/enrollment-service/repository/nominal-roll-band.repository';
import { NominalRollBand } from '@app/shared/enrollment-service/entities/nominal-roll-band.entity';
import { AmendmentRequestRepository } from '@app/shared/enrollment-service/repositories/amendment-request.repository';
import { AmendmentRequest } from '@app/shared/enrollment-service/entities/amendment-request.entity';
import { WorkflowHelperService } from '@app/shared/workflow/services/workflow-helper.service';
import { TbcNominalRollPfaMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-nominal-roll-pfa-memo-document-repository.service';
import { TbcNominalRollPfaMemoDocument } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-pfa-memo-document';
import { TbcNominalRollPfcMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-nominal-roll-pfc-memo-document-repository.service';
import { TbcNominalRollPfcMemoDocument } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-pfc-memo-document';
import { MdaEmployeeBiodataRepository } from '@app/shared/login-service/repositories/mda-employee-biodata.repository';
import { MdaEmployeeBiodata } from '@app/shared/login-service/entities/employee-biodata.entity';
import { TbcEnrollmentPfaMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-enrollment-pfa-memo-document-repository.service';
import { TbcEnrollmentPfcMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-enrollment-pfc-memo-document-repository.service';
import { TbcEnrollmentPfcMemoDocument } from '@app/shared/enrollment-service/entities/tbc-enrollment-pfc-memo-document';
import { TbcEnrollmentPfaMemoDocument } from '@app/shared/enrollment-service/entities/tbc-enrollment-pfa-memo-document';
import { ExcelPfaScheduleService } from '@app/shared/enrollment-service/services/excel-pfa-schedule.service';
import { TbcReconcilePay } from '@app/shared/enrollment-service/entities/tbc-reconcile-pay.entity';
import { TbcReconcilePayRepository } from '@app/shared/enrollment-service/repository/tbc-reconcile-pay.repository';

@Module({
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([
      Pfa,
      Pfc,
      Mda,
      Setting,
      TblBatch,
      CobraUser,
      BatchRecord,
      NominalRoll,
      SerialNumber,
      WorkflowState,
      EnrolmentDraft,
      BatchDocuments,
      EmploymentDetail,
      EnrolmentSummary,
      TbeOrganisations,
      EnrolmentBiodata,
      EnrolmentComment,
      AmendmentRequest,
      MdaEmployeeBiodata,
      AccruedRightsResult,
      NominalRollComment,
      NominalRollBand,
      TbcNominalRollBatch,
      TbcNominalRollBatchRecord,
      NominalRollBatchDocuments,
      TbcBatchAccountMemoDetails,
      MultiplePinResolutionRequest,
      TbcEnrollmentPfaMemoDocument,
      TbcEnrollmentPfcMemoDocument,
      TbcNominalRollPfaMemoDocument,
      TbcNominalRollPfcMemoDocument,
      TbcBatchNrAccountMemoDetails,
      MultiplePinResolutionDocument,
      MultiplePinResolutionPin,
      MultiplePinResolutionTransactionHistory,
      TbcReconcilePay,
    ]),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    LoggerModule,
    RedisCacheModule.register(),
    SettingMsLibModule,
  ],
  providers: [
    WorkflowUtils,
    MemoService,
    ExcelPfaScheduleService,
    SlipService,
    WorkflowService,
    NrWorkflowService,
    NominalRollWorkflowService,
    RedisCacheService,
    SettingMsLibService,
    SerialNumberService,
    SettingMsLibService,
    FormValidatorService,
    WorkflowHelperService,
    MultiplePinResolutionService,
    AccruedBenefitsWorkflowService,
    MultiplePinResolutionWorkFlowService,
    PfaRepository,
    PfcRepository,
    MdaRepository,
    BatchRepository,
    CobraUserRepository,
    BatchRecordRepository,
    NominalRollRepository,
    SettingMsLibRepository,
    BatchDocumentRepository,
    WorkflowStateRepository,
    EnrolmentDraftRepository,
    EnrolmentDetailRepository,
    NominalRollBandRepository,
    NrBatchDocumentRepository,
    NominalRollBatchRepository,
    EnrolmentSummaryRepository,
    TbeOrganisationsRepository,
    EnrolmentBiodataRepository,
    AmendmentRequestRepository,
    EnrolmentCommentRepository,
    MdaEmployeeBiodataRepository,
    NominalRollCommentRepository,
    AccruedRightsResultRepository,
    TbcNominalRollBatchRecordRepository,
    TbcBatchAccountMemoDetailsRepository,
    TbcEnrollmentPfaMemoDocumentRepository,
    TbcEnrollmentPfcMemoDocumentRepository,
    TbcNominalRollPfaMemoDocumentRepository,
    TbcNominalRollPfcMemoDocumentRepository,
    TbcBatchNrAccountMemoDetailsRepository,
    MultiplePinResolutionRequestRepository,
    MultiplePinResolutionDocumentRepository,
    MultiplePinResolutionPinRepository,
    MultiplePinResolutionTransactionHistoryRepository,
    TbcReconcilePayRepository,
    {
      provide: 'NOTIFICATION_SERVICE_CLIENT',
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: configService.get('NOTIFICATION_SERVICE_HOST', '127.0.0.1'),
            port: configService.get('NOTIFICATION_SERVICE_TCP_PORT', 3008),
          },
        });
      },
    },
    {
      provide: 'ECRS_SERVICE_CLIENT',
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: configService.get('ECRS_SERVICE_HOST', '0.0.0.0'),
            port: configService.get('ECRS_SERVICE_TCP_PORT', 3017),
          },
        });
      },
    },
  ],
  exports: [
    WorkflowUtils,
    WorkflowService,
    NrWorkflowService,
    WorkflowHelperService,
    NominalRollWorkflowService,
    AccruedBenefitsWorkflowService,
    ExcelPfaScheduleService,
    WorkflowStateRepository,
    MultiplePinResolutionRequestRepository,
    NrBatchDocumentRepository,
    NominalRollBatchRepository,
    TbcNominalRollBatchRecordRepository,
    TbcNominalRollPfaMemoDocumentRepository,
    TbcNominalRollPfcMemoDocumentRepository,
    TbcBatchNrAccountMemoDetailsRepository,
    TbcEnrollmentPfaMemoDocumentRepository,
    TbcEnrollmentPfcMemoDocumentRepository,
    NominalRollBandRepository,
    AmendmentRequestRepository,
    MdaEmployeeBiodataRepository,
    NominalRollCommentRepository,
    MultiplePinResolutionPinRepository,
    MultiplePinResolutionWorkFlowService,
    'NOTIFICATION_SERVICE_CLIENT',
  ],
})
export class WorkflowModule {}
