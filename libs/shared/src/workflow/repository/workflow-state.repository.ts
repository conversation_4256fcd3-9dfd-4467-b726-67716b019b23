import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { WorkflowState } from '@app/shared/workflow/entities/workflow-state.entity';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class WorkflowStateRepository extends AbstractRepository<WorkflowState> {
  constructor(
    @InjectRepository(WorkflowState)
    settingRepository: Repository<WorkflowState>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(settingRepository, entityManager);
  }
}
