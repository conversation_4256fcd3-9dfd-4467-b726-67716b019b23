/* eslint-disable */
import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { AuditInterceptor } from './interceptors/audit.interceptor';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuditProducer } from './producers/audit.producer';
import { AUDIT_QUEUE_NAME } from '@app/shared/constants';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    ClientsModule.registerAsync([
      {
        name: 'AUDIT_LOG_SERVICE',
        useFactory: (configService: ConfigService) => ({
          transport: Transport.RMQ,
          options: {
            urls: [configService.get<string>('RABBIT_MQ_URL')],
            queue: AUDIT_QUEUE_NAME,
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  providers: [AuditInterceptor, AuditProducer],
  exports: [AuditInterceptor, AuditProducer],
})
export class AuditLibModule {}
