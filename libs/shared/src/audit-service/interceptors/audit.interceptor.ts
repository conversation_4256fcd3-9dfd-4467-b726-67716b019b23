/* eslint-disable */
import { Call<PERSON>and<PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Reflector } from '@nestjs/core';
import {
  AUDIT_EVENT_KEY,
  AUDIT_METADATA_EXTRACTOR_KEY,
  AuditMetadataExtractor,
} from '@app/shared/decorators/audit.decorator';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { AuditProducer } from '../producers/audit.producer';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { extractAuditMetadata } from '@app/shared/utils/extract-audit-metadata';
import { CustomException } from '@app/shared/filters/exception.dto';

@Injectable()
export class AuditInterceptor implements NestInterceptor {
  constructor(
    private readonly reflector: Reflector,
    private readonly auditProducer: AuditProducer
  ) {}

  intercept(context: ExecutionContext, next: <PERSON><PERSON>and<PERSON>): Observable<any> {
    const eventType = this.reflector.get<AuditEventTypeEnum>(AUDIT_EVENT_KEY, context.getHandler());
    const metadataExtractor = this.reflector.get<AuditMetadataExtractor>(
      AUDIT_METADATA_EXTRACTOR_KEY,
      context.getHandler()
    );

    if (!eventType) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest<ICustomRequest>();
    const params = context.switchToHttp().getRequest().params;

    request.metadata = extractAuditMetadata(request);

    // If a metadata extractor is provided, extract the extra data and add it directly to request.metadata
    if (metadataExtractor) {
      const extractedData = metadataExtractor(request, params);
      if (extractedData && extractedData.extra) {
        request.metadata.extra = extractedData.extra;
      }
    }

    return next.handle().pipe(
      tap({
        next: (response: any) => {
          let code = 1;
          let description = 'success';
          /* 
            To avoid a breaking change 
          */
          if (response) {
            code = response['code'];
            description = response['description'];
            if (code === undefined) {
              code = 1;
              description = 'success';
            }
          }

          this.auditProducer.log(eventType, request, code < 1 ? 'failure' : 'success', request.metadata, description);
        },
        error: (err) => {
          if (!request?.user) {
            // audit will not log without a user
            return;
          }
          let message = 'Error occurred while processing request';

          if (err instanceof CustomException) {
            message = err.message;
          }

          this.auditProducer.log(eventType, request, 'failure', request.metadata, message);
        },
      })
    );
  }
}
