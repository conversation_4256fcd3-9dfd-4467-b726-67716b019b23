import { AbstractRepository } from '@app/shared/database';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { AuditLog } from '../entities/audit-logs.entity';
import { PinoLogger } from 'nestjs-pino';

Injectable();
export class AuditLogRepository extends AbstractRepository<AuditLog> {
  constructor(
    @InjectRepository(AuditLog) auditLogRepository: Repository<AuditLog>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(auditLogRepository, entityManager);
  }
}
