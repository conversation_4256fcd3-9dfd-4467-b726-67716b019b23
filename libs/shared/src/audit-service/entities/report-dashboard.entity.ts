import { AbstractEntity } from '@app/shared/database';
import { AfterLoad, BeforeInsert, BeforeUpdate, Column, Entity } from 'typeorm';

@Entity('COBRA_REPORT_DASHBOARD')
export class CobraReportDashboard extends AbstractEntity<CobraReportDashboard> {
  @Column({
    name: 'RESOURCE_TYPE',
    type: 'varchar2',
    length: 50,
    unique: true,
    nullable: false,
  })
  resourceType: string;

  @Column({
    name: 'RESOURCE_CONFIG',
    type: 'clob',
    nullable: false,
  })
  resourceConfig: string;

  @Column({
    name: 'MDA_PARAMS',
    type: 'clob',
    nullable: true,
  })
  mdaParams: string;

  @Column({
    name: 'PFA_PARAMS',
    type: 'clob',
    nullable: true,
  })
  pfaParams: string;

  @Column({
    name: 'PENCOM_PARAMS',
    type: 'clob',
    nullable: true,
  })
  pencomParams: string;

  @BeforeInsert()
  @BeforeUpdate()
  stringifyFields() {
    if (this.resourceConfig && typeof this.resourceConfig === 'object') {
      this.resourceConfig = JSON.stringify(this.resourceConfig);
    }
    if (this.mdaParams && typeof this.mdaParams === 'object') {
      this.mdaParams = JSON.stringify(this.mdaParams);
    }
    if (this.pfaParams && typeof this.pfaParams === 'object') {
      this.pfaParams = JSON.stringify(this.pfaParams);
    }
    if (this.pencomParams && typeof this.pencomParams === 'object') {
      this.pencomParams = JSON.stringify(this.pencomParams);
    }
  }

  @AfterLoad()
  parseFields() {
    if (this.resourceConfig && typeof this.resourceConfig === 'string') {
      try {
        this.resourceConfig = JSON.parse(this.resourceConfig);
      } catch (e) {
        console.error('Error parsing resource JSON:', e);
      }
    }
    if (this.mdaParams && typeof this.mdaParams === 'string') {
      try {
        this.mdaParams = JSON.parse(this.mdaParams);
      } catch (e) {
        console.error('Error parsing MDA params JSON:', e);
      }
    }
    if (this.pfaParams && typeof this.pfaParams === 'string') {
      try {
        this.pfaParams = JSON.parse(this.pfaParams);
      } catch (e) {
        console.error('Error parsing PFA params JSON:', e);
      }
    }
    if (this.pencomParams && typeof this.pencomParams === 'string') {
      try {
        this.pencomParams = JSON.parse(this.pencomParams);
      } catch (e) {
        console.error('Error parsing PENCOM params JSON:', e);
      }
    }
  }

  constructor(entity: Partial<CobraReportDashboard>) {
    super(entity);
  }
}
