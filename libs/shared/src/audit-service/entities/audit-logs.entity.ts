/* eslint-disable */
import { AbstractEntity } from '@app/shared/database';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { AfterLoad, BeforeInsert, BeforeUpdate, Column, Entity } from 'typeorm';

@Entity('AUDIT_LOGS')
export class AuditLog extends AbstractEntity<AuditLog> {
  @Column({
    type: 'varchar2',
    length: 50,
    name: 'EVENT_TYPE',
  })
  eventType: AuditEventTypeEnum;

  @Column({
    type: 'varchar2',
    length: 36,
    nullable: true,
    name: 'USER_ID',
  })
  userId: string;

  @Column({
    type: 'varchar2',
    length: 45,
    name: 'IP_ADDRESS',
  })
  ipAddress: string;

  @Column({
    type: 'varchar2',
    length: 20,
    nullable: true,
    name: 'MDA_CODE',
  })
  mdaCode: string;

  @Column({
    type: 'varchar2',
    length: 10,
    nullable: true,
    name: 'PFA_CODE',
  })
  pfaCode: string;

  @Column({
    type: 'clob',
    nullable: true,
    name: 'METADATA',
  })
  metadata: string;

  @Column({
    type: 'timestamp',
    precision: 6,
    name: 'EVENT_TIMESTAMP',
    default: () => 'CURRENT_TIMESTAMP',
  })
  timestamp: Date;

  @Column({
    type: 'varchar2',
    length: 10,
    name: 'STATUS',
  })
  status: 'success' | 'failure';

  @Column({
    type: 'varchar2',
    length: 1024,
    nullable: true,
    name: 'DESCRIPTION',
  })
  description: string;

  @BeforeInsert()
  @BeforeUpdate()
  stringifyMetadata() {
    if (this.metadata && typeof this.metadata === 'object') {
      this.metadata = JSON.stringify(this.metadata);
    }
  }

  @AfterLoad()
  parseMetadata() {
    if (this.metadata && typeof this.metadata === 'string') {
      try {
        this.metadata = JSON.parse(this.metadata);
      } catch (e) {
        console.error('Error parsing metadata JSON:', e);
      }
    }
  }
}
