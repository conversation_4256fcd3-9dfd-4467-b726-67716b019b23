import { AbstractRepository } from '@app/shared/database';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { SecurityQuestion } from '@app/shared/login-service/entities/security-question.entity';
import { PinoLogger } from 'nestjs-pino';

Injectable();
export class SecurityQuestionsRepository extends AbstractRepository<SecurityQuestion> {
  constructor(
    @InjectRepository(SecurityQuestion)
    securityQuestionRepository: Repository<SecurityQuestion>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(securityQuestionRepository, entityManager);
  }
}
