import { AbstractRepository } from '@app/shared/database';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { SecurityQuestionAnswer } from '@app/shared/login-service/entities/security-question-answer.entity';
import { PinoLogger } from 'nestjs-pino';

Injectable();
export class SecurityQuestionsAnswerRepository extends AbstractRepository<SecurityQuestionAnswer> {
  constructor(
    @InjectRepository(SecurityQuestionAnswer)
    securityQuestionRepository: Repository<SecurityQuestionAnswer>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(securityQuestionRepository, entityManager);
  }
}
