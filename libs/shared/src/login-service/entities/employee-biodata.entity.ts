import { AbstractEntity } from '@app/shared/database';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';

@Entity({ name: 'TBL_MDA_EMPLOYEE_BIODATA' })
export class MdaEmployeeBiodata extends AbstractEntity<MdaEmployeeBiodata> {
  @Column({ name: 'RSAPIN', type: 'varchar', unique: true, nullable: true })
  rsaPin: string;

  @Column({ name: 'FIRSTNAME', type: 'varchar' })
  firstName: string;

  @Column({ name: 'SURNAME', type: 'varchar', length: 30 })
  surname: string;

  @Column({ name: 'STAFF_ID', type: 'varchar' })
  staffId: string;

  @Column({
    name: 'GENDER',
    type: 'varchar2',
    length: 1,
    transformer: {
      to: (value: boolean | string): string => {
        if (typeof value === 'boolean') {
          return value ? 'F' : 'M';
        }
        return value;
      },
      from: (value: string): boolean => value === 'F',
    },
  })
  gender: string;

  @Column({ name: 'MDA', type: 'varchar' })
  mdaCode: string;

  @Column({ name: 'PFA', type: 'varchar' })
  pfaCode: string;

  @Column({ name: 'MDA_NAME', type: 'varchar', nullable: true })
  mdaName: string;

  @Column({ name: 'PFA_NAME', type: 'varchar', nullable: true })
  pfaName: string;

  @Column({ name: 'DATE_OF_DEATH', type: 'date', nullable: true })
  dateOfDeath: Date | null;

  @Column({
    name: 'DATE_OF_TRANSFER_OF_SERVICE',
    type: 'date',
    nullable: true,
  })
  dts: Date | null;

  @Column({ name: 'EXPECTED_DATE_OF_RETIREMENT', type: 'date' })
  edor: Date;

  @Column({ name: 'DATE_OF_BIRTH', type: 'date' })
  dateOfBirth: Date;

  @Column({ name: 'DATE_OF_FIRST_APPOINTMENT', type: 'date', nullable: true })
  dofa: Date;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: false })
  @JoinColumn({ name: 'CREATED_BY', referencedColumnName: 'pk' })
  createdBy: CobraUser;

  @Column({ name: 'EMPLOYER_JUNE_2004', type: 'varchar', nullable: true })
  employerJune2004: string;

  @Column({ name: 'SALARY_STRUCTURE_JUNE_2004', type: 'varchar', length: 50, nullable: true })
  salaryStructureJune2004: string;

  @Column({ name: 'GRADE_LEVEL_JUNE_2004', type: 'varchar', length: 10, nullable: true })
  gradeLevelJune2004: string;

  @Column({ name: 'STEP_JUNE_2004', type: 'varchar', length: 10, nullable: true })
  stepJune2004: string;

  @Column({ name: 'RETIREE_USER_TYPE', nullable: false })
  retireeUserType: string;
}
