import { AbstractEntity } from '@app/shared/database';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { <PERSON>umn, <PERSON>tity, JoinColumn, ManyToOne } from 'typeorm';

@Entity({ name: 'SECURITY_QUESTION_ANSWER' })
export class SecurityQuestionAnswer extends AbstractEntity<SecurityQuestionAnswer> {
  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk)
  @JoinColumn({ name: 'COBRA_USER_ID', referencedColumnName: 'pk' })
  cobraUser: CobraUser;

  @Column({ name: 'QUESTION', type: 'clob', nullable: false })
  question: string;

  @Column({ name: 'ANSWER', type: 'clob', nullable: false })
  answer: string;
}
