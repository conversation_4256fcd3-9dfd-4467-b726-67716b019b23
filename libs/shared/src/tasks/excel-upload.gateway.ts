/* eslint-disable */
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';

interface ProgressUpdatePayload {
  taskId: string;
  percentage: number;
}

// @WebSocketGateway({ cors: true }) // Enable CORS for WebSocket connections
@WebSocketGateway({ cors: { origin: '*' } }) // Allow all origins
export class ExcelUploadTasksGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  afterInit(server: Server) {
    console.log('WebSocket gateway initialized');
    console.log('Adapter rooms:', server.sockets.adapter.rooms);
  }

  handleConnection(client: Socket, ...args: any[]) {
    console.log(`Client connected: ${client.id}, ${args}`);
  }

  handleDisconnect(client: Socket) {
    console.log(`Client disconnected: ${client.id}`);
    // Remove the client from all rooms when they disconnect
    for (const room of client.rooms) {
      if (room !== client.id) {
        client.leave(room);
      }
    }
  }

  @SubscribeMessage('joinTaskRoom')
  handleJoinTaskRoom(@MessageBody() taskId: string, @ConnectedSocket() client: Socket) {
    const roomName = `task_${taskId}`;
    console.log(`Client ${client.id} attempting to join room ${roomName}`);
    client.join(roomName);
    console.log(`Client ${client.id} joined room ${roomName}`);
    console.log('Current rooms:', this.server.sockets.adapter.rooms);
  }

  @SubscribeMessage('leaveTaskRoom')
  handleLeaveTaskRoom(@MessageBody() taskId: string, @ConnectedSocket() client: Socket) {
    client.leave(`task_${taskId}`); // Leave the room
    console.log(`Client ${client.id} left room task_${taskId}`);
  }

  public broadcastAll(event_name: string, message: Record<string, unknown>) {
    this.server.emit(event_name, message);
  }

  // emitProgressUpdate(taskId: string, percentage: number) {
  //   const payload: ProgressUpdatePayload = { taskId, percentage };
  //   console.log(`Emitting progress update to room task_${taskId}: ${percentage}%`);
  //   this.server.to(`task_${taskId}`).emit('progressUpdate', payload);
  //   console.log(`============ ----done Emitting progress update to room task_${taskId}: ${percentage}%`);
  // }
  emitProgressUpdate(taskId: string, percentage: number) {
    const payload: ProgressUpdatePayload = { taskId, percentage };
    console.log(`Emitting progress update to room task_${taskId}: ${percentage}%`);

    // Log all rooms and clients
    const rooms = this.server.sockets.adapter.rooms;
    console.log('Rooms:', rooms);

    // Check if the room exists
    if (rooms.has(`task_${taskId}`)) {
      console.log(`Room task_${taskId} exists with clients:`, rooms.get(`task_${taskId}`));
    } else {
      console.log(`Room task_${taskId} does not exist.`);
    }

    // Emit the event
    this.server.to(`task_${taskId}`).emit('progressUpdate', payload);
    console.log(`============ ----done Emitting progress update to room task_${taskId}: ${percentage}%`);
    console.log('Current rooms:', this.server.sockets.adapter.rooms);
  }

  emitNewTask(taskId: string) {
    this.server.emit('newTask', { taskId });
    console.log(`============emit  task called for ${taskId}`);
  }
}
