import { AbstractRepository } from '@app/shared/database';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { CobraPrivilege } from '../entities/cobra-priviledge.entity';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { PinoLogger } from 'nestjs-pino';

Injectable();
export class CobraPriviledgeRepository extends AbstractRepository<CobraPrivilege> {
  constructor(
    @InjectRepository(CobraPrivilege) cobraPriviledgeRepository: Repository<CobraPrivilege>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(cobraPriviledgeRepository, entityManager);
  }

  async seedPrivileges() {
    this.logger.debug('Seeding CobraPrivileges...');

    const existingPrivileges = await this.find();

    const newEntries: CobraPrivilege[] = [];

    for (const [privilege, data] of Object.entries(CobraPrivileges)) {
      for (const userType of data.userTypes) {
        // Check if the privilege-userType pair already exists
        const exists = existingPrivileges.some((p) => p.privilege === privilege && p.userType === userType);

        if (!exists) {
          newEntries.push(this.entityRepository.create({ privilege, userType }));
        }
      }
    }

    if (newEntries.length > 0) {
      await this.saveEntities(newEntries); // Bulk insert using the custom repository method
      this.logger.debug(`Inserted ${newEntries.length} new CobraPrivileges.`);
    } else {
      this.logger.warn('No new CobraPrivileges to insert.');
    }
  }

  async saveEntities(entities: CobraPrivilege[]): Promise<CobraPrivilege[]> {
    return this.entityManager.save(entities);
  }
}
