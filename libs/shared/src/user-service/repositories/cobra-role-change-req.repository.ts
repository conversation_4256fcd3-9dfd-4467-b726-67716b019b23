import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database';
import { UserRoleChangeRequest } from '../entities/cobra-role-change-request.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PaginatedSearchDto } from '../../dto/paginated-search.dto';
import { RoleChangeRequestDataDto, RoleChangeRequestDetailedDto } from '../dtos/cobra-role-change-request.dto';
import { BaseResponseWithNoCountInfo } from '../../dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class CobraUserRoleChangeReqRepository extends AbstractRepository<UserRoleChangeRequest> {
  constructor(
    @InjectRepository(UserRoleChangeRequest) private readonly cobraUserRoleChangeReq: Repository<UserRoleChangeRequest>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(cobraUserRoleChangeReq, entityManager);
  }

  async searchRequests(filter: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<RoleChangeRequestDataDto>> {
    const query = this.cobraUserRoleChangeReq
      .createQueryBuilder('userRoleChangeReq')
      .leftJoinAndSelect('userRoleChangeReq.approvedBy', 'approvedBy')
      .leftJoinAndSelect('userRoleChangeReq.requestedBy', 'requestedBy')
      .leftJoinAndSelect('userRoleChangeReq.primaryUser', 'primaryUser')
      .leftJoin('TBL_MDA', 'mda', 'userRoleChangeReq.mdaCode = mda.EMPLOYER_ID')
      .leftJoin('TBL_PFA', 'pfa', 'userRoleChangeReq.pfaCode = pfa.PFACODE')
      .select([
        'userRoleChangeReq.pk AS "pk"',
        'userRoleChangeReq.mdaCode AS "mdaCode"',
        'userRoleChangeReq.pfaCode AS "pfaCode"',
        'userRoleChangeReq.status AS "status"',
        'userRoleChangeReq.comment AS "comment"',
        'userRoleChangeReq.createDate AS "createDate"',
        'approvedBy.emailAddress AS "approvedBy"',
        'requestedBy.emailAddress AS "requestedBy"',
        'primaryUser.emailAddress AS "primaryUserEmail"',
        'mda.EMPLOYER_NAME AS "mdaName"',
        'pfa.PFANAME AS "pfaName"',
      ]);

    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (key === 'createDate') {
            query.andWhere(`TO_CHAR(userRoleChangeReq.${key}, 'YYYY-MM-DD') = :${key}`, { [key]: value });
          } else if (key === 'approvedBy') {
            query.andWhere(`LOWER("approvedBy"."EMAIL_ADDRESS") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'requestedBy') {
            query.andWhere(`LOWER("requestedBy"."EMAIL_ADDRESS") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'primaryUserEmail') {
            query.andWhere(`LOWER("primaryUser"."EMAIL_ADDRESS") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else {
            query.andWhere(`LOWER(userRoleChangeReq.${key}) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          }
        }
      });
    }

    query.orderBy('userRoleChangeReq.createDate', 'DESC');

    const limit = Math.max(filter.limit || 10, 1);
    const page = Math.max(filter.page || 1, 1);
    const offset = (page - 1) * limit;

    const requests = await query.offset(offset).limit(limit).getRawMany();

    const resp = new BaseResponseWithNoCountInfo<RoleChangeRequestDataDto>(ResponseCodeEnum.SUCCESS);
    resp.hasNext = requests?.length === limit;
    resp.hasPrevious = page > 1;

    if (!Array.isArray(requests)) {
      resp.content = [];
      return resp;
    }

    const content = requests.map((request) => ({
      pk: request.pk,
      mdaCode: request.mdaCode,
      pfaCode: request.pfaCode,
      organizationName: request.mdaCode ? request.mdaName : request.pfaName,
      status: request.status,
      comment: request.comment,
      createDate: request.createDate,
      approvedBy: request.approvedBy,
      requestedBy: request.requestedBy,
      primaryUserEmail: request.primaryUserEmail,
    }));

    resp.content = content;

    return resp;
  }

  serializeEntity(entity: UserRoleChangeRequest): RoleChangeRequestDataDto {
    return {
      pk: entity.pk,
      mdaCode: entity.mdaCode,
      pfaCode: entity.pfaCode,
      status: entity.status,
      comment: entity.comment,
      createDate: entity.createDate,
      approvedBy: entity.approvedBy?.emailAddress,
      requestedBy: entity.requestedBy?.emailAddress,
      primaryUserEmail: entity.primaryUser?.emailAddress,
    };
  }

  async getRoleChangeRequestWithDetails(requestId: number): Promise<RoleChangeRequestDetailedDto> {
    const request = await this.cobraUserRoleChangeReq
      .createQueryBuilder('rcr')
      .leftJoinAndSelect('rcr.primaryUser', 'pu')
      .leftJoinAndSelect('rcr.secondary', 'su')
      .leftJoinAndSelect('su.roles', 'secondaryRoles')
      .leftJoinAndSelect('pu.roles', 'primaryRoles')
      .leftJoinAndSelect('rcr.role', 'targetRole')
      .leftJoinAndSelect('rcr.requestedBy', 'rb')
      .leftJoinAndMapOne('rcr.organizationName', 'TBL_MDA', 'mda', 'pu.mdaCode = mda.EMPLOYER_ID')
      .leftJoinAndMapOne('rcr.organizationName', 'TBL_PFA', 'pfa', 'pu.pfaCode = pfa.PFACODE')
      .select([
        'rcr.pk AS "pk"',
        'rcr.mdaCode AS "mdaCode"',
        'rcr.pfaCode AS "pfaCode"',
        'rcr.status AS "status"',
        'rcr.comment AS "comment"',
        'rcr.createDate AS "createDate"',
        'pu.firstName AS "primaryUserFirstName"',
        'pu.surname AS "primaryUserSurname"',
        'pu.staffId AS "staffId"',
        'pu.emailAddress AS "primaryUserEmail"',
        'pu.phoneNumber AS "primaryUserPhoneNumber"',
        'pu.location AS "location"',
        'su.emailAddress AS "secondaryUserEmail"',
        'su.firstName AS "secondaryUserFirstName"',
        'secondaryRoles.role AS "secondaryUserRole"',
        'primaryRoles.role AS "primaryUserRole"',
        'targetRole.role AS "targetRole"',
        'rb.emailAddress AS "requesterEmail"',
        'mda.EMPLOYER_NAME AS "mdaOrgName"',
        'pfa.PFANAME AS "pfaOrgName"',
      ])
      .where('rcr.pk = :requestId', { requestId })
      .getRawOne();

    return {
      pk: request.pk,
      organizationName: request.mdaCode ? request.mdaOrgName : request.pfaOrgName,
      primaryUserFirstName: request.primaryUserFirstName,
      primaryUserSurname: request.primaryUserSurname,
      staffId: request.staffId,
      secondaryUserRole: request.secondaryUserRole || request.targetRole,
      primaryUserEmail: request.primaryUserEmail,
      primaryUserPhoneNumber: request.primaryUserPhoneNumber,
      location: request.location,
      secondaryUserEmail: request.secondaryUserEmail,
      secondaryUserFirstName: request.secondaryUserFirstName,
      requesterEmail: request.requesterEmail,
      createDate: request.createDate,
      status: request.status,
      comment: request.comment,
      primaryUserRole: request.primaryUserRole,
    };
  }
}
