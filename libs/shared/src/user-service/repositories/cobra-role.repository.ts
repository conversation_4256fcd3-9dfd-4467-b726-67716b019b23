import { AbstractRepository } from '@app/shared/database';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository, SelectQueryBuilder } from 'typeorm';
import { CobraRole } from '../entities/cobra-role.entity';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { GetRoleDetail } from '@app/shared/user-service/dtos/cobra-role.dto';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class CobraRoleRepository extends AbstractRepository<CobraRole> {
  constructor(
    @InjectRepository(CobraRole)
    private readonly cobraRoleRepository: Repository<CobraRole>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(cobraRoleRepository, entityManager);
  }

  private buildRoleQuery(): SelectQueryBuilder<CobraRole> {
    return this.cobraRoleRepository
      .createQueryBuilder('role')
      .select([
        'role.pk AS "rolePk"',
        'role.role AS "roleName"',
        'role.description AS "description"',
        'role.userType AS "userType"',
        'role.isAdmin AS "isAdmin"',
        "CASE WHEN role.active = 1 THEN 'true' ELSE 'false' END AS \"active\"",
        'TO_CHAR(role.createDate, \'YYYY-MM-DD\') AS "createDate"',
      ])
      .leftJoin('role.createdBy', 'createdBy')
      .addSelect(['createdBy.firstName AS "createdByFirstName"', 'createdBy.surname AS "createdByLastName"'])
      .leftJoin('role.privileges', 'privilege')
      .addSelect([
        'privilege.privilege AS "privilege"',
        'privilege.userType AS "privilegeUserType"',
        "CASE WHEN privilege.active = 1 THEN 'true' ELSE 'false' END AS \"privilegeActive\"",
      ]);
  }

  private buildRoleQuery2(): SelectQueryBuilder<CobraRole> {
    return this.cobraRoleRepository
      .createQueryBuilder('role')
      .select([
        'role.pk AS "rolePk"',
        'role.role AS "roleName"',
        'role.description AS "description"',
        'role.userType AS "userType"',
        'role.isAdmin AS "isAdmin"',
        "CASE WHEN role.active = 1 THEN 'true' ELSE 'false' END AS \"active\"",
        'TO_CHAR(role.createDate, \'YYYY-MM-DD\') AS "createDate"',
        `LISTAGG(privilege.privilege, ', ') WITHIN GROUP (ORDER BY privilege.privilege) AS "privileges"`,
      ])
      .leftJoin('role.createdBy', 'createdBy')
      .addSelect(['createdBy.firstName AS "createdByFirstName"', 'createdBy.surname AS "createdByLastName"'])
      .leftJoin('role.privileges', 'privilege')
      .groupBy(
        'role.pk, role.role, role.description, role.userType, role.active, role.createDate, createdBy.firstName, createdBy.surname, role.isAdmin'
      );
  }

  async getRolesByName(getRoleDetail: GetRoleDetail) {
    const query = this.buildRoleQuery()
      .where('role.role = :roleName', { roleName: getRoleDetail.role })
      .orderBy('role.createDate', 'DESC');

    this.logger.debug(`getRoleByName SQL: ${query.getSql()}`);

    return this.mapRoles(await query.getRawMany());
  }

  async searchPaginatedRoles(filter: PaginatedSearchDto) {
    const query = this.buildRoleQuery2().orderBy('role.createDate', 'DESC');
    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (['createDate'].includes(key)) {
            query.andWhere(`TO_CHAR(role.${key}, 'YYYY-MM-DD') = :${key}`, { [key]: value });
          } else if (key === 'createdByFirstName') {
            query.andWhere(`LOWER("createdBy"."FIRST_NAME") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'createdByLastName') {
            query.andWhere(`LOWER("createdBy"."SURNAME") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'blacklisted' || key === 'active') {
            const booleanValue = value === 'false' ? false : Boolean(value);
            query.andWhere(`role.${key} = :${key}`, { [key]: booleanValue });
          } else {
            query.andWhere(`LOWER(role.${key}) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          }
        }
      });
    }

    const total = await query.getCount();

    const limit = filter.limit || 10;
    const page = filter.page || 1;
    const offset = (page - 1) * limit;
    query.offset(offset).limit(limit);

    const rawResults = await query.getRawMany();

    const result = {
      total,
      page,
      limit,
      data: this.mapRoles2(rawResults),
    };

    return result;
  }

  private mapRoles2(rawResults: any[]): any[] {
    return rawResults.map((row) => ({
      pk: row.rolePk,
      active: row.active,
      createDate: row.createDate,
      role: row.roleName,
      description: row.description,
      userType: row.userType,
      createdByFirstName: row.createdByFirstName,
      createdByLastName: row.createdByLastName,
      isAdmin: row.isAdmin,
      privileges: row.privileges
        ? row.privileges.split(', ').map((privilege) => ({
            privilege: privilege.trim(),
          }))
        : [],
    }));
  }

  private mapRoles(rawResults: any[]): any[] {
    const roleMap = new Map<string, any>();

    rawResults.forEach((row) => {
      const roleKey = row.rolePk;
      if (!roleMap.has(roleKey)) {
        roleMap.set(roleKey, {
          pk: row.rolePk,
          active: row.active,
          createDate: row.createDate,
          role: row.roleName,
          description: row.description,
          userType: row.userType,
          createdByFirstName: row.createdByFirstName,
          createdByLastName: row.createdByLastName,
          isAdmin: row.isAdmin,
          privileges: [],
        });
      }

      if (row.privilege) {
        roleMap.get(roleKey).privileges.push({
          active: row.privilegeActive,
          privilege: row.privilege,
          userType: row.privilegeUserType,
        });
      }
    });

    return Array.from(roleMap.values());
  }
}
