import { AbstractRepository } from '@app/shared/database';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { SpecimenData } from '@app/shared/user-service/entities/specimen-data.entity';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { BaseResponseWithNoCountInfo } from '@app/shared/dto/response/base-response-with-content';
import { SpecimenDataResponseDto } from '../dtos/specimen-data.dto';
import { ResponseCodeEnum } from '@app/shared/enums';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class SpecimenDataRepository extends AbstractRepository<SpecimenData> {
  constructor(
    @InjectRepository(SpecimenData)
    private readonly specimenRepository: Repository<SpecimenData>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(specimenRepository, entityManager);
  }

  async searchSpecimens(filter: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<SpecimenDataResponseDto>> {
    const query = this.specimenRepository
      .createQueryBuilder('specimen')
      .leftJoinAndSelect('specimen.createdBy', 'createdBy')
      .select([
        'specimen.pk as "pk"',
        'specimen.name as "name"',
        'specimen.year as "year"',
        'specimen.mdaCode as "mdaCode"',
        'specimen.documentData as "documentData"', // Make sure documentData is selected
        'specimen.createDate as "createDate"',
        'specimen.docExt as "docExt"',
        'createdBy.emailAddress as "createdByEmail"',
      ]);

    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (key === 'year') {
            query.andWhere(`specimen.${key} = :${key}`, { [key]: value });
          } else if (['createDate'].includes(key)) {
            query.andWhere(`TO_CHAR(specimen.${key}, 'YYYY-MM-DD') = :${key}`, { [key]: value });
          } else if (key === 'createdBy') {
            query.andWhere(`LOWER("createdBy"."EMAIL_ADDRESS") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key !== 'documentData') {
            query.andWhere(`LOWER(specimen.${key}) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          }
        }
      });
    }

    query.orderBy('specimen.year', 'DESC');

    const limit = Math.max(filter.limit || 10, 1);
    const page = Math.max(filter.page || 1, 1);
    const offset = (page - 1) * limit;

    const specimens = await query.offset(offset).limit(limit).getRawMany();

    const resp = new BaseResponseWithNoCountInfo<SpecimenDataResponseDto>(ResponseCodeEnum.SUCCESS);
    resp.hasNext = specimens.length === limit;
    resp.hasPrevious = page > 1;
    resp.content = specimens.map((specimen) => this.mapToDto(specimen));

    return resp;
  }

  mapToDto(entity): SpecimenDataResponseDto {
    // Remove any existing data URI prefix if present

    return {
      pk: entity.pk,
      name: entity.name,
      year: entity.year,
      mdaCode: entity.mdaCode,
      createdBy: entity?.createdBy?.emailAddress || entity.createdByEmail,
      createDate: entity.createDate,
    };
  }
}
