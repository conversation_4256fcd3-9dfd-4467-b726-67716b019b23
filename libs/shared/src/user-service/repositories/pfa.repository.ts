import { AbstractRepository } from '@app/shared/database';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { Pfa } from '../entities/pfa.entity';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { PinoLogger } from 'nestjs-pino';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { Pfc } from '@app/shared/user-service/entities/pfc.entity';

Injectable();

export class PfaRepository extends AbstractRepository<Pfa> {
  constructor(
    @InjectRepository(Pfa) private readonly pfaRepository: Repository<Pfa>,
    entityManager: EntityManager,
    readonly logger: PinoLogger,
    private readonly redisCacheService: RedisCacheService
  ) {
    super(pfaRepository, entityManager);
  }

  async searchPfas(filter: PaginatedSearchDto) {
    const query = this.pfaRepository
      .createQueryBuilder('pfa')
      .select(['pfa.pfaCode AS "pfaCode"', 'pfa.pfaName AS "pfaName"', 'pfa.domainUrl AS "domainUrl"']);

    if (filter && filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          query.andWhere(`LOWER(pfa.${key}) LIKE LOWER(:${key})`, {
            [key]: `%${value}%`,
          });
        }
      });
    }

    return await query.getRawMany();
  }

  async findPfaNameByCode(pfaCode: string): Promise<string | null> {
    const result = await this.pfaRepository
      .createQueryBuilder('pfa')
      .select('pfa.pfaName', 'pfaName')
      .where('pfa.pfaCode = :pfaCode', { pfaCode })
      .getRawOne();

    return result?.pfaName || null;
  }

  async findPfaByCodeAndAssociatedCode(pfaCode: string): Promise<Pfa | undefined> {
    return await this.pfaRepository
      .createQueryBuilder('pfa')
      .where('pfa.pfaCode = :code', { code: pfaCode })
      .orWhere('pfa.associatedPfaCode LIKE :likeCode', {
        likeCode: `%${pfaCode}%`,
      })
      .getOne();
  }

  async getPfaDetails(pfaCode: string): Promise<Pfa | null> {
    return await this.pfaRepository
      .createQueryBuilder('pfa')
      .select(['pfa.pfaCode', 'pfa.pfaName', 'pfa.address1', 'pfa.phoneNumber', 'pfa.emailAddress'])
      .where('pfa.pfaCode = :pfaCode', { pfaCode })
      .getOne();
  }

  async getPfaNameByCode(pfaCodes: string[]): Promise<string[] | null> {
    const pfaCodesString = pfaCodes.map((code) => `'${code}'`).join(',');

    const result = await this.pfaRepository
      .createQueryBuilder('pfa')
      .select('pfa.pfaName', 'pfaName')
      .where(`pfa.pfaCode IN (${pfaCodesString})`)
      .getRawMany();

    return result.map((row) => row.pfaName);
  }

  async getCachedPfaDetails(pfaCode: string): Promise<Pfa> {
    let pfaDetail: any | undefined;

    const cacheKey = `pfa_pfc_Details_${pfaCode}`;
    const cachedData = await this.redisCacheService.get(cacheKey);

    if (cachedData) {
      this.logger.debug(`returning pfa/pfc detail from cache for pfaCode <<<<<<<<<<<<<>>>>>>>>>>>>>>>> ${cacheKey}`);
      // Create a new Pfa instance with the cached data
      const parsedData = JSON.parse(cachedData);
      const pfa = new Pfa(parsedData);

      // Handle pfc relationship if it exists in the cached data
      if (parsedData.pfc) {
        pfa.pfc = new Pfc(parsedData.pfc);
      }

      return pfa;
    }

    pfaDetail = await this.pfaRepository.findOne({
      where: { pfaCode },
    });

    if (pfaDetail) {
      await this.redisCacheService.set(cacheKey, JSON.stringify(pfaDetail));
    }

    return pfaDetail;
  }

  async retrievePfas(filter: PaginatedSearchDto): Promise<{
    result: any[];
    total: number;
    page: number;
    limit: number;
  }> {
    const query = this.pfaRepository
      .createQueryBuilder('pfa')
      .leftJoin('pfa.pfc', 'pfc')
      .select([
        'pfa.pk AS "pk"',
        'pfa.pfaCode AS "pfaCode"',
        'pfa.pfaName AS "pfaName"',
        'pfa.phoneNumber AS "phoneNumber"',
        'pfa.emailAddress AS "emailAddress"',
        'pfa.accountName AS "accountName"',
        'pfa.accountNumber AS "accountNumber"',
        'pfa.domainUrl AS "domainUrl"',
        'pfa.associatedPfaCode AS "associatedPfaCode"',
        'pfc.pfcCode AS "pfcCode"',
        'pfc.pfcName AS "pfcName"',
        'pfa.address1 AS "address1"',
        'pfa.address2 AS "address2"',
        'pfa.address3 AS "address3"',
        'pfa.address4 AS "address4"',
        'pfa.address5 AS "address5"',
        "CASE WHEN pfa.active = 1 THEN 'true' ELSE 'false' END AS \"active\"",
        'TO_CHAR(pfa.createDate, \'YYYY-MM-DD\') AS "createDate"',
      ]);

    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (key === 'createDate') {
            query.andWhere(`TO_CHAR(pfa.createDate, 'YYYY-MM-DD') = :${key}`, {
              [key]: value,
            });
          } else if (key === 'pfcCode') {
            query.andWhere(`LOWER(pfc.pfcCode) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          } else if (key === 'pfcName') {
            query.andWhere(`LOWER(pfc.pfcName) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          } else if (key === 'active') {
            const booleanValue = value === 'false' ? false : Boolean(value);
            query.andWhere(`pfa.active = :${key}`, { [key]: booleanValue });
          } else {
            query.andWhere(`LOWER(pfa.${key}) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          }
        }
      });
    }

    const limit = Math.max(filter.limit || 10, 1);
    const page = Math.max(filter.page || 1, 1);
    const offset = (page - 1) * limit;

    query.orderBy('pfa.createDate', 'DESC');

    const total = await query.getCount();
    const pfaList = await query.offset(offset).limit(limit).getRawMany();

    return {
      page,
      limit,
      total,
      result: pfaList,
    };
  }
}
