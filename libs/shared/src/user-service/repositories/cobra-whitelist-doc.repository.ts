import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database';
import { WhitelistDocument } from '../entities/cobra-whitelist-doc.entity';
import { WhitelistDocumentResult } from '../dtos/cobra-whitelist-request.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class CobraWhitelistDocRepository extends AbstractRepository<WhitelistDocument> {
  constructor(
    @InjectRepository(WhitelistDocument) private readonly whitelistDocRepository: Repository<WhitelistDocument>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(whitelistDocRepository, entityManager);
  }

  async findByWhitelistReqId(whitelistReqId: number): Promise<WhitelistDocumentResult[]> {
    const documents = await this.whitelistDocRepository
      .createQueryBuilder('doc')
      .where('doc.whitelistReq.pk = :whitelistReqId', { whitelistReqId })
      .getMany();

    return documents.map(this.mapToDto);
  }

  async saveDocuments(documents: WhitelistDocument[]): Promise<WhitelistDocument[]> {
    return await this.entityManager.save(documents);
  }

  public mapToDto(document: WhitelistDocument): WhitelistDocumentResult {
    return {
      pk: document.pk,
      fileName: document.name,
      fileContent: `data:${document.type};base64,${Buffer.from(document.data).toString('base64')}`,
      mimeType: document.type,
    };
  }
}
