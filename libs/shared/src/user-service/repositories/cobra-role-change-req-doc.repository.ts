import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database';
import { UserRoleChangeRequest } from '../entities/cobra-role-change-request.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { RoleChangeDocument } from '../entities/cobra-role-doc.entity';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class CobraRoleChangeReqDocRepository extends AbstractRepository<RoleChangeDocument> {
  constructor(
    @InjectRepository(UserRoleChangeRequest) private readonly roleChangeRepository: Repository<RoleChangeDocument>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(roleChangeRepository, entityManager);
  }

  async saveEntities(documents: RoleChangeDocument[]): Promise<RoleChangeDocument[]> {
    return await this.entityManager.save(documents);
  }
}
