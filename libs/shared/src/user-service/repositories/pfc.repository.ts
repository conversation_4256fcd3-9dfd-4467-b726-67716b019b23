import { AbstractRepository } from '@app/shared/database';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { Pfc } from '@app/shared/user-service/entities/pfc.entity';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';

Injectable();

export class PfcRepository extends AbstractRepository<Pfc> {
  constructor(
    @InjectRepository(Pfc) private readonly pfcRepository: Repository<Pfc>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(pfcRepository, entityManager);
  }

  async searchPfcs(filter: PaginatedSearchDto) {
    const query = this.pfcRepository
      .createQueryBuilder('pfc')
      .select(['pfc.pfcCode AS "pfcCode"', 'pfc.pfcName AS "pfcName"']);

    if (filter && filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          query.andWhere(`LOWER(pfc.${key}) LIKE LOWER(:${key})`, {
            [key]: `%${value}%`,
          });
        }
      });
    }

    return await query.getRawMany();
  }

  async retrievePfcs(filter: PaginatedSearchDto): Promise<{
    result: any[];
    total: number;
    page: number;
    limit: number;
  }> {
    const query = this.pfcRepository
      .createQueryBuilder('pfc')
      .select([
        'pfc.pk AS "pk"',
        'pfc.pfcCode AS "pfcCode"',
        'pfc.pfcName AS "pfcName"',
        'pfc.shortName AS "shortName"',
        'pfc.contactPerson AS "contactPerson"',
        'pfc.phoneNumber AS "phoneNumber"',
        'pfc.emailAddress AS "emailAddress"',
        'pfc.address1 AS "address1"',
        'pfc.address2 AS "address2"',
        'pfc.address3 AS "address3"',
        'pfc.address4 AS "address4"',
        'pfc.address5 AS "address5"',
        "CASE WHEN pfc.active = 1 THEN 'true' ELSE 'false' END AS \"active\"",
        'TO_CHAR(pfc.dateOfRegistration, \'YYYY-MM-DD\') AS "dateOfRegistration"',
        'TO_CHAR(pfc.createDate, \'YYYY-MM-DD\') AS "createDate"',
      ]);

    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (key === 'createDate' || key === 'dateOfRegistration') {
            query.andWhere(`TO_CHAR(pfc.${key}, 'YYYY-MM-DD') = :${key}`, {
              [key]: value,
            });
          } else {
            query.andWhere(`LOWER(pfc.${key}) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          }
        }
      });
    }

    const limit = Math.max(filter.limit || 10, 1);
    const page = Math.max(filter.page || 1, 1);
    const offset = (page - 1) * limit;

    query.orderBy('pfc.createDate', 'DESC');

    const total = await query.getCount();
    const pfcList = await query.offset(offset).limit(limit).getRawMany();

    return {
      page,
      limit,
      total,
      result: pfcList,
    };
  }
}
