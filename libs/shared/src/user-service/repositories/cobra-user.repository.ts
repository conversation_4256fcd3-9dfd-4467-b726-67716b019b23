/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, EntityManager, Repository } from 'typeorm';
import { CobraUser } from '../entities/cobra-user.entity';
import { Mda } from '../entities/mda.entity';
import { CobraUserProfileDto } from '@app/shared/dto/cobra-user-profile.dto';
import { Pfa } from '@app/shared/user-service/entities/pfa.entity';
import { BaseResponseListWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums/ResponseCodeEnum';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { PinoLogger } from 'nestjs-pino';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { RegistrationStatusEnum } from '@app/shared/enums/registration-status.enum';

@Injectable()
export class CobraUserRepository extends AbstractRepository<CobraUser> {
  constructor(
    @InjectRepository(CobraUser) private readonly cobraUserRepository: Repository<CobraUser>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(cobraUserRepository, entityManager);
  }

  public serialize(user: CobraUser, includePassword?: boolean): Partial<CobraUser> {
    return {
      rsaPin: user.rsaPin,
      firstName: user.firstName,
      surname: user.surname,
      emailAddress: user.emailAddress,
      staffId: user.staffId,
      password: includePassword ? user.password : undefined,
      mdaCode: user.mdaCode,
      pfaCode: user.pfaCode,
      userType: user.userType,
      avatar: user.avatar,
      signature: user.signature,
      active: user.active,
      phoneNumber: user.phoneNumber,
      location: user.location,
    };
  }

  async searchUserForReassignment(roleName: string, action: string, emailAddress: string) {
    const isAuditor = action === 'auditor';

    const query = this.cobraUserRepository
      .createQueryBuilder('usr') // changed alias from 'user' to 'usr'
      .leftJoinAndSelect('usr.roles', 'role')
      .leftJoin(
        'ENROLLMENT_SUMMARY',
        'es',
        isAuditor
          ? 'es.AUDITOR_ASSIGNED_TO = usr.EMAIL_ADDRESS  AND es.STATUS = :status '
          : 'es.ASSIGNED_TO = usr.pk AND es.STATUS = :status'
      )
      .select([
        'usr.pk AS "pk"',
        'usr.firstName AS "firstName"',
        'usr.surname AS "surname"',
        'usr.emailAddress AS "emailAddress"',
        'usr.staffId AS "staffId"',
        'usr.userType AS "userType"',
        'COUNT(es.pk) AS "pendingCount"',
      ])
      .where('role.role = :roleName', { roleName })
      .andWhere('usr.active = :active', { active: true })
      .andWhere('usr.emailAddress != :emailAddress', { emailAddress })
      .setParameter('status', isAuditor ? RegistrationStatusEnum.COMPUTED : RegistrationStatusEnum.ENROLLED)
      .groupBy('usr.pk, usr.firstName, usr.surname, usr.emailAddress, usr.staffId, usr.userType');

    this.logger.debug(`searchUserForReassignment query: ${query.getSql()}`);

    return await query.getRawMany();
  }

  async searchUserByType(filter: PaginatedSearchDto): Promise<{
    result: any;
    total: number;
    page: number;
    limit: number;
  }> {
    const query = this.cobraUserRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'role')
      .leftJoin('user.createdBy', 'createdBy')
      .select([
        'user.pk AS "pk"',
        'user.firstName AS "firstName"',
        'user.surname AS "surname"',
        'user.emailAddress AS "emailAddress"',
        'user.staffId AS "staffId"',
        'user.userType AS "userType"',
        'TO_CHAR(user.createDate, \'YYYY-MM-DD\') AS "createDate"',
        'role.role AS "roleName"',
        'role.pk AS "rolePk"',
        "CASE WHEN user.active = 1 THEN 'true' ELSE 'false' END AS \"active\"",
        "CASE WHEN user.blacklisted = 1 THEN 'true' ELSE 'false' END AS \"blacklisted\"",
        'createdBy.firstName AS "createdByFirstName"',
        'createdBy.surname AS "createdByLastName"',
        'user.phoneNumber AS "phoneNumber"',
      ]);

    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (['createDate'].includes(key)) {
            query.andWhere(`TO_CHAR(user.${key}, 'YYYY-MM-DD') = :${key}`, { [key]: value });
          } else if (key === 'createdByFirstName') {
            query.andWhere(`LOWER("createdBy"."FIRST_NAME") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'createdByLastName') {
            query.andWhere(`LOWER("createdBy"."SURNAME") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'roleName') {
            query.andWhere(`LOWER("role"."ROLE") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'userType' && value === 'MDA') {
            query
              .addSelect('mda.employerName AS "mdaName"')
              .addSelect('user.mdaCode AS "mdaCode"')
              .leftJoin(Mda, 'mda', 'user.mdaCode = mda.employerId');
            query.andWhere(`LOWER(user.${key}) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'mdaName') {
            query.andWhere(`LOWER("mda"."EMPLOYER_NAME") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'userType' && value === 'PFA') {
            query
              .addSelect('pfa.pfaName AS "pfaName"')
              .addSelect('user.pfaCode AS "pfaCode"')
              .leftJoin(Pfa, 'pfa', 'user.pfaCode = pfa.pfaCode');
            query.andWhere(`LOWER(user.${key}) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'pfaName') {
            query.andWhere(`LOWER("pfa"."PFANAME") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'blacklisted' || key === 'active') {
            const booleanValue = value === 'false' ? false : Boolean(value);
            query.andWhere(`user.${key} = :${key}`, { [key]: booleanValue });
          } else {
            query.andWhere(`LOWER(user.${key}) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          }
        }
      });
    }

    const limit = Math.max(filter.limit || 10, 1); // Ensures limit is at least 1
    const page = Math.max(filter.page || 1, 1); // Ensures page starts at 1
    const offset = (page - 1) * limit;

    query.orderBy('user.createDate', 'DESC'); // Ensure createDate exists
    // this.logger.debug(`query: ${query.getSql()}`);

    const total = await query.getCount();
    const users = await query.offset(offset).limit(limit).getRawMany();
    // this.logger.debug(`users: ${JSON.stringify(users)} total: ${total}`);

    if (!Array.isArray(users)) {
      this.logger.error('Unexpected query result format:', { users });
      return { page, limit, total: 0, result: [] };
    }

    return {
      page,
      limit,
      total,
      result: users.map((user) => ({
        pk: user.pk,
        firstName: user.firstName,
        surname: user.surname,
        emailAddress: user.emailAddress,
        staffId: user.staffId,
        userType: user.userType,
        active: user.active,
        blacklisted: user.blacklisted,
        createdByFirstName: user.createdByFirstName,
        createdByLastName: user.createdByLastName,
        createDate: user.createDate,
        roleName: user.roleName,
        roleFk: user.rolePk,
        phoneNumber: user.phoneNumber,
        ...(filter.filters.userType === 'MDA' && { mdaName: user.mdaName, mdaCode: user.mdaCode }),
        ...(filter.filters.userType === 'PFA' && { pfaName: user.pfaName, pfaCode: user.pfaCode }),
      })),
    };
  }

  async getUserProfile(pCobraUser: Partial<CobraUser>): Promise<CobraUserProfileDto | null> {
    const emailAddress = pCobraUser.emailAddress;
    const queryBuilder = this.cobraUserRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.roles', 'role')
      .leftJoin(CobraUser, 'creator', 'user.createdBy = creator.pk')
      .select([
        'user.rsaPin AS "rsaPin"',
        'user.firstName AS "firstName"',
        'user.surname AS "surname"',
        'user.emailAddress as "emailAddress"',
        'user.staffId AS "staffId"',
        'user.userType AS "userType"',
        'user.signature AS "signature"',
        'user.avatar AS "avatar"',
        'user.gender AS "gender"',
        "CASE WHEN user.blacklisted = 1 THEN 'true' ELSE 'false' END AS \"blacklisted\"",
        "CASE WHEN user.active = 1 THEN 'true' ELSE 'false' END AS \"active\"",
        "CASE WHEN user.termsAndConditions = 1 THEN 'true' ELSE 'false' END AS \"termsAndConditions\"",
        'user.phoneNumber AS "phoneNumber"',
        'role.role AS "roleName"',
        'role.pk AS "rolePk"',
        'creator.firstName AS "createdByFirstName"',
        'creator.surname AS "createdByLastName" ',
        'user.location AS "location"',
      ])
      .where('user.emailAddress = :emailAddress', { emailAddress });

    if (pCobraUser.userType === 'MDA') {
      queryBuilder
        .addSelect('mda.employerName AS "mdaName"')
        .addSelect('user.mdaCode AS "mdaCode"')
        .leftJoin(Mda, 'mda', 'user.mdaCode = mda.employerId');
    }
    if (pCobraUser.userType === 'PFA') {
      queryBuilder
        .addSelect('pfa.pfaName AS "pfaName"')
        .addSelect('user.pfaCode AS pfaCode')
        .leftJoin(Pfa, 'pfa', 'user.pfaCode = ' + 'pfa.pfaCode');
    }

    this.logger.debug(`getUserProfile query: ${queryBuilder.getSql()}`);

    const result: CobraUserProfileDto | undefined = await queryBuilder.getRawOne();
    this.logger.debug(`getUserProfile result: ${JSON.stringify(result)}`);
    if (!result) {
      return Promise.resolve(null);
    }

    return result;
  }

  /**
   * method return user list of admins if the userId selected for upgrade is not an admin
   * and vice versa
   *
   */

  async getUserForUpgrade(
    mda: string,
    pfa: string,
    isAdmin: boolean
  ): Promise<BaseResponseListWithContentNoPagination<any>> {
    const query = this.cobraUserRepository
      .createQueryBuilder('user')
      .select([
        'user.firstName AS "firstName"',
        'user.surname AS "surname"',
        'user.emailAddress AS "emailAddress"',
        'user.userType AS "userType"',
      ])
      .innerJoin('user.roles', 'role')
      .where('role.isAdmin = :isAdmin', { isAdmin: !isAdmin });

    if (mda) {
      query.andWhere('user.mdaCode = :mda', { mda });
    }

    if (pfa) {
      query.andWhere('user.pfaCode = :pfa', { pfa });
    }

    const users = await query.getRawMany();

    const userList = users.map((user) => ({
      firstName: user.firstName,
      surname: user.surname,
      emailAddress: user.emailAddress,
      userType: user.userType,
    }));

    const response = new BaseResponseListWithContentNoPagination<any>(ResponseCodeEnum.SUCCESS);
    response.content = userList;
    return response;
  }

  async isUserAdmin(userEmail: string): Promise<boolean> {
    const result = await this.cobraUserRepository
      .createQueryBuilder('user')
      .innerJoin('user.roles', 'role')
      .where('user.emailAddress = :userEmail', { userEmail })
      .andWhere('role.isAdmin = :isAdmin', { isAdmin: true })
      .getCount();

    return result > 0;
  }

  getAdminCountForOrganization(mfaCode: string, pfaCode: string) {
    return this.cobraUserRepository
      .createQueryBuilder('user')
      .innerJoin('user.roles', 'role')
      .where('role.isAdmin = :isAdmin', { isAdmin: true })
      .andWhere('user.mdaCode = :mfaCode', { mfaCode })
      .andWhere('user.pfaCode = :pfaCode', { pfaCode })
      .getCount();
  }

  async findApproversByRoles(roles: string[]): Promise<{ emailAddress: string; firstName: string }[]> {
    if (roles.length === 0) {
      return [];
    }

    const query = this.cobraUserRepository
      .createQueryBuilder('user')
      .innerJoin('user.roles', 'role')
      .where('role.role IN (:...roles)', { roles })
      .andWhere('user.active = :active', { active: true })
      .select(['user.emailAddress', 'user.firstName']);

    return await query.getMany();
  }

  async findAndDeactivateInactiveUsers(
    batchSize: number,
    inactiveDays: number
  ): Promise<{ deactivatedUsers: CobraUser[] }> {
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - inactiveDays);

    const query = this.cobraUserRepository
      .createQueryBuilder('user')
      .select([
        'user.pk AS "pk"',
        'user.active AS "active"',
        'user.blacklisted AS "blacklisted"',
        'user.createDate AS "createDate"',
        'user.surname AS "surname"',
        'user.staffId AS "staffId"',
        'user.emailAddress AS "emailAddress"',
      ])
      .leftJoin(
        (subQuery) => {
          return subQuery
            .select('audit.USER_ID', 'USER_ID')
            .addSelect('MAX(audit.EVENT_TIMESTAMP)', 'lastLogin')
            .from('AUDIT_LOGS', 'audit')
            .where('audit.EVENT_TYPE = :eventType')
            .groupBy('audit.USER_ID');
        },
        'recent_activity',
        '("user"."EMAIL_ADDRESS" = "recent_activity"."USER_ID" OR "user"."STAFF_ID" = "recent_activity"."USER_ID" OR "user"."RSA_PIN" = "recent_activity"."USER_ID")'
      )
      .where('user.active = :active')
      .andWhere('user.blacklisted = :blacklisted')
      .andWhere(
        new Brackets((qb) => {
          qb.where('"recent_activity"."USER_ID" IS NULL').orWhere('"recent_activity"."lastLogin" < :loginCutoffDate');
        })
      )
      .andWhere('user.createDate < :createCutoffDate')
      .setParameters({
        loginCutoffDate: ninetyDaysAgo,
        createCutoffDate: ninetyDaysAgo,
        eventType: AuditEventTypeEnum.LOGIN_ATTEMPT,
        active: true,
        blacklisted: false,
      });

    this.logger.debug(`findAndDeactivateInactiveUsers query: ${query.getSql()}`);

    // Get batch of users and deactivate them
    const usersToDeactivate = await query.skip(0).take(batchSize).getRawMany();

    if (usersToDeactivate.length > 0) {
      await this.cobraUserRepository
        .createQueryBuilder()
        .update(CobraUser)
        .set({ active: false })
        .whereInIds(usersToDeactivate.map((user) => user.pk))
        .execute();
    }

    return {
      deactivatedUsers: usersToDeactivate,
    };
  }

  async getUsersByRoleNameAndPfaCode(roleName: string, pfaCode: string): Promise<string[]> {
    const query = this.cobraUserRepository
      .createQueryBuilder('cu')
      .innerJoin('cu.roles', 'cr')
      .where('cr.role = :role', { role: roleName })
      .select('cu.emailAddress AS "emailAddress"')
      .andWhere('cu.pfaCode = :pfaCode', { pfaCode });

    const users = await query.getRawMany();
    return users.map((user) => user.emailAddress);
  }

  async getUsersByRoleNameAndUserType(roles: string[], userType: UserTypeEnum): Promise<CobraUser[]> {
    const query = this.cobraUserRepository
      .createQueryBuilder('cu')
      .innerJoin('cu.roles', 'cr')
      .select([
        'cu.pk AS "pk"',
        'cu.firstName AS "firstName"',
        'cu.surname AS "surname"',
        'cu.emailAddress AS "emailAddress"',
      ])
      .where('cr.role IN (:...roles)', { roles })
      .andWhere('cu.userType = :userType', { userType })
      .andWhere('cu.active = :active', { active: true });
    return await query.getRawMany();
  }

  async getFullUsersByRoleNameAndUserType(roles: string[], userType: UserTypeEnum): Promise<CobraUser[]> {
    const query = this.cobraUserRepository
      .createQueryBuilder('cu')
      .innerJoin('cu.roles', 'cr')
      .where('cr.role IN (:...roles)', { roles })
      .andWhere('cu.userType = :userType', { userType })
      .andWhere('cu.active = :active', { active: true });
    return await query.getMany();
  }
}
