import { AbstractRepository } from '@app/shared/database';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { Mda } from '../entities/mda.entity';
import { BaseResponseListWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { PinoLogger } from 'nestjs-pino';

Injectable();

export class MdaRepository extends AbstractRepository<Mda> {
  constructor(
    @InjectRepository(Mda) private readonly mdaRepository: Repository<Mda>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(mdaRepository, entityManager);
  }

  async searchAllMdas(filter: PaginatedSearchDto): Promise<BaseResponseListWithContentNoPagination<Mda[]>> {
    const query = await this.mdaRepository
      .createQueryBuilder('mda')
      .select([
        'mda.sectorCode AS "sectorCode"',
        'mda.employerId AS "employerId"',
        'mda.employerName AS "employerName"',
        'mda.domainUrl AS "domainUrl"',
        'TO_CHAR(mda.ippisDate, \'YYYY-MM-DD\') AS "ippisDate"',
      ]);

    if (filter && filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (key === 'ippisDate') {
            query.andWhere(`TO_CHAR(mda.${key}, 'YYYY-MM-DD') = :${key}`, {
              [key]: value,
            });
          } else {
            query.andWhere(`LOWER(mda.${key}) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          }
        }
      });
    }
    const result = await query.getRawMany();
    const response = new BaseResponseListWithContentNoPagination<Mda[]>(ResponseCodeEnum.SUCCESS);
    response.content = result;
    return response;
  }

  async findMdaNameByCode(employerCode: string): Promise<string | null> {
    const result = await this.mdaRepository
      .createQueryBuilder('mda')
      .select('mda.employerName', 'employerName')
      .where('mda.employerId = :employerCode', { employerCode })
      .getRawOne();

    return result?.employerName || null;
  }

  async retrieveMdas(filter: PaginatedSearchDto): Promise<{
    result: any[];
    total: number;
    page: number;
    limit: number;
  }> {
    const query = this.mdaRepository
      .createQueryBuilder('mda')
      .select([
        'mda.sectorCode AS "sectorCode"',
        'mda.employerId AS "employerId"',
        'mda.employerName AS "employerName"',
        'mda.ecrsEmployerCode AS "ecrsEmployerCode"',
        'mda.domainUrl AS "domainUrl"',
        'TO_CHAR(mda.ippisDate, \'YYYY-MM-DD\') AS "ippisDate"',
        "CASE WHEN mda.active = 1 THEN 'true' ELSE 'false' END AS \"active\"",
        'TO_CHAR(mda.createDate, \'YYYY-MM-DD\') AS "createDate"',
      ]);

    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (key === 'ippisDate') {
            query.andWhere(`TO_CHAR(mda.ippisDate, 'YYYY-MM-DD') = :${key}`, {
              [key]: value,
            });
          } else if (key === 'active') {
            const booleanValue = value === 'false' ? false : Boolean(value);
            query.andWhere(`mda.active = :${key}`, { [key]: booleanValue });
          } else {
            query.andWhere(`LOWER(mda.${key}) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          }
        }
      });
    }

    const limit = Math.max(filter.limit || 10, 1);
    const page = Math.max(filter.page || 1, 1);
    const offset = (page - 1) * limit;

    query.orderBy('mda.createDate', 'DESC');

    const total = await query.getCount();
    const mdaList = await query.offset(offset).limit(limit).getRawMany();

    return {
      page,
      limit,
      total,
      result: mdaList,
    };
  }
}
