import { Injectable } from '@nestjs/common';
import { Buffer } from 'buffer';
import { AbstractRepository } from '@app/shared/database';
import { UserWhitelistRequest } from '../entities/cobra-whitelist-request.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PaginatedSearchDto } from '../../dto/paginated-search.dto';
import { CreateWhitelistRequestDto, WhitelistRequestDataDto } from '../dtos/cobra-whitelist-request.dto';
import { BaseResponseWithNoCountInfo } from '../../dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums';
import { WhitelistDocument } from '../entities/cobra-whitelist-doc.entity';
import { ApprovalStatusEnum } from '../../enums/ApprovalStatusEnum';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class CobraWhitelistReqRepository extends AbstractRepository<UserWhitelistRequest> {
  constructor(
    @InjectRepository(UserWhitelistRequest) private readonly whitelistReq: Repository<UserWhitelistRequest>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(whitelistReq, entityManager);
  }

  async searchRequests(filter: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<WhitelistRequestDataDto>> {
    const query = this.whitelistReq
      .createQueryBuilder('whitelistReq')
      .leftJoinAndSelect('whitelistReq.approvedBy', 'approvedBy')
      .leftJoinAndSelect('whitelistReq.user', 'user')
      .leftJoinAndSelect('whitelistReq.requestedBy', 'requestedBy')
      .leftJoinAndSelect('user.roles', 'role')
      .leftJoin('TBL_MDA', 'mda', 'whitelistReq.mdaCode = mda.EMPLOYER_ID')
      .leftJoin('TBL_PFA', 'pfa', 'whitelistReq.pfaCode = pfa.PFACODE')
      .select([
        'whitelistReq.pk AS "pk"',
        'whitelistReq.requestId AS "requestId"',
        'whitelistReq.mdaCode AS "mdaCode"',
        'whitelistReq.pfaCode AS "pfaCode"',
        'whitelistReq.status AS "status"',
        'whitelistReq.comment AS "comment"',
        'whitelistReq.createDate AS "createDate"',
        'approvedBy.emailAddress AS "approvedBy"',
        'whitelistReq.userType AS "userType"',
        'user.emailAddress AS "email"',
        'user.staffId AS "staffId"',
        'user.phoneNumber AS "phoneNumber"',
        'user.location AS "location"',
        'user.firstName AS "firstName"',
        'user.surname AS "lastName"',
        'role.role AS "role"',
        "CASE WHEN whitelistReq.userType = 'MDA' THEN mda.EMPLOYER_NAME WHEN whitelistReq.userType = 'PFA' THEN pfa.PFANAME ELSE NULL END AS \"organizationName\"",
        'requestedBy.emailAddress AS "requesterEmail"',
      ]);

    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (key === 'createDate') {
            query.andWhere(`TO_CHAR(whitelistReq.${key}, 'YYYY-MM-DD') = :${key}`, { [key]: value });
          } else if (key === 'requesterEmail') {
            query.andWhere(`LOWER(requestedBy.emailAddress) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'email') {
            query.andWhere(`LOWER(user.emailAddress) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'role') {
            query.andWhere(`LOWER(role.role) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'firstName') {
            query.andWhere(`LOWER(user.firstName) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'lastName') {
            query.andWhere(`LOWER(user.surname) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'staffId') {
            query.andWhere(`LOWER(user.staffId) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'phoneNumber') {
            query.andWhere(`LOWER(user.phoneNumber) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'organizationName') {
            query.andWhere(
              `(whitelistReq.userType = 'MDA' AND LOWER(mda.EMPLOYER_NAME) LIKE LOWER(:${key})) OR (whitelistReq.userType = 'PFA' AND LOWER(pfa.PFANAME) LIKE LOWER(:${key}))`,
              { [key]: `%${value}%` }
            );
          } else {
            console.log(`key in::::: ${key}`);
            query.andWhere(`LOWER(whitelistReq.${key}) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          }
        }
      });
    }

    query.orderBy('whitelistReq.createDate', 'DESC');

    const limit = Math.max(filter.limit || 10, 1);
    const page = Math.max(filter.page || 1, 1);
    const offset = (page - 1) * limit;

    const requests = await query.offset(offset).limit(limit).getRawMany();

    const resp = new BaseResponseWithNoCountInfo<WhitelistRequestDataDto>(ResponseCodeEnum.SUCCESS);
    resp.hasNext = requests?.length === limit;
    resp.hasPrevious = page > 1;
    resp.content = requests.map(this.mapToDto);

    return resp;
  }

  public mapToDto(request: any): WhitelistRequestDataDto {
    return {
      pk: request.pk,
      requestId: request.requestId,
      mdaCode: request.mdaCode,
      pfaCode: request.pfaCode,
      status: request.status,
      comment: request.comment,
      createDate: request.createDate,
      approvedBy: request.approvedBy?.emailAddress,
      userType: request.userType,
      role: request.role,
      email: request.email,
      organizationName: request.organizationName,
      staffId: request.staffId,
      phoneNumber: request.phoneNumber,
      location: request.location,
      firstName: request.firstName,
      lastName: request.lastName,
      requesterEmail: request.requesterEmail,
    };
  }

  async updateWhitelistRequest(request: UserWhitelistRequest): Promise<UserWhitelistRequest> {
    return await this.whitelistReq.save(request);
  }

  async createRequest(
    dto: CreateWhitelistRequestDto,
    mdaCode: string,
    pfaCode: string
  ): Promise<WhitelistRequestDataDto> {
    const request = new UserWhitelistRequest({});
    request.mdaCode = mdaCode;
    request.pfaCode = pfaCode;
    request.status = ApprovalStatusEnum.PENDING;

    // Create WhitelistDocument entities from the DTO
    const documents = dto.documents.map((doc) => {
      const document = new WhitelistDocument({});
      document.name = doc.documentName;
      document.type = doc.documentType;
      document.data = Buffer.from(doc.documentContent, 'base64');
      document.whitelistReq = request;
      return document;
    });

    request.documents = documents;

    const savedRequest = await this.whitelistReq.save(request);
    return this.mapToDto(savedRequest);
  }
}
