import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'typeorm';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import { AbstractEntity } from '../../database';
import { UserRoleChangeRequest } from './cobra-role-change-request.entity';

@Entity('COBRA_ROLE_CHANGE_DOCUMENT')
export class RoleChangeDocument extends AbstractEntity<RoleChangeDocument> {
  @ManyToOne(() => UserRoleChangeRequest, (roleChangeReq) => roleChangeReq.documents, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'ROLE_CHANGE_PK' })
  roleChangeReq: UserRoleChangeRequest;

  @Column({ name: 'DOCUMENT_NAME', nullable: false })
  name: string;

  @Column({ name: 'DOCUMENT_TYPE', nullable: false })
  type: string; // "application/pdf", "image/jpeg", etc.

  @Column({ name: 'DOCUMENT_DATA', type: 'blob' })
  data: Buffer;
}
