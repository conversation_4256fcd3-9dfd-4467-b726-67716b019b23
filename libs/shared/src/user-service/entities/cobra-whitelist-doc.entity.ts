import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'typeorm';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import { AbstractEntity } from '../../database';
import { UserWhitelistRequest } from './cobra-whitelist-request.entity';

@Entity('COBRA_WHITELIST_DOCUMENT')
export class WhitelistDocument extends AbstractEntity<WhitelistDocument> {
  @ManyToOne(() => UserWhitelistRequest, (whitelistReq) => whitelistReq.documents, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'WHITELIST_REQ_PK' })
  whitelistReq: UserWhitelistRequest;

  @Column({ name: 'DOCUMENT_NAME', nullable: false })
  name: string;

  @Column({ name: 'DOCUMENT_TYPE', nullable: false })
  type: string;

  @Column({ name: 'DOCUMENT_DATA', type: 'blob' })
  data: <PERSON>uff<PERSON>;
}
