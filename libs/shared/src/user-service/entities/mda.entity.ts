import { AbstractEntity } from '@app/shared/database';
import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('TBL_MDA')
export class Mda extends AbstractEntity<Mda> {
  @PrimaryColumn({ name: 'SECTOR_CODE', type: 'varchar2', length: 10 })
  sectorCode: string;

  @PrimaryColumn({ name: 'EMPLOYER_ID', type: 'varchar2', length: 20, unique: true })
  employerId: string;

  @Column({
    name: 'EMPLOYER_NAME',
    type: 'varchar2',
    length: 120,
    nullable: true,
  })
  employerName?: string;

  @Column({ name: 'IPPIS_DATE', type: 'date', nullable: true })
  ippisDate?: Date;

  @Column({
    name: 'ECRS_EMPLOYER_CODE',
    type: 'varchar2',
    length: 15,
    nullable: true,
  })
  ecrsEmployerCode?: string;

  @Column({ name: 'DOMAIN_URL', type: 'varchar2', nullable: true })
  domainUrl: string;
}
