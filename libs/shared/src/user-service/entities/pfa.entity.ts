import { AbstractEntity } from '@app/shared/database';
import { Column, Entity, Join<PERSON>olumn, ManyToOne } from 'typeorm';
import { Pfc } from '@app/shared/user-service/entities/pfc.entity';

@Entity('TBL_PFA')
export class Pfa extends AbstractEntity<Pfa> {
  @Column({ name: 'PFACODE', length: 3, unique: true, nullable: false })
  pfaCode: string;

  @Column({ name: 'ASSOCIATED_PFA_CODE', nullable: true })
  associatedPfaCode: string;

  @Column({ name: 'PFANAME', length: 50, nullable: false })
  pfaName: string;

  @Column({ name: 'PHONE_NUMBER', nullable: true })
  phoneNumber: string;

  @Column({ name: 'ADDRESS1', length: 250, nullable: false })
  address1: string;

  @Column({
    name: 'EMAIL_ADDRESS',
    type: 'varchar2',
    length: 120,
    nullable: false,
  })
  emailAddress: string;

  @Column({
    name: 'ACCOUNT_NAME',
    type: 'varchar2',
    length: 100,
    nullable: true,
  })
  accountName: string;

  @Column({
    name: 'ACCOUNT_NUMBER',
    type: 'varchar2',
    length: 50,
    nullable: true,
  })
  accountNumber: string;

  @ManyToOne(() => Pfc, (pfc) => pfc.pfas, { nullable: false, eager: true })
  @JoinColumn({ name: 'PFCCODE', referencedColumnName: 'pfcCode' })
  pfc: Pfc;

  @Column({ name: 'ADDRESS2', type: 'varchar2', length: 100, nullable: true })
  address2: string;

  @Column({ name: 'ADDRESS3', type: 'varchar2', length: 100, nullable: true })
  address3: string;

  @Column({ name: 'ADDRESS4', type: 'varchar2', length: 100, nullable: true })
  address4: string;

  @Column({ name: 'ADDRESS5', type: 'varchar2', length: 100, nullable: true })
  address5: string;

  @Column({ name: 'DOMAIN_URL', type: 'varchar2', nullable: true })
  domainUrl: string;

  get fullCommaAddress(): string {
    return [this.address1, this.address2, this.address3, this.address4, this.address5]
      .filter((line) => !!line)
      .join(', ');
  }

  get fullHtmlAddress(): string {
    return [this.address1, this.address2, this.address3, this.address4, this.address5]
      .filter((line) => !!line)
      .join('<br />');
  }
}
