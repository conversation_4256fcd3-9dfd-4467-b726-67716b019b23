import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>ique, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { CobraUser } from './cobra-user.entity';
import { <PERSON>uffer } from 'buffer';

@Entity('TBL_SPECIMEN_DATA')
@Unique(['name', 'year']) // Add composite unique constraint
export class SpecimenData extends AbstractEntity<SpecimenData> {
  @Column({ name: 'NAME', nullable: false })
  name: string;

  @Column({ name: 'YEAR', nullable: false })
  year: number;

  @Column({ name: 'MDA_CODE', nullable: true })
  mdaCode: string;

  @Column({ name: 'DOCUMENT_DATA', type: 'blob', nullable: false })
  documentData: Buffer;

  @Column({ name: 'DOC_EXT', nullable: false })
  docExt: string;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: false, lazy: true })
  @JoinColumn({ name: 'CREATED_BY', referencedColumnName: 'pk' })
  createdBy: CobraUser;
}
