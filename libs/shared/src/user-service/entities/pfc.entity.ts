import { AbstractEntity } from '@app/shared/database';
import { Column, Entity, OneToMany } from 'typeorm';
import { Pfa } from '@app/shared/user-service/entities/pfa.entity';

@Entity('TBL_PFC')
export class Pfc extends AbstractEntity<Pfc> {
  @OneToMany(() => Pfa, (pfa) => pfa.pfc)
  pfas: Pfa[];

  @Column({ name: 'PFCCODE', type: 'varchar2', length: 3, unique: true })
  pfcCode: string;

  @Column({ name: 'PFCNAME', type: 'varchar2', length: 50, nullable: true })
  pfcName: string;

  @Column({ name: 'SHORTNAME', type: 'varchar2', length: 20, nullable: true })
  shortName: string;

  @Column({ name: 'CONTACT_PERSON', type: 'varchar2', length: 40, nullable: true })
  contactPerson: string;

  @Column({ name: 'PHONE_NUMBER', type: 'number', nullable: true })
  phoneNumber: string;

  @Column({ name: 'ADDRESS1', type: 'varchar2', length: 100, nullable: true })
  address1: string;

  @Column({ name: 'EMAIL_ADDRESS', type: 'varchar2', length: 100, nullable: true })
  emailAddress: string;

  @Column({ name: 'DATE_OF_REGISTRATION', type: 'date', nullable: true })
  dateOfRegistration: Date;

  @Column({ name: 'ADDRESS2', type: 'varchar2', length: 100, nullable: true })
  address2: string;

  @Column({ name: 'ADDRESS3', type: 'varchar2', length: 100, nullable: true })
  address3: string;

  @Column({ name: 'ADDRESS4', type: 'varchar2', length: 100, nullable: true })
  address4: string;

  @Column({ name: 'ADDRESS5', type: 'varchar2', length: 100, nullable: true })
  address5: string;

  get fullCommaAddress(): string {
    return [this.address1, this.address2, this.address3, this.address4, this.address5]
      .filter((line) => !!line)
      .join(', ');
  }

  get fullHtmlAddress(): string {
    return [this.address1, this.address2, this.address3, this.address4, this.address5]
      .filter((line) => !!line)
      .join('<br />');
  }
}
