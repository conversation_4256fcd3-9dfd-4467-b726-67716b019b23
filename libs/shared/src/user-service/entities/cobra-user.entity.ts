import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>, ManyToMany, ManyToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import * as bcrypt from 'bcryptjs';
import { CobraRole } from '@app/shared/user-service/entities/cobra-role.entity';

@Entity('COBRA_USER')
export class <PERSON><PERSON>ser extends AbstractEntity<CobraUser> {
  @Column({ name: 'RSA_PIN', nullable: true, unique: true })
  rsaPin: string;

  @Column({ name: 'FIRST_NAME', nullable: false })
  firstName: string;

  @Column({ name: 'SURNAME', nullable: false })
  surname: string;

  @Column({ name: 'EMAIL_ADDRESS', nullable: false, unique: true })
  emailAddress: string;

  @Column({ name: 'STAFF_ID', nullable: true, unique: true })
  staffId: string;

  @Column({ name: 'PASSWORD', nullable: false })
  password: string;

  @Column({ name: 'MDA_CODE', nullable: true })
  mdaCode: string;

  @Column({ name: 'PFA_CODE', nullable: true })
  pfaCode: string;

  @Column({ name: 'USER_TYPE', nullable: false })
  userType: string;

  @Column({
    name: 'AVATAR',
    nullable: true,
    type: 'clob',
  })
  avatar: string;

  @Column({
    name: 'SIGNATURE',
    nullable: true,
    type: 'clob',
  })
  signature: string;

  @Column({
    name: 'SUPPORTING_DOCUMENT',
    nullable: true,
    type: 'clob',
  })
  supportingDocument: string;

  @Column({ name: 'PHONE_NUMBER', nullable: true })
  phoneNumber: string;

  @Column({ name: 'LOCATION', nullable: true })
  location: string;

  @Column({
    name: 'GENDER',
    type: 'varchar2',
    length: 1,
    transformer: {
      to: (value: boolean | string): string => {
        if (typeof value === 'boolean') {
          return value ? 'F' : 'M';
        }
        return value;
      },
      from: (value: string): boolean => value === 'F',
    },
  })
  gender: string;

  @Column({
    name: 'FIRST_TIME_LOGIN',
    type: 'number',
    nullable: false,
    default: 1,
    transformer: {
      to: (value: boolean): number => (value ? 1 : 0),
      from: (value: number): boolean => Boolean(value),
    },
  })
  firstTimeLogin: boolean;

  @Column({
    name: 'UPDATE_PASSWORD',
    type: 'number',
    nullable: false,
    default: 0,
    transformer: {
      to: (value: boolean): number => (value ? 1 : 0),
      from: (value: number): boolean => Boolean(value),
    },
  })
  updatePassword: boolean;

  @Column({
    name: 'SECURITY_QUESTIONS',
    type: 'number',
    nullable: false,
    default: 1,
    transformer: {
      to: (value: boolean): number => (value ? 1 : 0),
      from: (value: number): boolean => Boolean(value),
    },
  })
  securityQuestions: boolean;

  @Column({
    name: 'BLACKLISTED',
    type: 'number',
    nullable: false,
    default: 0,
    transformer: {
      to: (value: boolean): number => (value ? 1 : 0),
      from: (value: number): boolean => Boolean(value),
    },
  })
  blacklisted: boolean;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: true })
  @JoinColumn({ name: 'CREATED_BY', referencedColumnName: 'pk' })
  createdBy: CobraUser;

  @ManyToMany(() => CobraRole, { eager: true, nullable: true })
  @JoinTable({
    name: 'COBRA_USER_ROLE',
    joinColumn: {
      name: 'USER_FK',
      referencedColumnName: 'pk',
    },
    inverseJoinColumn: {
      name: 'ROLE_FK',
      referencedColumnName: 'pk',
    },
  })
  roles: CobraRole[];

  @Column({
    name: 'TERMS_AND_CONDITIONS',
    type: 'number',
    nullable: false,
    default: 0,
    transformer: {
      to: (value: boolean): number => (value ? 1 : 0),
      from: (value: number): boolean => Boolean(value),
    },
  })
  termsAndConditions: boolean;

  @Column({ name: 'TERMS_AND_CONDITIONS_TIMESTAMP', type: 'timestamp', nullable: true })
  termsAndConditionsTimestamp: Date;

  @Column({ name: 'TWO_FACTOR_SECRET', nullable: true })
  twoFactorSecret: string;

  @BeforeInsert()
  async hashPassword(): Promise<void> {
    const saltRounds = 10;
    this.password = await bcrypt.hash(this.password, saltRounds);
  }
}
