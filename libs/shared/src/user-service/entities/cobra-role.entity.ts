import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>, ManyToMany, ManyToOne } from 'typeorm';
import { CobraUser } from './cobra-user.entity';
import { AbstractEntity } from '@app/shared/database';
import { CobraPrivilege } from './cobra-priviledge.entity';

@Entity({ name: 'COBRA_ROLE' })
export class <PERSON><PERSON>ole extends AbstractEntity<CobraRole> {
  @Column({ name: 'ROLE', unique: true })
  role: string;

  @Column({ name: 'DESCRIPTION', nullable: true })
  description: string;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: false })
  @JoinColumn({ name: 'CREATED_BY', referencedColumnName: 'pk' })
  createdBy: CobraUser;

  @Column({ name: 'USER_TYPE' })
  userType: string;

  @Column({ name: 'CODE', nullable: true })
  code: string;

  @Column({
    name: 'IS_ADMIN',
    type: 'number',
    precision: 1,
    nullable: false,
    default: 0,
    transformer: {
      // Transform database value (0/1) to boolean (false/true)
      from: (value: number): boolean => value === 1,
      // Transform boolean (false/true) to database value (0/1)
      to: (value: boolean): number => (value ? 1 : 0),
    },
  })
  isAdmin: boolean;

  @ManyToMany(() => CobraPrivilege, { eager: true })
  @JoinTable({
    name: 'COBRA_ROLE_PRIVILEGE',
    joinColumn: {
      name: 'ROLE_FK',
      referencedColumnName: 'pk',
    },
    inverseJoinColumn: {
      name: 'PRIVILEGE_FK',
      referencedColumnName: 'pk',
    },
  })
  privileges: CobraPrivilege[];
}
