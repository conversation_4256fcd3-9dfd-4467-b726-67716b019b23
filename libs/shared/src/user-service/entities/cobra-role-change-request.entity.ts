import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';

import { AbstractEntity } from '../../database';
import { CobraUser } from './cobra-user.entity';
import { RoleChangeDocument } from './cobra-role-doc.entity';
import { Cobra<PERSON><PERSON> } from './cobra-role.entity';

@Entity({ name: 'COBRA_ROLE_CHANGE_REQUEST' })
export class UserRoleChangeRequest extends AbstractEntity<UserRoleChangeRequest> {
  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: false, lazy: true })
  @JoinColumn({ name: 'PRIMARY_USER', referencedColumnName: 'pk' })
  primaryUser: CobraUser;

  /**
   * This is nullable because you can upgrade a role without a secondary user
   * if the admin is not maxed out
   *
   */
  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: true, lazy: true })
  @JoinColumn({ name: 'SECONDARY_USER', referencedColumnName: 'pk' })
  secondary?: CobraUser;

  @Column({ name: 'STATUS', nullable: false })
  status: string;

  @Column({ name: 'COMMENT', nullable: true })
  comment?: string;

  @Column({ name: 'MDA_CODE', nullable: true })
  mdaCode?: string;

  @Column({ name: 'PFA_CODE', nullable: true })
  pfaCode: string;

  @Column({ name: 'USER_TYPE', nullable: false })
  userType: string;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: true, lazy: true })
  @JoinColumn({ name: 'APPROVED_BY', referencedColumnName: 'pk' })
  approvedBy: CobraUser;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: false, lazy: true })
  @JoinColumn({ name: 'REQUESTED_BY_USER', referencedColumnName: 'pk' })
  requestedBy: CobraUser;

  @OneToMany(() => RoleChangeDocument, (document) => document.roleChangeReq, { cascade: true, nullable: false })
  documents: RoleChangeDocument[];

  @ManyToOne(() => CobraRole, (role) => role.pk, { nullable: true, lazy: true })
  @JoinColumn({ name: 'ROLE_FK', referencedColumnName: 'pk' })
  role?: CobraRole;
}
