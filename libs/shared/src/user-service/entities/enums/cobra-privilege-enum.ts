export const CobraPrivileges = {
  BLACKLIST_PENCOM_USER: { code: 'BPEU', description: 'BLACKLIST PENCOM USER', userTypes: ['PENCOM'] },
  BLACKLIST_MDA_USER: { code: 'BMU', description: 'B<PERSON>CKLIST MDA USER', userTypes: ['PENCOM', 'MDA'] },
  BLACKLIST_PFA_USER: { code: 'BPU', description: 'BLACKLIST PFA USER', userTypes: ['PENCOM', 'PFA'] },
  BLACKLIST_RETIREE_USER: { code: 'BRU', description: 'BLACKLIST RETIREE USER', userTypes: ['PENCOM'] },
  DEACTIVATE_PENCOM_USER: { code: 'DPEU', description: 'DEACTIVATE PENCOM USER', userTypes: ['PENCOM'] },
  DEACTIVATE_MDA_USER: { code: 'DMU', description: 'DEACTIVATE MDA USER', userTypes: ['PENCOM', 'MDA'] },
  DEACTIVATE_PFA_USER: { code: 'DPU', description: 'DEACTIVATE PFA USER', userTypes: ['PENCOM', 'PFA'] },
  DEACTIVATE_RETIREE_USER: { code: 'DRU', description: 'DEACTIVATE RETIREE USER', userTypes: ['PENCOM'] },
  MULTIPLE_PIN_RESOLUTION: { code: 'MPR', description: 'MULTIPLE PIN RESOLUTION', userTypes: ['PENCOM'] },
  REASSIGN_TASK: { code: 'RAT', description: 'REASSIGN TASK', userTypes: ['PENCOM'] },
  ADMINISTRATION_MODULE: { code: 'ADM', description: 'ADMINISTRATION MODULE', userTypes: ['PENCOM'] },
  ADMINISTRATION_PFA_SUB_MODULE: { code: 'PFASM', description: 'ADMINISTRATION PFA SUB MODULE', userTypes: ['PENCOM'] },
  RETRIEVE_PFA: { code: 'RPFA', description: 'RETRIEVE PFA', userTypes: ['PENCOM'] },
  CREATE_PFA: { code: 'CPFA', description: 'CREATE PFA', userTypes: ['PENCOM'] },
  DELETE_PFA: { code: 'DPFA', description: 'DELETE PFA', userTypes: ['PENCOM'] },
  EDIT_PFA: { code: 'EPFA', description: 'EDIT PFA', userTypes: ['PENCOM'] },
  ADMINISTRATION_PFC_SUB_MODULE: { code: 'PFCSM', description: 'ADMINISTRATION PFC SUB MODULE', userTypes: ['PENCOM'] },
  CREATE_PFC: { code: 'CPFC', description: 'CREATE PFC', userTypes: ['PENCOM'] },
  DELETE_PFC: { code: 'DPFC', description: 'DELETE PFC', userTypes: ['PENCOM'] },
  EDIT_PFC: { code: 'EPFC', description: 'EDIT PFC', userTypes: ['PENCOM'] },
  RETRIEVE_PFC: { code: 'RPFC', description: 'EDIT PFC', userTypes: ['PENCOM'] },
  ADMINISTRATION_MDA_SUB_MODULE: { code: 'MDASM', description: 'ADMINISTRATION MDA SUB MODULE', userTypes: ['PENCOM'] },
  CREATE_MDA: { code: 'CMDA', description: 'CREATE MDA', userTypes: ['PENCOM'] },
  DELETE_MDA: { code: 'DMDA', description: 'DELETE MDA', userTypes: ['PENCOM'] },
  EDIT_MDA: { code: 'EMDA', description: 'EDIT MDA', userTypes: ['PENCOM'] },
  RETRIEVE_MDA: { code: 'RMDA', description: 'EDIT MDA', userTypes: ['PENCOM'] },
  MULTIPLE_PIN_RESOLUTION_CONFIRM_PAYMENT: {
    code: 'MPRCP',
    description: 'MULTIPLE PIN RESOLUTION CONFIRM PAYMENT',
    userTypes: ['PENCOM'],
  },
  ALL_MULTIPLE_PIN_RESOLUTION: {
    code: 'AMPR',
    description: 'ALL MULTIPLE PIN RESOLUTION STATUSES',
    userTypes: ['PENCOM'],
  },
  CREATE_MULTIPLE_PIN_RESOLUTION: {
    code: 'NMPR',
    description: 'CREATE MULTIPLE PIN RESOLUTION',
    userTypes: ['PENCOM'],
  },
  VIEW_MULTIPLE_PIN_RESOLUTION: {
    code: 'VMPR',
    description: 'VIEW MULTIPLE PIN RESOLUTION',
    userTypes: ['PENCOM'],
  },
  REVIEW_MULTIPLE_PIN_RESOLUTION: {
    code: 'RMPR',
    description: 'REVIEW MULTIPLE PIN RESOLUTION',
    userTypes: ['PENCOM'],
  },
  VIEW_MULTIPLE_PIN_RESOLUTION_REVIEW: {
    code: 'VRMPR',
    description: 'VIEW MULTIPLE PIN RESOLUTION REVIEW',
    userTypes: ['PENCOM'],
  },
  // New role management privileges
  ROLE_MANAGEMENT_PAGE: {
    code: 'RMP',
    description: 'ROLE MANAGEMENT PAGE',
    userTypes: ['PENCOM'],
  },
  ROLE_MANAGEMENT_CREATE: {
    code: 'RMC',
    description: 'ROLE MANAGEMENT CREATE',
    userTypes: ['PENCOM'],
  },
  ROLE_MANAGEMENT_DEACTIVATE: {
    code: 'RMD',
    description: 'ROLE MANAGEMENT DEACTIVATE',
    userTypes: ['PENCOM'],
  },
  ROLE_MANAGEMENT_EDIT: {
    code: 'RME',
    description: 'ROLE MANAGEMENT EDIT',
    userTypes: ['PENCOM'],
  },
  // Upgrade request privileges
  UPGRADE_REQUEST_PAGE: {
    code: 'URP',
    description: 'UPGRADE REQUEST PAGE',
    userTypes: ['PENCOM', 'MDA', 'PFA'],
  },
  UPGRADE_REQUEST_ADMIN: {
    code: 'URA',
    description: 'UPGRADE REQUEST ADMIN',
    userTypes: ['PENCOM'],
  },
  UPGRADE_REQUEST_CREATE: {
    code: 'UPC',
    description: 'UPGRADE REQUEST CREATE',
    userTypes: ['MDA', 'PFA'],
  },
  // Whitelist request privileges
  WHITELIST_REQUEST_PAGE: {
    code: 'WRP',
    description: 'WHITELIST REQUEST PAGE',
    userTypes: ['PENCOM', 'MDA', 'PFA'],
  },
  WHITELIST_REQUEST_ADMIN: {
    code: 'WRA',
    description: 'WHITELIST REQUEST ADMIN',
    userTypes: ['PENCOM'],
  },
  WHITELIST_REQUEST_CREATE: {
    code: 'WRC',
    description: 'WHITELIST REQUEST CREATE',
    userTypes: ['MDA', 'PFA'],
  },
  // User management privileges
  PENCOM_USER_MANAGEMENT: {
    code: 'PUM',
    description: 'PENCOM USER MANAGEMENT',
    userTypes: ['PENCOM'],
  },
  PFA_USER_MANAGEMENT: {
    code: 'PFUM',
    description: 'PFA USER MANAGEMENT',
    userTypes: ['PENCOM', 'PFA'],
  },
  MDA_USER_MANAGEMENT: {
    code: 'MUM',
    description: 'MDA USER MANAGEMENT',
    userTypes: ['PENCOM', 'MDA'],
  },
  USER_MANAGEMENT_MODULE: {
    code: 'UMS',
    description: 'USER MANAGEMENT MODULE',
    userTypes: ['PENCOM', 'MDA', 'PFA'],
  },
  USER_MANAGEMENT_DEACTIVATION: {
    code: 'UMD',
    description: 'USER MANAGEMENT DEACTIVATION',
    userTypes: ['PENCOM', 'MDA', 'PFA'],
  },
  USER_MANAGEMENT_ACTIVATION: {
    code: 'UMA',
    description: 'USER MANAGEMENT ACTIVATION',
    userTypes: ['PENCOM', 'MDA', 'PFA'],
  },
  USER_MANAGEMENT_WHITELIST: {
    code: 'UMW',
    description: 'USER MANAGEMENT WHITELIST',
    userTypes: ['PENCOM'],
  },
  USER_MANAGEMENT_BLACKLIST: {
    code: 'UMB',
    description: 'USER MANAGEMENT BLACKLIST',
    userTypes: ['PENCOM', 'MDA', 'PFA'],
  },
  FLAG_RSA_HOLDER_SUBMODULE: {
    code: 'FRHSM',
    description: 'FLAG RSA HOLDER ACCOUNT SUBMODULE',
    userTypes: ['PENCOM'],
  },
  CREATE_ACCRUED_RIGHTS_LEDGER: {
    code: 'CARL',
    description: 'CREATE ACCRUED RIGHTS LEDGER',
    userTypes: ['PENCOM'],
  },
  RETRIEVE_ACCRUED_RIGHTS_LEDGER: {
    code: 'RARL',
    description: 'RETRIEVE ACCRUED RIGHTS LEDGER',
    userTypes: ['PENCOM'],
  },
  CREATE_CONTRIBUTION_LEDGER: {
    code: 'CCL',
    description: 'CREATE CONTRIBUTION LEDGER',
    userTypes: ['PENCOM'],
  },
  RETRIEVE_CONTRIBUTION_LEDGER: {
    code: 'RCL',
    description: 'RETRIEVE CONTRIBUTION LEDGER',
    userTypes: ['PENCOM'],
  },
  FLAG_RSA_HOLDER: {
    code: 'FRH',
    description: 'FLAG RSA HOLDER ACCOUNT',
    userTypes: ['PENCOM'],
  },
  EDIT_FLAG_RSA_HOLDER: {
    code: 'EFR',
    description: 'EDIT FLAG RSA HOLDER ACCOUNT',
    userTypes: ['PENCOM'],
  },
  RETRIEVE_FLAG_RSA_HOLDER: {
    code: 'RFR',
    description: 'RETRIEVE FLAGGED RSA HOLDER ACCOUNT',
    userTypes: ['PENCOM'],
  },
  USER_MANAGEMENT_CREATE: {
    code: 'UMC',
    description: 'USER MANAGEMENT CREATE',
    userTypes: ['PENCOM', 'MDA', 'PFA'],
  },
  USER_MANAGEMENT_EDIT: {
    code: 'UME',
    description: 'USER MANAGEMENT EDIT',
    userTypes: ['PENCOM', 'MDA', 'PFA'],
  },
  // Amendment Request privileges
  AMENDMENT_REQUEST_PAGE: {
    code: 'AMP',
    description: 'AMENDMENT REQUEST PAGE',
    userTypes: ['PENCOM', 'PFA'],
  },
  AMENDMENT_REQUEST_CREATION: {
    code: 'AMC',
    description: 'AMENDMENT REQUEST CREATION',
    userTypes: ['PFA'],
  },
  // Multiple PIN Resolution privileges
  MULTIPLE_PIN_RESOLUTION_PAGE: {
    code: 'MPRP',
    description: 'MULTIPLE PIN RESOLUTION PAGE',
    userTypes: ['PENCOM', 'PFA'],
  },
  MULTIPLE_PIN_RESOLUTION_CREATE: {
    code: 'MPRC',
    description: 'MULTIPLE PIN RESOLUTION CREATE',
    userTypes: ['PENCOM'],
  },
  MULTIPLE_PIN_TRANSACTION_HISTORY: {
    code: 'MPTH',
    description: 'MULTIPLE PIN TRANSACTION HISTORY',
    userTypes: ['PFA'],
  },
  MULTIPLE_PIN_GENERATE_DOCUMENT: {
    code: 'MPGD',
    description: 'MULTIPLE PIN GENERATE DOCUMENT',
    userTypes: ['PENCOM'],
  },
  MULTIPLE_PIN_CONFIRM_PAYMENT: {
    code: 'MPCP',
    description: 'MULTIPLE PIN CONFIRM PAYMENT',
    userTypes: ['PFA'],
  },
  // Record management privileges
  UPDATE_RECORD_MODULE: {
    code: 'URS',
    description: 'UPDATE RECORD SUB MODULE',
    userTypes: ['PFA'],
  },
  UPDATE_RECORD: {
    code: 'UDR',
    description: 'UPDATE RECORD',
    userTypes: ['PFA'],
  },
  // Enrollment and Record privileges
  RETIREE_RECORD_PAGE: {
    code: 'RRP',
    description: 'RETIREE RECORD PAGE',
    userTypes: ['PENCOM', 'PFA'],
  },
  ENROLLMENT_PAGE: {
    code: 'EMP',
    description: 'ENROLLMENT PAGE',
    userTypes: ['PENCOM', 'PFA'],
  },
  PENDING_APPROVAL_PAGE: {
    code: 'PAP',
    description: 'PENDING APPROVAL PAGE',
    userTypes: ['PENCOM', 'PFA'],
  },
  // Specimen privileges
  SPECIMEN_PAGE: {
    code: 'SMP',
    description: 'SPECIMEN PAGE',
    userTypes: ['MDA', 'PENCOM', 'PFA'],
  },
  SPECIMEN_CREATION: {
    code: 'SMC',
    description: 'SPECIMEN CREATION',
    userTypes: ['MDA'],
  },
  SPECIMEN_DELETION: {
    code: 'SMD',
    description: 'SPECIMEN DELETION',
    userTypes: ['PENCOM', 'MDA'],
  },
  // Nominal Roll privileges
  NOMINAL_ROLL_PAGE: {
    code: 'NRP',
    description: 'NOMINAL ROLL PAGE',
    userTypes: ['PENCOM', 'PFA', 'MDA'],
  },
  NOMINAL_ROLL_UPLOAD: {
    code: 'NRU',
    description: 'NOMINAL ROLL UPLOAD',
    userTypes: ['PENCOM', 'PFA', 'MDA'],
  },
  // Employee Record privileges
  EMPLOYEE_RECORD_PAGE: {
    code: 'ERP',
    description: 'EMPLOYEE RECORD PAGE',
    userTypes: ['PENCOM', 'MDA'],
  },
  EMPLOYEE_RECORD_UPLOAD: {
    code: 'ERU',
    description: 'EMPLOYEE RECORD UPLOAD',
    userTypes: ['PENCOM', 'MDA'],
  },
  // MDA Upload privileges
  MDA_RETIREE_DATA_UPLOAD: {
    code: 'MRDU',
    description: 'MDA RETIREE DATA UPLOAD',
    userTypes: ['PENCOM', 'MDA'],
  },
  MDA_DECEASED_DATA_UPLOAD: {
    code: 'MDDU',
    description: 'MDA DECEASED DATA UPLOAD',
    userTypes: ['PENCOM', 'MDA'],
  },
  ACCRUED_RIGHTS_UPLOAD: {
    code: 'ARU',
    description: 'ACCRUED RIGHTS UPLOAD',
    userTypes: ['PENCOM'],
  },
  // Accrued Rights privileges
  ACCRUED_RIGHT_PAGE: {
    code: 'ARP',
    description: 'ACCRUED RIGHT PAGE',
    userTypes: ['PENCOM', 'PFA'],
  },
  ACCRUED_RIGHT_PAYMENT_UPLOAD: {
    code: 'ARPU',
    description: 'ACCRUED RIGHT PAYMENT UPLOAD',
    userTypes: ['PENCOM', 'PFA'],
  },
  LEGACY_RECORD_SUB_MODULE: {
    code: 'LRS',
    description: 'LEGACY RECORD SUB MODULE',
    userTypes: ['PENCOM'],
  },
  // Record and validation privileges
  LEGACY_RECORD_PAGE: {
    code: 'LRP',
    description: 'LEGACY RECORD PAGE',
    userTypes: ['PENCOM', 'PFA'],
  },
  RECORD_VALIDATION_PAGE: {
    code: 'RVP',
    description: 'RECORD VALIDATION PAGE',
    userTypes: ['PENCOM', 'PFA'],
  },
  RECORD_VALIDATION: {
    code: 'REV',
    description: 'RECORD VALIDATION',
    userTypes: ['PENCOM', 'PFA'],
  },
  AUDIT_VALIDATION_PAGE: {
    code: 'AVP',
    description: 'AUDIT VALIDATION PAGE',
    userTypes: ['PENCOM'],
  },
  AUDIT_VALIDATION: {
    code: 'ADV',
    description: 'AUDIT VALIDATION',
    userTypes: ['PENCOM'],
  },

  // Certificate privileges
  CERTIFICATE_REVIEW_PAGE: {
    code: 'CRP',
    description: 'CERTIFICATE REVIEW PAGE',
    userTypes: ['PENCOM', 'PFA'],
  },
  CERTIFICATE_REVIEW: {
    code: 'CRV',
    description: 'CERTIFICATE REVIEW',
    userTypes: ['PENCOM', 'PFA'],
  },
  // Batch management privileges
  BATCH_RECORDS_PAGE: {
    code: 'BRP',
    description: 'BATCH RECORDS PAGE',
    userTypes: ['PENCOM', 'PFA'],
  },
  BATCH_MEMO_GENERATION: {
    code: 'BMG',
    description: 'BATCH MEMO GENERATION',
    userTypes: ['PENCOM'],
  },
  BATCH_ACCOUNT_MEMO_GENERATION: {
    code: 'BAMG',
    description: 'BATCH ACCOUNT MEMO GENERATION',
    userTypes: ['PENCOM'],
  },
  BATCH_STAMP_SCHEDULE: {
    code: 'BSS',
    description: 'BATCH STAMP SCHEDULE',
    userTypes: ['PENCOM'],
  },
  BATCH_DOCUMENT_UPLOAD: {
    code: 'BDU',
    description: 'BATCH DOCUMENT UPLOAD',
    userTypes: ['PENCOM'],
  },
  // Settings management privileges
  SETTING_PAGE: {
    code: 'SEP',
    description: 'SETTING PAGE',
    userTypes: ['PENCOM'],
  },
  SETTING_EDIT: {
    code: 'SEE',
    description: 'SETTING EDIT',
    userTypes: ['PENCOM'],
  },
  // Side menu privileges
  ROLE_MANAGEMENT_SUB_MODULE: {
    code: 'RMS',
    description: 'ROLE MANAGEMENT SUB MODULE',
    userTypes: ['PENCOM'],
  },
  PFA_USER_MANAGEMENT_SUB_MODULE: {
    code: 'PFUMS',
    description: 'PFA USER MANAGEMENT SUB MODULE',
    userTypes: ['PENCOM'],
  },
  MDA_USER_MANAGEMENT_SUB_MODULE: {
    code: 'MUMS',
    description: 'MDA USER MANAGEMENT SUB MODULE',
    userTypes: ['PENCOM'],
  },
  WHITELIST_REQUEST_SUB_MODULE: {
    code: 'WRS',
    description: 'WHITELIST REQUEST SUB MODULE',
    userTypes: ['PENCOM', 'MDA', 'PFA'],
  },
  UPGRADE_REQUEST_SUB_MODULE: {
    code: 'UGRS',
    description: 'UPGRADE REQUEST SUB MODULE',
    userTypes: ['PENCOM', 'MDA', 'PFA'],
  },
  MULTIPLE_PIN_SUB_MODULE: {
    code: 'MPS',
    description: 'MULTIPLE PIN SUB MODULE',
    userTypes: ['PENCOM', 'PFA'],
  },

  AMENDMENT_REQUEST_MODULE: {
    code: 'AMS',
    description: 'AMENDMENT REQUEST MODULE',
    userTypes: ['PENCOM', 'PFA'],
  },
  NOMINAL_ROLL_MODULE: {
    code: 'NRM',
    description: 'NOMINAL ROLL MODULE',
    userTypes: ['PENCOM'],
  },
  COMPUTE_RECORD_SUB_MODULE: {
    code: 'CRSM',
    description: 'COMPUTE RECORD SUB MODULE',
    userTypes: ['PENCOM'],
  },
  COMPUTE_RECORD_PAGE: {
    code: 'CMRP',
    description: 'COMPUTE RECORD PAGE',
    userTypes: ['PENCOM'],
  },
  NOMINAL_ROLL_SUB_MODULE: {
    code: 'NRS',
    description: 'NOMINAL ROLL SUB MODULE',
    userTypes: ['PENCOM', 'PFA', 'MDA'],
  },
  SPECIMEN_SUB_MODULE: {
    code: 'SMS',
    description: 'SPECIMEN SUB MODULE',
    userTypes: ['PFA', 'MDA'],
  },
  SETTING_MODULE: {
    code: 'SES',
    description: 'SETTING MODULE',
    userTypes: ['PENCOM'],
  },
  BATCH_RECORDS_SUB_MODULE: {
    code: 'BRS',
    description: 'BATCH RECORDS SUB MODULE',
    userTypes: ['PENCOM', 'PFA'],
  },
  ACCRUED_RIGHT_SUB_MODULE: {
    code: 'ARS',
    description: 'ACCRUED RIGHT SUB MODULE',
    userTypes: ['PENCOM', 'PFA'],
  },
  RECORD_VALIDATION_SUB_MODULE: {
    code: 'RVS',
    description: 'RECORD VALIDATION SUB MODULE',
    userTypes: ['PENCOM', 'PFA'],
  },
  AUDIT_VALIDATION_SUB_MODULE: {
    code: 'AVS',
    description: 'AUDIT VALIDATION SUB MODULE',
    userTypes: ['PENCOM'],
  },
  CERTIFICATE_REVIEW_SUB_MODULE: {
    code: 'CRS',
    description: 'CERTIFICATE REVIEW SUB MODULE',
    userTypes: ['PENCOM', 'PFA'],
  },
  // New privileges
  PENDING_APPROVAL_SUB_MODULE: {
    code: 'PAS',
    description: 'PENDING APPROVAL SUB MODULE',
    userTypes: ['PENCOM', 'PFA'],
  },
  TRANSACTION_HISTORY_MODULE: {
    code: 'THS',
    description: 'TRANSACTION HISTORY MODULE',
    userTypes: ['PENCOM', 'PFA'],
  },
  TRANSACTION_HISTORY_PAGE: {
    code: 'THP',
    description: 'TRANSACTION HISTORY PAGE',
    userTypes: ['PENCOM', 'PFA'],
  },
  PAYMENT_MODULE: {
    code: 'PMS',
    description: 'PAYMENT MODULE',
    userTypes: ['PENCOM', 'PFA'],
  },
  REPORT_MODULE: {
    code: 'RPS',
    description: 'REPORT MODULE',
    userTypes: ['PENCOM', 'PFA', 'MDA'],
  },
  DOCUMENT_MODULE: {
    code: 'DCS',
    description: 'DOCUMENT SUB MODULE',
    userTypes: ['PENCOM', 'PFA'],
  },
  MEMO_SUB_MODULE: {
    code: 'MMS',
    description: 'MEMO SUB MODULE',
    userTypes: ['PFA'],
  },
  MEMO_PAGE: {
    code: 'MMP',
    description: 'MEMO PAGE',
    userTypes: ['PFA'],
  },
  MEMO_EDIT: {
    code: 'MME',
    description: 'MEMO EDIT',
    userTypes: ['PFA'],
  },
  UPLOAD_MODULE: {
    code: 'UPS',
    description: 'UPLOAD SUB MODULE',
    userTypes: ['MDA'],
  },
  DATA_UPLOAD_SUB_MODULE: {
    code: 'DPS',
    description: 'DATA UPLOAD SUB MODULE',
    userTypes: ['MDA'],
  },
  DATA_UPLOAD_PAGE: {
    code: 'DPP',
    description: 'DATA UPLOAD PAGE',
    userTypes: ['MDA'],
  },
  RETIREE_RECORD_SUB_MODULE: {
    code: 'RRS',
    description: 'RETIREE RECORD SUB MODULE',
    userTypes: ['PENCOM', 'PFA'],
  },
  ENROLMENT_SUB_MODULE: {
    code: 'ERS',
    description: 'ENROLMENT SUB MODULE',
    userTypes: ['PFA'],
  },
  ENROLLMENT: {
    code: 'ERL',
    description: 'ENROLLMENT',
    userTypes: ['PENCOM', 'PFA'],
  },
  CANCEL_AMENDMENT_REQUEST: {
    code: 'CAR',
    description: 'CANCEL AMENDMENT REQUEST',
    userTypes: ['PENCOM', 'PFA'],
  },
  APPROVE_AMENDMENT_REQUEST: {
    code: 'AAR',
    description: 'APPROVE AMENDMENT REQUEST',
    userTypes: ['PENCOM'],
  },
  REJECT_AMENDMENT_REQUEST: {
    code: 'RAR',
    description: 'REJECT AMENDMENT REQUEST',
    userTypes: ['PENCOM'],
  },
  CANCEL_MULTIPLE_PIN_REQUEST: {
    code: 'CMP',
    description: 'CANCEL MULTIPLE PIN REQUEST',
    userTypes: ['PENCOM'],
  },
  REJECT_MULTIPLE_PIN_REQUEST: {
    code: 'MPRR',
    description: 'REJECT MULTIPLE PIN REQUEST',
    userTypes: ['PENCOM'],
  },
  RECORD_MANAGEMENT_MODULE: {
    code: 'RMM',
    description: 'RECORD MANAGEMENT MODULE',
    userTypes: ['PENCOM'],
  },

  MAIL_HISTORY_PAGE: {
    code: 'MHP',
    description: 'MAIL HISTORY PAGE',
    userTypes: ['PENCOM'],
  },
  MAIL_HISTORY_RESEND: {
    code: 'MHR',
    description: 'MAIL HISTORY RESEND',
    userTypes: ['PENCOM'],
  },
  MAIL_HISTORY_MODULE: {
    code: 'MHS',
    description: 'MAIL HISTORY MODULE',
    userTypes: ['PENCOM'],
  },
  PORTRAIT_OVERRIDE: {
    code: 'POV',
    description: 'PORTRAIT OVERRIDE',
    userTypes: ['PFA'],
  },
  RESET_2FA_SECRET: {
    code: 'R2S',
    description: 'RESET 2FA SECRET',
    userTypes: ['PENCOM'],
  },
  FETCH_TBE_ORGANISATIONS: {
    code: 'FTO',
    description: 'FETCH TBE ORGANISATIONS',
    userTypes: ['PENCOM'],
  },
  CREATE_TBE_ORGANISATIONS: {
    code: 'CTO',
    description: 'CREATE TBE ORGANISATIONS',
    userTypes: ['PENCOM'],
  },
  EDIT_TBE_ORGANISATIONS: {
    code: 'ETO',
    description: 'EDIT TBE ORGANISATIONS',
    userTypes: ['PENCOM'],
  },
} as const;

export type CobraPrivilegeKey = keyof typeof CobraPrivileges;
export type CobraPrivilege = (typeof CobraPrivileges)[CobraPrivilegeKey];

// Utility type to ensure unique codes at compile time
type EnsureUnique<T extends Record<string, { code: string }>> = {
  [K in keyof T]: Extract<K, { [P in keyof T]: T[P]['code'] extends T[K]['code'] ? P : never }[keyof T]> extends K
    ? K
    : never;
}[keyof T];

// Validate uniqueness at compile time
type ValidateCobraPrivileges = EnsureUnique<typeof CobraPrivileges>;

const privilegeCodes = Object.values(CobraPrivileges).map((privilege) => privilege.code);
const duplicateCodes = privilegeCodes.filter((code, index, arr) => arr.indexOf(code) !== index);
const uniqueDuplicates = [...new Set(duplicateCodes)]; // Remove duplicate occurrences

if (uniqueDuplicates.length > 0) {
  console.error(`Duplicate privilege codes found: ${JSON.stringify(uniqueDuplicates)}`);
  throw new Error(`Duplicate privilege codes found: ${JSON.stringify(uniqueDuplicates)}`);
}

export function getPrivilegeDescriptionsByCodes(codes: string[]): string[] {
  return codes.map((code) => {
    const privilege = Object.values(CobraPrivileges).find((priv) => priv.code === code);
    return privilege?.description ?? `Unknown privilege (${code})`;
  });
}
