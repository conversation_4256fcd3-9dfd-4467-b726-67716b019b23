import { <PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>any } from 'typeorm';
import { AbstractEntity } from '../../database';
import { CobraUser } from './cobra-user.entity';
import { WhitelistDocument } from './cobra-whitelist-doc.entity';

@Entity({ name: 'COBRA_WHITELIST_REQUEST' })
export class UserWhitelistRequest extends AbstractEntity<UserWhitelistRequest> {
  @Column({ name: 'REQUEST_ID', unique: true })
  requestId: string;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: false, lazy: true })
  @JoinColumn({ name: 'REQUESTED_BY_USER', referencedColumnName: 'pk' })
  requestedBy: CobraUser;

  @Column({ name: 'STATUS', nullable: false })
  status: string;

  @Column({ name: 'COMMENT', nullable: true })
  comment?: string;

  @Column({ name: 'MDA_CODE', nullable: true })
  mdaCode?: string;

  @Column({ name: 'PFA_CODE', nullable: true })
  pfaCode: string;

  @Column({ name: 'USER_TYPE', nullable: false })
  userType: string;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: false })
  @JoinColumn({ name: 'IMPACTED_USER', referencedColumnName: 'pk' })
  user: CobraUser;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: true, lazy: true })
  @JoinColumn({ name: 'APPROVED_BY', referencedColumnName: 'pk' })
  approvedBy: CobraUser;

  @OneToMany(() => WhitelistDocument, (document) => document.whitelistReq, {
    cascade: true,
    nullable: false,
    lazy: true,
  })
  documents: WhitelistDocument[];
}
