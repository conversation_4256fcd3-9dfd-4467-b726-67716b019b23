import { PartialType } from '@nestjs/mapped-types';
import { IsBoolean, IsEmail, IsNotEmpty, IsNumberString, IsOptional, IsString, Length } from 'class-validator';

export class CreatePfaDto {
  @IsString()
  @Length(3, 3)
  @IsNotEmpty()
  pfaCode: string;

  @IsString()
  @IsOptional()
  associatedPfaCode?: string;

  @IsString()
  @Length(1, 50)
  @IsNotEmpty()
  pfaName: string;

  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @IsString()
  @Length(1, 250)
  @IsNotEmpty()
  address1: string;

  @IsEmail()
  @Length(1, 120)
  @IsNotEmpty()
  emailAddress: string;

  @IsOptional()
  @IsString()
  @Length(0, 100)
  accountName?: string;

  @IsOptional()
  @IsString()
  @Length(0, 50)
  accountNumber?: string;

  @IsString()
  @IsNotEmpty()
  pfcCode: string;

  @IsOptional()
  @IsString()
  @Length(0, 100)
  address2?: string;

  @IsOptional()
  @IsString()
  @Length(0, 100)
  address3?: string;

  @IsOptional()
  @IsString()
  @Length(0, 100)
  address4?: string;

  @IsOptional()
  @IsString()
  @Length(0, 100)
  address5?: string;

  @IsOptional()
  @IsString()
  domainUrl?: string;

  @IsOptional()
  @IsBoolean()
  active?: boolean;
}

export class UpdatePfaDto extends PartialType(CreatePfaDto) {
  @IsNotEmpty()
  @IsString()
  pfa: string;
}

export class CreatePfcDto {
  @IsString()
  @Length(1, 3)
  pfcCode: string;

  @IsOptional()
  @IsString()
  @Length(1, 50)
  pfcName?: string;

  @IsOptional()
  @IsString()
  @Length(1, 20)
  shortName?: string;

  @IsOptional()
  @IsString()
  @Length(1, 40)
  contactPerson?: string;

  @IsOptional()
  @IsNumberString()
  phoneNumber?: string;

  @IsOptional()
  @IsString()
  @Length(1, 100)
  address1?: string;

  @IsOptional()
  @IsEmail()
  emailAddress?: string;

  @IsOptional()
  @IsString()
  dateOfRegistration?: string;

  @IsOptional()
  @IsString()
  @Length(1, 100)
  address2?: string;

  @IsOptional()
  @IsString()
  @Length(1, 100)
  address3?: string;

  @IsOptional()
  @IsString()
  @Length(1, 100)
  address4?: string;

  @IsOptional()
  @IsString()
  @Length(1, 100)
  address5?: string;

  @IsOptional()
  @IsBoolean()
  active?: boolean;
}

export class UpdatePfcDto extends PartialType(CreatePfcDto) {
  @IsNotEmpty()
  @IsString()
  pfc: string;
}

export class CreateMdaDto {
  @IsNotEmpty()
  @IsString()
  @Length(1, 10)
  sectorCode: string;

  @IsNotEmpty()
  @IsString()
  @Length(1, 20)
  employerId: string;

  @IsOptional()
  @IsString()
  @Length(0, 120)
  employerName?: string;

  @IsOptional()
  @IsString()
  ippisDate?: string;

  @IsOptional()
  @IsString()
  @Length(0, 15)
  ecrsEmployerCode?: string;

  @IsOptional()
  @IsBoolean()
  active?: boolean;

  @IsOptional()
  @IsString()
  domainUrl?: string;
}

export class UpdateMdaDto extends PartialType(CreateMdaDto) {
  @IsNotEmpty()
  @IsString()
  employerId: string;
}
