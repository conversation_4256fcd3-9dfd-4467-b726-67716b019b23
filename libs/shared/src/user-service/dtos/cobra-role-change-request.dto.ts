import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsEnum, IsNumber, IsArray, IsString } from 'class-validator';
import { ApprovalStatusEnum } from '../../enums/ApprovalStatusEnum';

export class RoleChangeDocumentDto {
  @ApiProperty({ description: 'Document Name', example: 'document.pdf' })
  documentName: string;

  @ApiProperty({ description: 'Document Type', example: 'application/pdf' })
  documentType: string;

  @ApiProperty({ description: 'Document Content', example: 'base64 encoded string' })
  documentContent: string;
}

export class CreateRoleChangeRequestDto {
  @ApiProperty({ description: 'Primary user Email making the request', example: '<EMAIL>', required: true })
  @IsString()
  @IsNotEmpty()
  primaryUserEmail: string;

  @ApiProperty({ description: 'Secondary user Email (if applicable)', example: '<EMAIL>', required: false })
  @IsString()
  @IsOptional()
  secondaryUserEmail?: string;

  @ApiProperty({
    description: 'Comment regarding the role change',
    example: 'Requesting upgrade due to new responsibilities',
    required: false,
  })
  @IsOptional()
  comment?: string;

  @ApiProperty({ description: 'Documents captured', type: RoleChangeDocumentDto, isArray: true })
  @IsArray()
  @IsNotEmpty()
  documents: RoleChangeDocumentDto[];
}
export class UpdateUserRoleChangeRequestDto {
  requestId?: number;

  @ApiProperty({ description: 'Status to update', enum: ApprovalStatusEnum })
  @IsEnum(ApprovalStatusEnum)
  @IsNotEmpty()
  status: ApprovalStatusEnum;

  @ApiProperty({ description: 'Approver comments', example: 'Approved' })
  @IsString()
  comment: string;
}

export class RoleChangeRequestDataDto {
  pk: number;
  mdaCode: string;
  pfaCode: string;
  status: string;
  comment: string;
  createDate: Date;
  approvedBy: string;
  requestedBy: string;
  primaryUserEmail: string;
}

export class RoleChangeRequestDetailedDto {
  pk: number;

  @ApiProperty({ description: 'MDA/PFA Name' })
  organizationName: string;

  @ApiProperty({ description: 'Primary User First Name' })
  primaryUserFirstName: string;

  @ApiProperty({ description: 'Primary User Surname' })
  primaryUserSurname: string;

  @ApiProperty({ description: 'Staff ID' })
  staffId: string;

  @ApiProperty({ description: 'Secondary User Role' })
  secondaryUserRole: string;

  @ApiProperty({ description: 'Primary User Email' })
  primaryUserEmail: string;

  @ApiProperty({ description: 'Primary User Phone Number' })
  primaryUserPhoneNumber: string;

  @ApiProperty({ description: 'Location' })
  location: string;

  @ApiProperty({ description: 'Secondary User Email' })
  secondaryUserEmail: string;

  @ApiProperty({ description: 'Secondary User First Name' })
  secondaryUserFirstName: string;

  @ApiProperty({ description: 'Requester Email' })
  requesterEmail: string;

  @ApiProperty({ description: 'Request Creation Date' })
  createDate: Date;

  @ApiProperty({ description: 'Request Status' })
  status: string;

  @ApiProperty({ description: 'Request Comment' })
  comment: string;

  @ApiProperty({ description: 'Primary User Role' })
  primaryUserRole: string;
}
