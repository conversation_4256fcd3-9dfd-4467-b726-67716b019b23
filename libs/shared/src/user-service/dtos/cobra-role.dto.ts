import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { CobraPrivilege } from '@app/shared/user-service/entities/cobra-priviledge.entity';
import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsArray, IsBoolean, IsDateString, IsInt, IsNotEmpty, IsOptional, IsString, Min } from 'class-validator';

export class CreateRoleDto {
  @ApiProperty({
    description: 'Name of the role',
    example: 'Admin',
  })
  @IsNotEmpty()
  @IsString()
  roleName: string;

  @ApiProperty({
    description: 'Description of the role',
    example: 'Administrator role with full privileges',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'List of privileges associated with the role',
    type: [CobraPrivilege],
  })
  @IsArray()
  privileges: CobraPrivilege[];

  @ApiProperty({
    description: 'Type of user associated with the role',
    enum: UserTypeEnum,
  })
  @IsNotEmpty()
  @IsString()
  userType: UserTypeEnum;

  @ApiProperty({
    description: 'Flag to indicate if the role is an admin role',
    example: false,
  })
  @IsBoolean()
  isAdmin: boolean = false;

  @ApiProperty({
    description: 'Flag to activate or deactivate a role',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  active: boolean = true;
}

export class RoleStatusDto {
  @ApiProperty({
    description: 'Name of the role',
    example: 'Admin',
  })
  @IsNotEmpty()
  @IsString()
  roleName: string;

  @ApiProperty({
    description: 'Flag to activate or deactivate a role',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  active: boolean = false;
}

export class EditRoleDto extends PartialType(CreateRoleDto) {
  @ApiProperty({
    description: 'Name of the role',
    example: 'Admin',
  })
  @IsNotEmpty()
  @IsString()
  role: string;
}
export class GetRoleDetail {
  @IsNotEmpty()
  @IsString()
  role: string;
}

export class RoleSearchDto {
  @IsOptional()
  @IsString()
  searchTerm?: string;

  @IsOptional()
  @IsString()
  roleName?: string;

  @IsOptional()
  @IsString()
  userType?: UserTypeEnum;

  @IsOptional()
  @IsDateString()
  createDate?: string;

  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10), { toClassOnly: true })
  @IsInt()
  @Min(1)
  page?: number;

  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10), { toClassOnly: true })
  @IsInt()
  @Min(1)
  limit?: number;
}
