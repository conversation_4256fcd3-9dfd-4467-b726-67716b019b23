import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsString, Length, Matches, MaxLength } from 'class-validator';
import { Transform } from 'class-transformer';

export class AccrLegacyPaymentsDto {
  @IsNotEmpty()
  @IsString()
  surname: string;

  @IsNotEmpty()
  @IsString()
  mdaName: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Date of Retirement must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  @Transform(({ value }) => String(value))
  dor: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Date of First Appointment must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  @Transform(({ value }) => String(value))
  dofa: string;

  @IsString()
  @Length(1, 30)
  rsaPin: string;

  @IsOptional()
  @IsString()
  @MaxLength(20)
  salStruc?: string;

  @IsOptional()
  @IsString()
  @MaxLength(5)
  glJun2004?: string;

  @IsOptional()
  @IsString()
  @MaxLength(2)
  stepJun2004?: string;

  @IsString()
  @Length(1, 20)
  batchName: string;

  @IsString()
  @IsOptional()
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Date of Introduction must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  @Transform(({ value }) => String(value))
  introDate?: string;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  annualPenAllow?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  accrBenfitActual?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  accrBenfitApprox?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  accrBenfitAdd?: number;

  @IsString()
  @IsOptional()
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'AccruedBenefit Add Date must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  @Transform(({ value }) => String(value))
  accrBenfitAddDate?: string;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  accrBenfit2ndAdd?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  accrBenfitRecovered?: number;

  @IsOptional()
  @IsString()
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Date of Birth must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  @Transform(({ value }) => String(value))
  accrBenfitRecoveredDate?: string;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  accrBenfitNet?: number;

  @IsOptional()
  @IsString()
  @MaxLength(20)
  exitMode?: string;
}
