import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateSpecimenDataDto {
  @ApiProperty({
    description: 'Name of the specimen',
    example: 'Specimen 2023',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Year of the specimen',
    example: 2023,
  })
  @IsNotEmpty()
  @IsNumber()
  year: number;

  @ApiProperty({
    description: 'Document data in base64 format',
    example: 'base64EncodedString',
  })
  @IsNotEmpty()
  @IsString()
  documentData: string;

  @ApiProperty({
    description: 'Document extension',
    example: 'pdf',
  })
  @IsNotEmpty()
  @IsString()
  docExt: string;
}

export class CreateSpecimenListDto {
  @ApiProperty({
    description: 'List of specimens to create',
    type: [CreateSpecimenDataDto],
  })
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CreateSpecimenDataDto)
  specimens: CreateSpecimenDataDto[];
}

export class DownloadDocument {
  @ApiProperty({
    description: 'PK of the specimen',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  pk: number;
}

export class SpecimenDataResponseDto {
  pk: number;
  name: string;
  year: number;
  mdaCode: string;
  createdBy: string;
  createDate: Date;
}
