import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsEnum, IsNumber, IsArray, IsString } from 'class-validator';
import { ApprovalStatusEnum } from '../../enums/ApprovalStatusEnum';

export class WhitelistDocumentDto {
  @ApiProperty({ description: 'Document Name', example: 'document.pdf' })
  documentName: string;

  @ApiProperty({ description: 'Document Type', example: 'application/pdf' })
  documentType: string;

  @ApiProperty({ description: 'Document Content', example: 'base64 encoded string' })
  documentContent: string;
}

export class CreateWhitelistRequestDto {
  @ApiProperty({ description: 'User ID requesting whitelist', example: '<EMAIL>', required: true })
  @IsString()
  @IsNotEmpty()
  requestUserEmail: string;

  @ApiProperty({ description: 'Supporting documents', type: WhitelistDocumentDto, isArray: true })
  @IsArray()
  @IsNotEmpty()
  documents: WhitelistDocumentDto[];
}

export class UpdateWhitelistRequestDto {
  requestId?: number;

  @ApiProperty({ description: 'Status to update', enum: ApprovalStatusEnum })
  @IsEnum(ApprovalStatusEnum)
  @IsNotEmpty()
  status: ApprovalStatusEnum;

  @ApiProperty({ description: 'Approver comments', example: 'Approved' })
  @IsString()
  comment: string;

  approverEmail?: string;
}

export class WhitelistRequestDataDto {
  pk: number;
  requestId: string;
  mdaCode: string;
  pfaCode: string;
  status: string;
  approvedBy: string;
  createDate: Date;
  comment: string;
  userType: string;
  role?: string;
  email?: string;
  organizationName?: string;
  staffId?: string;
  phoneNumber?: string;
  location?: string;
  firstName?: string;
  lastName?: string;
  requesterEmail?: string;
}

export class WhitelistDocumentResult {
  pk: number;
  fileName: string;
  fileContent: string;
  mimeType: string;
}
