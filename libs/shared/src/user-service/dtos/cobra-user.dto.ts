import { UserStatusAction } from '@app/shared/enums/UserStatusActionEnum';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsDateString,
  IsEmail,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  Min,
} from 'class-validator';
import { MdaEmployeeBiodata } from '@app/shared/login-service/entities/employee-biodata.entity';
import { IsValidBase64 } from '@app/shared/decorators/is-valid-base64.decorator';

export class UploadProfilePictureDto {
  @ApiProperty({
    description: "User's email address",
    example: '<EMAIL>',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  emailAddress: string;

  @ApiProperty({
    description: 'Base64 encoded image string',
    example: 'data:image/jpeg;base64,/9j/4AAQSkZJRg...',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^data:image\/(png|jpeg|jpg);base64,/)
  avatar: string;
}

export class SaveSignatureDto {
  @ApiProperty({
    description: "User's email address",
    example: '<EMAIL>',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  emailAddress: string;

  @ApiProperty({
    description: 'Base64 encoded signature image',
    example: 'data:image/png;base64,iVBORw0KGgoAAAAN...',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^data:image\/(png|jpeg|jpg);base64,/)
  signature: string;
}

export class CreateUserWithoutPasswordDto {
  @ApiProperty({
    description: 'RSA PIN for user identification',
    example: 'RSA123456',
    required: false,
  })
  @IsString()
  @IsOptional()
  rsaPin?: string;

  @ApiProperty({
    description: "User's first name",
    example: 'John',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({
    description: "User's email address",
    example: '<EMAIL>',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  emailAddress: string;

  @ApiProperty({
    description: "User's surname",
    example: 'Doe',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  surname: string;

  @ApiProperty({
    description: 'Staff identification number',
    example: 'STAFF001',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  staffId: string;

  @ApiProperty({
    description: 'Ministry, Department, or Agency Code',
    example: 'PU0000300001',
    required: false,
  })
  @IsString()
  @IsOptional()
  mdaCode: string;

  @ApiProperty({
    description: 'PFA Code',
    example: 'PU0000072727',
    required: false,
  })
  @IsString()
  @IsOptional()
  pfaCode: string;

  @ApiProperty({
    description: 'Admin',
    example: 1,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  roleFk?: number;

  @ApiProperty({
    description: 'Base64 encoded supporting document (PDF, image, DOC, etc.)',
    example: 'data:application/pdf;base64,JVBERi0xLjcKJc...',
    required: false,
  })
  @IsString()
  @IsOptional()
  @IsValidBase64({
    message: 'Supporting Document must be a valid Base64 string',
  })
  supportingDocument?: string;

  @ApiProperty({
    description: 'Type of user account',
    example: 'admin',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  userType: string;

  @ApiProperty({
    description: 'User phone number',
    example: '***********',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @IsBoolean()
  @IsOptional()
  updatePassword?: boolean;

  createdBy?: CobraUser;

  active: boolean;
  securityQuestions: boolean;

  mdaEmployeeBiodata?: MdaEmployeeBiodata;

  @ApiProperty({
    description: 'Gender',
    example: 'M',
    required: true,
  })
  @IsString()
  gender: string;
}

export class CreateCobraUserDto extends CreateUserWithoutPasswordDto {
  @ApiProperty({
    description: "User's password",
    example: 'StrongP@ssw0rd123',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  password: string;
}

export class EditCobraUserDto extends PartialType(CreateCobraUserDto) {
  @IsString()
  @IsOptional()
  location?: string;

  @IsString()
  @IsOptional()
  newEmailAddress?: string;
}

export class UpdateUserStatusDto {
  @ApiProperty({
    description: "User's email address",
    example: '<EMAIL>',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Action to perform on the user',
    example: UserStatusAction.ACTIVATE,
    required: true,
  })
  @IsEnum(UserStatusAction)
  @IsNotEmpty()
  action: UserStatusAction;
}

export class PfaSearchDto {
  @IsString()
  @IsOptional()
  pfaCode?: string;
}

export class UserSearchDto {
  @ApiProperty({ required: false, description: 'searchTerm' })
  @IsOptional()
  @IsString()
  searchTerm?: string;

  @ApiProperty({ required: false, description: 'firstName' })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({ required: false, description: 'surname' })
  @IsOptional()
  @IsString()
  surname?: string;

  @ApiProperty({ required: false, description: 'Staff ID' })
  @IsOptional()
  @IsString()
  staffId?: string;

  @ApiProperty({ required: true, description: 'User type' })
  @IsString()
  @IsNotEmpty()
  userType: string;

  @IsOptional()
  @IsString()
  roleName?: string;

  @ApiProperty({ required: false, description: 'emailAddress' })
  @IsOptional()
  @IsString()
  emailAddress?: string;

  @ApiProperty({ required: false, description: 'mdaCode' })
  @IsOptional()
  @IsString()
  mdaCode?: string;

  @IsOptional()
  @IsString()
  pfaCode?: string;

  @ApiProperty({ required: false, description: 'blacklisted' })
  @IsOptional()
  @IsBoolean()
  blacklisted?: boolean;

  @ApiProperty({ required: false, description: 'active' })
  @IsOptional()
  @IsBoolean()
  active?: boolean;

  @ApiProperty({ required: false, description: 'Create date' })
  @IsOptional()
  @IsDateString()
  createDate?: string;

  @ApiProperty({ required: false, description: 'Page number' })
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10), { toClassOnly: true })
  @IsInt()
  @Min(1)
  page?: number;

  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10), { toClassOnly: true })
  @IsInt()
  @Min(1)
  limit?: number;
}

export class GetUserDto {
  @IsString()
  @IsNotEmpty()
  userType: string;

  @IsOptional()
  @IsString()
  email?: string;
}

export class AgreeToTermsDto {
  @IsEmail()
  @IsString()
  @IsNotEmpty()
  email: string;
}
