import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON>Optional, IsString } from 'class-validator';

export class ContributionLedgerDto {
  @IsString()
  @IsNotEmpty()
  rsaPin: string;

  @IsString()
  @IsOptional()
  grade: string;

  @IsString()
  @IsOptional()
  step: string;

  @IsString()
  @IsOptional()
  salaryType: string;

  @IsString()
  agencyCode?: string;

  @IsNumber()
  mthEmol?: number;

  @IsNumber()
  amount: number;

  @IsNotEmpty()
  @IsString()
  name?: string;

  @IsString()
  @IsNotEmpty()
  payType?: string;
}
