import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ot<PERSON><PERSON>y, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString, Matches } from 'class-validator';
import { Transform } from 'class-transformer';
import { IsValidBase64 } from '@app/shared/decorators/is-valid-base64.decorator';
import { LeaveTypeEnum } from '@app/shared/enums/LeaveTypeEnum';
import { RequireAllLeaveFields } from '@app/shared/decorators/validate-leave-fields.decorator';
import { IsStartDateBeforeEndDate } from '@app/shared/decorators/validate-start-end-dates.decorator';
import { IsValidBand } from '@app/shared/decorators/band-validator.decorator';

export class OtherEmploymentDetailsDto {
  @IsNotEmpty()
  @IsValidBand()
  year: string;

  @IsOptional()
  @Transform(({ value }) => (typeof value === 'string' && value.trim() === '' ? undefined : value))
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Promotion date must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  promotionYear: string;

  @IsNotEmpty()
  @IsString()
  organizationSector: string;

  @IsNotEmpty({ message: 'Employer details must be provided' })
  @IsString()
  employerCode: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'IPPIS date must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  @Transform(({ value }) => String(value))
  ippisDate: string;

  @IsNotEmpty()
  @IsString()
  designation: string;

  @IsNotEmpty()
  @IsString()
  staffId: string;

  @IsNotEmpty()
  @IsString()
  salaryStructure: string;

  @IsNotEmpty()
  @IsNumber()
  gradeLevel: number;

  @IsNotEmpty()
  @IsNumber()
  step: number;

  @IsOptional()
  @Transform(({ value }) => (typeof value === 'string' && value.trim() === '' ? undefined : value))
  @IsEnum(LeaveTypeEnum, { message: `LeaveType must be one of: ${Object.values(LeaveTypeEnum).join(', ')}` })
  leaveType?: LeaveTypeEnum;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => (typeof value === 'string' && value.trim() === '' ? undefined : value))
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Leave Start date must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  leaveStartDate?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => (typeof value === 'string' && value.trim() === '' ? undefined : value))
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Leave End date must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  leaveEndDate?: string;

  @RequireAllLeaveFields({
    message: 'If Leave Type, Leave Start Date, or Leave End Date is provided, all three must be provided.',
  })
  validateLeaveFields?: any; // This field exists only for validation purposes

  @IsStartDateBeforeEndDate()
  validateLeaveDates?: any; // This field exists only for validation purposes

  @IsOptional()
  @IsValidBase64({ message: 'Promotion Letter file content must be a valid Base64 string' })
  promotionLetter: string;

  @IsOptional()
  @IsValidBase64({ message: 'Transfer of service document must be a valid Base64 string' })
  transferOfService: string;
}
