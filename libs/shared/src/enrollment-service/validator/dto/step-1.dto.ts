import { <PERSON>Email, IsIn, <PERSON>NotEmpty, <PERSON><PERSON>ptional, IsString, Matches, ValidateIf } from 'class-validator';
import { Transform } from 'class-transformer';
import { IsValidBase64 } from '@app/shared/decorators/is-valid-base64.decorator';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';
import { RsaPinValidator } from '@app/shared/decorators/rsapin-validator.decorator';

export class Step1Dto {
  @IsNotEmpty()
  @RsaPinValidator()
  rsaPin: string;

  @IsNotEmpty()
  @IsString()
  pfaCode: string;

  @IsNotEmpty()
  @IsString()
  pfaName: string;

  @IsNotEmpty()
  @IsString()
  employerCode: string;

  @IsNotEmpty()
  @IsString()
  employerName: string;

  @IsNotEmpty()
  @IsString()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  surname: string;

  @IsOptional()
  @IsString()
  middleName: string;

  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value?.toLowerCase())
  @IsIn(['male', 'female'], {
    message: 'Gender must be either "Male" or "Female"',
  })
  gender: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Date of Retirement must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  @Transform(({ value }) => String(value))
  dateOfBirth: string;

  @IsNotEmpty()
  @IsString()
  contactAddress: string;

  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value?.toUpperCase())
  @IsIn([RetireeUserTypeEnum.DECEASED, RetireeUserTypeEnum.RETIREE], {
    message: 'Retiree user type must be either "RETIREE" or "DECEASED"',
  })
  retireeUserType: string;

  @IsNotEmpty()
  @Matches(/^\d{11}$/, { message: 'Phone number must be exactly 11 digits' })
  phoneNumber: string;

  @IsOptional()
  @Matches(/^\d{11}$/, { message: 'Alternate Phone number must be exactly 11 digits' })
  alternatePhoneNumber: string;

  @IsNotEmpty()
  @IsEmail({}, { message: 'Invalid email format' })
  email: string;

  @IsOptional()
  @IsValidBase64({ message: 'Change of Name Document must be a valid Base64 string' })
  changeOfName: string;

  @ValidateIf((o) => o.retireeUserType?.toUpperCase() === RetireeUserTypeEnum.DECEASED)
  @IsNotEmpty({ message: 'Certificate of Birth Document is required for DECEASED retiree user type' })
  @IsValidBase64({ message: 'Certificate of Birth Document must be a valid Base64 string' })
  certificateOfBirth: string;

  @ValidateIf((o) => o.retireeUserType?.toUpperCase() === RetireeUserTypeEnum.DECEASED)
  @IsNotEmpty({ message: 'Certificate of Death Document is required for DECEASED retiree user type' })
  @IsValidBase64({ message: 'Certificate of Death Document must be a valid Base64 string' })
  certificateOfDeath: string;
}
