import { IsIn, IsNotEmpty, IsOptional, IsString, Length, Matches, ValidateIf, ValidateNested } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { Step1Dto } from '@app/shared/enrollment-service/validator/dto/step-1.dto';
import { IsValidBase64 } from '@app/shared/decorators/is-valid-base64.decorator';
import { PdoDetailsDto } from '@app/shared/enrollment-service/validator/dto/pdo-details.dto';
import { NokDetailsDto } from '@app/shared/enrollment-service/validator/dto/nok-details.dto';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';

export class Step2Dto extends Step1Dto {
  @IsString()
  @IsNotEmpty()
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Date of FirstAppointment must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  @Transform(({ value }) => String(value))
  dofa: string;

  @IsString()
  @IsOptional()
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Date of FirstAppointment must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  @Transform(({ value }) => String(value))
  dts: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^([0-2][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/, {
    message: 'Date of FirstAppointment must be in DD-MM-YYYY format (e.g., 04-04-2025)',
  })
  @Transform(({ value }) => String(value))
  edor: string;

  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value?.toLowerCase())
  @IsIn(['voluntary', 'statutory'], {
    message: "modeOfRetirement must be either 'VOLUNTARY' or 'STATUTORY'",
  })
  modeOfRetirement: string;

  @ValidateIf((obj) => obj.modeOfRetirement === 'voluntary') // Only validate if modeOfRetirement is "voluntary"
  @IsNotEmpty({ message: "reasonForVoluntaryRetirement is required when modeOfRetirement is 'VOLUNTARY'" })
  @IsString()
  @Transform(({ value }) => value?.toLowerCase()) // Convert input to lowercase
  @IsIn(['medical', 'disengagement', 'resignation', 'dismissal'], {
    message: "reasonForVoluntaryRetirement must be either 'MEDICAL', 'DISENGAGEMENT',  'DISMISSAL', or 'RESIGNATION'",
  })
  reasonForVoluntaryRetirement?: string;

  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value?.toLowerCase())
  @IsIn(['single employment', 'multiple employments'], {
    message: "Number of Employment must be either 'SINGLE EMPLOYMENT' or 'MULTIPLE EMPLOYMENTS'",
  })
  numberOfEmployment: string;

  @IsString()
  @IsNotEmpty()
  @Length(3, 15, { message: 'Staff Id must be between 3 to 15 characters' })
  @Transform(({ value }) => String(value).trim()) // Ensure it's always a string
  staffId: string;

  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value?.toLowerCase())
  @IsIn(['yes', 'no'], {
    message: "Critical Health Challenge must be either 'yes' or 'NO'",
  })
  criticalHealthChallenge: string;

  /* @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value?.toLowerCase())
  @IsIn(['yes', 'no'], {
    message: "Already paid must be either 'YES' or 'NO'",
  })
  alreadyPaid: string;*/

  @IsNotEmpty()
  @IsValidBase64({ message: 'Letter of First Appointment document must be a valid Base64 string' })
  letterOfFirstAppointment: string;

  @IsOptional() //TODO find out when mandatory
  @IsValidBase64({ message: 'Letter of Extension Of Service document must be a valid Base64 string' })
  letterOfExtensionOfService: string;

  @ValidateIf((o) => o.criticalHealthChallenge.toLowerCase() === 'yes')
  @IsNotEmpty({ message: 'Medical Report is mandatory when critical health challenge is Yes' })
  @IsValidBase64({ message: 'Medical Report Document must be a valid Base64 string' })
  medicalReport: string;

  @ValidateIf((o) => o.criticalHealthChallenge.toLowerCase() === 'yes')
  @IsNotEmpty({ message: 'Letter of Introduction is mandatory when critical health challenge is Yes' })
  @IsValidBase64({ message: 'Letter of Introduction Document must be a valid Base64 string' })
  letterOfIntroduction: string;

  @ValidateIf((o) => o.criticalHealthChallenge.toLowerCase() === 'yes')
  @IsNotEmpty({ message: 'PDO Details are required when critical health challenge is Yes' })
  @ValidateNested()
  @Type(() => PdoDetailsDto)
  pdoDetails: PdoDetailsDto;

  @ValidateIf((o) => o.retireeUserType?.toUpperCase() === RetireeUserTypeEnum.DECEASED)
  @IsNotEmpty({ message: 'NOK Details are required For Deceased Employee' })
  @ValidateNested()
  @Type(() => NokDetailsDto)
  nokDetails: NokDetailsDto;
}
