import { Is<PERSON><PERSON>, <PERSON>NotEmpty, IsString } from 'class-validator';

export class PdoDetailsDto {
  @IsNotEmpty()
  @IsString()
  userId: string;

  @IsNotEmpty({ message: 'PDO first name must be provided' })
  @IsString()
  firstName: string;

  @IsNotEmpty({ message: 'PDO surname must be provided' })
  @IsString()
  surname: string;

  @IsEmail()
  @IsNotEmpty({ message: 'PDO Email address must be provided' })
  emailAddress: string;
}
