import { ArrayMinSize, IsArray, IsNotEmpty, ValidateNested } from 'class-validator';
import { Step2Dto } from '@app/shared/enrollment-service/validator/dto/step-2.dto';
import { OtherEmploymentDetailsDto } from '@app/shared/enrollment-service/validator/dto/other-employment-details.dto';
import { Type } from 'class-transformer';

export class Step3Dto extends Step2Dto {
  @IsNotEmpty({ message: 'Other employment details must be provided' })
  @IsArray({ message: 'Other employment details must be an array' })
  @ArrayMinSize(1, { message: 'At least one employment detail must be provided' })
  @ValidateNested({ each: true }) // Ensures each item in the array is validated
  @Type(() => OtherEmploymentDetailsDto)
  otherEmploymentDetails: OtherEmploymentDetailsDto[];
}
