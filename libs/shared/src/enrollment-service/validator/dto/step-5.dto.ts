import { IsNotEmpty, <PERSON><PERSON>ptional, ValidateIf } from 'class-validator';
import { IsValidBase64 } from '@app/shared/decorators/is-valid-base64.decorator';
import { Step4Dto } from '@app/shared/enrollment-service/validator/dto/step-4.dto';

export class Step5Dto extends Step4Dto {
  @IsNotEmpty()
  @IsValidBase64({ message: 'Subscriber portrait must be a valid Base64 string' })
  portrait: string;

  @IsOptional()
  overrideVerification: boolean;

  @ValidateIf((o) => o.overrideVerification === true)
  @IsNotEmpty({ message: 'Portrait override reason is required when Portrait capture is overridden' })
  overrideVerificationReason: string;
}
