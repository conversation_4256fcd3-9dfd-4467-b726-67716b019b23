import { Is<PERSON><PERSON>, <PERSON>NotEmpty, IsString } from 'class-validator';

export class NokDetailsDto {
  @IsNotEmpty({ message: 'NOK Phone Number must be provided' })
  @IsString()
  phoneNumber: string;

  @IsNotEmpty({ message: 'NOK First name must be provided' })
  @IsString()
  firstName: string;

  @IsNotEmpty({ message: 'NOK Surname must be provided' })
  @IsString()
  surname: string;

  @IsEmail()
  @IsNotEmpty({ message: 'NOK Email address must be provided' })
  emailAddress: string;
}
