import { AbstractRepository } from '@app/shared/database';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { NominalRollBand } from '@app/shared/enrollment-service/entities/nominal-roll-band.entity';

Injectable();
export class NominalRollBandRepository extends AbstractRepository<NominalRollBand> {
  constructor(
    @InjectRepository(NominalRollBand)
    readonly nominalRollBandRepository: Repository<NominalRollBand>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(nominalRollBandRepository, entityManager);
  }
}
