import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { NominalRollBatchDocuments } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch-documents.entity';

@Injectable()
export class NrBatchDocumentRepository extends AbstractRepository<NominalRollBatchDocuments> {
  constructor(
    @InjectRepository(NominalRollBatchDocuments)
    private readonly nominalRollBatchDocumentsRepository: Repository<NominalRollBatchDocuments>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(nominalRollBatchDocumentsRepository, entityManager);
  }
}
