import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { TbcEnrollmentPfcMemoDocument } from '@app/shared/enrollment-service/entities/tbc-enrollment-pfc-memo-document';

@Injectable()
export class TbcEnrollmentPfcMemoDocumentRepository extends AbstractRepository<TbcEnrollmentPfcMemoDocument> {
  constructor(
    @InjectRepository(TbcEnrollmentPfcMemoDocument)
    private readonly tbcEnrollmentPfcMemoDocumentRepository: Repository<TbcEnrollmentPfcMemoDocument>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(tbcEnrollmentPfcMemoDocumentRepository, entityManager);
  }
}
