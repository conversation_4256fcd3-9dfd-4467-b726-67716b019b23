import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { TbcNominalRollPfcMemoDocument } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-pfc-memo-document';

@Injectable()
export class TbcNominalRollPfcMemoDocumentRepository extends AbstractRepository<TbcNominalRollPfcMemoDocument> {
  constructor(
    @InjectRepository(TbcNominalRollPfcMemoDocument)
    private readonly tbcNominalRollPfcMemoDocumentRepository: Repository<TbcNominalRollPfcMemoDocument>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(tbcNominalRollPfcMemoDocumentRepository, entityManager);
  }
}
