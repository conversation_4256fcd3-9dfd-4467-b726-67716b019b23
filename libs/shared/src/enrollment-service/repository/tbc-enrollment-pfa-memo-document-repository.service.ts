import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { TbcEnrollmentPfaMemoDocument } from '@app/shared/enrollment-service/entities/tbc-enrollment-pfa-memo-document';

@Injectable()
export class TbcEnrollmentPfaMemoDocumentRepository extends AbstractRepository<TbcEnrollmentPfaMemoDocument> {
  constructor(
    @InjectRepository(TbcEnrollmentPfaMemoDocument)
    private readonly tbcEnrollmentPfaMemoDocumentRepository: Repository<TbcEnrollmentPfaMemoDocument>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(tbcEnrollmentPfaMemoDocumentRepository, entityManager);
  }
}
