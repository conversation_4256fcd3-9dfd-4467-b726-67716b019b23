import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { TbcNominalRollBatch } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';

@Injectable()
export class NominalRollBatchRepository extends AbstractRepository<TbcNominalRollBatch> {
  constructor(
    @InjectRepository(TbcNominalRollBatch)
    private readonly tbcNominalRollBatchRepository: Repository<TbcNominalRollBatch>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(tbcNominalRollBatchRepository, entityManager);
  }

  async retrieveBatchDetails(filter: PaginatedSearchDto) {
    const query = this.tbcNominalRollBatchRepository
      .createQueryBuilder('batch')
      .select([
        'batch.pk AS "pk"',
        'batch.batchName AS "batchName"',
        'batch.totalRefunds AS "totalRefunds"',
        'batch.totalContributions AS "totalContributions"',
        'batch.totalCount AS "totalCount"',
        'batch.paymentConfirmedPfa AS "paymentConfirmedPfa"',
        'TO_CHAR(batch.createDate, \'YYYY-MM-DD\') AS "createDate"',
        'TO_CHAR(batch.uploadStartDate, \'YYYY-MM-DD\') AS "uploadStartDate"',
        'TO_CHAR(batch.uploadEndDate, \'YYYY-MM-DD\') AS "uploadEndDate"',
        'batch.status AS "status"',
      ])
      .orderBy('batch.createDate', 'DESC');

    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (['createDate', 'uploadStartDate', 'uploadEndDate'].includes(key)) {
            query.andWhere(`TO_CHAR(batch.${key}, 'YYYY-MM-DD') = :${key}`, { [key]: value });
          } else {
            query.andWhere(`LOWER(batch.${key}) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          }
        }
      });
    }

    const total = await query.getCount();
    const limit = filter.limit || 10;
    const page = filter.page || 1;
    const offset = (page - 1) * limit;
    query.offset(offset).limit(limit);

    console.log(query.getQueryAndParameters());
    const rawResults = await query.getRawMany();
    return {
      total,
      page,
      limit,
      data: rawResults,
    };
  }
}
