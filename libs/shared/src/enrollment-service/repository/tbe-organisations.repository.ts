import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository, SelectQueryBuilder } from 'typeorm';
import {
  RetrieveCompleteEmployerDetailsDto,
  RetrieveYearDetailsDto,
  RetrieveYearSectorDetailsDto,
} from '@app/shared/dto/enrollment/enrolment-draft.dto';
import { PinoLogger } from 'nestjs-pino';
import { TbeOrganisations } from '@app/shared/enrollment-service/entities/tbe-organisations.entity';
import { AS_AT_JUNE_30TH_BAND } from '@app/shared/constants';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';

@Injectable()
export class TbeOrganisationsRepository extends AbstractRepository<TbeOrganisations> {
  constructor(
    @InjectRepository(TbeOrganisations)
    private readonly tbeOrganisationsRepository: Repository<TbeOrganisations>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(tbeOrganisationsRepository, entityManager);
  }

  async getEmployerCompleteSalaryDetails(retrieveCompleteEmployerDetailsDto: RetrieveCompleteEmployerDetailsDto) {
    let {
      employerCode: employerId,
      year: salaryYear,
      organizationSector: sectorCode,
      salaryStructure,
      step,
      gradelevel,
      ippisDate,
    } = retrieveCompleteEmployerDetailsDto;
    const query: SelectQueryBuilder<TbeOrganisations> = this.tbeOrganisationsRepository
      .createQueryBuilder('to')
      .leftJoin('TBE_SECTOR_SAL_MAP', 'tssm', '"tssm".SECTOR_CODE = to.SECTOR_CODE')
      .leftJoin('TBE_SAL_STRUCTURE', 'tss', '"tss".SAL_STRUCT = "tssm".SAL_STRUCT')
      .select(['to.EMPLOYER_NAME AS "employerName"', 'to.ippisDate AS "ippisDate"']);

    if (employerId) {
      query.andWhere('to.EMPLOYER_ID = :employerId', { employerId });
    }

    if (salaryYear) {
      if (salaryYear === AS_AT_JUNE_30TH_BAND) {
        salaryYear = '2004';
      }
      query.andWhere('"tssm".SAL_YEAR = :salaryYear', { salaryYear });
      query.andWhere('"tss".YEAR = :structYear', { structYear: salaryYear });
    }

    if (sectorCode) {
      query.andWhere('to.SECTOR_CODE = :sectorCode', { sectorCode });
    }

    if (salaryStructure) {
      query.andWhere('"tss".SAL_STRUCT = :salaryStructure', { salaryStructure });
    }

    if (gradelevel) {
      query.andWhere('"tss".GL = :gradelevel', { gradelevel });
    }

    // if (ippisDate) {
    //   query.andWhere("TO_CHAR(to.ippisDate, 'DD-MM-YYYY') = :ippisDate", { ippisDate });
    // }

    if (step) {
      query.andWhere('"tss".STEP_LOWER_BAND <= :lowerStep', { lowerStep: step });
      query.andWhere(':upperStep <= "tss".STEP_UPPER_BAND', { upperStep: step });
    }
    return await query.getRawMany();
  }

  async getEmployerSalaryDetails(employerId: string, salaryYear: string, sectorCode: string) {
    const query: SelectQueryBuilder<TbeOrganisations> = this.tbeOrganisationsRepository
      .createQueryBuilder('to')
      .leftJoin('TBE_SECTOR_SAL_MAP', 'tssm', 'tssm.SECTOR_CODE = to.SECTOR_CODE')
      .leftJoin('TBE_SAL_STRUCTURE', 'tss', 'tss.SAL_STRUCT = tssm.SAL_STRUCT')
      .select([
        'to.EMPLOYER_NAME AS "employerName"',
        'tssm.SAL_YEAR AS "salaryYear"',
        'tssm.SAL_STRUCT AS "salaryStructure"',
        'tss.YEAR AS "year"',
        'tss.gl AS "gradeLevel"',
        'tss.sortOrder AS "sortOrder"',
        'tss.STEP_LOWER_BAND AS "stepLowerBand"',
        'tss.STEP_UPPER_BAND AS "stepUpperBand"',
        'TO_CHAR(to.ippisDate, \'YYYY-MM-DD\') AS "ippisDate"',
        'to.SECTOR_CODE AS "sectorCode"',
        'to.EMPLOYER_ID AS "employerId"',
      ]);

    if (employerId) {
      console.log(`employerId in::::: ${employerId}`);
      query.andWhere('to.EMPLOYER_ID = :employerId', { employerId });
    }

    if (salaryYear) {
      if (salaryYear === AS_AT_JUNE_30TH_BAND) {
        salaryYear = '2004';
      }
      query.andWhere('tssm.SAL_YEAR = :salaryYear', { salaryYear });
      query.andWhere('tss.YEAR = :structYear', { structYear: salaryYear }); // Fix here
    }

    if (sectorCode) {
      query.andWhere('to.SECTOR_CODE = :sectorCode', { sectorCode });
    }

    return await query.getRawMany();
  }

  async retrieveYearSectorCodeDetails(retrieveEmployerDetailsDto: RetrieveYearDetailsDto) {
    const result = await this.tbeOrganisationsRepository
      .createQueryBuilder('to')
      .distinct(true)
      .select('to.SECTOR_CODE', 'sectorCode')
      .leftJoin('TBE_SECTOR_SAL_MAP', 'tssm', 'tssm.SECTOR_CODE = to.SECTOR_CODE')
      .leftJoin('TBE_SAL_STRUCTURE', 'tss', 'tss.SAL_STRUCT = tssm.SAL_STRUCT')
      .where('tssm.SAL_YEAR = :year', { year: retrieveEmployerDetailsDto.year })
      .andWhere('tss.YEAR = :structYear', { structYear: retrieveEmployerDetailsDto.year })
      .getRawMany();

    if (!result) {
      return result;
    }

    return result.map((item) => item.sectorCode);
  }

  async retrieveSectorEmployerDetails(retrieveEmployerDetailsDto: RetrieveYearSectorDetailsDto) {
    return this.tbeOrganisationsRepository
      .createQueryBuilder('to')
      .select(['to.EMPLOYER_ID AS "employerId"', 'to.EMPLOYER_NAME AS "employerName"'])
      .distinct(true)
      .leftJoin('TBE_SECTOR_SAL_MAP', 'tssm', 'tssm.SECTOR_CODE = to.SECTOR_CODE')
      .leftJoin('TBE_SAL_STRUCTURE', 'tss', 'tss.SAL_STRUCT = tssm.SAL_STRUCT')
      .where('tssm.SAL_YEAR = :year', { year: retrieveEmployerDetailsDto.year })
      .andWhere('tss.YEAR = :structYear', { structYear: retrieveEmployerDetailsDto.year })
      .andWhere('to.SECTOR_CODE = :sectorCode', { sectorCode: retrieveEmployerDetailsDto.organizationSector })
      .getRawMany();
  }

  async getOrganisationSectorIppisDetails(employerCode: string) {
    return this.tbeOrganisationsRepository
      .createQueryBuilder('to')
      .select([
        'to.EMPLOYER_ID AS "employerId"',
        'TO_CHAR(to.ippisDate, \'DD-MM-YYYY\') as "ippisDate"',
        'to.EMPLOYER_NAME AS "employerName"',
        'to.SECTOR_CODE AS "sectorCode"',
      ])
      .where('to.EMPLOYER_ID = :employerCode', { employerCode })
      .getRawOne();
  }

  async getAllOrganisations() {
    return await this.tbeOrganisationsRepository
      .createQueryBuilder('org')
      .select([
        'org.sectorCode AS "sectorCode"',
        'org.employerId AS "employerId"',
        'org.employerName AS "employerName"',
        'org.ippisDate AS "ippisDate"',
      ])
      .getRawMany();
  }

  async fetchTbeOrganisations(filter: PaginatedSearchDto) {
    const query = this.tbeOrganisationsRepository
      .createQueryBuilder('tbeOrganisations')
      .select([
        'tbeOrganisations.sectorCode AS "sectorCode"',
        'tbeOrganisations.employerId AS "employerId"',
        'tbeOrganisations.employerName AS "employerName"',
        'TO_CHAR(tbeOrganisations.ippisDate, \'YYYY-MM-DD\') AS "ippisDate"',
        'tbeOrganisations.ecrsEmployerCode AS "ecrsEmployerCode"',
        'tbeOrganisations.active AS "active"',
        'tbeOrganisations.deleted AS "deleted"',
        'TO_CHAR(tbeOrganisations.createDate, \'YYYY-MM-DD\') AS "createDate"',
        'TO_CHAR(tbeOrganisations.lastModified, \'YYYY-MM-DD\') AS "lastModified"',
      ]);

    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (['ippisDate', 'createDate', 'lastModified'].includes(key)) {
            query.andWhere(`TO_CHAR(tbeOrganisations.${key}, 'YYYY-MM-DD') = :${key}`, { [key]: value });
          } else if (['active', 'deleted'].includes(key)) {
            const booleanValue = value === 'false' ? false : Boolean(value);
            query.andWhere(`tbeOrganisations.${key} = :${key}`, { [key]: booleanValue ? 1 : 0 });
          } else {
            query.andWhere(`LOWER(tbeOrganisations.${key}) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          }
        }
      });
    }

    const limit = Math.max(filter.limit || 10, 1);
    const page = Math.max(filter.page || 1, 1);
    const offset = (page - 1) * limit;

    query.orderBy('tbeOrganisations.lastModified', 'DESC');

    const total = await query.getCount();
    const organisationsList = await query.offset(offset).limit(limit).getRawMany();

    return {
      page,
      limit,
      total,
      result: organisationsList,
    };
  }
}
