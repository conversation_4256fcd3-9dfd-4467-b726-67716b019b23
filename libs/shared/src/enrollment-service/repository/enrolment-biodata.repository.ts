import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { EnrolmentBiodata } from '@app/shared/enrollment-service/entities/enrolment-biodata.entity';
import { PinoLogger } from 'nestjs-pino';
import { BatchFilterDto } from '@app/shared/dto/enrollment/exit-batching/batch-filter.dto';
import { EnrolmentSummary } from '@app/shared/enrollment-service/entities/enrolment-summary.entity';
import { RegistrationStatusEnum } from '@app/shared/enums/registration-status.enum';
import { HYPHEN_DATE_FORMAT } from '@app/shared/constants';
import { convertHypenToIsoDate } from '@app/shared/workflow/utils/workflow-utils';

@Injectable()
export class EnrolmentBiodataRepository extends AbstractRepository<EnrolmentBiodata> {
  constructor(
    @InjectRepository(EnrolmentBiodata)
    private readonly enrolmentBiodataRepository: Repository<EnrolmentBiodata>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(enrolmentBiodataRepository, entityManager);
  }

  async fetchEnrolmentBiodataWithDetailsAndDocumentsAndComments(rsaPin: string) {
    return await this.enrolmentBiodataRepository.findOne({
      where: { rsaPin },
      relations: ['employmentDetails', 'documents', 'comments'],
    });
  }

  async fetchEnrolmentBiodataBatch(filterDto: BatchFilterDto) {
    const { rsaHolderType, batchType, enrollmentStartDate, enrollmentEndDate, retirementStartDate, retirementEndDate } =
      filterDto;

    let query = this.enrolmentBiodataRepository
      .createQueryBuilder('biodata')
      .select([
        'biodata.pk AS "pk"',
        'biodata.rsaPin AS "rsaPin"',
        'biodata.firstName AS "firstName"',
        'biodata.surname AS "surname"',
        'biodata.dateOfRetirement AS "dateOfRetirement"',
        'biodata.dateOfFirstAppointment AS "dateOfFirstAppointment"',
        'biodata.accruedRightsAmount AS "accruedRightsAmount"',
        'biodata.totalPensionPlusInterest AS "contributionAmount"',
        'biodata.createDate AS "enrollmentEndDate"',
        'biodata.pfaCode AS "pfaCode"',
        'biodata.pfaName AS "pfaName"',
        'biodata.employerCode AS "mdaCode"',
        'biodata.employerName AS "mdaName"',
        'summary.status AS "recordStatus"',
        'summary.contributionStatus AS "contributionStatus"',
        'summary.accruedRightsStatus AS "accruedRightsStatus"',
      ])
      .leftJoin(EnrolmentSummary, 'summary', 'biodata.rsaPin = summary.rsaPin AND summary.status = :status', {
        status: RegistrationStatusEnum.CERTIFIED,
      })
      .where('biodata.retireeUserType = :rsaHolderType', { rsaHolderType });

    // Apply date filters only if both dates in the pair are present
    if (enrollmentStartDate && enrollmentEndDate) {
      const formattedEnrollmentStartDate = convertHypenToIsoDate(enrollmentStartDate, HYPHEN_DATE_FORMAT);
      const formattedEnrollmentEndDate = convertHypenToIsoDate(enrollmentEndDate, HYPHEN_DATE_FORMAT);
      query = query.andWhere('biodata.createDate BETWEEN :enrollmentStartDate AND :enrollmentEndDate', {
        enrollmentStartDate: formattedEnrollmentStartDate,
        enrollmentEndDate: formattedEnrollmentEndDate,
      });
    }

    if (retirementStartDate && retirementEndDate) {
      const formattedRetirementStartDate = convertHypenToIsoDate(retirementStartDate, HYPHEN_DATE_FORMAT);
      const formattedRetirementEndDate = convertHypenToIsoDate(retirementEndDate, HYPHEN_DATE_FORMAT);
      query = query.andWhere('biodata.dateOfRetirement BETWEEN :retirementStartDate AND :retirementEndDate', {
        retirementStartDate: formattedRetirementStartDate,
        retirementEndDate: formattedRetirementEndDate,
      });
    }

    // Batch type logic
    if (batchType === 'Accrued Rights') {
      query = query
        .andWhere('biodata.accruedRightsBatchId IS NULL')
        .andWhere('biodata.accruedRightsAmount IS NOT NULL');
    } else if (batchType === 'Contributions') {
      query = query
        .andWhere('biodata.contributionBatchId IS NULL')
        .andWhere('biodata.totalPensionPlusInterest IS NOT NULL');
    }

    // console.log('query', query.getQueryAndParameters());
    query = query
      .andWhere('summary.pk IS NOT NULL')
      .orderBy('biodata.dateOfRetirement', 'ASC')
      .addOrderBy('biodata.createDate', 'ASC');

    return await query.getRawMany();
  }

  async fetchEnrolmentBiodataWithDetails(rsaPin: string) {
    return await this.enrolmentBiodataRepository.findOne({
      where: { rsaPin },
      relations: ['employmentDetails'],
    });
  }

  async getPinsByCrBatchName(batchName: string) {
    const rsaPins = await this.enrolmentBiodataRepository
      .createQueryBuilder('biodata')
      .select('biodata.rsaPin as "rsaPin"')
      .where('biodata.contributionBatchId = :batchId', { batchId: batchName })
      .getRawMany();

    return rsaPins.map((row) => row.rsaPin);
  }

  async getPinsByArBatchName(batchName: string) {
    const rsaPins = await this.enrolmentBiodataRepository
      .createQueryBuilder('biodata')
      .select('biodata.rsaPin as "rsaPin"')
      .where('biodata.accruedRightsBatchId = :batchId', { batchId: batchName })
      .getRawMany();

    return rsaPins.map((row) => row.rsaPin);
  }

  async findPfaPaymentStatsInBatch(action: string, batchName: string, pfaCode: string) {
    const isContribution = action.startsWith('CR');

    const query = this.enrolmentBiodataRepository
      .createQueryBuilder('biodata')
      .select([
        // Matching PFA & in batch
        `SUM(CASE WHEN biodata.pfaCode = :pfaCode1 AND ${isContribution ? 'biodata.contributionBatchId' : 'biodata.accruedRightsBatchId'} = :batchId1 THEN 1 ELSE 0 END) AS "totalInBatchForPfa"`,
        // Non-matching PFA & in batch
        `SUM(CASE WHEN biodata.pfaCode != :pfaCode2 AND ${isContribution ? 'biodata.contributionBatchId' : 'biodata.accruedRightsBatchId'} = :batchId2 THEN 1 ELSE 0 END) AS "nonMatchingPfaInBatch"`,
        // Total pending records for PFA
        `SUM(CASE WHEN biodata.pfaCode = :pfaCode3 AND biodata.paymentStatus IS NULL THEN 1 ELSE 0 END) AS "pendingInBatchForPfa"`,
        // Total pending records with different PFA
        `SUM(CASE WHEN biodata.pfaCode != :pfaCode4 AND biodata.paymentStatus IS NULL THEN 1 ELSE 0 END) AS "totalPendingNotForPfa"`,
        // All pending in the batch
        `SUM(CASE WHEN ${isContribution ? 'biodata.contributionBatchId' : 'biodata.accruedRightsBatchId'} = :batchId3 AND biodata.paymentStatus IS NULL THEN 1 ELSE 0 END) AS "totalPendingPinInBatch"`,
      ])
      .setParameters({
        pfaCode1: pfaCode,
        pfaCode2: pfaCode,
        pfaCode3: pfaCode,
        pfaCode4: pfaCode,
        batchId1: batchName,
        batchId2: batchName,
        batchId3: batchName,
      });

    return await query.getRawOne();
  }
}
