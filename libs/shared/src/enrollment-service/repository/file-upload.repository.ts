import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { FileUploadProcess } from '../entities/file-upload.entity';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class FileUploadRepository extends AbstractRepository<FileUploadProcess> {
  constructor(
    @InjectRepository(FileUploadProcess)
    fileUploadRepository: Repository<FileUploadProcess>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(fileUploadRepository, entityManager);
  }
}
