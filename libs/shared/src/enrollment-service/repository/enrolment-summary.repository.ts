import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository, SelectQueryBuilder } from 'typeorm';
import { EnrolmentSummary } from '@app/shared/enrollment-service/entities/enrolment-summary.entity';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { RegistrationStatusEnum } from '@app/shared/enums/registration-status.enum';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class EnrolmentSummaryRepository extends AbstractRepository<EnrolmentSummary> {
  constructor(
    @InjectRepository(EnrolmentSummary)
    private readonly enrolmentSummaryRepository: Repository<EnrolmentSummary>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(enrolmentSummaryRepository, entityManager);
  }

  async getEnrollmentSummaryByRsaPin(rsaPin: string) {
    const query = this.buildSummaryQuery(null)
      .where('summary.rsaPin = :rsaPin', { rsaPin: rsaPin })
      .orderBy('summary.createDate', 'DESC');

    return this.mapSummaries(await query.getRawMany());
  }

  async getEnrollmentSummaryASliPDetailsByRsaPin(rsaPin: string) {
    const query = this.enrolmentSummaryRepository
      .createQueryBuilder('summary')
      .leftJoin('TBL_MDA_EMPLOYEE_BIODATA', 'empData', 'summary.rsaPin = "empData"."RSAPIN"')
      .leftJoin('TBL_PFA', 'pfa', '"empData".PFA = "pfa"."PFACODE"')
      .select([
        'summary.rsaPin AS "rsaPin"',
        'summary.firstName AS "firstName"',
        'summary.surname AS "surname"',
        'TO_CHAR(summary.createDate, \'YYYY-MM-DD\') AS "createDate"',
        '"pfa"."PFANAME" AS "pfaName"',
      ])
      .where('summary.rsaPin = :rsaPin', { rsaPin: rsaPin })
      .orderBy('summary.createDate', 'DESC');

    return await query.getRawOne();
  }

  async searchPaginatedEnrollmentSummary(filter: PaginatedSearchDto, currentUserEmail: string) {
    const query = this.buildSummaryQuery(filter, currentUserEmail).orderBy('summary.createDate', 'DESC');
    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (
            [
              'dateOfFirstAppointment',
              'createDate',
              'expectedDateOfRetirement',
              'dateOfTransferOfService',
              'dateOfDeath',
            ].includes(key)
          ) {
            // Ensure exact match for date fields in 'YYYY-MM-DD' format
            query.andWhere(`TO_CHAR(summary.${key}, 'YYYY-MM-DD') = :${key}`, { [key]: value });
          } else if (key === 'blacklisted' || key === 'active') {
            const booleanValue = value === 'false' ? false : Boolean(value);
            query.andWhere(`summary.${key} = :${key}`, { [key]: booleanValue });
          } else if (key === 'pfaCode') {
            query.andWhere(`LOWER("pfa"."PFACODE") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'pfaName') {
            query.andWhere(`LOWER("pfa"."PFANAME") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'employerName') {
            query.andWhere(`LOWER("mda"."EMPLOYER_NAME") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'mdaCode' || key === 'employerCode') {
            query.andWhere(`LOWER("mda"."EMPLOYER_ID") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else {
            query.andWhere(`LOWER(summary.${key}) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          }
        }
      });
    }

    const limit = filter.limit || 10;
    const page = filter.page || 1;
    const offset = (page - 1) * limit;
    const total = await query.getCount();

    const rawResults = await query.offset(offset).limit(limit).getRawMany();
    const result = {
      total,
      page,
      limit,
      data: this.mapSummaries(rawResults),
    };

    // this.logger.debug(`searchPaginatedEnrollmentSummary result: ${JSON.stringify(result)}`);
    return result;
  }

  async getValidatorWorkload(): Promise<{ validatorId: number; recordCount: number }[]> {
    return this.enrolmentSummaryRepository
      .createQueryBuilder('summary')
      .select('summary.assignedTo', 'validatorId')
      .addSelect('COUNT(*)', 'recordCount')
      .where('summary.assignedTo IS NOT NULL')
      .andWhere('summary.status = :status', { status: RegistrationStatusEnum.ENROLLED })
      .groupBy('summary.assignedTo')
      .getRawMany();
  }
  async getAuditorWorkload() {
    return this.enrolmentSummaryRepository
      .createQueryBuilder('summary')
      .select('summary.auditorAssignedTo', 'auditorEmail')
      .addSelect('COUNT(*)', 'recordCount')
      .where('summary.auditorAssignedTo IS NOT NULL')
      .andWhere('summary.status = :status', { status: RegistrationStatusEnum.COMPUTED })
      .groupBy('summary.auditorAssignedTo')
      .getRawMany();
  }

  async getEnrollmentOfficerDetails(rsaPin: string): Promise<Partial<CobraUser> | null> {
    const query = this.enrolmentSummaryRepository
      .createQueryBuilder('es')
      .leftJoinAndSelect('es.enrolledBy', 'cu')
      .select([
        'cu.location AS "location"',
        'cu.signature AS "signature"',
        'cu.surname AS "surname" ',
        'cu.firstName AS "firstName"',
        'cu.emailAddress AS "emailAddress"',
      ])
      .where('es.rsaPin = :rsaPin', { rsaPin });

    const result = await query.getRawOne();
    return result;
  }

  async getUnassignedRecords(status: RegistrationStatusEnum): Promise<EnrolmentSummary[]> {
    const query = await this.enrolmentSummaryRepository
      .createQueryBuilder('enrolment')
      .leftJoin('enrolment.enrolledBy', 'enrolledBy')
      .addSelect(['enrolledBy.firstName', 'enrolledBy.surname'])
      .where('enrolment.assignedTo IS NULL')
      .andWhere('enrolment.status = :status', { status });

    return await query.getMany();
  }

  async getUnassignedComputedRecords(): Promise<EnrolmentSummary[]> {
    const query = this.enrolmentSummaryRepository
      .createQueryBuilder('enrolment')
      .leftJoin('enrolment.enrolledBy', 'enrolledBy')
      .addSelect(['enrolledBy.firstName', 'enrolledBy.surname'])
      .where('enrolment.auditorAssignedTo IS NULL')
      .andWhere('enrolment.status = :status', { status: RegistrationStatusEnum.COMPUTED });

    return await query.getMany();
  }

  async getRecordsByStatus(status: RegistrationStatusEnum): Promise<EnrolmentSummary[]> {
    const query = this.enrolmentSummaryRepository
      .createQueryBuilder('enrolment')
      .andWhere('enrolment.status = :status', { status });
    return await query.getMany();
  }

  private buildSummaryQuery(
    filter: PaginatedSearchDto,
    currentUserEmail: string = ''
  ): SelectQueryBuilder<EnrolmentSummary> {
    let query = this.enrolmentSummaryRepository
      .createQueryBuilder('summary')
      .leftJoin('summary.enrolledBy', 'enrolledBy')
      .leftJoin('summary.submittedBy', 'submittedBy')
      .leftJoin('summary.assignedTo', 'assignedTo')
      .select([
        'summary.pk AS "pk"',
        'summary.rsaPin AS "rsaPin"',
        'summary.firstName AS "firstName"',
        'summary.surname AS "surname"',
        'summary.emailAddress AS "emailAddress"',
        'summary.gender AS "gender"',
        "CASE WHEN summary.active = 1 THEN 'true' ELSE 'false' END AS \"active\"",
        'summary.createDate AS "createDate"',
        'TO_CHAR(summary.dateOfFirstAppointment, \'YYYY-MM-DD\') AS "dateOfFirstAppointment"',
        'TO_CHAR(summary.expectedDateOfRetirement, \'YYYY-MM-DD\') AS "expectedDateOfRetirement"',
        'TO_CHAR(summary.dateOfTransferOfService, \'YYYY-MM-DD\') AS "dateOfTransferOfService"',
        'TO_CHAR(summary.dateOfDeath, \'YYYY-MM-DD\') AS "dateOfDeath"',
        'TO_CHAR(summary.enrolmentDate, \'YYYY-MM-DD\') AS "enrolmentDate"',
        'TO_CHAR(summary.createDate, \'YYYY-MM-DD\') AS "createDate"',
        'summary.status AS "status"',
        'enrolledBy.firstName AS "enrolledByFirstName"',
        'enrolledBy.surname AS "enrolledBySurname"',
        'submittedBy.firstName AS "submittedByFirstName"',
        'submittedBy.surname AS "submittedBySurname"',
        'assignedTo.emailAddress AS "cbrdValidatorAssignedToEmailAddress"',
        'summary.auditorAssignedTo AS "auditorAssignedToEmailAddress"',
      ]);

    let joinEnrollmentBiodata = false;

    if (filter) {
      joinEnrollmentBiodata =
        (filter.filters?.status ?? '') !== '' &&
        (filter.filters?.status ?? '') !== RegistrationStatusEnum.REGISTERED &&
        (filter.filters?.status ?? '') !== RegistrationStatusEnum.PENDING_PFA_REVIEW;

      if (filter.filters?.status === RegistrationStatusEnum.ENROLLED && currentUserEmail) {
        query = query.andWhere('lower(assignedTo.emailAddress) = :cbrdValidatorAssignedToEmailAddress', {
          cbrdValidatorAssignedToEmailAddress: currentUserEmail.toLowerCase(),
        });
      }

      if (filter.filters?.status === RegistrationStatusEnum.COMPUTED && currentUserEmail) {
        query = query.andWhere('lower(summary.auditorAssignedTo) = :auditorAssignedToEmailAddress', {
          auditorAssignedToEmailAddress: currentUserEmail.toLowerCase(),
        });
      }
    }

    if (joinEnrollmentBiodata) {
      query = query
        .leftJoin('ENROLMENT_BIODATA', 'enrollmentBiodata', '"enrollmentBiodata".RSA_PIN = "summary"."RSA_PIN"')
        .leftJoin('TBL_PFA', 'pfa', '"enrollmentBiodata".PFA_CODE = "pfa"."PFACODE"')
        .leftJoin('TBL_MDA', 'mda', 'enrollmentBiodata.employerCode = "mda"."EMPLOYER_ID"')
        .addSelect(['"pfa"."PFANAME" AS "pfaName"', '"pfa"."PFACODE" AS "pfaCode"'])
        .addSelect(['"mda"."EMPLOYER_NAME" AS "employerName"', '"mda"."EMPLOYER_ID" AS "employerCode"']);
    } else {
      query = query
        .leftJoin('TBL_MDA_EMPLOYEE_BIODATA', 'employeeBiodata', '"employeeBiodata".RSAPIN = "summary"."RSA_PIN"')
        .leftJoin('TBL_PFA', 'pfa', '"employeeBiodata".PFA = "pfa"."PFACODE"')
        .leftJoin('TBL_MDA', 'mda', 'employeeBiodata.MDA = "mda"."EMPLOYER_ID"')
        .addSelect(['"pfa"."PFANAME" AS "pfaName"', '"pfa"."PFACODE" AS "pfaCode"'])
        .addSelect(['"mda"."EMPLOYER_NAME" AS "employerName"', '"mda"."EMPLOYER_ID" AS "employerCode"']);
    }
    return query;
  }

  private mapSummaries(rawResults: any[]): any[] {
    const summariesMap = new Map<string, any>();
    rawResults.forEach((row) => {
      const summaryKey = row.pk;
      if (!summariesMap.has(summaryKey)) {
        summariesMap.set(summaryKey, {
          pk: row.pk,
          active: row.active,
          createDate: row.createDate,
          firstName: row.firstName,
          surname: row.surname,
          rsaPin: row.rsaPin,
          emailAddress: row.emailAddress,
          gender: row.gender,
          dateOfFirstAppointment: row.dateOfFirstAppointment,
          expectedDateOfRetirement: row.expectedDateOfRetirement,
          dateOfTransferOfService: row.dateOfTransferOfService,
          dateOfDeath: row.dateOfDeath,
          enrolmentDate: row.enrolmentDate,
          status: row.status,
          enrolledByFirstName: row.enrolledByFirstName,
          enrolledBySurname: row.enrolledBySurname,
          submittedByFirstName: row.submittedByFirstName,
          submittedByBySurname: row.submittedBySurname,
          cbrdValidatorAssignedToEmailAddress: row.cbrdValidatorAssignedToEmailAddress,
          auditorAssignedTo: row.auditorAssignedToEmailAddress,
          pfaName: row.pfaName,
          pfaCode: row.pfaCode,
          employerName: row.employerName,
          employerCode: row.employerCode,
        });
      }
    });

    return Array.from(summariesMap.values());
  }

  async getPendingRecordByAuditorAssignedTo(emailAddress: string): Promise<EnrolmentSummary[]> {
    return await this.enrolmentSummaryRepository
      .createQueryBuilder('summary')
      .select(['summary.pk AS "pk"'])
      .where('lower(summary.auditorAssignedTo) = :emailAddress', { emailAddress: emailAddress.toLowerCase() })
      .andWhere('summary.status = :status', { status: RegistrationStatusEnum.COMPUTED }) // fix status for the RegistrationStatusEnum
      .getMany();
  }

  async getPendingRecordByAssignedTo(emailAddress: string): Promise<EnrolmentSummary[]> {
    return await this.enrolmentSummaryRepository
      .createQueryBuilder('summary')
      .leftJoin('summary.assignedTo', 'assigned')
      .where('lower(assigned.emailAddress) = :emailAddress', { emailAddress: emailAddress.toLowerCase() })
      .andWhere('summary.status = :status', { status: RegistrationStatusEnum.ENROLLED })
      .getMany();
  }

  async getCountOfPendingRecordByAssignedTo(emailAddress: string): Promise<number> {
    return await this.enrolmentSummaryRepository
      .createQueryBuilder('summary')
      .leftJoin('summary.assignedTo', 'assigned')
      .select(['summary.pk AS "pk"'])
      .where('lower(assigned.emailAddress) = :emailAddress', { emailAddress: emailAddress.toLowerCase() })
      .andWhere('summary.status = :status', { status: RegistrationStatusEnum.ENROLLED })
      .getCount();
  }

  async getCountOfPendingRecordByAuditorAssignedTo(emailAddress: string): Promise<number> {
    return await this.enrolmentSummaryRepository
      .createQueryBuilder('summary')
      .select(['summary.pk AS "pk"'])
      .where('lower(summary.auditorAssignedTo) = :emailAddress', { emailAddress: emailAddress.toLowerCase() })
      .andWhere('summary.status = :status', { status: RegistrationStatusEnum.COMPUTED }) // fix status for the RegistrationStatusEnum
      .getCount();
  }

  async existsByRsaPin(rsaPin: string): Promise<boolean> {
    return !!(await this.enrolmentSummaryRepository
      .createQueryBuilder('summary')
      .where('summary.rsaPin = :rsaPin', { rsaPin })
      .getCount());
  }
}
