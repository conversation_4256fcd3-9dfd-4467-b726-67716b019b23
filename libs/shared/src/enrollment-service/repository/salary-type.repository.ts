import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { <PERSON><PERSON>Logger } from 'nestjs-pino';
import { SalaryType } from '@app/shared/enrollment-service/entities/salary-type.entity';

@Injectable()
export class SalaryTypeRepository extends AbstractRepository<SalaryType> {
  constructor(
    @InjectRepository(SalaryType)
    private readonly salaryTypeRepository: Repository<SalaryType>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(salaryTypeRepository, entityManager);
  }

  /**
   * Get salary details based on the provided code, gradeCode, and levelCode.
   * @param salaryType - The salary type code.
   * @param gradeCode - The grade code.
   * @param levelCode - The level code.
   * @returns Salary details or null if not found.
   */
  async getSalaryDetails(salaryType: string, gradeCode: string, levelCode: string) {
    const query = await this.salaryTypeRepository
      .createQueryBuilder('st')
      .select([
        'st.SALARY_TYPE as "salaryType"',
        'sat.GRADE_CODE as "gradeCode"',
        'sat.LEVEL_CODE as "levelCode"',
        'sat.SALARY as "salary"',
        'sat.HOUSING as "housing"',
        'sat.TRANSPORT as "transport"',
      ])
      .innerJoin('SAL_ALLOWANCE_TAB', 'sat', 'st.INSTITUTION_TYPE_CODE = sat.INSTITUTION_TYPE_CODE')
      .leftJoin('TBC_SALARY_MAPPING', 'tsm', '"tsm".SALARY_TYPE = st.SALARY_TYPE')
      .where('"tsm".TBE_SAL_STRUCTURE = :salaryType', { salaryType })
      .andWhere('sat.GRADE_CODE = :gradeCode', { gradeCode })
      .andWhere('sat.LEVEL_CODE = :levelCode', { levelCode });

    const result = await query.getRawOne();
    return result || null;
  }
}
