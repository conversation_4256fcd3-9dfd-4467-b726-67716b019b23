import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { EnrolmentDraft } from '@app/shared/enrollment-service/entities/enrolment-draft.entity';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class EnrolmentDraftRepository extends AbstractRepository<EnrolmentDraft> {
  constructor(
    @InjectRepository(EnrolmentDraft)
    private readonly enrolmentDraftRepository: Repository<EnrolmentDraft>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(enrolmentDraftRepository, entityManager);
  }
}
