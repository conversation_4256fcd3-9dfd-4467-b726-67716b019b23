/* eslint-disable */
import { AbstractRepository } from '@app/shared/database';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { NominalRollHistory } from '@app/shared/enrollment-service/entities/nominal-roll-history.entity';

Injectable();
export class NominalRollHistoryRepository extends AbstractRepository<NominalRollHistory> {
  constructor(
    @InjectRepository(NominalRollHistory)
    readonly nominalRollHistoryRepository: Repository<NominalRollHistory>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(nominalRollHistoryRepository, entityManager);
  }

  async getNominalRollDetailByRsaPin(rsaPin: string) {
    return await this.nominalRollHistoryRepository
      .createQueryBuilder('nominalRoll')
      .leftJoinAndSelect('nominalRoll.bands', 'bands')
      .leftJoinAndSelect('bands.nominalRollBandDetails', 'bandDetails')
      .leftJoin('nominalRoll.createdBy', 'createdBy')
      .addSelect(['createdBy.firstName', 'createdBy.surname'])
      .where('nominalRoll.rsaPin = :rsaPin', { rsaPin })
      .orderBy('nominalRoll.createDate', 'DESC')
      .getMany();
  }
}
