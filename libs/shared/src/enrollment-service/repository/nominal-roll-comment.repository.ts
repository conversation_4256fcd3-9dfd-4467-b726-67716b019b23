import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { NominalRollComment } from '@app/shared/enrollment-service/entities/nominal-roll-comment.entity';

@Injectable()
export class NominalRollCommentRepository extends AbstractRepository<NominalRollComment> {
  constructor(
    @InjectRepository(NominalRollComment)
    private readonly nominalRollCommentRepository: Repository<NominalRollComment>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(nominalRollCommentRepository, entityManager);
  }
}
