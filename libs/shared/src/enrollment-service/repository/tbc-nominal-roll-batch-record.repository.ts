import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { TbcNominalRollBatchRecord } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch-record';

@Injectable()
export class TbcNominalRollBatchRecordRepository extends AbstractRepository<TbcNominalRollBatchRecord> {
  constructor(
    @InjectRepository(TbcNominalRollBatchRecord)
    private readonly tbcNominalRollBatchRecordRepository: Repository<TbcNominalRollBatchRecord>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(tbcNominalRollBatchRecordRepository, entityManager);
  }
}
