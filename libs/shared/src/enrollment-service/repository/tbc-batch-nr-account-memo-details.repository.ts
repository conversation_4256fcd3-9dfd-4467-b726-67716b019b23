import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { TbcBatchNrAccountMemoDetails } from '@app/shared/enrollment-service/entities/tbc-batch-nr-account-memo-details.entity';

@Injectable()
export class TbcBatchNrAccountMemoDetailsRepository extends AbstractRepository<TbcBatchNrAccountMemoDetails> {
  constructor(
    @InjectRepository(TbcBatchNrAccountMemoDetails)
    private readonly tbcBatchNrAccountMemoDetailsRepository: Repository<TbcBatchNrAccountMemoDetails>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(tbcBatchNrAccountMemoDetailsRepository, entityManager);
  }

  async retrieveAccountMemoDetails(batchName: string) {
    return await this.tbcBatchNrAccountMemoDetailsRepository
      .createQueryBuilder('memo')
      .leftJoin('TBL_PFA', 'pfa', 'pfa.PFACODE = memo.PFA_CODE')
      .leftJoin('TBL_PFC', 'pfc', 'pfc.PFCCODE = memo.PFC_CODE')
      .leftJoin('TBC_NOMINAL_ROLL_BATCH', 'batch', 'batch.pk = memo.BATCH_ID')
      .where('batch.BATCH_NAME = :batchName', { batchName: batchName }) // Use dynamic value if needed
      .select([
        'memo.PFA_NAME AS "pfaName"',
        'memo.PFA_CODE AS "pfaCode"',
        'memo.PFA_ACCOUNT_NAME AS "pfaAccountName"',
        'memo.PFA_ACCOUNT_NUMBER AS "pfaAccountNumber"',
        'pfa.EMAIL_ADDRESS AS "pfaEmail"',
        'pfc.EMAIL_ADDRESS AS "pfcEmail"',
        'memo.PFC_NAME AS "pfcName"',
        'memo.PFC_CODE AS "pfcCode"',
        'memo.AMOUNT AS "amount"',
        'memo.BATCH_ID AS "batchId"',
        'batch.BATCH_NAME AS "batchName"',
      ])
      .orderBy('memo.AMOUNT', 'ASC')
      .getRawMany();
  }
}
