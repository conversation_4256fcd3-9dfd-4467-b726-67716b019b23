import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { AccrLegacyPayments } from '@app/shared/enrollment-service/entities/accr-legacy-payments.entity';
import { PinoLogger } from 'nestjs-pino';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';

@Injectable()
export class AccrLegacyPaymentsRepository extends AbstractRepository<AccrLegacyPayments> {
  constructor(
    @InjectRepository(AccrLegacyPayments)
    private readonly accrLegacyPaymentsRepository: Repository<AccrLegacyPayments>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(accrLegacyPaymentsRepository, entityManager);
  }

  async getTotalPaidByPin(pin: string) {
    const result = await this.accrLegacyPaymentsRepository
      .createQueryBuilder('r')
      .select('SUM(r.accrBenfitApprox)', 'paid')
      .where('r.pin = :pin', { pin })
      .groupBy('r.pin')
      .getRawOne();

    return result?.paid ?? 0;
  }

  async accruedRightsLedger(filter: PaginatedSearchDto) {
    const query = this.accrLegacyPaymentsRepository
      .createQueryBuilder('accrLegacyPayments')
      .select([
        'accrLegacyPayments.pin AS "pin"',
        'accrLegacyPayments.fullName AS "fullName"',
        'accrLegacyPayments.gender AS "gender"',
        'TO_CHAR(accrLegacyPayments.dor, \'YYYY-MM-DD\') AS "dor"',
        'TO_CHAR(accrLegacyPayments.dob, \'YYYY-MM-DD\') AS "dob"',
        'TO_CHAR(accrLegacyPayments.dofa, \'YYYY-MM-DD\') AS "dofa"',
        'TO_CHAR(accrLegacyPayments.createDate, \'YYYY-MM-DD\') AS "createDate"',
        'TO_CHAR(accrLegacyPayments.lastModified, \'YYYY-MM-DD\') AS "lastModified"',
        'accrLegacyPayments.pfaName AS "pfaName"',
        'accrLegacyPayments.mdaName AS "mdaName"',
        'accrLegacyPayments.salStruc AS "salStruc"',
        'accrLegacyPayments.glJun2004 AS "glJun2004"',
        'accrLegacyPayments.stepJun2004 AS "stepJun2004"',
        'accrLegacyPayments.batchName AS "batchName"',
        'TO_CHAR(accrLegacyPayments.introDate, \'YYYY-MM-DD\') AS "introDate"',
        'accrLegacyPayments.annualPenAllow AS "annualPenAllow"',
        'accrLegacyPayments.accrBenfitActual AS "accrBenfitActual"',
        'accrLegacyPayments.accrBenfitApprox AS "accrBenfitApprox"',
        'accrLegacyPayments.accrBenfitAdd AS "accrBenfitAdd"',
        'TO_CHAR(accrLegacyPayments.accrBenfitAddDate, \'YYYY-MM-DD\') AS "accrBenfitAddDate"',
        'accrLegacyPayments.accrBenfit2ndAdd AS "accrBenfit2ndAdd"',
        'accrLegacyPayments.accrBenfitRecovered AS "accrBenfitRecovered"',
        'TO_CHAR(accrLegacyPayments.accrBenfitRecoveredDate, \'YYYY-MM-DD\') AS "accrBenfitRecoveredDate"',
        'accrLegacyPayments.accrBenfitNet AS "accrBenfitNet"',
        'accrLegacyPayments.exitMode AS "exitMode"',
        'accrLegacyPayments.actorEmail AS "actorEmail"',
      ]);

    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (
            [
              'dor',
              'dob',
              'dofa',
              'introDate',
              'accrBenfitAddDate',
              'accrBenfitRecoveredDate',
              'createDate',
              'lastModified',
            ].includes(key)
          ) {
            query.andWhere(`TO_CHAR(accrLegacyPayments.${key}, 'YYYY-MM-DD') = :${key}`, { [key]: value });
          } else {
            query.andWhere(`LOWER(accrLegacyPayments.${key}) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          }
        }
      });
    }

    const limit = Math.max(filter.limit || 10, 1);
    const page = Math.max(filter.page || 1, 1);
    const offset = (page - 1) * limit;

    query.orderBy('accrLegacyPayments.introDate', 'DESC');

    const total = await query.getCount();
    const accrLegacyPaymentsList = await query.offset(offset).limit(limit).getRawMany();

    return {
      page,
      limit,
      total,
      result: accrLegacyPaymentsList,
    };
  }
}
