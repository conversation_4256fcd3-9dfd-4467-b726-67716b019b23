import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { TbcBatchAccountMemoDetails } from '@app/shared/enrollment-service/entities/tbc-batch-account-memo-details.entity';

@Injectable()
export class TbcBatchAccountMemoDetailsRepository extends AbstractRepository<TbcBatchAccountMemoDetails> {
  constructor(
    @InjectRepository(TbcBatchAccountMemoDetails)
    private readonly tbcBatchAccountMemoDetailsRepository: Repository<TbcBatchAccountMemoDetails>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(tbcBatchAccountMemoDetailsRepository, entityManager);
  }
}
