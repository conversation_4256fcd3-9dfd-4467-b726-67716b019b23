import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { TbcNominalRollPfaMemoDocument } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-pfa-memo-document';

@Injectable()
export class TbcNominalRollPfaMemoDocumentRepository extends AbstractRepository<TbcNominalRollPfaMemoDocument> {
  constructor(
    @InjectRepository(TbcNominalRollPfaMemoDocument)
    private readonly tbcNominalRollPfaMemoDetailRepository: Repository<TbcNominalRollPfaMemoDocument>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(tbcNominalRollPfaMemoDetailRepository, entityManager);
  }
}
