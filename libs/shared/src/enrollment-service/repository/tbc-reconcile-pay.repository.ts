import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { TbcReconcilePay } from '@app/shared/enrollment-service/entities/tbc-reconcile-pay.entity';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';

@Injectable()
export class TbcReconcilePayRepository extends AbstractRepository<TbcReconcilePay> {
  constructor(
    @InjectRepository(TbcReconcilePay)
    private readonly tbcReconcilePayRepository: Repository<TbcReconcilePay>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(tbcReconcilePayRepository, entityManager);
  }

  async getTotalPaidByPin(pin: string): Promise<number> {
    const result = await this.tbcReconcilePayRepository
      .createQueryBuilder('r')
      .select('SUM(r.amount)', 'paid')
      .where('r.pin = :pin', { pin })
      .groupBy('r.pin')
      .getRawOne();

    return result?.paid ?? 0;
  }

  async getLastReconcilePayByPin(pin: string): Promise<TbcReconcilePay> {
    return await this.tbcReconcilePayRepository
      .createQueryBuilder('reconcilePay')
      .where('reconcilePay.pin = :pin', { pin })
      .orderBy('reconcilePay.createDate', 'DESC')
      .getOne();
  }

  async fetchContributionLedger(filter: PaginatedSearchDto) {
    const query = this.tbcReconcilePayRepository
      .createQueryBuilder('reconcilePay')
      .select([
        'reconcilePay.pin AS "pin"',
        'reconcilePay.actorEmail AS "actorEmail"',
        'reconcilePay.amount AS "amount"',
        'reconcilePay.payType AS "payType"',
        'reconcilePay.grade AS "grade"',
        'reconcilePay.step AS "step"',
        'reconcilePay.agencyCode AS "agencyCode"',
        'reconcilePay.mthEmol AS "mthEmol"',
        'reconcilePay.paidToDate AS "paidToDate"',
        'reconcilePay.name AS "name"',
        'reconcilePay.pfaCode AS "pfaCode"',
        'reconcilePay.pfaName AS "pfaName"',
        'reconcilePay.salaryType AS "salaryType"',
        'TO_CHAR(reconcilePay.createDate, \'YYYY-MM-DD\') AS "createDate"',
        'TO_CHAR(reconcilePay.lastPaydate, \'YYYY-MM-DD\') AS "lastPaydate"',
        'TO_CHAR(reconcilePay.lastModified, \'YYYY-MM-DD\') AS "lastModified"',
      ]);

    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (['createDate', 'lastModified', 'lastPaydate'].includes(key)) {
            query.andWhere(`TO_CHAR(reconcilePay.${key}, 'YYYY-MM-DD') = :${key}`, { [key]: value });
          } else if (key === 'active') {
            const booleanValue = value === 'false' ? false : Boolean(value);
            query.andWhere(`reconcilePay.active = :${key}`, { [key]: booleanValue });
          } else {
            query.andWhere(`LOWER(reconcilePay.${key}) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          }
        }
      });
    }

    const limit = Math.max(filter.limit || 10, 1);
    const page = Math.max(filter.page || 1, 1);
    const offset = (page - 1) * limit;

    query.orderBy('reconcilePay.lastModified', 'DESC');

    const total = await query.getCount();
    const reconcilePayList = await query.offset(offset).limit(limit).getRawMany();

    return {
      page,
      limit,
      total,
      result: reconcilePayList,
    };
  }
}
