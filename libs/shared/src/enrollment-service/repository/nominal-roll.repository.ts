/* eslint-disable */
import { AbstractRepository } from '@app/shared/database';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';
import { NominalRollSearchDto } from '../../dto/enrollment/nominalroll/nominal-roll-employee.dto';
import { PinoLogger } from 'nestjs-pino';
import { NominalRollContributionFilterDto } from '@app/shared/dto/enrollment/nominal-roll-contribution-filter-dto';
import { convertHypenToIsoDate } from '@app/shared/workflow/utils/workflow-utils';
import { HYPHEN_DATE_FORMAT } from '@app/shared/constants';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { PfaPaymentStats } from '@app/shared/enrollment-service/entities/entity.types';

Injectable();

export class NominalRollRepository extends AbstractRepository<NominalRoll> {
  constructor(
    @InjectRepository(NominalRoll)
    readonly nominalRollRepository: Repository<NominalRoll>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(nominalRollRepository, entityManager);
  }

  async getNominalRoll(searchParams: NominalRollSearchDto) {
    const query = this.nominalRollRepository
      .createQueryBuilder('nr')
      .select([
        'nr.pk AS "pk"',
        'nr.rsaPin AS "rsaPin"',
        'nr.firstName AS "firstName"',
        'nr.surname AS "surname"',
        'nr.otherName AS "otherName"',
        'nr.pfaCode AS "pfaCode"',
        'nr.mdaCode AS "mdaCode"',
        'TO_CHAR(nr.createDate, \'YYYY-MM-DD\') AS "createDate"',
        'TO_CHAR(nr.dateOfFirstAppointment, \'YYYY-MM-DD\') AS "dateOfFirstAppointment"',
        'TO_CHAR(nr.dateOfExit, \'YYYY-MM-DD\') AS "dateOfExit"',
      ])
      .orderBy('nr.createDate', 'DESC');

    const {
      searchTerm,
      rsaPin,
      firstName,
      surname,
      otherName,
      pfaCode,
      mdaCode,
      dateOfFirstAppointment,
      createDate,
      dateOfExit,
      page,
      limit,
    } = searchParams;

    if (searchTerm) {
      query.andWhere(
        `LOWER(nr.firstName) LIKE LOWER(:searchTerm1) 
     OR LOWER(nr.surname) LIKE LOWER(:searchTerm2) 
     OR LOWER(nr.otherName) LIKE LOWER(:searchTerm3)`,
        {
          searchTerm1: `%${searchTerm}%`,
          searchTerm2: `%${searchTerm}%`,
          searchTerm3: `%${searchTerm}%`,
        }
      );
    }

    if (rsaPin) {
      query.andWhere('nr.rsaPin = :rsaPin', { rsaPin });
    }

    if (firstName) {
      query.andWhere('LOWER(nr.firstName) LIKE LOWER(:firstName)', { firstName: `%${firstName}%` });
    }

    if (surname) {
      query.andWhere('LOWER(nr.surname) LIKE LOWER(:surname)', { surname: `%${surname}%` });
    }

    if (pfaCode) {
      query.andWhere('LOWER(nr.pfaCode) LIKE LOWER(:pfaCode)', { pfaCode: `%${pfaCode}%` });
    }

    if (mdaCode) {
      query.andWhere('LOWER(nr.mdaCode) LIKE LOWER(:mdaCode)', { mdaCode: `%${mdaCode}%` });
    }

    if (otherName) {
      query.andWhere('LOWER(nr.otherName) LIKE LOWER(:otherName)', { otherName: `%${otherName}%` });
    }

    if (dateOfFirstAppointment) {
      query.andWhere("TO_CHAR(nr.dateOfFirstAppointment, 'YYYY-MM-DD') = :dateOfFirstAppointment", {
        dateOfFirstAppointment,
      });
    }

    if (dateOfExit) {
      query.andWhere("TO_CHAR(nr.dateOfExit, 'YYYY-MM-DD') = :dateOfExit", {
        dateOfExit,
      });
    }

    if (createDate) {
      query.andWhere("TO_CHAR(nr.createDate, 'YYYY-MM-DD') = :createDate", { createDate });
    }

    const plimit = limit || 10;
    const ppage = page || 1;
    const offset = (ppage - 1) * plimit;

    query.offset(offset).limit(plimit);
    const rawResults = await query.getRawMany();
    const total = await query.getCount();
    return { results: rawResults, total, page: ppage, limit: plimit };
  }

  async getNominalRollDetailByRsaPin(rsaPin: string) {
    return await this.nominalRollRepository
      .createQueryBuilder('nominalRoll')
      .leftJoinAndSelect('nominalRoll.bands', 'bands')
      .leftJoinAndSelect('nominalRoll.comments', 'comments')
      .leftJoinAndSelect('bands.nominalRollBandDetails', 'bandDetails')
      .leftJoin('nominalRoll.createdBy', 'createdBy')
      .addSelect(['createdBy.firstName', 'createdBy.surname'])
      .where('nominalRoll.rsaPin = :rsaPin', { rsaPin })
      .getOne();
  }

  async getNominalRollByRsaPin(rsaPin: string) {
    const query = await this.nominalRollRepository
      .createQueryBuilder('nr')
      .select([
        'nr.pk AS "pk"',
        'nr.rsaPin AS "rsaPin"',
        'TO_CHAR(nr.dateOfFirstAppointment, \'YYYY-MM-DD\') AS "dateOfFirstAppointment"',
        'TO_CHAR(nr.dateOfExit, \'YYYY-MM-DD\') AS "dateOfExit"',
        'nr.firstName AS "firstName"',
        'nr.surname AS "surname"',
        'nr.otherName AS "otherName"',
        'nr.pfaCode AS "pfaCode"',
        'nr.mdaCode AS "mdaCode"',
        'createdBy.firstName AS "createdByFirstName"',
        'createdBy.surname AS "createdByLastName"',
        'band.year AS "bandYear"',
        'bandDetails.gradeLevel AS "gradeLevel"',
        'bandDetails.salaryStructure AS "salaryStructure"',
        'bandDetails.step AS "step"',
      ])
      .leftJoin('nr.createdBy', 'createdBy')
      .leftJoin('nr.bands', 'band')
      .leftJoin('band.nominalRollBandDetails', 'bandDetails')
      .where('nr.rsaPin = :rsaPin', { rsaPin: rsaPin })
      .orderBy('band.year', 'ASC');

    // console.log('getQueryAndParameters', query.getQueryAndParameters());
    const rawResult = await query.getRawMany();
    // console.log('rawResult', rawResult);

    if (!rawResult) {
      return null;
    }

    const formattedResult = rawResult.reduce((acc, row) => {
      let nominalRoll = acc[row.pk];

      if (!nominalRoll) {
        nominalRoll = {
          pk: row.pk,
          rsaPin: row.rsaPin,
          dateOfFirstAppointment: row.dateOfFirstAppointment,
          dateOfExit: row.dateOfExit,
          firstName: row.firstName,
          surname: row.surname,
          otherName: row.otherName,
          pfaCode: row.pfaCode,
          mdaCode: row.mdaCode,
          createdByFirstName: row.createdByFirstName,
          createdByLastName: row.createdByLastName,
          bands: [],
        };
        acc[row.pk] = nominalRoll;
      }

      // Group bands correctly
      if (row.bandYear) {
        let band = nominalRoll.bands.find((b) => b.year === row.bandYear);
        if (!band) {
          band = {
            year: row.bandYear,
            nominalRollBandDetails: [],
          };
          nominalRoll.bands.push(band);
        }

        if (row.gradeLevel) {
          band.nominalRollBandDetails.push({
            gradeLevel: row.gradeLevel,
            salaryStructure: row.salaryStructure,
            step: row.step,
          });
        }
      }

      return acc;
    }, {});

    return Object.values(formattedResult)[0];
  }

  async fetchNominalRollForBatching(filterDto: NominalRollContributionFilterDto) {
    return this.fetchNominalRollWithFilter(filterDto, 'CR_CERTIFIED');
  }

  async fetchNominalRollForComputation(filterDto: NominalRollContributionFilterDto) {
    return this.fetchNominalRollWithFilter(filterDto, 'UPLOADED');
  }

  async fetchNominalRollWithFilter(filterDto: NominalRollContributionFilterDto, status: string) {
    const { rsaPin, mdaCode, pfaCode, uploadStartDate, uploadEndDate } = filterDto;

    const query = this.nominalRollRepository
      .createQueryBuilder('roll')
      .select([
        'roll.pk AS "pk"',
        'roll.rsaPin AS "rsaPin"',
        'roll.firstName AS "firstName"',
        'roll.surname AS "surname"',
        'roll.mdaName AS "mdaName"',
        'roll.mdaCode AS "mdaCode"',
        'roll.pfaName AS "pfaName"',
        'roll.pfaCode AS "pfaCode"',
        'roll.totalPension AS "totalPension"',
        'roll.totalPensionPlusInterest AS "totalPensionPlusInterest"',
        'roll.totalInterest AS "totalInterest"',
        'roll.totalEmolument AS "totalEmolument"',
        'TO_CHAR(roll.lastModified, \'YYYY-MM-DD\') AS "lastModified"',
        'roll.status AS "status"',
      ])
      .where('roll.status = :status', { status: status });

    if (rsaPin) {
      query.andWhere('roll.rsaPin = :rsaPin', { rsaPin });
    }

    if (mdaCode) {
      query.andWhere('roll.mdaCode = :mdaCode', { mdaCode });
    }

    if (pfaCode) {
      query.andWhere('roll.pfaCode = :pfaCode', { pfaCode });
    }

    if (uploadStartDate && uploadEndDate) {
      const startDate = convertHypenToIsoDate(uploadStartDate, HYPHEN_DATE_FORMAT);
      const endDate = convertHypenToIsoDate(uploadEndDate, HYPHEN_DATE_FORMAT);
      query.andWhere('roll.lastModified BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    return await query.getRawMany();
  }

  async getPinsByBatchName(batchName: any) {
    const rsaPins = await this.nominalRollRepository
      .createQueryBuilder('nominalRoll')
      .select('nominalRoll.rsaPin as "rsaPin"')
      .where('nominalRoll.batchId = :batchId', { batchId: batchName })
      .getRawMany();

    return rsaPins.map((row) => row.rsaPin);
  }

  async retrieveNominalRollList(filter: PaginatedSearchDto): Promise<{
    result: any;
    total: number;
    page: number;
    limit: number;
  }> {
    const query = this.nominalRollRepository
      .createQueryBuilder('nominalRoll')
      .leftJoin('nominalRoll.createdBy', 'createdBy')
      .select([
        'nominalRoll.pk AS "pk"',
        'nominalRoll.rsaPin AS "rsaPin"',
        'nominalRoll.otherName AS "otherName"',
        'nominalRoll.firstName AS "firstName"',
        'nominalRoll.surname AS "surname"',
        'nominalRoll.mdaName AS "mdaName"',
        'nominalRoll.mdaCode AS "mdaCode"',
        'nominalRoll.pfaCode AS "pfaCode"',
        'nominalRoll.pfaName AS "pfaName"',
        'nominalRoll.status AS "status"',
        'TO_CHAR(nominalRoll.dateOfFirstAppointment, \'YYYY-MM-DD\') AS "dateOfFirstAppointment"',
        'TO_CHAR(nominalRoll.lastModified, \'YYYY-MM-DD\') AS "lastModified"',
        'createdBy.firstName AS "createdByFirstName"',
        'createdBy.surname AS "createdByLastName"',
      ]);

    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (['lastModified'].includes(key)) {
            query.andWhere(`TO_CHAR(nominalRoll.${key}, 'YYYY-MM-DD') = :${key}`, { [key]: value });
          } else if (key === 'createdByFirstName') {
            query.andWhere(`LOWER("createdBy"."FIRST_NAME") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else if (key === 'createdByLastName') {
            query.andWhere(`LOWER("createdBy"."SURNAME") LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          } else {
            query.andWhere(`LOWER(nominalRoll.${key}) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          }
        }
      });
    }

    const limit = Math.max(filter.limit || 10, 1); // Ensures limit is at least 1
    const page = Math.max(filter.page || 1, 1); // Ensures page starts at 1
    const offset = (page - 1) * limit;

    query.orderBy('nominalRoll.lastModified', 'DESC'); // Ensure createDate exists

    const total = await query.getCount();
    const nominalRolls = await query.offset(offset).limit(limit).getRawMany();

    if (!Array.isArray(nominalRolls)) {
      this.logger.error('Unexpected query result format:', { nominalRolls });
      return { page, limit, total: 0, result: [] };
    }

    return {
      page,
      limit,
      total,
      result: nominalRolls.map((nominalRoll) => ({
        pk: nominalRoll.pk,
        firstName: nominalRoll.firstName,
        surname: nominalRoll.surname,
        otherName: nominalRoll.otherName,
        createdByFirstName: nominalRoll.createdByFirstName,
        createdByLastName: nominalRoll.createdByLastName,
        lastModified: nominalRoll.lastModified,
        dateOfFirstAppointment: nominalRoll.dateOfFirstAppointment,
        status: nominalRoll.status,
        rsaPin: nominalRoll.rsaPin,
        mdaName: nominalRoll.mdaName,
        mdaCode: nominalRoll.mdaCode,
        pfaName: nominalRoll.pfaName,
        pfaCode: nominalRoll.pfaCode,
      })),
    };
  }

  async findPfaPaymentStatsInBatch(batchId: string, pfaCode: string): Promise<PfaPaymentStats> {
    const query = this.nominalRollRepository
      .createQueryBuilder('nominalRoll')
      .select([
        `SUM(CASE WHEN nominalRoll.pfaCode = :pfaCode1 AND nominalRoll.batchId = :batchId1 THEN 1 ELSE 0 END) AS "totalInBatchForPfa"`,
        `SUM(CASE WHEN nominalRoll.pfaCode = :pfaCode2 AND nominalRoll.batchId = :batchId2 AND nominalRoll.paymentStatus IS NULL THEN 1 ELSE 0 END) AS "pendingInBatchForPfa"`,
        `SUM(CASE WHEN nominalRoll.pfaCode = :pfaCode3 AND nominalRoll.batchId = :batchId3 AND nominalRoll.paymentStatus IS NOT NULL THEN 1 ELSE 0 END) AS "paidInBatchForPfa"`,
        `SUM(CASE WHEN nominalRoll.batchId = :batchId4 AND nominalRoll.paymentStatus IS NULL THEN 1 ELSE 0 END) AS "totalPendingPinInBatch"`,
      ])
      .setParameters({
        pfaCode1: pfaCode,
        pfaCode2: pfaCode,
        pfaCode3: pfaCode,
        batchId1: batchId,
        batchId2: batchId,
        batchId3: batchId,
        batchId4: batchId,
      });

    return await query.getRawOne();
  }
}
