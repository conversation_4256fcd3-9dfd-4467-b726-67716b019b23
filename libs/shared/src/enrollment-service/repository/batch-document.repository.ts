import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { BatchDocuments } from '@app/shared/enrollment-service/entities/batch-documents.entity';

@Injectable()
export class BatchDocumentRepository extends AbstractRepository<BatchDocuments> {
  constructor(
    @InjectRepository(BatchDocuments)
    private readonly batchDocumentsRepository: Repository<BatchDocuments>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(batchDocumentsRepository, entityManager);
  }
}
