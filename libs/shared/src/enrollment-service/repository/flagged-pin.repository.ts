import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { FlaggedPin } from '@app/shared/enrollment-service/entities/flagged-pin.entity';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';

@Injectable()
export class FlaggedPinRepository extends AbstractRepository<FlaggedPin> {
  constructor(
    @InjectRepository(FlaggedPin)
    private readonly flaggedPinRepository: Repository<FlaggedPin>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(flaggedPinRepository, entityManager);
  }

  async fetchFlaggedRsaHolderAccount(filter: PaginatedSearchDto) {
    const query = this.flaggedPinRepository
      .createQueryBuilder('flaggedPin')
      .select([
        'flaggedPin.rsaPin AS "rsaPin"',
        'flaggedPin.reason AS "employerId"',
        "CASE WHEN flaggedPin.active = 1 THEN 'true' ELSE 'false' END AS \"active\"",
        'TO_CHAR(flaggedPin.createDate, \'YYYY-MM-DD\') AS "createDate"',
        'TO_CHAR(flaggedPin.lastModified, \'YYYY-MM-DD\') AS "lastModified"',
      ]);

    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (['createDate', 'lastModified'].includes(key)) {
            query.andWhere(`TO_CHAR(reconcilePay.${key}, 'YYYY-MM-DD') = :${key}`, { [key]: value });
          } else if (key === 'active') {
            const booleanValue = value === 'false' ? false : Boolean(value);
            query.andWhere(`flaggedPin.active = :${key}`, { [key]: booleanValue });
          } else {
            query.andWhere(`LOWER(flaggedPin.${key}) LIKE LOWER(:${key})`, {
              [key]: `%${value}%`,
            });
          }
        }
      });
    }

    const limit = Math.max(filter.limit || 10, 1);
    const page = Math.max(filter.page || 1, 1);
    const offset = (page - 1) * limit;

    query.orderBy('flaggedPin.lastModified', 'DESC');

    const total = await query.getCount();
    const flaggedPinList = await query.offset(offset).limit(limit).getRawMany();

    return {
      page,
      limit,
      total,
      result: flaggedPinList,
    };
  }
}
