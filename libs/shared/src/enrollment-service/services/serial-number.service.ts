import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SerialNumber } from '@app/shared/enrollment-service/entities/serial-number.entity';

@Injectable()
export class SerialNumberService {
  constructor(
    @InjectRepository(SerialNumber)
    private serialNumberRepository: Repository<SerialNumber>
  ) {}

  async getNextSerialNumber(type: string, currentYear: number): Promise<string> {
    let serialNumber = await this.serialNumberRepository.findOne({
      where: { numberType: type, currentYear: currentYear },
    });

    if (!serialNumber) {
      serialNumber = this.serialNumberRepository.create({
        numberType: type,
        currentYear,
        lastNumber: 0,
      });
    }

    serialNumber.lastNumber += 1;

    await this.serialNumberRepository.save(serialNumber);

    return String(serialNumber.lastNumber).padStart(3, '0');
  }
}
