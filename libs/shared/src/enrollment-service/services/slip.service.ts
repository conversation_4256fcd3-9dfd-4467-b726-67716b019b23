import { Injectable } from '@nestjs/common';
import { EnrolmentBiodataRepository } from '@app/shared/enrollment-service/repository/enrolment-biodata.repository';
import { PinoLogger } from 'nestjs-pino';
import * as fs from 'fs';
import { SlipTemplates } from '@app/shared/utils/SlipTemplates';
import { format } from 'date-fns';
import * as ejs from 'ejs';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { EnrolmentDetailRepository } from '@app/shared/enrollment-service/repository/enrolment-detail.repository';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { SettingsEnumKey } from '@app/shared/enums';
import { AS_AT_JUNE_30TH_BAND, ISO_HYPHEN_DATE_FORMAT } from '@app/shared/constants';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';

@Injectable()
export class SlipService {
  constructor(
    private readonly enrolmentBiodataRepository: EnrolmentBiodataRepository,
    private readonly enrolmentDetailRepository: EnrolmentDetailRepository,
    private readonly enrolmentSummaryRepository: EnrolmentSummaryRepository,
    private readonly settingMsService: SettingMsLibService,
    private readonly logger: PinoLogger
  ) {}

  async generatePdf(
    htmlContent: string | Buffer,
    landscape: boolean = false,
    options?: Record<string, any>
  ): Promise<Uint8Array<ArrayBufferLike>> {
    // If htmlContent is a Buffer, assume it's already a PDF file
    if (Buffer.isBuffer(htmlContent)) {
      return htmlContent;
    }

    // Otherwise, proceed with the normal PDF generation from HTML
    const fullFeaturedRequest = options ?? {
      html: htmlContent as string,
      footer: 'Confidential',
      showPageNumber: true,
      borders: false,
      format: 'A4',
      orientation: landscape ? 'landscape' : 'portrait',
      margin: {
        top: '5mm',
        bottom: '10mm',
        left: '5mm',
        right: '5mm',
      },
    };

    try {
      const pdfServiceUrl = await this.settingMsService.getSetting(SettingsEnumKey.PDF_SERVICE_URL);
      console.log('pdfServiceUrl', pdfServiceUrl);
      const response = await fetch(pdfServiceUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(fullFeaturedRequest),
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      return Buffer.from(arrayBuffer);
    } catch (error) {
      console.log('Error fetching PDF from Express service:', error);
      this.logger.error('Error fetching PDF from Express service:', error);
      return null;
    }
  }

  async retrieveSlipDetails(rsaPin: string, slipType: string): Promise<Uint8Array<ArrayBufferLike> | null> {
    if (slipType === 'ENROLMENT') {
      return await this.retrieveEnrolmentSlipDetails(rsaPin);
    }

    return await this.retrieveAcknowledgementSlipDetails(rsaPin);
  }

  private async retrieveAcknowledgementSlipDetails(rsaPin: string): Promise<Uint8Array<ArrayBufferLike> | null> {
    const slipDetails = await this.enrolmentSummaryRepository.getEnrollmentSummaryASliPDetailsByRsaPin(rsaPin);
    if (!slipDetails) {
      return null;
    }
    const placeholders: Record<string, any> = {};
    placeholders['bannerImage'] = this.getBannerImage();
    placeholders['rsaPin'] = slipDetails.rsaPin;
    placeholders['fullName'] = `${slipDetails.firstName} ${slipDetails.surname} `;
    placeholders['pfaName'] = slipDetails.pfaName;
    placeholders['regDate'] = slipDetails.createDate;

    const template = SlipTemplates.ACKNOWLEDGEMENT_SLIP;
    const htmlContent = fs.readFileSync(template.messagePath, 'utf8');

    const message = ejs.render(htmlContent, placeholders);
    return await this.generateEmployeeSlip(message);
  }

  async retrieveEnrolmentSlipDetails(rsaPin: string): Promise<Uint8Array<ArrayBufferLike> | null> {
    const slipDetails = await this.enrolmentBiodataRepository
      .findOneWithRelations({ rsaPin: rsaPin }, ['employmentDetails', 'documents'])
      .catch((error) => {
        this.logger.error('Error fetching Enrollment details', error);
        return Promise.resolve(null);
      });

    if (!slipDetails) {
      console.log('No slip details found for the provided RSA Pin:', rsaPin);
      return null;
    }

    const employmentHistoryList = slipDetails.employmentDetails;

    const employmentHistory = employmentHistoryList.map((record) => ({
      year: record.employmentYear,
      salaryStructure: record.salaryStructure,
      employerName: record.employerCode,
      designation: record.designation,
      gradeLevel: record.gradeLevel,
      step: record.step,
    }));

    employmentHistory.sort((a, b) => {
      if (a.year === AS_AT_JUNE_30TH_BAND) return -1;
      if (b.year === AS_AT_JUNE_30TH_BAND) return 1;

      // Otherwise, sort numerically (convert year to number if possible)
      const aYear = parseInt(a.year);
      const bYear = parseInt(b.year);
      return (isNaN(aYear) ? 0 : aYear) - (isNaN(bYear) ? 0 : bYear);
    });

    const placeholders: Record<string, any> = {};

    const passportBase64 = slipDetails.documents?.portrait ? slipDetails.documents.portrait.toString('base64') : '-';

    const retireeSignatureBase64 = slipDetails.documents?.signature
      ? slipDetails.documents.signature.toString('base64')
      : '-';

    const officerDetails = await this.enrolmentSummaryRepository.getEnrollmentOfficerDetails(rsaPin);
    placeholders['officerSignatureBase64'] = officerDetails?.signature ? officerDetails.signature : '-';
    placeholders['enrollmentOfficer'] = officerDetails ? `${officerDetails.firstName} ${officerDetails.surname}` : '-';
    placeholders['location'] = officerDetails?.location || '-';

    placeholders['bannerImage'] = this.getBannerImage();
    placeholders['rsaPin'] = slipDetails.rsaPin;
    placeholders['pfaName'] = slipDetails.pfaName;
    placeholders['fullName'] = `${slipDetails.firstName} ${slipDetails.surname} ${slipDetails.otherName} `;
    placeholders['dofa'] = this.getDateString(slipDetails.dateOfFirstAppointment);
    placeholders['gender'] = slipDetails.gender;
    placeholders['edor'] = this.getDateString(slipDetails.dateOfRetirement);
    placeholders['dob'] = this.getDateString(slipDetails.dateOfBirth);
    placeholders['dob'] = this.getDateString(slipDetails.dateOfBirth);
    placeholders['employmentHistory'] = employmentHistory;
    placeholders['registrationDate'] = this.getDateString(slipDetails.createDate);
    placeholders['passportBase64'] = passportBase64;
    placeholders['retireeSignatureBase64'] = retireeSignatureBase64;

    placeholders['isDeceased'] = slipDetails.retireeUserType === RetireeUserTypeEnum.DECEASED;

    //pdo details
    placeholders['pdoSurname'] = slipDetails.pdoSurname;
    placeholders['pdoUserId'] = slipDetails.pdoUserId || '-';
    placeholders['pdoFirstName'] = slipDetails.pdoFirstName || '-';
    placeholders['pdoEmailAddress'] = slipDetails.pdoEmailAddress || '-';

    //pdo details
    placeholders['nokSurname'] = slipDetails.nokSurname;
    placeholders['nokPhoneNumber'] = slipDetails.nokPhoneNumber || '-';
    placeholders['nokFirstName'] = slipDetails.nokFirstName || '-';
    placeholders['nokEmailAddress'] = slipDetails.nokEmailAddress || '-';

    const template = SlipTemplates.ENROLMENT_SLIP;
    const htmlContent = fs.readFileSync(template.messagePath, 'utf8');

    const message = ejs.render(htmlContent, placeholders);
    return await this.generateEmployeeSlip(message);
  }

  async generateEmployeeSlip(message: string | Buffer) {
    // If message is already a Buffer (PDF file), pass it directly
    if (Buffer.isBuffer(message)) {
      return await this.generatePdf(message, false);
    }

    // Otherwise, use the normal HTML-to-PDF conversion
    const fullFeaturedRequest = {
      html: message as string,
      showPageNumber: true,
      footer: 'Public',
      borders: false,
      format: 'A4',
      orientation: 'portrait',
      margin: {
        top: '15mm',
        bottom: '15mm',
        left: '0mm',
        right: '0mm',
      },
    };
    return await this.generatePdf(message, false, fullFeaturedRequest);
  }

  getDateString(date: Date): string {
    if (!date) {
      return '-';
    }

    return format(date, ISO_HYPHEN_DATE_FORMAT);
  }

  getBannerImage(template: { emailSubject: string; messagePath: string } = SlipTemplates.HEADER_BANNER): string {
    try {
      const imageBuffer = fs.readFileSync(template.messagePath); // Read file as a buffer
      return imageBuffer.toString('base64'); // Convert to Base64
    } catch (error) {
      this.logger.error('Error reading banner image:', error);
      return '';
    }
  }
}
