import { Injectable } from '@nestjs/common';
import * as XLSX from 'xlsx';

// Define interfaces for the data structure
interface Record {
  rsaPin: string;
  mdaName: string;
  mdaCode: string;
  pfaName: string;
  pfaCode: string;
  firstName: string;
  surname: string;
  totalMonths?: string;
  totalPension: string;
  batchName: string;
}

interface PfaSchedule {
  pfa: string;
  pfaCode: string;
  batchName: string;
  showTotalMonths: boolean;
  outstanding: Record[];
  refund: Record[];
  accruedRights: any[];
}

@Injectable()
export class ExcelPfaScheduleService {
  // Generate Excel file buffer
  generateExcel(pfaSchedule: PfaSchedule): Buffer {
    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet([]);

    // Define headers with split NAME into FIRST NAME and SURNAME
    const headers = [
      'S/N',
      'PIN',
      'FIRST NAME',
      'SURNAME',
      'MDA',
      'MDA CODE',
      'PFA',
      'PFA CODE',
      ...(pfaSchedule.showTotalMonths ? ['TOTAL MONTHS'] : []),
      'BATCH NAME',
      'TOTAL PENSION @ INT 7%',
    ];
    // Add Outstanding Contributions section
    if (pfaSchedule.outstanding && pfaSchedule.outstanding.length > 0) {
      const batchName = pfaSchedule.outstanding[0].batchName;
      XLSX.utils.sheet_add_aoa(ws, [[`OUTSTANDING CONTRIBUTIONS FOR (${batchName})`]], { origin: 'A1' });

      // Merge and center the header across columns A to J
      ws['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: 10 } }];
      const headerCell = ws[XLSX.utils.encode_cell({ r: 0, c: 0 })];
      if (headerCell) {
        headerCell.s = { alignment: { horizontal: 'center', vertical: 'middle' } };
      }
      XLSX.utils.sheet_add_aoa(ws, [headers], { origin: 'A2' });

      // Add data rows
      let contributionSum = 0;
      const data = pfaSchedule.outstanding.map((record, index) => {
        const total = parseFloat(record.totalPension || '0');
        contributionSum += total;
        return [
          index + 1,
          record.rsaPin,
          record.firstName,
          record.surname,
          record.mdaName,
          record.mdaCode,
          record.pfaName,
          record.pfaCode,
          ...(pfaSchedule.showTotalMonths ? [record.totalMonths] : []),
          record.batchName,
          total,
        ];
      });
      XLSX.utils.sheet_add_aoa(ws, data, { origin: 'A3' });

      // Add grand total with merged cells and right alignment
      const totalRow = Array(headers.length - 1).fill('');
      totalRow[0] = 'GRAND TOTAL';
      totalRow[headers.length - 1] = contributionSum;
      XLSX.utils.sheet_add_aoa(ws, [totalRow], { origin: `A${3 + data.length}` });
      ws['!merges'] = [...(ws['!merges'] || []), { s: { r: 2 + data.length, c: 0 }, e: { r: 2 + data.length, c: 8 } }];
      const totalCell = ws[XLSX.utils.encode_cell({ r: 2 + data.length, c: 0 })];
      if (totalCell) {
        totalCell.s = { alignment: { horizontal: 'right', vertical: 'middle' } };
      }
    }

    // Add Refund section (empty in this case)
    if (pfaSchedule.refund && pfaSchedule.refund.length > 0) {
      const batchName = pfaSchedule.refund[0].batchName;
      XLSX.utils.sheet_add_aoa(ws, [[`OUTSTANDING CONTRIBUTIONS (REFUNDS) FOR (${batchName})`]], {
        origin: `A${6 + pfaSchedule.outstanding.length + 3}`,
      });

      // Define headers
      const headers = [
        'S/N',
        'PIN',
        'FIRST NAME',
        'SURNAME',
        'MDA',
        'MDA CODE',
        'PFA',
        'PFA CODE',
        ...(pfaSchedule.showTotalMonths ? ['TOTAL MONTHS'] : []),
        'BATCH NAME',
        'TOTAL REFUND @ INT 7%',
      ];
      XLSX.utils.sheet_add_aoa(ws, [headers], { origin: `A${6 + pfaSchedule.outstanding.length + 5}` });

      // Add data rows
      let refundSum = 0;
      const data = pfaSchedule.refund.map((record, index) => {
        const refundVal = parseFloat(record.totalPension || '0');
        refundSum += refundVal;
        return [
          index + 1,
          record.rsaPin,
          record.firstName,
          record.surname,
          record.mdaName,
          record.mdaCode,
          record.pfaName,
          record.pfaCode,
          ...(pfaSchedule.showTotalMonths ? [record.totalMonths] : []),
          record.batchName,
          -Math.abs(refundVal),
        ];
      });
      XLSX.utils.sheet_add_aoa(ws, data, { origin: `A${6 + pfaSchedule.outstanding.length + 6}` });

      // Add grand total
      const totalRow = Array(headers.length - 1).fill('');
      totalRow[0] = 'GRAND TOTAL';
      totalRow[headers.length - 1] = -Math.abs(refundSum);
      XLSX.utils.sheet_add_aoa(ws, [totalRow], { origin: `A${6 + pfaSchedule.outstanding.length + 6 + data.length}` });
    }

    // Format cells
    const range = XLSX.utils.decode_range(ws['!ref']);
    for (let C = range.s.c; C <= range.e.c; C++) {
      for (let R = range.s.r; R <= range.e.r; R++) {
        const cell = ws[XLSX.utils.encode_cell({ c: C, r: R })];
        if (!cell) continue;
        cell.s = { alignment: { horizontal: 'center' } };
        if (R === 1 || (R >= 5 + pfaSchedule.outstanding.length + 4 && R <= 5 + pfaSchedule.outstanding.length + 4)) {
          cell.s = { font: { bold: true }, alignment: { horizontal: 'center' } };
        }
        if (C === headers.length - 1 && R >= 3 && R < 3 + pfaSchedule.outstanding.length) {
          cell.s = { alignment: { horizontal: 'right' }, numFmt: '#,##0.00' };
        }
        if (R === 2 + pfaSchedule.outstanding.length && C === 0) {
          cell.s = { alignment: { horizontal: 'right', vertical: 'middle' } };
        }
      }
    }

    // Set column widths
    ws['!cols'] = headers.map(() => ({ wch: 15 }));
    ws['!cols'][2] = { wch: 15 }; // FIRST NAME
    ws['!cols'][3] = { wch: 15 }; // SURNAME
    ws['!cols'][4] = { wch: 40 }; // MDA
    ws['!cols'][6] = { wch: 30 }; // PFA

    // Append worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Certification Schedule');

    // Generate buffer
    return XLSX.write(wb, { bookType: 'xlsx', type: 'buffer' });
  }
}
