import { Injectable } from '@nestjs/common';
import {
  RetrieveCompleteEmployerDetailsDto,
  SaveEnrolmentDraftDto,
} from '@app/shared/dto/enrollment/enrolment-draft.dto';
import { plainToInstance } from 'class-transformer';
import { Step1Dto } from '@app/shared/enrollment-service/validator/dto/step-1.dto';
import { validate, ValidationError } from 'class-validator';
import { ResponseCodeEnum, SettingsEnumKey } from '@app/shared/enums';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { Step2Dto } from '@app/shared/enrollment-service/validator/dto/step-2.dto';
import { PinoLogger } from 'nestjs-pino';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { RegistrationStatusEnum } from '@app/shared/enums/registration-status.enum';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { MdaRepository } from '@app/shared/user-service/repositories/mda.repository';
import { validateDofa, validateDts, validateEdor } from '@app/shared/utils/input-field-validator-utils';
import { EnrolmentSummary } from '@app/shared/enrollment-service/entities/enrolment-summary.entity';
import { Step3Dto } from '@app/shared/enrollment-service/validator/dto/step-3.dto';
import { Step4Dto } from '@app/shared/enrollment-service/validator/dto/step-4.dto';
import { Step5Dto } from '@app/shared/enrollment-service/validator/dto/step-5.dto';
import { Step6Dto } from '@app/shared/enrollment-service/validator/dto/step-6.dto';
import { HYPHEN_DATE_FORMAT, THIRTIETH_JUNE_2004_DATE } from '@app/shared/constants';
import { OtherEmploymentDetailsDto } from '@app/shared/enrollment-service/validator/dto/other-employment-details.dto';
import { TbeOrganisationsRepository } from '@app/shared/enrollment-service/repository/tbe-organisations.repository';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { EnrolmentBiodataRepository } from '@app/shared/enrollment-service/repository/enrolment-biodata.repository';
import { AS_AT_JUNE_30TH_BAND } from '@app/shared/constants/enrolment-services-contants';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { convertHypenToIsoDate } from '@app/shared/workflow/utils/workflow-utils';

@Injectable()
export class FormValidatorService {
  constructor(
    private readonly cobraUserRepository: CobraUserRepository,
    private readonly enrolmentBiodataRepository: EnrolmentBiodataRepository,
    private readonly enrolmentSummaryRepository: EnrolmentSummaryRepository,
    private readonly tbeOrganisationsRepository: TbeOrganisationsRepository,
    private readonly pfaRepository: PfaRepository,
    private readonly mdaRepository: MdaRepository,
    private readonly settingMsLibService: SettingMsLibService,
    private readonly logger: PinoLogger
  ) {}

  async validateStep(data: SaveEnrolmentDraftDto): Promise<BaseResponseWithContentNoPagination<any>> {
    const { stepNumber, formData } = data;

    // console.log(`stepNumber ${stepNumber}, formData ${JSON.stringify(formData)}`);
    switch (stepNumber) {
      case 1: {
        return await this.validateBiodata(formData);
      }
      case 2: {
        return await this.validateEmploymentDetails(formData);
      }
      case 3: {
        return await this.validateOtherEmploymentDetails(formData);
      }
      case 4: {
        return await this.validateDocumentUpload(formData);
      }
      case 5: {
        return await this.validatePortrait(formData);
      }
      case 6: {
        return await this.validateSignature(formData);
      }
      case 7: {
        return await this.finalReviewAndSubmit(formData);
      }
      default: {
        const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
        response.setDescription(`Validation for step ${stepNumber} is not implemented.`);
        return response;
      }
    }
  }

  private async validateFormData<T extends object>(
    formData: Record<string, any>,
    dtoClass: new () => T
  ): Promise<{ dto: T | null; errors: Record<string, string[]> | null }> {
    // const dto = plainToInstance(dtoClass, formData);
    const dto = plainToInstance(dtoClass, formData, {
      enableImplicitConversion: true, // ✅ Ensures automatic type conversion
      exposeDefaultValues: true, // ✅ Ensures default values in DTOs are respected
    });
    // console.log('Transformed DTO:', JSON.stringify(dto)); // Debugging output

    if (!dto) {
      return { dto: null, errors: { general: ['Invalid form data input provided'] } };
    }

    const validationErrors = await validate(dto);
    if (validationErrors.length > 0) {
      const errors = this.flattenValidationErrors(validationErrors);
      return { dto: null, errors };
    }

    return { dto, errors: null };
  }

  private flattenValidationErrors(errors: ValidationError[]): Record<string, string[]> {
    const formattedErrors: Record<string, string[]> = {};

    const extractErrors = (error: ValidationError, parentKey = '') => {
      const key = parentKey ? `${parentKey}.${error.property}` : error.property;

      if (error.constraints) {
        formattedErrors[key] = Object.values(error.constraints);
      }

      if (error.children?.length) {
        for (const child of error.children) {
          extractErrors(child, key);
        }
      }
    };

    for (const error of errors) {
      extractErrors(error);
    }

    return formattedErrors;
  }

  async validateBiodata(formData: Record<string, any>): Promise<BaseResponseWithContentNoPagination<any>> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);

    const { dto: bioDataDto, errors } = await this.validateFormData(formData, Step1Dto);
    if (errors) {
      response.content = errors;
      return response;
    }

    const [cobraUser, pinStatus, mdaStatus] = await Promise.all([
      this.cobraUserRepository.findOne({ rsaPin: bioDataDto.rsaPin }),
      this.enrolmentSummaryRepository.findOne({ rsaPin: bioDataDto.rsaPin }),
      this.mdaRepository.findOne({ employerId: bioDataDto.employerCode }),
    ]);

    if (!cobraUser) {
      response.setDescription(
        `Email address: ${bioDataDto.email} has not been registered, kindly go ahead and register before proceeding.`
      );
      return response;
    }

    if ((cobraUser as CobraUser).emailAddress !== bioDataDto.email) {
      response.setDescription(`Email address: ${bioDataDto.email} provided does not belong to the RSA pin provided`);
      return response;
    }

    if (!pinStatus) {
      response.setDescription(`RSA Pin: ${bioDataDto.rsaPin} has not been registered`);
      return response;
    }

    const enrolmentStatus = (pinStatus as EnrolmentSummary)?.status;
    if (
      !enrolmentStatus ||
      (enrolmentStatus !== RegistrationStatusEnum.REGISTERED &&
        enrolmentStatus !== RegistrationStatusEnum.PENDING_PFA_REVIEW &&
        enrolmentStatus !== RegistrationStatusEnum.ENROLLED)
    ) {
      response.setDescription(`Pin status ${enrolmentStatus} is not allowed to register`);
      return response;
    }

    if ((pinStatus as EnrolmentSummary)?.retireeUserType !== bioDataDto.retireeUserType) {
      response.setDescription(
        `Retiree User type: ${bioDataDto.retireeUserType} does not match the one uploaded by MDA`
      );
      return response;
    }

    if (!mdaStatus) {
      response.setDescription(`Invalid Employer code ${bioDataDto.employerCode} provided`);
      return response;
    }

    console.log(`successfully validated step1`);
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Validated successfully');
    return response;
  }

  async validateEmploymentDetails(formData: Record<string, any>) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const { dto: bioDataDto, errors } = await this.validateFormData(formData, Step2Dto);
    if (errors) {
      response.content = errors;
      return response;
    }

    const dtsValidation = validateDts(bioDataDto.dts, HYPHEN_DATE_FORMAT);
    if (dtsValidation) {
      response.setDescription(dtsValidation);
      return response;
    }

    const dofaValidation = validateDofa(bioDataDto.dofa, HYPHEN_DATE_FORMAT);
    if (dofaValidation) {
      response.setDescription(dofaValidation);
      return response;
    }

    const edorValidation = validateEdor(
      bioDataDto.edor,
      bioDataDto.dateOfBirth,
      bioDataDto.dofa,
      bioDataDto.dts,
      HYPHEN_DATE_FORMAT
    );

    if (edorValidation) {
      response.setDescription(edorValidation);
      return response;
    }

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Validated successfully');
    return response;
  }

  async validateOtherEmploymentDetails(formData: Record<string, any>) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const { dto: bioDataDto, errors } = await this.validateFormData(formData, Step3Dto);
    if (errors) {
      response.content = errors;
      return response;
    }

    const edor = bioDataDto.edor;
    const dofa = bioDataDto.dofa;
    const { years, employerCodes, transferOfServiceDocs, errorDetails, bands } = await this.getUniqueEmploymentDetails(
      bioDataDto.otherEmploymentDetails,
      edor,
      dofa
    );

    console.log(`errorDetails: ${JSON.stringify(errorDetails)}`);
    if (errorDetails && errorDetails.size > 0) {
      response.description = Array.from(errorDetails).join(', ');
      return response;
    }

    if (
      bioDataDto.numberOfEmployment.toLowerCase() === 'single employment' &&
      (!bioDataDto.otherEmploymentDetails || employerCodes.size !== 1)
    ) {
      response.setDescription(`Single Employment type requires just 1 other employment details`);
      return response;
    }

    if (bioDataDto.numberOfEmployment.toLowerCase() === 'multiple employments') {
      if (!bioDataDto.otherEmploymentDetails || employerCodes.size <= 1) {
        response.setDescription(`Multiple Employments type requires more than 1 other employment details`);
        return response;
      }

      if (transferOfServiceDocs.size === 0) {
        response.setDescription(`Multiple Employments type requires transfer of service document`);
        return response;
      }
    }

    if (years.size !== bands.length) {
      const missingBands = bands.filter((band) => !years.has(band)); // Find missing bands
      const extraYears = [...years].filter((year) => !bands.includes(year)); // Find extra years

      if (missingBands.length > 0) {
        response.setDescription(
          `Bands are incomplete as ${missingBands.length} band(s) is/are missing: ${missingBands.join(', ')}`
        );
        return response;
      }

      if (extraYears.length > 0) {
        response.setDescription(
          `Invalid year(s) provided: ${extraYears.join(', ')}. These years are not supposed to be included.`
        );
        return response;
      }
    }

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Validated successfully');
    return response;
  }

  async validateDocumentUpload(formData: Record<string, any>) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const { dto: bioDataDto, errors } = await this.validateFormData(formData, Step4Dto);
    if (errors) {
      response.content = errors;
      return response;
    }

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Validated successfully');
    return response;
  }

  async validatePortrait(formData: Record<string, any>) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const { dto: bioDataDto, errors } = await this.validateFormData(formData, Step5Dto);
    if (errors) {
      response.content = errors;
      return response;
    }

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Validated successfully');
    return response;
  }

  async validateSignature(formData: Record<string, any>) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const { dto: bioDataDto, errors } = await this.validateFormData(formData, Step6Dto);
    if (errors) {
      response.content = errors;
      return response;
    }
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Validated successfully');
    return response;
  }

  async finalReviewAndSubmit(formData: Record<string, any>) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const { dto: bioDataDto, errors } = await this.validateFormData(formData, Step6Dto);
    console.log(`bioDataDto: ${bioDataDto} Error ${errors}`);
    if (errors) {
      response.content = errors;
      return response;
    }

    // send email to supervisor
    // update status to pending review
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Validated successfully');
    console.log('Process Event Response:', response);
    return response;
  }

  private async getUniqueEmploymentDetails(
    details: OtherEmploymentDetailsDto[],
    edor: string,
    dofa: string
  ): Promise<{
    years: Set<string>;
    employerCodes: Set<string>;
    transferOfServiceDocs: Set<string>;
    errorDetails: Set<string>;
    bands: string[];
  }> {
    const years = new Set<string>();
    const employerCodes = new Set<string>();
    const leaveRanges: { start: string; end: string }[] = [];
    const transferOfServiceDocs = new Set<string>();
    const errorDetails = new Set<string>();

    let maxIppisDate: string | null = null;
    let maxYear: string | null = null;
    const validBands = await this.settingMsLibService.getSettingJSONList(SettingsEnumKey.ENROLMENT_YEAR_BANDS);
    for (const detail of details) {
      const { year, ippisDate, employerCode, leaveStartDate, leaveEndDate, transferOfService } = detail;
      if (!validBands || validBands.length === 0) {
        errorDetails.add('Failed to retrieve valid enrolment year bands, please try again later.');
        return { years, employerCodes, transferOfServiceDocs, errorDetails, bands: [] };
      }

      await this.validateEmploymentOtherDetails(errorDetails, detail);
      if (validBands.includes(year)) {
        years.add(year);
        // Determine the highest year for maxIppisDate
        if (!maxYear || validBands.indexOf(year) > validBands.indexOf(maxYear)) {
          maxYear = year;
          maxIppisDate = ippisDate || `${new Date()}`; // if no IPPIS date, use current date
        }
      } else {
        errorDetails.add(`Invalid year: ${year}`);
      }

      employerCodes.add(employerCode);
      transferOfServiceDocs.add(transferOfService);

      if (leaveStartDate && leaveEndDate) {
        leaveRanges.push({ start: leaveStartDate, end: leaveEndDate });
      }
    }

    // Determine bands based on max IPPIS date
    const bands = this.generateBands(maxIppisDate, edor, leaveRanges, dofa);

    return { years, employerCodes, transferOfServiceDocs, errorDetails, bands };
  }

  generateBands(
    ippisDate: string | null,
    edor: string,
    leavePeriods: { start: string; end: string }[],
    dofa: string
  ): string[] {
    const currentYear = new Date().getFullYear();

    const ippisYear = ippisDate ? convertHypenToIsoDate(ippisDate, HYPHEN_DATE_FORMAT).getFullYear() : currentYear;

    const edorDate = convertHypenToIsoDate(edor, HYPHEN_DATE_FORMAT);
    const edorYear = edorDate.getFullYear();

    const limitYear = Math.min(ippisYear, edorYear);

    const dofaDate = convertHypenToIsoDate(dofa, HYPHEN_DATE_FORMAT);
    const dofaYear = dofaDate.getFullYear();

    // Determine if dofa is on or before June 30, 2004
    const dofaIsBeforeOrOnJune2004 = dofaDate <= THIRTIETH_JUNE_2004_DATE;

    // Find the band for dofaYear (bands start at 2004, increment by 3)
    let startBandYear = 2004;
    while (startBandYear + 3 <= dofaYear) {
      startBandYear += 3;
    }

    // Find the band for edorYear
    let maxBandYear = startBandYear;
    while (maxBandYear + 3 <= limitYear) {
      maxBandYear += 3;
    }

    // Generate bands from startBandYear to maxBandYear as strings
    const bands: string[] = [];
    if (dofaIsBeforeOrOnJune2004) {
      bands.push(AS_AT_JUNE_30TH_BAND);
    }

    let year = startBandYear;
    while (year <= maxBandYear) {
      bands.push(year.toString());
      year += 3;
    }

    // Convert leave periods to Date ranges
    const leaveRanges = leavePeriods.map(({ start, end }) => ({
      start: new Date(start.split('-').reverse().join('-')),
      end: new Date(end.split('-').reverse().join('-')),
    }));

    // Filter out bands fully covered by leave
    return bands.filter((band) => {
      if (band === AS_AT_JUNE_30TH_BAND) {
        return true;
      }
      const bandYear = parseInt(band, 10);
      const bandStart = new Date(bandYear, 0, 1); // Jan 1st of band year
      const bandEnd = new Date(bandYear + 3, 0, 1); // Jan 1st of next band

      // Check if the band is fully covered by any leave period
      return !leaveRanges.some(({ start, end }) => start <= bandStart && end >= bandEnd);
    });
  }

  /*generateBands(ippisDate: string | null, edor: string, leavePeriods: { start: string; end: string }[], dofa: string): string[] {
    const currentYear = new Date().getFullYear();

    const ippisYear = ippisDate ? new Date(ippisDate.split('-').reverse().join('-')).getFullYear() : currentYear;

    const edorDate = new Date(edor.split('-').reverse().join('-'));
    const edorYear = edorDate.getFullYear();

    // Determine the earlier year between ippisYear and edorYear
    const limitYear = Math.min(ippisYear, edorYear);

    let maxBandYear = 2004;
    while (maxBandYear + 3 <= limitYear) {
      maxBandYear += 3;
    }

    const bands: number[] = [];
    let year = 2004;

    while (year <= maxBandYear) {
      bands.push(year);
      year += 3;
    }

    // Convert leave periods to Date ranges
    const leaveRanges = leavePeriods.map(({ start, end }) => ({
      start: new Date(start.split('-').reverse().join('-')),
      end: new Date(end.split('-').reverse().join('-')),
    }));

    // Filter out bands fully covered by leave
    return bands.filter((bandYear) => {
      const bandStart = new Date(bandYear, 0, 1); // Jan 1st of band year
      const bandEnd = new Date(bandYear + 3, 0, 1); // Jan 1st of next band

      // Check if the band is fully covered by any leave period
      return !leaveRanges.some(({ start, end }) => start <= bandStart && end >= bandEnd);
    });
  }*/

  // validated employerId against ippis date, sector, year
  // verify salary structure gl step
  // corroborate the leave of absence in calculation the number of required bands
  // evidence of transfer document when multiple reg
  //ippis date
  // determine how many bands based on ippis date
  private async validateEmploymentOtherDetails(
    errors: Set<string>,
    otherEmploymentDetailsDto: OtherEmploymentDetailsDto
  ): Promise<void> {
    const retrieveEmployerDetailsDto: RetrieveCompleteEmployerDetailsDto = new RetrieveCompleteEmployerDetailsDto();
    retrieveEmployerDetailsDto.employerCode = otherEmploymentDetailsDto.employerCode;
    retrieveEmployerDetailsDto.organizationSector = otherEmploymentDetailsDto.organizationSector;
    retrieveEmployerDetailsDto.year =
      otherEmploymentDetailsDto.year === AS_AT_JUNE_30TH_BAND ? '2004' : `${otherEmploymentDetailsDto.year}`; // 2004 and AS_AT_JUNE_30TH_BAND are the same band
    retrieveEmployerDetailsDto.salaryStructure = otherEmploymentDetailsDto.salaryStructure;
    retrieveEmployerDetailsDto.gradelevel = `${otherEmploymentDetailsDto.gradeLevel}`;
    retrieveEmployerDetailsDto.step = `${otherEmploymentDetailsDto.step}`;
    retrieveEmployerDetailsDto.ippisDate = `${otherEmploymentDetailsDto.ippisDate}`;

    const bandMatch =
      await this.tbeOrganisationsRepository.getEmployerCompleteSalaryDetails(retrieveEmployerDetailsDto);

    // todo ensure ippisdate not greater than otherEmploymentDetailsDto.year
    if (!bandMatch || bandMatch.length === 0) {
      errors.add(
        `Employer details not found for Year: ${otherEmploymentDetailsDto.year}, Sector: ${otherEmploymentDetailsDto.organizationSector}, Employer code: ${otherEmploymentDetailsDto.employerCode}, Salary Structure: ${otherEmploymentDetailsDto.salaryStructure}, Grade Level: ${otherEmploymentDetailsDto.gradeLevel}, Step: ${otherEmploymentDetailsDto.step} \n`
      );
    }

    if (otherEmploymentDetailsDto.promotionYear && otherEmploymentDetailsDto.year !== AS_AT_JUNE_30TH_BAND) {
      const promotion = convertHypenToIsoDate(
        otherEmploymentDetailsDto.promotionYear,
        HYPHEN_DATE_FORMAT
      ).getFullYear();
      const difference = promotion - parseInt(otherEmploymentDetailsDto.year);
      if (difference > 2 || difference < 0) {
        // ensuring that it is within the band
        errors.add(
          `Promotion Date: ${otherEmploymentDetailsDto.promotionYear} should be within the current band: ${otherEmploymentDetailsDto.year}`
        );
      }
    }

    if (otherEmploymentDetailsDto.leaveStartDate && otherEmploymentDetailsDto.leaveEndDate) {
      const leaveStartDate = new Date(otherEmploymentDetailsDto.leaveStartDate.split('-').reverse().join('-'));
      const leaveEndDate = new Date(otherEmploymentDetailsDto.leaveEndDate.split('-').reverse().join('-'));

      const bandRange = parseInt(otherEmploymentDetailsDto.year) + 2;
      if (leaveStartDate.getFullYear() > bandRange) {
        errors.add(
          `Leave Start Year: ${leaveStartDate.getFullYear()} should be within the current band: ${otherEmploymentDetailsDto.year}`
        );
      }
      if (leaveEndDate.getFullYear() > bandRange) {
        errors.add(
          `Leave End Year: ${leaveEndDate.getFullYear()} should be within the current band: ${otherEmploymentDetailsDto.year}`
        );
      }
    }
  }
}
