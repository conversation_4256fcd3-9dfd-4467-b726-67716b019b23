import { BadRequestException, Injectable } from '@nestjs/common';
import { format } from 'date-fns';
import { toWords } from 'number-to-words';
import fs from 'fs';
import * as ejs from 'ejs';
import { SlipService } from './slip.service';
import {
  BatchDocumentDto,
  BatchMemoGenerationDto,
  StampScheduleDto,
} from '@app/shared/dto/enrollment/exit-batching/batch-filter.dto';
import { BatchRepository } from '@app/shared/enrollment-service/repository/batch.repository';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum, SettingsEnumKey } from '@app/shared/enums';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { BatchRecordRepository } from '@app/shared/enrollment-service/repository/batch-record.repository';
import { BatchDocumentRepository } from '@app/shared/enrollment-service/repository/batch-document.repository';
import { BatchDocuments } from '@app/shared/enrollment-service/entities/batch-documents.entity';
import { SerialNumberService } from '@app/shared/enrollment-service/services/serial-number.service';
import { BatchRecord } from '@app/shared/enrollment-service/entities/batch-record';
import { AccruedRightsResultRepository } from '@app/shared/dto/enrollment/repositories/accrued-rights-result.repository';
import { EnrolmentDetailRepository } from '@app/shared/enrollment-service/repository/enrolment-detail.repository';
import { PDFDocument, rgb } from 'pdf-lib';
import { SlipTemplates } from '@app/shared/utils/SlipTemplates';
import { QueryRunner } from 'typeorm';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { TbcBatchAccountMemoDetailsRepository } from '@app/shared/enrollment-service/repository/tbc-batch-account-memo-details.repository';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { PfcRepository } from '@app/shared/user-service/repositories/pfc.repository';
import { PinoLogger } from 'nestjs-pino';
import { TbcBatchAccountMemoDetails } from '@app/shared/enrollment-service/entities/tbc-batch-account-memo-details.entity';
import { TblBatch } from '@app/shared/enrollment-service/entities/tbl-batch';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { NominalRollBatchRepository } from '@app/shared/enrollment-service/repository/nominal-roll-batch.repository';
import { TbcNominalRollBatch } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch';
import { TbcBatchNrAccountMemoDetails } from '@app/shared/enrollment-service/entities/tbc-batch-nr-account-memo-details.entity';
import { TbcNominalRollBatchRecordRepository } from '@app/shared/enrollment-service/repository/tbc-nominal-roll-batch-record.repository';
import { TbcBatchNrAccountMemoDetailsRepository } from '@app/shared/enrollment-service/repository/tbc-batch-nr-account-memo-details.repository';
import { NrBatchDocumentRepository } from '@app/shared/enrollment-service/repository/nr-batch-document.repository';
import { NominalRollBatchDocuments } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch-documents.entity';
import { TbcNominalRollBatchRecord } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch-record';
import { NominalRollBandRepository } from '@app/shared/enrollment-service/repository/nominal-roll-band.repository';
import BigNumber from 'bignumber.js';
import { WorkflowHelperService } from '@app/shared/workflow/services/workflow-helper.service';
import { TbcNominalRollPfaMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-nominal-roll-pfa-memo-document-repository.service';
import { Pfa } from '@app/shared/user-service/entities/pfa.entity';
import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';
import { Pfc } from '@app/shared/user-service/entities/pfc.entity';
import { TbcNominalRollPfaMemoDocument } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-pfa-memo-document';
import { TbcNominalRollPfcMemoDocument } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-pfc-memo-document';
import { TbcNominalRollPfcMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-nominal-roll-pfc-memo-document-repository.service';
import { TbcEnrollmentPfaMemoDocument } from '@app/shared/enrollment-service/entities/tbc-enrollment-pfa-memo-document';
import { TbcEnrollmentPfaMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-enrollment-pfa-memo-document-repository.service';
import { TbcEnrollmentPfcMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-enrollment-pfc-memo-document-repository.service';
import { TbcEnrollmentPfcMemoDocument } from '@app/shared/enrollment-service/entities/tbc-enrollment-pfc-memo-document';
import { AS_AT_JUNE_30TH_BAND, ISO_HYPHEN_DATE_FORMAT } from '@app/shared/constants';
import { ExcelPfaScheduleService } from '@app/shared/enrollment-service/services/excel-pfa-schedule.service';

interface GroupKey {
  pfaCode: string;
  pfaName: string;
  pfaAccountName: string;
  pfcName: string;
  pfcCode: string;
  accountNumber: string;
}

@Injectable()
export class MemoService {
  constructor(
    readonly logger: PinoLogger,
    readonly slipService: SlipService,
    readonly serialNumberService: SerialNumberService,
    private readonly settingsService: SettingMsLibService,
    private readonly batchRepository: BatchRepository,
    private readonly nominalRollBatchRepository: NominalRollBatchRepository,
    private readonly batchRecordRepository: BatchRecordRepository,
    private readonly tbcNominalRollBatchRecordRepository: TbcNominalRollBatchRecordRepository,
    private readonly cobraUserRepository: CobraUserRepository,
    private readonly batchDocumentRepository: BatchDocumentRepository,
    private readonly nrBatchDocumentRepository: NrBatchDocumentRepository,
    private readonly pfaRepository: PfaRepository,
    private readonly pfcRepository: PfcRepository,
    private readonly accruedRightsResultRepository: AccruedRightsResultRepository,
    private readonly enrolmentDetailRepository: EnrolmentDetailRepository,
    private readonly tbcBatchAccountMemoDetailsRepository: TbcBatchAccountMemoDetailsRepository,
    private readonly nominalRollBandRepository: NominalRollBandRepository,
    private readonly tbcNominalRollPfaMemoDetailRepository: TbcNominalRollPfaMemoDocumentRepository,
    private readonly tbcNominalRollPfcMemoDocumentRepository: TbcNominalRollPfcMemoDocumentRepository,
    private readonly tbcEnrollmentPfaMemoDocumentRepository: TbcEnrollmentPfaMemoDocumentRepository,
    private readonly tbcEnrollmentPfcMemoDocumentRepository: TbcEnrollmentPfcMemoDocumentRepository,
    private readonly workflowHelperService: WorkflowHelperService,
    private readonly excelPfaScheduleService: ExcelPfaScheduleService,
    private readonly tbcBatchNrAccountMemoDetailsRepository: TbcBatchNrAccountMemoDetailsRepository
  ) {}

  private async validateMemoRequest(
    batchName: string,
    email: string
  ): Promise<{ batchDetails: TblBatch; cobraUser: CobraUser }> {
    const batchDetails = await this.batchRepository.findOne({ batchName });
    if (!batchDetails) throw new Error('Provided Batch name does not exist');

    const cobraUser = await this.cobraUserRepository.findOne({ emailAddress: email });
    if (!cobraUser) throw new Error('Unable to retrieve user details, please try again');

    if (cobraUser.userType !== UserTypeEnum.PENCOM) {
      throw new Error('You are not authorized to generate this document, please contact support');
    }

    if (!cobraUser.signature) {
      throw new Error('Requesting user does not have a signature, please go to profile and update signature');
    }

    return { batchDetails, cobraUser };
  }

  private async validateCrMemoRequest(
    batchName: string,
    email: string
  ): Promise<{ cobraUser: CobraUser; batchDetails: TbcNominalRollBatch }> {
    const batchDetails = await this.nominalRollBatchRepository.findOne({ batchName });
    console.log('batchName, email', batchName, email);
    if (!batchDetails) {
      throw new Error('Provided Batch name does not exist');
    }

    const cobraUser = await this.cobraUserRepository.findOne({ emailAddress: email });
    if (!cobraUser) throw new Error('Unable to retrieve user details, please try again');

    if (cobraUser.userType !== UserTypeEnum.PENCOM) {
      throw new Error('You are not authorized to generate this document, please contact support');
    }

    if (!cobraUser.signature) {
      throw new Error('Requesting user does not have a signature, please go to profile and update signature');
    }

    return { batchDetails, cobraUser };
  }

  private formatCurrency(value: number): string {
    return value
      .toLocaleString('en-NG', {
        style: 'currency',
        currency: 'NGN',
        minimumFractionDigits: 2,
      })
      .replace('NGN', '₦');
  }

  async generateAccountMemoPdf(
    batchData: BatchDocumentDto,
    req: ICustomRequest
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);

    const { batchDetails, cobraUser } = await this.validateMemoRequest(batchData.batchName, req.user.email);

    let accountMemoDetails: TbcBatchAccountMemoDetails[] = [];
    const regenerateAccountDetails = await this.settingsService.getSettingBoolean(
      SettingsEnumKey.ALWAYS_REGENERATE_ACCOUNT_MEMO
    );

    if (regenerateAccountDetails) {
      accountMemoDetails = await this.generateAccountMemoDetails(batchDetails);
    }

    if (accountMemoDetails.length === 0) {
      accountMemoDetails = await this.tbcBatchAccountMemoDetailsRepository.find({
        where: { batch: { pk: batchDetails.pk } },
        order: { amount: 'ASC' },
      });

      if (!accountMemoDetails || accountMemoDetails.length === 0) {
        accountMemoDetails = await this.generateAccountMemoDetails(batchDetails);
      }
    }

    if (!accountMemoDetails || accountMemoDetails.length === 0) {
      response.setDescription('Unable to retrieve account memo details, please try again');
      return response;
    }

    // accrued rights/ contribution
    const slipTemplate =
      batchDetails.batchType === 'Contributions' ? SlipTemplates.ACCOUNT_MEMO_CONTRIBUTION : SlipTemplates.ACCOUNT_MEMO;
    const buffer = await this.generateAccountMemoPdfDocument(cobraUser, accountMemoDetails, batchDetails, slipTemplate);

    if (!buffer) {
      response.setDescription('Unable to generate memo, please try again');
      return response;
    }

    let batchDocument = await this.batchDocumentRepository.findOne({
      batch: { batchName: batchData.batchName },
    });

    if (!batchDocument) {
      batchDocument = new BatchDocuments({});
    }
    batchDocument.accountMemo = Buffer.from(buffer);
    await this.batchDocumentRepository.saveEntity(batchDocument);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.content = { accountMemo: buffer };
    response.description = 'Memo generated successfully';
    return response;
  }

  async generateExcoMemoPdf(
    batchData: BatchMemoGenerationDto,
    req: ICustomRequest
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const { batchDetails, cobraUser } = await this.validateMemoRequest(batchData.batchName, req.user.email);

    if (batchDetails.batchType.toLowerCase() === 'contributions') {
      return await this.generateContributionExcoMemoPdf(
        batchData,
        req,
        'EXIT_RECORD',
        batchDetails.rsaHolder as RetireeUserTypeEnum,
        batchDetails,
        cobraUser
      );
    }

    const auditHod: CobraUser = await this.getAuditHod();
    if (!auditHod || !auditHod.signature) {
      throw new Error('Audit Hod profile/signature needs to be setup to proceed with this process');
    }

    const template = SlipTemplates.EXCO_MEMO;
    const htmlContent = fs.readFileSync(template.messagePath, 'utf8');

    const currentDate = format(new Date(), 'd MMMM, yyyy');
    const currentYear = new Date().getFullYear();

    const authorizerName = `${cobraUser.firstName} ${cobraUser.surname}`;
    const authorizerSignature = cobraUser.signature;
    const batchRecords = await this.batchRecordRepository.find({
      where: { batch: { pk: batchDetails.pk } },
      relations: ['enrolment'],
    });
    const pfaBreakdown = await this.getPfaBreakDown(batchRecords);

    const memoType = 'EXCO';

    const referenceNo = `TECH/COBRA/${memoType}/${currentYear}/${await this.serialNumberService.getNextSerialNumber(memoType, currentYear)}`;
    const lastWorkingDay = format(this.getLastWorkingDay(), 'd MMMM, yyyy');
    const templateData = {
      requestDate: currentDate,
      referenceNo,
      lastWorkingDay,
      rbbrfBalance: this.formatCurrency(batchData.rbbrfBalance),
      rbbrfBalanceWord: this.formatNairaToWords(batchData.rbbrfBalance),
      batchName: batchDetails.batchName,
      batchCount: batchDetails.totalCount,
      rsaHolderType: batchDetails.rsaHolder === RetireeUserTypeEnum.DECEASED ? 'Deceased Employees' : 'Retirees',
      batchAmount: this.formatCurrency(batchDetails.totalAmount), // TODO fix this for contribution process
      batchAmountWords: this.formatNairaToWords(batchDetails.totalAmount),
      authorizerName,
      authorizerSignature,
      pfaBreakdown,
    };

    const message = ejs.render(htmlContent, templateData);

    const buffer = await this.slipService.generatePdf(message);
    if (!buffer) {
      response.setDescription('Unable to generate memo, please try again');
      return response;
    }

    const schedule = await this.generateSchedule(batchRecords, batchDetails.batchName);
    let batchDocument = await this.batchDocumentRepository.findOne({ batch: { pk: batchDetails.pk } });
    if (!batchDocument) {
      batchDocument = new BatchDocuments({});
    }
    const stampedSchedule = await this.generateStampedDocument(
      Buffer.from(schedule),
      auditHod,
      SlipTemplates.CERTIFIED_STAMP
    );

    batchDocument.batch = batchDetails;
    batchDocument.excoMemo = Buffer.from(buffer);
    batchDocument.schedule = stampedSchedule;

    await this.batchDocumentRepository.saveEntity(batchDocument);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.content = { schedule: stampedSchedule, excoMemo: buffer };
    response.description = 'Memo generated successfully';
    return response;
  }

  async stampDocument(
    stampScheduleDto: StampScheduleDto,
    queryRunner?: QueryRunner
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    try {
      const { batchDetails, cobraUser } =
        stampScheduleDto.processType === 'NOMINAL_ROLL'
          ? await this.validateCrMemoRequest(stampScheduleDto.batchName, stampScheduleDto.actorEmailAddress)
          : await this.validateMemoRequest(stampScheduleDto.batchName, stampScheduleDto.actorEmailAddress);

      const batchDocument = await this.getBatchDocuments(batchDetails.batchName, stampScheduleDto.processType);
      if (!batchDocument) {
        response.setDescription('Unable to retrieve batch document, please try again');
        return response;
      }
      if (stampScheduleDto.stampType === 'AUDIT_SCHEDULE') {
        if (batchDocument.schedule === null) {
          response.setDescription('Unable to retrieve AUDIT SCHEDULE document, please try again');
          return response;
        } else {
          const stampedSchedule = await this.generateStampedDocument(
            batchDocument.schedule,
            cobraUser,
            SlipTemplates.CERTIFIED_STAMP
          );
          batchDocument.schedule = stampedSchedule;
          response.content = { schedule: stampedSchedule };
        }
      }

      if (stampScheduleDto.stampType === 'EXCO_MEMO') {
        if (batchDocument.excoMemo === null) {
          response.setDescription('Unable to retrieve EXCO MEMO document, please try again');
          return response;
        } else {
          const stampedExcoMemo = await this.generateStampedDocument(
            batchDocument.excoMemo,
            cobraUser,
            SlipTemplates.APPROVED_STAMP
          );

          batchDocument.excoMemo = stampedExcoMemo;
          response.content = { excoMemo: stampedExcoMemo };
        }
      }

      if (batchDocument instanceof NominalRollBatchDocuments) {
        await this.nrBatchDocumentRepository.saveEntity(batchDocument, queryRunner);
      } else {
        await this.batchDocumentRepository.saveEntity(batchDocument, queryRunner);
      }

      response.setResponseCode(ResponseCodeEnum.SUCCESS);
      response.description = 'Memo generated successfully';
      return response;
    } catch (error) {
      this.logger.error(`Error in stampDocument: ${error.message}`, error.stack);
      response.setDescription(error.message || 'An unexpected error occurred while stamping the document');
      return response;
    }
  }

  // async stampDocument2(
  //   stampScheduleDto: StampScheduleDto,
  //   queryRunner?: QueryRunner,
  // ): Promise<BaseResponseWithContentNoPagination<unknown>> {
  //   const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
  //   const batchDetails = await this.batchRepository.findOne({ batchName: stampScheduleDto.batchName });
  //   if (!batchDetails) {
  //     response.setDescription('Provided Batch name does not exist');
  //     return response;
  //   }
  //
  //   const cobraUser = await this.cobraUserRepository.findOne({ emailAddress: stampScheduleDto.actorEmailAddress });
  //   if (!cobraUser) {
  //     response.setDescription('Unable to retrieve user details, please try again');
  //     return response;
  //   }
  //
  //   if (cobraUser.userType !== UserTypeEnum.PENCOM) {
  //     response.setDescription('You are not authorized to generate this document, please contact support');
  //     return response;
  //   }
  //
  //   if (!cobraUser.signature) {
  //     response.setDescription('Requesting user does not have a signature, please go to profile and update signature');
  //     return response;
  //   }
  //
  //   const batchDocument = await this.batchDocumentRepository.findOne({ batch: { pk: batchDetails.pk } });
  //   if (!batchDocument) {
  //     response.setDescription('Unable to retrieve batch document, please try again');
  //     return response;
  //   }
  //   if (stampScheduleDto.stampType === 'AUDIT_SCHEDULE' && batchDocument.schedule === null) {
  //     response.setDescription('Unable to retrieve AUDIT SCHEDULE document, please try again');
  //     return response;
  //   } else {
  //     const stampedSchedule = await this.generateStampedDocument(
  //       batchDocument.schedule,
  //       cobraUser,
  //       SlipTemplates.CERTIFIED_STAMP,
  //     );
  //     batchDocument.schedule = stampedSchedule;
  //     response.content = { schedule: stampedSchedule };
  //   }
  //
  //   if (stampScheduleDto.stampType === 'EXCO_MEMO' && batchDocument.excoMemo === null) {
  //     response.setDescription('Unable to retrieve EXCO MEMO document, please try again');
  //     return response;
  //   } else {
  //     const stampedExcoMemo = await this.generateStampedDocument(
  //       batchDocument.excoMemo,
  //       cobraUser,
  //       SlipTemplates.APPROVED_STAMP,
  //     );
  //
  //     batchDocument.excoMemo = stampedExcoMemo;
  //     response.content = { excoMemo: stampedExcoMemo };
  //   }
  //
  //   await this.batchDocumentRepository.saveEntity(batchDocument, queryRunner);
  //   response.setResponseCode(ResponseCodeEnum.SUCCESS);
  //   response.description = 'Memo generated successfully';
  //   return response;
  // }

  private async generateStampedDocument(
    document: Buffer,
    cobraUser: CobraUser,
    template: { emailSubject: string; messagePath: string } = SlipTemplates.APPROVED_STAMP
  ) {
    const pdfDoc = await PDFDocument.load(document);
    const stampImageBuffer = this.slipService.getBannerImage(template);
    const stampImage = await pdfDoc.embedPng(stampImageBuffer); // your image is PNG
    const pages = pdfDoc.getPages();

    const base64Data = cobraUser.signature.replace(/^data:image\/\w+;base64,/, '');
    const signatureImageBuffer = Buffer.from(base64Data, 'base64');
    const pngSignature = await pdfDoc.embedPng(signatureImageBuffer);

    const firstPage = pages[0];
    const { width, height } = firstPage.getSize();

    const stampX = width - 180;
    const stampY = height - 200;
    firstPage.drawImage(stampImage, {
      x: stampX,
      y: stampY,
      width: 150,
      height: 150,
      opacity: template.emailSubject === 'APPROVED_STAMP' ? 0.85 : 0.2,
    });

    firstPage.drawImage(pngSignature, {
      x: stampX + 20, // small horizontal alignment under stamp
      y: stampY - 20, // just below the stamp
      width: 80,
      height: 30,
    });

    const formattedDate = format(new Date(), 'dd-MMM-yyyy');

    firstPage.drawText(`${cobraUser.firstName} ${cobraUser.surname} \n ${formattedDate}`, {
      x: stampX + 25,
      y: stampY - 35,
      size: 10,
      color: rgb(0.2, 0.2, 0.2),
    });

    const stampedPdfBytes = await pdfDoc.save();
    return Buffer.from(stampedPdfBytes);
  }

  async generateSchedule(batchRecords: BatchRecord[], batchName: string) {
    const records = await Promise.all(
      batchRecords.map(async (record) => {
        const enrol = record.enrolment;
        const fullName = `${enrol.surname} ${enrol.firstName}`.trim();

        const employmentDetail2004 = await this.enrolmentDetailRepository.findOne({
          enrolmentBiodata: { rsaPin: enrol.rsaPin },
          employmentYear: AS_AT_JUNE_30TH_BAND,
        });

        return {
          name: fullName,
          gender: enrol.gender,
          rsaNumber: enrol.rsaPin,
          pfa: enrol.pfaName,
          dob: enrol.dateOfBirth ? format(enrol.dateOfBirth, ISO_HYPHEN_DATE_FORMAT) : '',
          dofa: enrol.dateOfFirstAppointment ? format(enrol.dateOfFirstAppointment, ISO_HYPHEN_DATE_FORMAT) : '',
          edor: enrol.dateOfRetirement ? format(enrol.dateOfRetirement, ISO_HYPHEN_DATE_FORMAT) : '',
          salary2004: employmentDetail2004?.salaryStructure,
          gl2004: employmentDetail2004?.gradeLevel,
          step2004: employmentDetail2004?.step,
          apaValue: enrol.apaValue2004,
          accruedRight: record.amount,
          roundValue: record.amount,
        };
      })
    );

    const templateData = {
      batchName,
      records,
    };

    const template = SlipTemplates.EXIT_SCHEDULE;
    const htmlContent = fs.readFileSync(template.messagePath, 'utf8');
    const message = ejs.render(htmlContent, templateData);

    return await this.slipService.generatePdf(message, true);
  }

  async generateContributionSchedule(records: TbcNominalRollBatchRecord[] | BatchRecord[], batchName: string) {
    const contributionRecords = [];
    const refundRecords = [];

    await Promise.all(
      records.map(async (record) => {
        const data = 'nominalRoll' in record ? record.nominalRoll : (record as BatchRecord).enrolment;

        if (!data) {
          return;
        }

        const fullName = `${data.surname} ${data.firstName}`.trim();
        const totalPensionPlusInterest = new BigNumber(data.totalPensionPlusInterest || 0);
        const paidToDate = new BigNumber(data.paidToDate || 0);
        const exitDate =
          'nominalRoll' in record ? record.nominalRoll.dateOfExit : (record as BatchRecord).enrolment.dateOfRetirement;

        const formattedRecord: any = {
          name: fullName,
          rsaPin: data.rsaPin,
          gender: data.gender,
          pfa: data.pfaName,
          dob: data.dateOfBirth ? format(data.dateOfBirth, 'yyyy-MM-dd') : '',
          dofa: data.dateOfFirstAppointment ? format(data.dateOfFirstAppointment, 'yyyy-MM-dd') : '',
          edor: exitDate ? format(exitDate, 'yyyy-MM-dd') : '',
          paidToDate: paidToDate.abs(),
        };

        const totalEmolument = new BigNumber(data.totalEmolument || 0);
        if (totalPensionPlusInterest.lt(0)) {
          formattedRecord.totalDue = totalEmolument;
          formattedRecord.refund = new BigNumber(data.totalPension || 0);
          refundRecords.push(formattedRecord);
        } else {
          const totalPensionPlusInterest = new BigNumber(data.totalPensionPlusInterest || 0);
          formattedRecord.totalDue = totalEmolument.plus(new BigNumber(data.totalInterest || 0));
          formattedRecord.additionalPension = totalPensionPlusInterest.abs();
          contributionRecords.push(formattedRecord);
        }
      })
    );

    const templateData = {
      batchName,
      contributionRecords,
      refundRecords,
    };

    const template = SlipTemplates.CONTRIBUTION_SCHEDULE;
    const htmlContent = fs.readFileSync(template.messagePath, 'utf8');
    const message = ejs.render(htmlContent, templateData);

    return await this.slipService.generatePdf(message, true);
  }

  getLastWorkingDay(fromDate = new Date()): Date {
    const day = fromDate.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    const result = new Date(fromDate);

    if (day === 1) {
      // Today is Monday → go back to Friday
      result.setDate(fromDate.getDate() - 3);
    } else if (day === 0) {
      // Today is Sunday → go back to Friday
      result.setDate(fromDate.getDate() - 2);
    } else {
      // Any other day → go back by 1 day
      result.setDate(fromDate.getDate() - 1);
    }

    return result;
  }

  async getPfaBreakDown(batchRecords: BatchRecord[]) {
    const breakdownMap = new Map<string, { count: number; amount: number; pfaCode: string }>();

    for (const record of batchRecords) {
      const pfaName = record.enrolment['pfaName'];
      const pfaCode = record.enrolment['pfaCode'];
      const amount = parseFloat(record.amount.replace(/,/g, ''));

      if (!breakdownMap.has(pfaName)) {
        breakdownMap.set(pfaName, { count: 1, amount, pfaCode });
      } else {
        const current = breakdownMap.get(pfaName)!;
        current.count += 1;
        current.amount += amount;
      }
    }

    return Array.from(breakdownMap.entries()).map(([pfaName, stats]) => ({
      name: pfaName,
      pfaCode: stats.pfaCode,
      pfaName: pfaName,
      numberOfRetirees: stats.count,
      amountFloat: stats.amount,
      amount: stats.amount
        .toLocaleString('en-NG', {
          style: 'currency',
          currency: 'NGN',
          minimumFractionDigits: 2,
        })
        .replace('NGN', '₦'),
    }));
  }

  toTitleCase(text: string): string {
    return text.toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase());
  }

  formatNairaToWords(amount: number): string {
    const [nairaStr, koboStr] = amount.toFixed(2).split('.');
    const naira = parseInt(nairaStr, 10);
    const kobo = parseInt(koboStr, 10);

    let words = `${toWords(naira)} Naira`;
    if (kobo > 0) {
      words += ` and ${toWords(kobo)} Kobo`;
    }

    return this.toTitleCase(words);
  }

  private async generateAccountMemoDetails(batchDetails: TblBatch) {
    const batchRecords = await this.batchRecordRepository.find({
      where: { batch: { pk: batchDetails.pk } },
      relations: ['enrolment'],
    });

    const batchType = batchDetails.batchType;

    const memoMap = new Map<string, { key: GroupKey; amount: number }>();
    for (const record of batchRecords) {
      const enrolment = record.enrolment;
      if (
        batchType === 'Contributions' &&
        (!enrolment.totalPensionPlusInterest || parseFloat(enrolment.totalPensionPlusInterest) < 0)
      ) {
        this.logger.warn('Contribution is refund for ', enrolment.rsaPin);
        continue;
      }

      const amountRaw =
        batchType === 'Contributions' ? enrolment.totalPensionPlusInterest : enrolment.accruedRightsAmount;

      const amount = parseFloat((amountRaw || '0').replace(/,/g, ''));

      if (!amount || isNaN(amount)) {
        this.logger.error(`Invalid amount for enrolment: ${enrolment.rsaPin}, batchType: ${batchType}`);
        continue;
      }

      const pfa = await this.pfaRepository.findOne({ pfaCode: enrolment.pfaCode });

      if (!pfa) {
        this.logger.error(`PFA not found for enrolment: ${enrolment.rsaPin}, PFA Code: ${enrolment.pfaCode}`);
        continue;
      }

      const groupKey: GroupKey = {
        pfaCode: enrolment.pfaCode,
        pfaName: pfa.pfaName,
        pfaAccountName: pfa.accountName,
        pfcName: pfa.pfc.pfcName,
        pfcCode: pfa.pfc.pfcCode,
        accountNumber: pfa.accountNumber,
      };

      const mapKey = JSON.stringify(groupKey);
      if (memoMap.has(mapKey)) {
        memoMap.get(mapKey)!.amount += amount;
      } else {
        memoMap.set(mapKey, { key: groupKey, amount });
      }
    }

    const memoDetails: TbcBatchAccountMemoDetails[] = [];
    for (const { key, amount } of memoMap.values()) {
      const memo = new TbcBatchAccountMemoDetails({
        pfaCode: key.pfaCode,
        pfaName: key.pfaName,
        pfcCode: key.pfcCode,
        pfcName: key.pfcName,
        pfaAccountName: key.pfaAccountName,
        pfaAccountNumber: key.accountNumber,
        amount,
        batch: batchDetails,
        batchType,
      });
      memoDetails.push(memo);
    }

    await this.tbcBatchAccountMemoDetailsRepository.findManyAndDelete({
      batch: { pk: batchDetails.pk },
    });

    await this.tbcBatchAccountMemoDetailsRepository.saveEntities(memoDetails);

    return memoDetails;
  }

  private async generateNrAccountMemoDetails(batchDetails: TbcNominalRollBatch) {
    const batchRecords = await this.tbcNominalRollBatchRecordRepository.find({
      where: { batch: { pk: batchDetails.pk } },
      relations: ['nominalRoll'],
    });

    const memoMap = new Map<string, { key: GroupKey; amount: number }>();
    for (const record of batchRecords) {
      const nominalRoll = record.nominalRoll;
      const totalPensionPlusInterest = new BigNumber(nominalRoll.totalPensionPlusInterest || 0);

      if (totalPensionPlusInterest.lte(0)) {
        this.logger.warn('Contribution is refund for ', nominalRoll.rsaPin);
        continue;
      }

      const amount = parseFloat(nominalRoll.totalPensionPlusInterest || '0');
      if (!amount || isNaN(amount)) {
        this.logger.error(`Invalid amount for Nominal Roll: ${nominalRoll.rsaPin}`);
        continue;
      }

      const pfa = await this.pfaRepository.findOne({ pfaCode: nominalRoll.pfaCode });

      if (!pfa) {
        this.logger.error(`PFA not found for enrolment: ${nominalRoll.rsaPin}, PFA Code: ${nominalRoll.pfaCode}`);
        continue;
      }

      const groupKey: GroupKey = {
        pfaCode: nominalRoll.pfaCode,
        pfaName: pfa.pfaName,
        pfaAccountName: pfa.accountName,
        pfcName: pfa.pfc.pfcName,
        pfcCode: pfa.pfc.pfcCode,
        accountNumber: pfa.accountNumber,
      };

      const mapKey = JSON.stringify(groupKey);
      if (memoMap.has(mapKey)) {
        memoMap.get(mapKey)!.amount += amount;
      } else {
        memoMap.set(mapKey, { key: groupKey, amount });
      }
    }

    const memoDetails: TbcBatchNrAccountMemoDetails[] = [];
    for (const { key, amount } of memoMap.values()) {
      const memo = new TbcBatchNrAccountMemoDetails({
        pfaCode: key.pfaCode,
        pfaName: key.pfaName,
        pfcCode: key.pfcCode,
        pfcName: key.pfcName,
        pfaAccountName: key.pfaAccountName,
        pfaAccountNumber: key.accountNumber,
        amount,
        batch: batchDetails,
      });
      memoDetails.push(memo);
    }

    await this.tbcBatchNrAccountMemoDetailsRepository.findManyAndDelete({
      batch: { pk: batchDetails.pk },
    });

    await this.tbcBatchNrAccountMemoDetailsRepository.saveEntities(memoDetails);

    return memoDetails;
  }

  async generateCrAccountMemoPdf(
    batchData: BatchDocumentDto,
    req: ICustomRequest
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const { batchDetails, cobraUser } = await this.validateCrMemoRequest(batchData.batchName, req.user.email);

    let accountMemoDetails: TbcBatchNrAccountMemoDetails[] = [];
    const regenerateAccountDetails = await this.settingsService.getSettingBoolean(
      SettingsEnumKey.ALWAYS_REGENERATE_ACCOUNT_MEMO
    );

    if (regenerateAccountDetails) {
      accountMemoDetails = await this.generateNrAccountMemoDetails(batchDetails);
    }

    if (accountMemoDetails.length === 0) {
      accountMemoDetails = await this.tbcBatchNrAccountMemoDetailsRepository.find({
        where: { batch: { pk: batchDetails.pk } },
        order: { amount: 'ASC' },
      });

      if (!accountMemoDetails || accountMemoDetails.length === 0) {
        accountMemoDetails = await this.generateNrAccountMemoDetails(batchDetails);
      }
    }

    if (!accountMemoDetails || accountMemoDetails.length === 0) {
      response.setDescription('Unable to retrieve account memo details, please try again');
      return response;
    }

    const buffer = await this.generateAccountMemoPdfDocument(
      cobraUser,
      accountMemoDetails,
      batchDetails,
      SlipTemplates.ACCOUNT_MEMO_CONTRIBUTION
    );

    if (!buffer) {
      response.setDescription('Unable to generate memo, please try again');
      return response;
    }

    let nrBatchDocument = await this.nrBatchDocumentRepository.findOne({
      batch: { batchName: batchData.batchName },
    });

    if (!nrBatchDocument) {
      nrBatchDocument = new NominalRollBatchDocuments({});
    }
    nrBatchDocument.accountMemo = Buffer.from(buffer);
    await this.nrBatchDocumentRepository.saveEntity(nrBatchDocument);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.content = { accountMemo: buffer };
    response.description = 'Memo generated successfully';

    return response;
  }

  async generateCrExcoMemoPdf(
    batchData: BatchMemoGenerationDto,
    req: ICustomRequest
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    const { batchDetails, cobraUser } = await this.validateCrMemoRequest(batchData.batchName, req.user.email);
    return await this.generateContributionExcoMemoPdf(
      batchData,
      req,
      'NOMINAL_ROLL',
      RetireeUserTypeEnum.RETIREE,
      batchDetails,
      cobraUser
    );
  }

  async generateContributionExcoMemoPdf(
    batchData: BatchMemoGenerationDto,
    req: ICustomRequest,
    requestType: 'NOMINAL_ROLL' | 'EXIT_RECORD',
    rsaHolderTypes: RetireeUserTypeEnum,
    batchDetails,
    cobraUser: CobraUser
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const template = SlipTemplates.EXCO_MEMO_CONTRIBUTIONS;
    const htmlContent = fs.readFileSync(template.messagePath, 'utf8');

    const currentDate = format(new Date(), 'd MMMM, yyyy');
    const currentYear = new Date().getFullYear();

    const authorizerName = `${cobraUser.firstName} ${cobraUser.surname}`;
    const authorizerSignature = cobraUser.signature;

    const auditHod: CobraUser = await this.getAuditHod();
    if (!auditHod || !auditHod.signature) {
      throw new Error('Audit Hod profile/signature needs to be setup to proceed with this process');
    }

    const batchRecords = await this.workflowHelperService.getBatchRecords(batchDetails.pk, requestType);

    const memoType = 'EXCO';
    const referenceNo = `TECH/COBRA/${memoType}/${currentYear}/${await this.serialNumberService.getNextSerialNumber(memoType, currentYear)}`;
    const lastWorkingDay = format(this.getLastWorkingDay(), 'd MMMM, yyyy');
    const interestRate = await this.settingsService.getSettingInt(SettingsEnumKey.EXIT_CONTRIBUTION_INTEREST_RATE);
    const pfaBreakdown = await this.getPfaContributionBreakDown(batchRecords);

    const contributionCount = pfaBreakdown.reduce((sum, item) => sum + item.outstanding.count, 0);
    const contributionDueAmount = pfaBreakdown.reduce((sum, item) => sum + item.outstanding.totalDue, 0);
    const contributionPaidToDateAmount = pfaBreakdown.reduce((sum, item) => sum + item.outstanding.paidToDate, 0);
    const contributionAdditionalPensionAmount = pfaBreakdown.reduce(
      (sum, item) => sum + item.outstanding.additionalPension,
      0
    );
    const pfaContributionCount = pfaBreakdown.filter((pfa) => pfa.outstanding.count > 0).length;

    const refundCount = pfaBreakdown.reduce((sum, item) => sum + item.refund.count, 0);
    const totalRefund = pfaBreakdown.reduce((sum, item) => sum + item.refund.refund, 0);
    const totalRefundDue = pfaBreakdown.reduce((sum, item) => sum + item.refund.totalDue, 0);
    const totalRefundPaidToDate = pfaBreakdown.reduce((sum, item) => sum + item.refund.paidToDate, 0);
    const pfaRefundCount = pfaBreakdown.filter((pfa) => pfa.refund.count > 0).length;

    const templateData = {
      requestDate: currentDate,
      referenceNo,
      lastWorkingDay,
      batchName: batchDetails.batchName,
      batchCount: batchDetails.totalCount,
      rsaHolderType: rsaHolderTypes === RetireeUserTypeEnum.DECEASED ? 'DECEASED EMPLOYEES' : 'RETIREES',
      modeOfRetirement: rsaHolderTypes === RetireeUserTypeEnum.RETIREE ? 'month of retirement' : 'date of death',
      interestRate,
      contributionCount,
      contributionDueAmount: this.formatCurrency(contributionDueAmount),
      contributionPaidToDateAmount: this.formatCurrency(contributionPaidToDateAmount),
      contributionAdditionalPensionAmount: this.formatCurrency(contributionAdditionalPensionAmount),
      pfaContributionCount,
      refundCount,
      totalRefund: this.formatCurrency(Math.abs(totalRefund)),
      totalRefundDue: this.formatCurrency(totalRefundDue),
      totalRefundPaidToDate: this.formatCurrency(totalRefundPaidToDate),
      pfaRefundCount,
      transitBalance: this.formatCurrency(batchData.rbbrfBalance),
      transitBalanceWord: this.formatNairaToWords(batchData.rbbrfBalance),
      authorizerName,
      authorizerSignature,
    };

    const message = ejs.render(htmlContent, templateData);

    const buffer = await this.slipService.generatePdf(message);
    if (!buffer) {
      response.setDescription('Unable to generate memo, please try again');
      return response;
    }

    const appendixTemplate = SlipTemplates.EXCO_MEMO_CONTRIBUTIONS_APPENDIX;
    const htmlContentAppendix = fs.readFileSync(appendixTemplate.messagePath, 'utf8');

    const appendixTemplateData = {
      batchName: batchDetails.batchName,
      pfaBreakdown,
    };
    const pfaBreakdownMessage = ejs.render(htmlContentAppendix, appendixTemplateData);

    const bufferAppendix = await this.slipService.generatePdf(pfaBreakdownMessage, true);
    if (!bufferAppendix) {
      response.setDescription('Unable to generate memo Appendix, please try again');
      return response;
    }

    const schedule = await this.generateContributionSchedule(batchRecords, batchDetails.batchName);

    let batchDocument = await this.getBatchDocuments(batchDetails.batchName, requestType);
    if (!batchDocument) {
      batchDocument = requestType === 'NOMINAL_ROLL' ? new NominalRollBatchDocuments({}) : new BatchDocuments({});
    }

    const stampedSchedule = await this.generateStampedDocument(
      Buffer.from(schedule),
      auditHod,
      SlipTemplates.CERTIFIED_STAMP
    );

    batchDocument.batch = batchDetails;
    batchDocument.excoMemo = Buffer.from(buffer);
    batchDocument.excoMemoAppendix = Buffer.from(bufferAppendix);
    batchDocument.schedule = stampedSchedule;

    if (batchDocument instanceof NominalRollBatchDocuments) {
      await this.nrBatchDocumentRepository.saveEntity(batchDocument);
    } else {
      await this.batchDocumentRepository.saveEntity(batchDocument);
    }

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.content = { schedule: stampedSchedule, excoMemo: buffer, appendix: bufferAppendix };
    response.description = 'Memo generated successfully';
    return response;
  }

  async generateNrBatchPfaDocument(batchData: BatchDocumentDto, req: ICustomRequest) {
    const { batchDetails, cobraUser } = await this.validateCrMemoRequest(batchData.batchName, req.user.email);
    return await this.generateContributionPfaMemoPdf(
      req,
      'NOMINAL_ROLL',
      RetireeUserTypeEnum.RETIREE,
      batchDetails,
      cobraUser
    );
  }

  async generateExitBatchPfaDocument(batchData: BatchDocumentDto, req: ICustomRequest) {
    const { batchDetails, cobraUser } = await this.validateMemoRequest(batchData.batchName, req.user.email);

    if (batchDetails.batchType === 'Contributions') {
      return await this.generateContributionPfaMemoPdf(
        req,
        'EXIT_RECORD',
        batchDetails.rsaHolder as RetireeUserTypeEnum,
        batchDetails,
        cobraUser
      );
    } else {
      return await this.generateAccruedRightsPfaMemoPdf(req, batchDetails, cobraUser);
    }
  }

  async generateAccruedRightsPfaMemoPdf(
    req: ICustomRequest,
    batchDetails: TblBatch,
    cobraUser: CobraUser
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const batchRecords = await this.batchRecordRepository.find({
      where: { batch: { pk: batchDetails.pk } },
      relations: ['enrolment'],
    });

    const rsaHolderTypes = batchDetails.rsaHolder as RetireeUserTypeEnum;

    const content: any = {};
    const authorizerName = `${cobraUser.firstName} ${cobraUser.surname}`;
    const authorizerSignature = cobraUser.signature;
    const currentDate = format(new Date(), 'd MMMM, yyyy');
    const currentYear = new Date().getFullYear();
    const memoType = 'PFA';
    const pfaBreakdown = await this.getPfaBreakDown(batchRecords);
    const scheduleDetails = await this.generatePfaSchedule(batchRecords, batchDetails);

    const pfcMemoTemplate = SlipTemplates.PFC_ACCRUED_RIGHTS_MEMO;
    const pfcMemoHtmlContent = fs.readFileSync(pfcMemoTemplate.messagePath, 'utf8');

    const scheduleTemplate = SlipTemplates.PFA_ACCRUED_RIGHTS_SCHEDULE;
    const scheduleHtmlContent = fs.readFileSync(scheduleTemplate.messagePath, 'utf8');

    const template = SlipTemplates.PFA_ACCRUED_RIGHTS_MEMO;
    const htmlContent = fs.readFileSync(template.messagePath, 'utf8');
    content.pfa = [];
    content.pfc = [];

    const pfcMap = new Map<
      string,
      {
        pfcCode: string;
        pfc: Pfc;
        pfas: {
          pfaCode: string;
          pfaName: string;
          pfaAccountName: string;
          pfaAccountNumber: string;
          amount: number;
        }[];
      }
    >();

    for (const breakdown of pfaBreakdown) {
      const referenceNo = `PenCom/TECH/CBRD/C&R/COBRA/${currentYear}/${await this.serialNumberService.getNextSerialNumber(memoType, currentYear)}`;
      const pfa: Pfa = await this.pfaRepository.getCachedPfaDetails(breakdown.pfaCode);

      const currentPfc = pfa.pfc;
      const pfcCode = currentPfc.pfcCode;
      const pfcDetail = {
        pfc: currentPfc,
        pfaCode: pfa.pfaCode,
        pfaName: pfa.pfaName,
        pfaAccountName: pfa.accountName,
        pfaAccountNumber: pfa.accountNumber,
        amount: breakdown.amountFloat,
      };

      if (!pfcMap.has(pfcCode)) {
        pfcMap.set(pfcCode, {
          pfcCode,
          pfc: pfa.pfc,
          pfas: [],
        });
      }

      pfcMap.get(pfcCode)!.pfas.push(pfcDetail);

      const address = `The Managing Director/CEO <br>  ${pfa.pfaName} <br> ${pfa.fullHtmlAddress}`;
      const templateData = {
        requestDate: currentDate,
        referenceNo,
        batchName: batchDetails.batchName,
        batchCount: batchDetails.totalCount,
        rsaHolderType: rsaHolderTypes === RetireeUserTypeEnum.DECEASED ? 'DECEASED EMPLOYEES' : 'RETIREES',
        address: address,
        employeeCount: breakdown.numberOfRetirees,
        pfcName: pfa.pfc.pfcName,
        accruedRightsAmount: this.formatCurrency(Math.abs(breakdown.amountFloat)),
        gracePeriod: await this.settingsService.getSetting(SettingsEnumKey.PFA_AWAITING_CONFIRMATION_WAIT_PERIOD),
        authorizerName,
        authorizerSignature,
      };

      const message = ejs.render(htmlContent, templateData);
      const buffer = await this.slipService.generatePdf(message);
      if (!buffer) {
        response.setDescription('Unable to generate PFA memo, please try again');
        return response;
      }

      if (!scheduleDetails.has(breakdown.pfaName)) {
        throw new BadRequestException(`Unable to locate schedule details for PFA ${breakdown.pfaName}`);
      }
      const pfaSchedule = scheduleDetails.get(breakdown.pfaName);
      const pfaScheduleMessage = ejs.render(scheduleHtmlContent, pfaSchedule);
      const scheduleBuffer = await this.slipService.generatePdf(pfaScheduleMessage, true);
      if (!scheduleBuffer) {
        response.setDescription('Unable to generate PFA schedule, please try again');
        return response;
      }

      content.pfa.push({
        pfaCode: breakdown.pfaCode,
        name: pfa.pfc.pfcName,
        memo: (buffer as Buffer).toString('base64'),
        schedule: (scheduleBuffer as Buffer).toString('base64'),
      });

      const document = new TbcEnrollmentPfaMemoDocument({
        pfaName: pfa.pfaName,
        pfaCode: pfa.pfaCode,
        pfcName: pfa.pfc.pfcName,
        pfcCode: pfa.pfc.pfcCode,
        pfaMemo: Buffer.from(buffer),
        pfaSchedule: Buffer.from(scheduleBuffer),
        batch: batchDetails,
      });

      await this.tbcEnrollmentPfaMemoDocumentRepository.findOneAndDelete({
        pfaName: pfa.pfaName,
        batch: { pk: batchDetails.pk },
      });
      await this.tbcEnrollmentPfaMemoDocumentRepository.saveEntity(document);
    }

    for (const [pfcCode, pfcData] of pfcMap.entries()) {
      const pfcMemoType = 'PFC';
      const referenceNo = `PenCom/TECH/CBRD/C&R/COBRA/${currentYear}/${await this.serialNumberService.getNextSerialNumber(pfcMemoType, currentYear)}`;
      const address = `The Managing Director/CEO <br>  ${pfcData.pfc.pfcName} <br> ${pfcData.pfc.fullHtmlAddress}`;
      const data = {
        batchName: batchDetails.batchName,
        requestDate: currentDate,
        rsaHolderType: rsaHolderTypes === RetireeUserTypeEnum.DECEASED ? 'DECEASED EMPLOYEES' : 'RETIREES',
        referenceNo,
        address,
        pfcs: pfcData,
        authorizerName,
        authorizerSignature,
      };
      const pfcMessage = ejs.render(pfcMemoHtmlContent, data);
      const pfcMemoBuffer = await this.slipService.generatePdf(pfcMessage);
      content.pfc.push({
        pfcCode: pfcCode,
        name: pfcData.pfc.pfcName,
        memo: (pfcMemoBuffer as Buffer).toString('base64'),
      });

      const document = new TbcEnrollmentPfcMemoDocument({
        pfcName: pfcData.pfc.pfcName,
        pfcCode: pfcData.pfc.pfcCode,
        pfcMemo: Buffer.from(pfcMemoBuffer),
        batch: batchDetails,
      });

      await this.tbcEnrollmentPfcMemoDocumentRepository.findOneAndDelete({
        pfcName: pfcData.pfc.pfcName,
        batch: { pk: batchDetails.pk },
      });
      await this.tbcEnrollmentPfcMemoDocumentRepository.saveEntity(document);
    }
    response.content = content;
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    return response;
  }

  async generateContributionPfaMemoPdf(
    req: ICustomRequest,
    requestType: 'NOMINAL_ROLL' | 'EXIT_RECORD',
    rsaHolderTypes: RetireeUserTypeEnum,
    batchDetails: TbcNominalRollBatch | TblBatch,
    cobraUser: CobraUser
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    //todo check whether already created and then return already created record

    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const batchRecords = await this.workflowHelperService.getBatchRecords(batchDetails.pk, requestType);
    const content: any = {};
    const authorizerName = `${cobraUser.firstName} ${cobraUser.surname}`;
    const authorizerSignature = cobraUser.signature;
    const currentDate = format(new Date(), 'd MMMM, yyyy');
    const currentYear = new Date().getFullYear();
    const memoType = 'PFA';
    const pfaBreakdown = await this.getPfaContributionBreakDown(batchRecords);
    const scheduleDetails = await this.generatePfaSchedule(batchRecords, batchDetails);

    const pfcMemoTemplate = SlipTemplates.PFC_CONTRIBUTION_MEMO;
    const pfcMemoHtmlContent = fs.readFileSync(pfcMemoTemplate.messagePath, 'utf8');

    const scheduleTemplate = SlipTemplates.PFA_CONTRIBUTION_SCHEDULE;
    const scheduleHtmlContent = fs.readFileSync(scheduleTemplate.messagePath, 'utf8');
    const template = SlipTemplates.PFA_CONTRIBUTION_MEMO;
    const htmlContent = fs.readFileSync(template.messagePath, 'utf8');
    content.pfa = [];
    content.pfc = [];

    const pfcMap = new Map<
      string,
      {
        pfcCode: string;
        pfc: Pfc;
        pfas: {
          pfaCode: string;
          pfaName: string;
          pfaAccountName: string;
          pfaAccountNumber: string;
          amount: number;
        }[];
      }
    >();

    for (const breakdown of pfaBreakdown) {
      const referenceNo = `PenCom/TECH/CBRD/C&R/COBRA/${currentYear}/${await this.serialNumberService.getNextSerialNumber(memoType, currentYear)}`;
      const pfa: Pfa = await this.pfaRepository.getCachedPfaDetails(breakdown.pfaCode);

      if (breakdown.outstanding.additionalPension > 0) {
        const currentPfc = pfa.pfc;
        const pfcCode = currentPfc.pfcCode; // assuming `pfcCode` is part of Pfa
        // const pfcName = currentPfc.pfcName;

        const pfcDetail = {
          pfc: currentPfc,
          pfaCode: pfa.pfaCode,
          pfaName: pfa.pfaName,
          pfaAccountName: pfa.accountName,
          pfaAccountNumber: pfa.accountNumber,
          amount: breakdown.outstanding.additionalPension,
        };

        if (!pfcMap.has(pfcCode)) {
          pfcMap.set(pfcCode, {
            pfcCode,
            pfc: pfa.pfc,
            pfas: [],
          });
        }

        pfcMap.get(pfcCode)!.pfas.push(pfcDetail);
      }

      const address = `The Managing Director/CEO <br>  ${pfa.pfaName} <br> ${pfa.fullHtmlAddress}`;
      const templateData = {
        requestDate: currentDate,
        referenceNo,
        batchName: batchDetails.batchName,
        batchCount: batchDetails.totalCount,
        rsaHolderType: rsaHolderTypes === RetireeUserTypeEnum.DECEASED ? 'DECEASED EMPLOYEES' : 'RETIREES',
        address: address,
        pfcName: pfa.pfc.pfcName,
        showContributions: breakdown.outstanding.count > 0,
        contributionCount: breakdown.outstanding.count,
        contributionAmount: this.formatCurrency(breakdown.outstanding.additionalPension),
        showRefunds: breakdown.refund.count > 0,
        refundCount: breakdown.refund.count,
        refundAmount: this.formatCurrency(Math.abs(breakdown.refund.refund)),
        gracePeriod: await this.settingsService.getSetting(SettingsEnumKey.PFA_AWAITING_CONFIRMATION_WAIT_PERIOD),
        authorizerName,
        authorizerSignature,
      };

      const message = ejs.render(htmlContent, templateData);
      const buffer = await this.slipService.generatePdf(message);
      if (!buffer) {
        response.setDescription('Unable to generate PFA memo, please try again');
        return response;
      }

      if (!scheduleDetails.has(breakdown.pfa)) {
        throw new BadRequestException(`Unable to locate schedule details for PFA ${breakdown.pfa}`);
      }
      const pfaSchedule = scheduleDetails.get(breakdown.pfa);
      const scheduleBuffer = this.excelPfaScheduleService.generateExcel(pfaSchedule);
      if (!scheduleBuffer) {
        response.setDescription('Unable to generate PFA schedule, please try again');
        return response;
      }

      content.pfa.push({
        pfaCode: breakdown.pfaCode,
        name: pfa.pfc.pfcName,
        memo: (buffer as Buffer).toString('base64'),
        schedule: (scheduleBuffer as Buffer).toString('base64'),
      });

      if (batchDetails instanceof TbcNominalRollBatch) {
        const document = new TbcNominalRollPfaMemoDocument({
          pfaName: pfa.pfaName,
          pfaCode: pfa.pfaCode,
          pfcName: pfa.pfc.pfcName,
          pfcCode: pfa.pfc.pfcCode,
          pfaMemo: Buffer.from(buffer),
          pfaSchedule: Buffer.from(scheduleBuffer),
          batch: batchDetails,
        });

        await this.tbcNominalRollPfaMemoDetailRepository.findOneAndDelete({
          pfaName: pfa.pfaName,
          batch: { pk: batchDetails.pk },
        });
        await this.tbcNominalRollPfaMemoDetailRepository.saveEntity(document);
      } else {
        const document = new TbcEnrollmentPfaMemoDocument({
          pfaName: pfa.pfaName,
          pfaCode: pfa.pfaCode,
          pfcName: pfa.pfc.pfcName,
          pfcCode: pfa.pfc.pfcCode,
          pfaMemo: Buffer.from(buffer),
          pfaSchedule: Buffer.from(scheduleBuffer),
          batch: batchDetails,
        });

        await this.tbcEnrollmentPfaMemoDocumentRepository.findOneAndDelete({
          pfaName: pfa.pfaName,
          batch: { pk: batchDetails.pk },
        });
        await this.tbcEnrollmentPfaMemoDocumentRepository.saveEntity(document);
      }
    }

    for (const [pfcCode, pfcData] of pfcMap.entries()) {
      const pfcMemoType = 'PFC';
      const referenceNo = `PenCom/TECH/CBRD/C&R/COBRA/${currentYear}/${await this.serialNumberService.getNextSerialNumber(pfcMemoType, currentYear)}`;
      const address = `The Managing Director/CEO <br>  ${pfcData.pfc.pfcName} <br> ${pfcData.pfc.fullHtmlAddress}`;
      const data = {
        batchName: batchDetails.batchName,
        requestDate: currentDate,
        rsaHolderType: rsaHolderTypes === RetireeUserTypeEnum.DECEASED ? 'DECEASED EMPLOYEES' : 'RETIREES',
        referenceNo,
        address,
        pfcs: pfcData,
        authorizerName,
        authorizerSignature,
      };
      const pfcMessage = ejs.render(pfcMemoHtmlContent, data);
      const pfcMemoBuffer = await this.slipService.generatePdf(pfcMessage);
      content.pfc.push({
        pfcCode: pfcCode,
        name: pfcData.pfc.pfcName,
        memo: (pfcMemoBuffer as Buffer).toString('base64'),
      });

      if (batchDetails instanceof TbcNominalRollBatch) {
        const document = new TbcNominalRollPfcMemoDocument({
          pfcName: pfcData.pfc.pfcName,
          pfcCode: pfcData.pfc.pfcCode,
          pfcMemo: Buffer.from(pfcMemoBuffer),
          batch: batchDetails,
        });

        await this.tbcNominalRollPfcMemoDocumentRepository.findOneAndDelete({
          pfcName: pfcData.pfc.pfcName,
          batch: { pk: batchDetails.pk },
        });
        await this.tbcNominalRollPfcMemoDocumentRepository.saveEntity(document);
      } else {
        const document = new TbcEnrollmentPfcMemoDocument({
          pfcName: pfcData.pfc.pfcName,
          pfcCode: pfcData.pfc.pfcCode,
          pfcMemo: Buffer.from(pfcMemoBuffer),
          batch: batchDetails,
        });

        await this.tbcEnrollmentPfcMemoDocumentRepository.findOneAndDelete({
          pfcName: pfcData.pfc.pfcName,
          batch: { pk: batchDetails.pk },
        });
        await this.tbcEnrollmentPfcMemoDocumentRepository.saveEntity(document);
      }
    }

    response.content = content;
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    return response;
  }

  private async generatePfaSchedule(
    batchRecords: TbcNominalRollBatchRecord[] | BatchRecord[],
    batchDetails: TbcNominalRollBatch | TblBatch
  ) {
    const breakdownMap = new Map<string, any>();
    for (const record of batchRecords) {
      const isNominalRoll = 'nominalRoll' in record;
      const currentRecord = isNominalRoll ? record.nominalRoll : record.enrolment;
      const pfa = currentRecord.pfaName;
      const pfaCode = currentRecord.pfaCode;

      if (!pfa) {
        continue;
      }

      if (!breakdownMap.has(pfa)) {
        breakdownMap.set(pfa, {
          pfa,
          pfaCode,
          batchName: batchDetails.batchName,
          showTotalMonths: isNominalRoll,
          outstanding: [],
          refund: [],
          accruedRights: [],
        });
      }

      const amountRaw =
        currentRecord instanceof NominalRoll
          ? currentRecord.totalPensionPlusInterest
          : (batchDetails as TblBatch).batchType === 'Contributions'
            ? currentRecord.totalPensionPlusInterest
            : currentRecord.accruedRightsAmount;

      const breakdown = breakdownMap.get(pfa);

      const currentSchedule = {
        rsaPin: currentRecord.rsaPin,
        mdaName: currentRecord instanceof NominalRoll ? currentRecord.mdaName : currentRecord.employerName,
        mdaCode: currentRecord instanceof NominalRoll ? currentRecord.mdaCode : currentRecord.employerCode,
        pfaName: currentRecord.pfaName,
        pfaCode: currentRecord.pfaCode,
        firstName: currentRecord.firstName,
        surname: currentRecord.surname,
        totalMonths: currentRecord.totalMonths,
        totalPension: amountRaw,
        batchName: batchDetails.batchName,
        batch: record.batch,
      };

      if (isNominalRoll) {
        if (parseFloat(amountRaw) < 0) {
          breakdown.refund.push(currentSchedule);
        } else {
          breakdown.outstanding.push(currentSchedule);
        }
      } else {
        breakdown.accruedRights.push(currentSchedule);
      }
    }
    return breakdownMap;
  }

  async generateAccountMemoPdfDocument(
    cobraUser: CobraUser,
    accountMemoDetails: TbcBatchAccountMemoDetails[] | TbcBatchNrAccountMemoDetails[],
    batchDetails: TblBatch | TbcNominalRollBatch,
    template: (typeof SlipTemplates)[keyof typeof SlipTemplates]
  ): Promise<Uint8Array<ArrayBufferLike>> {
    const currentDate = format(new Date(), 'd MMMM, yyyy');
    const currentYear = new Date().getFullYear();
    const authorizerName = `${cobraUser.firstName} ${cobraUser.surname}`;
    const authorizerSignature = cobraUser.signature;
    const memoType = 'ACD';
    const referenceNo = `TECH/COBRA/${memoType}/${currentYear}/${await this.serialNumberService.getNextSerialNumber(memoType, currentYear)}`;

    const htmlContent = fs.readFileSync(template.messagePath, 'utf8');
    const templateData = {
      requestDate: currentDate,
      referenceNo,
      batchName: batchDetails.batchName,
      batchAmount: this.formatCurrency(
        batchDetails instanceof TbcNominalRollBatch ? batchDetails.totalContributions : batchDetails.totalAmount
      ),
      batchAmountWords: this.formatNairaToWords(
        batchDetails instanceof TbcNominalRollBatch ? batchDetails.totalContributions : batchDetails.totalAmount
      ),
      authorizerName,
      authorizerSignature,
      pfaBreakdown: accountMemoDetails,
    };

    const message = ejs.render(htmlContent, templateData);

    return await this.slipService.generatePdf(message);
  }

  private async getPfaContributionBreakDown(batchRecords: TbcNominalRollBatchRecord[] | BatchRecord[]) {
    const breakdownMap = new Map<string, any>();

    for (const record of batchRecords) {
      const isNominalRoll = 'nominalRoll' in record;
      const pfa = isNominalRoll ? record.nominalRoll.pfaName : record.enrolment?.pfaName;
      const pfaCode = isNominalRoll ? record.nominalRoll.pfaCode : record.enrolment?.pfaCode;

      const totalPensionPlusInterest = parseFloat(
        isNominalRoll
          ? record.nominalRoll.totalPensionPlusInterest || '0'
          : record.enrolment?.totalPensionPlusInterest || '0'
      );
      const paidToDate = parseFloat(
        isNominalRoll ? record.nominalRoll.paidToDate || '0' : record.enrolment?.paidToDate || '0'
      );

      if (!pfa) {
        continue;
      }

      if (!breakdownMap.has(pfa)) {
        breakdownMap.set(pfa, {
          pfa,
          pfaCode,
          outstanding: {
            count: 0,
            totalDue: 0,
            paidToDate: 0,
            additionalPension: 0,
          },
          refund: {
            count: 0,
            totalDue: 0,
            paidToDate: 0,
            refund: 0,
          },
        });
      }

      const breakdown = breakdownMap.get(pfa);

      const totalEmolument = parseFloat(
        isNominalRoll ? record.nominalRoll.totalEmolument || '0' : record.enrolment?.totalEmolument || '0'
      );
      if (totalPensionPlusInterest < 0) {
        // REFUND
        const totalPension = parseFloat(
          isNominalRoll ? record.nominalRoll.totalPension || '0' : record.enrolment?.totalPension || '0'
        );
        breakdown.refund.count += 1;
        breakdown.refund.totalDue += Math.abs(totalEmolument);
        breakdown.refund.paidToDate += paidToDate;
        breakdown.refund.refund += totalPension;
      } else {
        // OUTSTANDING
        const totalInterest = parseFloat(
          isNominalRoll ? record.nominalRoll.totalInterest || '0' : record.enrolment?.totalInterest || '0'
        );
        breakdown.outstanding.count += 1;
        breakdown.outstanding.totalDue += totalEmolument + totalInterest; // totalEmolument + interest
        breakdown.outstanding.paidToDate += paidToDate;
        breakdown.outstanding.additionalPension += Math.abs(totalPensionPlusInterest);
      }
    }

    return Array.from(breakdownMap.values()).map((b) => ({
      ...b,
      outstanding: {
        ...b.outstanding,
        totalDue: parseFloat(b.outstanding.totalDue.toFixed(2)),
        paidToDate: parseFloat(b.outstanding.paidToDate.toFixed(2)),
        additionalPension: parseFloat(b.outstanding.additionalPension.toFixed(2)),
      },
      refund: {
        ...b.refund,
        totalDue: parseFloat(b.refund.totalDue.toFixed(2)),
        paidToDate: parseFloat(b.refund.paidToDate.toFixed(2)),
        refund: parseFloat(b.refund.refund.toFixed(2)),
      },
    }));
  }

  private async getBatchDocuments(batchName: string, requestType: 'NOMINAL_ROLL' | 'EXIT_RECORD') {
    if (requestType === 'NOMINAL_ROLL') {
      return await this.nrBatchDocumentRepository.findOne({ batch: { batchName: batchName } });
    }

    return await this.batchDocumentRepository.findOne({
      batch: { batchName: batchName },
    });
  }

  private async getAuditHod() {
    const auditHodRole = await this.settingsService.getSetting(SettingsEnumKey.AUDIT_HOD_ROLE_NAME);
    const actorRoles = auditHodRole.split(',').map((role) => role.trim());
    const auditHod = await this.cobraUserRepository.getFullUsersByRoleNameAndUserType(actorRoles, UserTypeEnum.PENCOM);
    if (!auditHod || auditHod.length === 0) {
      return null;
    }

    return auditHod[0];
  }
}
