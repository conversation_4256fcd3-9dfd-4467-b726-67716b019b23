import { Inject, Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ogger } from 'nestjs-pino';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { RegistrationStatusEnum } from '@app/shared/enums/registration-status.enum';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { SettingsEnumKey } from '@app/shared/enums';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { SendNotificationTemplateDto } from '@app/shared/dto/notification/send-notification-template.dto';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { firstValueFrom } from 'rxjs';
import { NotificationServiceClientConstant } from '@app/shared/constants/notification-service-client.constant';
import { ClientProxy } from '@nestjs/microservices';
import { format } from 'date-fns';

@Injectable()
export class EnrolmentDistributionService {
  constructor(
    private readonly enrolmentSummaryRepository: EnrolmentSummaryRepository,
    private readonly cobraUserRepository: CobraUserRepository,
    private readonly settingMsLibService: SettingMsLibService,
    @Inject('NOTIFICATION_SERVICE_CLIENT') private readonly notificationClient: ClientProxy,
    private readonly logger: PinoLogger
  ) {}

  /**
   * Distribute unassigned enrollment records among eligible validators
   * with workload balancing across multiple runs
   * @returns Count of distributed records
   */
  async distributeEnrolments(): Promise<{
    distributedCount: number;
    eligibleValidators: number;
    recordsPerValidator: number;
  }> {
    const validatorRoleName = await this.settingMsLibService.getSetting(SettingsEnumKey.PENCOM_VALIDATOR_ROLE_NAME);

    // 2. Find active users with the validator role
    const eligibleValidators = await this.cobraUserRepository.getUsersByRoleNameAndUserType(
      validatorRoleName.split(',').map((role) => role.trim()),
      UserTypeEnum.PENCOM
    );

    console.log('eligibleValidators:::::: ', eligibleValidators);

    if (eligibleValidators.length === 0) {
      this.logger.warn('No eligible validators found');
      return { distributedCount: 0, eligibleValidators: 0, recordsPerValidator: 0 };
    }

    // this.fileUploadRepository.find({ where: { taskId } });
    // 3. Find unassigned enrollment records
    const unassignedRecords = await this.enrolmentSummaryRepository.getUnassignedRecords(
      RegistrationStatusEnum.ENROLLED
    );

    console.log('unassignedRecords:::::: ', unassignedRecords);

    if (unassignedRecords.length === 0) {
      this.logger.debug('No unassigned records found');
      return {
        distributedCount: 0,
        eligibleValidators: eligibleValidators.length,
        recordsPerValidator: 0,
      };
    }

    // 4. Get current workload for each validator
    const workloadMap = new Map<number, number>();

    // Initialize all validators with zero workload
    eligibleValidators.forEach((validator) => {
      workloadMap.set(validator.pk, 0);
    });

    // Get current pending record counts for each validator
    const validatorWorkloads = await this.enrolmentSummaryRepository.getValidatorWorkload();

    // Update workload map with current counts
    validatorWorkloads.forEach((workload) => {
      if (workloadMap.has(workload.validatorId)) {
        workloadMap.set(workload.validatorId, workload.recordCount);
      }
    });

    // 5. Sort validators by current workload (ascending)
    const sortedValidators = [...eligibleValidators].sort((a, b) => {
      const workloadA = workloadMap.get(a.pk) || 0;
      const workloadB = workloadMap.get(b.pk) || 0;
      return workloadA - workloadB;
    });

    // 6. Distribute records to validators with lowest current workload
    let assignedCount = 0;
    const now = new Date();
    const totalRecords = unassignedRecords.length;

    // Create batches based on total validators and records
    for (const record of unassignedRecords) {
      // Get validator with current lowest workload
      const targetValidator = sortedValidators[0];

      // Assign record to this validator
      await this.enrolmentSummaryRepository.findOneAndUpdate(
        { pk: record.pk },
        {
          assignedTo: targetValidator,
          assignmentDate: now,
        }
      );

      // send email
      const enrolledBy = await record.enrolledBy;
      const placeholders: Record<string, string> = {};
      placeholders['userName'] = targetValidator.firstName;
      placeholders['rsaPin'] = record.rsaPin;
      placeholders['submittedBy'] = `${enrolledBy.firstName} ${enrolledBy.surname}`;
      placeholders['submissionDate'] = record.enrolmentDate
        ? format(record.enrolmentDate, 'yyyy-MM-dd HH:mm:ss')
        : 'N/A';

      const sendNotificationTemplateDto = new SendNotificationTemplateDto();
      sendNotificationTemplateDto.emailRecipients = [targetValidator.emailAddress];
      sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.VALIDATOR_REVIEW_REQUEST;
      sendNotificationTemplateDto.placeholders = placeholders;

      console.log('sendNotificationTemplateDto:::: ', sendNotificationTemplateDto);
      try {
        await firstValueFrom(
          this.notificationClient.send(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            sendNotificationTemplateDto
          )
        );
      } catch (error) {
        console.log('error', error);
        this.logger.error(
          `Failed to send SEND_EMAIL_NOTIFICATION_TEMPLATE notification to validator error: \n error:  ${error instanceof Error ? error.stack : error.message}`
        );
      }

      assignedCount++;

      // Update workload tracking
      const currentWorkload = workloadMap.get(targetValidator.pk) || 0;
      workloadMap.set(targetValidator.pk, currentWorkload + 1);

      // Re-sort validators based on updated workload
      sortedValidators.sort((a, b) => {
        const workloadA = workloadMap.get(a.pk) || 0;
        const workloadB = workloadMap.get(b.pk) || 0;
        return workloadA - workloadB;
      });
    }

    // Log distribution results
    this.logger.debug(`Distributed ${assignedCount} records among ${eligibleValidators.length} validators`);

    // Log individual validator workloads after distribution
    for (const validator of eligibleValidators) {
      this.logger.debug(
        `Validator ${validator.firstName} (ID: ${validator.pk}) now has ${workloadMap.get(validator.pk)} pending records`
      );
    }

    return {
      distributedCount: assignedCount,
      eligibleValidators: eligibleValidators.length,
      recordsPerValidator: Math.ceil(totalRecords / eligibleValidators.length),
    };
  }

  /**
   * Get detailed assignment statistics
   * @returns Statistics about current assignments and workload distribution
   */
  // async getAssignmentStats(validatorRoleName: string): Promise<any> {
  //   // Find the validator role
  //   const validatorRole = await this.roleRepository.findOne({
  //      role: validatorRoleName ,
  //   });
  //
  //   if (!validatorRole) {
  //     throw new Error(`Validator role "${validatorRoleName}" not found`);
  //   }
  //
  //   // Find active validators
  //   const validators = await this.userRepository.getUsersByRolePk(validatorRole.pk);
  //
  //   // Get assignment counts for each validator
  //   const validatorStats = await Promise.all(
  //     validators.map(async (validator) => {
  //       const pendingCount = await this.enrolmentSummaryRepository.count({
  //         where: {
  //           assignedTo: validator.pk,
  //           validationStatus: 'PENDING',
  //         },
  //       });
  //
  //       const completedCount = await this.enrolmentRepository.count({
  //         where: {
  //           assignedTo: validator.pk,
  //           validationStatus: In(['VALIDATED', 'REJECTED']),
  //         },
  //       });
  //
  //       // Calculate daily stats (records processed today)
  //       const today = new Date();
  //       today.setHours(0, 0, 0, 0);
  //
  //       const processedToday = await this.enrolmentRepository.count({
  //         where: {
  //           assignedTo: validator.pk,
  //           validationStatus: In(['VALIDATED', 'REJECTED']),
  //           updatedAt: LessThan(today),
  //         },
  //       });
  //
  //       return {
  //         validatorId: validator.pk,
  //         validatorName: validator.firstName,
  //         pendingCount,
  //         completedCount,
  //         processedToday,
  //         totalAssigned: pendingCount + completedCount,
  //       };
  //     }),
  //   );
  //
  //   // Count unassigned records
  //   const unassignedCount = await this.enrolmentRepository.count({
  //     where: {
  //       assignedTo: IsNull(),
  //       validationStatus: 'PENDING',
  //     },
  //   });
  //
  //   // Calculate workload distribution metrics
  //   const pendingCounts = validatorStats.map(v => v.pendingCount);
  //   const maxWorkload = Math.max(...pendingCounts, 0);
  //   const minWorkload = Math.min(...pendingCounts, 0);
  //   const avgWorkload = pendingCounts.length > 0
  //     ? pendingCounts.reduce((sum, count) => sum + count, 0) / pendingCounts.length
  //     : 0;
  //
  //   // Check for workload imbalance
  //   const imbalanceThreshold = 3; // Consider imbalanced if max-min > threshold
  //   const isImbalanced = (maxWorkload - minWorkload) > imbalanceThreshold;
  //
  //   return {
  //     validators: validatorStats,
  //     unassignedCount,
  //     totalValidators: validators.length,
  //     workloadDistribution: {
  //       min: minWorkload,
  //       max: maxWorkload,
  //       average: avgWorkload.toFixed(1),
  //       isImbalanced,
  //     },
  //   };
  // }

  /**
   * Distribute unassigned computed enrollment records among eligible auditors
   * with workload balancing across multiple runs
   * @returns Count of distributed records
   */
  async distributeComputedEnrolments(): Promise<{
    distributedCount: number;
    eligibleAuditors: number;
    recordsPerAuditor: number;
  }> {
    const auditorRoleName = await this.settingMsLibService.getSetting(SettingsEnumKey.PENCOM_AUDIT_VALIDATOR_ROLE_NAME);

    // Find active users with the auditor role
    const eligibleAuditors = await this.cobraUserRepository.getUsersByRoleNameAndUserType(
      auditorRoleName.split(',').map((role) => role.trim()),
      UserTypeEnum.PENCOM
    );

    console.log('eligibleAuditors:::::: ', eligibleAuditors);

    if (eligibleAuditors.length === 0) {
      this.logger.warn('No eligible auditors found');
      return { distributedCount: 0, eligibleAuditors: 0, recordsPerAuditor: 0 };
    }

    // Find unassigned computed records
    const unassignedRecords = await this.enrolmentSummaryRepository.getUnassignedComputedRecords();

    console.log('unassignedComputedRecords:::::: ', unassignedRecords);

    if (unassignedRecords.length === 0) {
      this.logger.debug('No unassigned computed records found');
      return {
        distributedCount: 0,
        eligibleAuditors: eligibleAuditors.length,
        recordsPerAuditor: 0,
      };
    }

    // Get current workload for each auditor
    const workloadMap = new Map<string, number>();

    // Initialize all auditors with zero workload
    eligibleAuditors.forEach((auditor) => {
      workloadMap.set(auditor.emailAddress, 0);
    });

    // Get current pending record counts for each auditor
    const auditorWorkloads = await this.enrolmentSummaryRepository.getAuditorWorkload();

    // Update workload map with current counts
    auditorWorkloads.forEach((workload) => {
      if (workloadMap.has(workload.auditorEmail)) {
        workloadMap.set(workload.auditorEmail, workload.recordCount);
      }
    });

    // Sort auditors by current workload (ascending)
    const sortedAuditors = [...eligibleAuditors].sort((a, b) => {
      const workloadA = workloadMap.get(a.emailAddress) || 0;
      const workloadB = workloadMap.get(b.emailAddress) || 0;
      return workloadA - workloadB;
    });

    // Distribute records to auditors with lowest current workload
    let assignedCount = 0;
    const now = new Date();
    const totalRecords = unassignedRecords.length;

    // Create batches based on total auditors and records
    for (const record of unassignedRecords) {
      // Get auditor with current lowest workload
      const targetAuditor = sortedAuditors[0];

      // Assign record to this auditor
      await this.enrolmentSummaryRepository.findOneAndUpdate(
        { pk: record.pk },
        {
          auditorAssignedTo: targetAuditor.emailAddress,
          auditorAssignmentDate: now,
        }
      );

      // send email
      const enrolledBy = await record.enrolledBy;
      const placeholders: Record<string, string> = {};
      placeholders['userName'] = targetAuditor.firstName;
      placeholders['rsaPin'] = record.rsaPin;
      placeholders['submittedBy'] = `${enrolledBy.firstName} ${enrolledBy.surname}`;
      placeholders['submissionDate'] = record.enrolmentDate
        ? format(record.enrolmentDate, 'yyyy-MM-dd HH:mm:ss')
        : 'N/A';

      const sendNotificationTemplateDto = new SendNotificationTemplateDto();
      sendNotificationTemplateDto.emailRecipients = [targetAuditor.emailAddress];
      sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.AUDIT_VALIDATOR_REVIEW_REQUEST;
      sendNotificationTemplateDto.placeholders = placeholders;

      console.log('sendNotificationTemplateDto:::: ', sendNotificationTemplateDto);
      try {
        await firstValueFrom(
          this.notificationClient.send(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            sendNotificationTemplateDto
          )
        );
      } catch (error) {
        console.log('error', error);
        this.logger.error(
          `Failed to send AUDIT_VALIDATOR_REVIEW_REQUEST notification to auditor error: \n error:  ${error instanceof Error ? error.stack : error.message}`
        );
      }

      assignedCount++;

      // Update workload tracking
      const currentWorkload = workloadMap.get(targetAuditor.emailAddress) || 0;
      workloadMap.set(targetAuditor.emailAddress, currentWorkload + 1);

      // Re-sort auditors based on updated workload
      sortedAuditors.sort((a, b) => {
        const workloadA = workloadMap.get(a.emailAddress) || 0;
        const workloadB = workloadMap.get(b.emailAddress) || 0;
        return workloadA - workloadB;
      });
    }

    // Log distribution results
    this.logger.debug(`Distributed ${assignedCount} computed records among ${eligibleAuditors.length} auditors`);

    // Log individual auditor workloads after distribution
    for (const auditor of eligibleAuditors) {
      this.logger.debug(
        `Auditor ${auditor.firstName} (ID: ${auditor.pk}) now has ${workloadMap.get(auditor.emailAddress)} pending records`
      );
    }

    return {
      distributedCount: assignedCount,
      eligibleAuditors: eligibleAuditors.length,
      recordsPerAuditor: Math.ceil(totalRecords / eligibleAuditors.length),
    };
  }

  /**
   * Rebalance workload among validators if significant imbalance is detected
   * @param validatorRoleName The role name for validator users
   * @param threshold Threshold difference that triggers rebalancing
   */
  /*async rebalanceWorkload(validatorRoleName: string, threshold: number = 5): Promise<any> {
    const stats = await this.getAssignmentStats(validatorRoleName);

    // Check if rebalancing is needed
    if (!stats.workloadDistribution.isImbalanced) {
      return {
        rebalanced: false,
        message: 'Workload is already balanced, no action taken',
      };
    }

    // Find validators with highest and lowest workloads
    const sortedByWorkload = [...stats.validators].sort((a, b) => b.pendingCount - a.pendingCount);

    const overloadedValidators = sortedByWorkload.filter(
      v => v.pendingCount > stats.workloadDistribution.average + threshold / 2,
    );

    const underloadedValidators = sortedByWorkload.filter(
      v => v.pendingCount < stats.workloadDistribution.average - threshold / 2,
    );

    if (overloadedValidators.length === 0 || underloadedValidators.length === 0) {
      return {
        rebalanced: false,
        message: 'Insufficient validators for rebalancing',
      };
    }

    let recordsReassigned = 0;

    // Process each overloaded validator
    for (const overloaded of overloadedValidators) {
      // Calculate how many records to reassign
      const excessRecords = Math.floor(
        overloaded.pendingCount - stats.workloadDistribution.average,
      );

      if (excessRecords <= 0) continue;

      // Find records to reassign (oldest assignments first)
      const recordsToReassign = await this.enrolmentRepository.find({
        where: {
          assignedTo: overloaded.validatorId,
          validationStatus: 'PENDING',
        },
        order: { assignmentDate: 'ASC' },
        take: excessRecords,
      });

      if (recordsToReassign.length === 0) continue;

      // Reassign to underloaded validators
      for (let i = 0; i < recordsToReassign.length; i++) {
        const record = recordsToReassign[i];
        const targetValidator = underloadedValidators[i % underloadedValidators.length];

        await this.enrolmentRepository.update(
          { pk: record.pk },
          {
            assignedTo: targetValidator.validatorId,
            assignmentDate: new Date(),
          },
        );

        recordsReassigned++;
      }
    }

    return {
      rebalanced: true,
      recordsReassigned,
      message: `Rebalanced ${recordsReassigned} records among validators`,
    };
  }*/
}
