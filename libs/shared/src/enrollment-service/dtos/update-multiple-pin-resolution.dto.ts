import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, IsEnum, IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString, ValidateNested } from 'class-validator';

export enum MultiplePinResolutionAction {
  NDMD_APPROVED = 'NDMD_APPROVED',
  UPLOAD_TRANSACTION_HISTORY = 'UPLOAD_TRANSACTION_HISTORY',
  REJECT_TRANSACTION_HISTORY = 'REJECT_TRANSACTION_HISTORY',
  REQUEST_PAYMENT = 'REQUEST_PAYMENT',
  PAYMENT_CONFIRMED = 'PAYMENT_CONFIRMED',
  CANCEL_REQUEST = 'CANCEL_REQUEST',
  REJECT = 'REJECT',
}

import { Type } from 'class-transformer';

export class PinResolutionDocumentDto {
  @ApiProperty({ description: 'Document Name', example: 'document.pdf' })
  @IsString()
  @IsNotEmpty()
  documentName: string;

  @ApiProperty({ description: 'Document Type', example: 'application/pdf' })
  @IsString()
  @IsNotEmpty()
  documentType: string;

  @ApiProperty({ description: 'Document Content', example: 'base64 encoded string' })
  @IsString()
  @IsNotEmpty()
  documentContent: string;
}
export class PinUpdateDto {
  @ApiProperty({
    description: 'PK of the pin',
    example: '74',
  })
  @IsNumber()
  @IsNotEmpty()
  pk: number;

  @ApiProperty({
    description: 'Reason for the update',
    example: 'Rejected for inaccurate transaction History',
  })
  @IsString()
  @IsOptional()
  reason?: string;
}

export class UpdateMultiplePinResolutionDto {
  @ApiProperty({
    description: 'PK of the multiple pin resolution request',
    example: '74',
  })
  @IsNumber()
  @IsNotEmpty()
  pk: number;

  @ApiProperty({
    description: 'Action to perform on the request',
    enum: MultiplePinResolutionAction,
    example: 'NDMD_APPROVED',
  })
  @IsEnum(MultiplePinResolutionAction)
  @IsNotEmpty()
  action: MultiplePinResolutionAction;

  @ApiProperty({
    description: 'Comment for the action',
    example: 'Approved by NDMD',
  })
  @IsString()
  @IsNotEmpty()
  feedback: string;

  @ApiProperty({
    description: 'Valid PIN',
    example: 'PIN123',
  })
  @IsString()
  @IsOptional()
  validPin?: string;

  @ApiProperty({
    description: 'List of pins to update',
    type: PinUpdateDto,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PinUpdateDto)
  thRejectedPins?: { pk: number; reason?: string }[];

  @ApiProperty({
    description: 'Supporting documents',
    type: PinResolutionDocumentDto,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PinResolutionDocumentDto)
  documents?: PinResolutionDocumentDto[];
}
