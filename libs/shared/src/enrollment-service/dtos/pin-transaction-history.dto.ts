import { ApiProperty } from '@nestjs/swagger';

export class PinTransactionHistoryDto {
  @ApiProperty({ description: 'RSA PIN' })
  pin: string;

  @ApiProperty({ description: 'Whether transaction history has been uploaded' })
  hasTransactionHistory: boolean;

  @ApiProperty({ description: 'Transaction history document in base64 format', required: false })
  transactionHistory?: string;

  @ApiProperty({ description: 'Document name', required: false })
  documentName?: string;
}
