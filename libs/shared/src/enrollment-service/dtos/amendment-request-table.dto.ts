import { ApiProperty } from '@nestjs/swagger';
import { AmendmentRequestStatus } from '../entities/amendment-request.entity';

export class AmendmentRequestTableDto {
  @ApiProperty()
  pk: string;

  @ApiProperty()
  rsaPin: string;

  @ApiProperty()
  reason: string;

  @ApiProperty()
  comment: string;

  @ApiProperty({ enum: AmendmentRequestStatus })
  status: AmendmentRequestStatus;

  @ApiProperty()
  createDate: Date;

  @ApiProperty()
  pfaCode: string;

  @ApiProperty()
  requestedBy: string;

  @ApiProperty()
  firstName: string;

  @ApiProperty()
  lastName: string;
}
