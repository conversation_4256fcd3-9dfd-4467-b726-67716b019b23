import { IsEnum, IsNotEmpty, IsNumber, IsString, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { AmendmentRequestStatus } from '../entities/amendment-request.entity';

export class UpdateAmendmentRequestDto {
  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  pk: number;

  @ApiProperty({ enum: AmendmentRequestStatus })
  @IsEnum(AmendmentRequestStatus)
  @IsNotEmpty()
  status: AmendmentRequestStatus;

  @ApiProperty()
  @IsOptional()
  @IsString()
  comment: string;
}
