import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAmendmentRequestDto {
  @ApiProperty({
    description: 'RSA PIN of the employee',
    example: '12345678901',
  })
  @IsNotEmpty()
  @IsString()
  rsaPin: string;

  @ApiProperty({
    description: 'Reason for amendment',
    example: 'Incorrect date of birth',
  })
  @IsNotEmpty()
  @IsString()
  reason: string;
}
