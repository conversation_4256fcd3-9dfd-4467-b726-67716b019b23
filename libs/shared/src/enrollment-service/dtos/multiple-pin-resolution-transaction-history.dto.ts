import { ApiProperty } from '@nestjs/swagger';

export class MultiplePinResolutionTransactionHistoryDto {
  @ApiProperty({ description: 'Primary key of the transaction history record' })
  pk: number;

  @ApiProperty({ description: 'PENCOM remittance amount' })
  pencomRemitAmount: number;

  @ApiProperty({ description: 'PENCOM investment income' })
  pencomInvestmentIncome: number;

  @ApiProperty({ description: 'PENCOM total' })
  pencomTotal: number;

  @ApiProperty({ description: 'Shortfall contribution' })
  shortfallContribution: number;

  @ApiProperty({ description: 'Shortfall investment income' })
  shortfallInvestmentIncome: number;

  @ApiProperty({ description: 'Shortfall total' })
  shortfallTotal: number;

  @ApiProperty({ description: 'IPPIS amount' })
  ippisAmount: number;

  @ApiProperty({ description: 'IPPIS investment income' })
  ippisInvestmentIncome: number;

  @ApiProperty({ description: 'IPPIS total' })
  ippisTotal: number;
}
