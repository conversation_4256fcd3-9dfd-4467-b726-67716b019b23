import { ApiProperty } from '@nestjs/swagger';
import { MultiplePinResolutionTransactionHistoryDto } from './multiple-pin-resolution-transaction-history.dto';

export class PinMemoDto {
  @ApiProperty({ description: 'Name of the document' })
  documentName: string;

  @ApiProperty({ description: 'Document data in base64 format' })
  documentData: string;

  @ApiProperty({ description: 'Document extension' })
  documentExt: string;
}

export class MultiplePinResolutionPinDto {
  @ApiProperty({ description: 'Primary key of the PIN record' })
  pk: number;

  @ApiProperty({ description: 'The RSA PIN' })
  pin: string;

  @ApiProperty({ description: 'Whether this is the valid PIN' })
  isValid: boolean;

  @ApiProperty({ description: 'Whether transaction history has been uploaded for this PIN' })
  hasTransactionHistory: boolean;

  @ApiProperty({ description: 'PFA code associated with the PIN' })
  pfaCode: string;

  @ApiProperty({ description: 'PFA name' })
  pfaName?: string;

  @ApiProperty({ description: 'Memo document for the PIN', type: PinMemoDto, nullable: true })
  memo: PinMemoDto | null;

  @ApiProperty({ description: 'Payment confirmation status of the PIN' })
  paymentConfirmationStatus: string;

  @ApiProperty({ description: 'Date when transaction history was requested for the PIN', nullable: true })
  thRequestedDate?: Date;

  @ApiProperty({ description: 'Date when transaction history was received for the PIN', nullable: true })
  thReceivedDate?: Date;

  @ApiProperty({
    description: 'Transaction history records for the PIN',
    type: [MultiplePinResolutionTransactionHistoryDto],
    nullable: true,
  })
  transactionHistory?: MultiplePinResolutionTransactionHistoryDto[];

  @ApiProperty({ description: 'First name of the PIN holder' })
  firstName?: string;

  @ApiProperty({ description: 'Surname of the PIN holder' })
  surname?: string;

  @ApiProperty({ description: 'Whether the PIN is enrolled' })
  isEnrolled: boolean;

  @ApiProperty({ description: 'Whether the PIN is a crs PIN' })
  crsStatus: boolean;

  @ApiProperty({ description: 'Whether the PIN is a ecr PIN' })
  ecrStatus: boolean;

  @ApiProperty({ description: 'Employer code' })
  employerCode: string;

  @ApiProperty({ description: 'Batch ID' })
  batchId?: string;

  @ApiProperty({ description: 'Valid Pin Payment Reference' })
  validPinPaymentRef?: string;

  @ApiProperty({ description: 'Payment Reference' })
  paymentRef?: string;
}

export class MultiplePinResolutionPinResponseDto {
  @ApiProperty({ description: 'Batch ID' })
  batchId: string;

  @ApiProperty({ description: 'Status of the request' })
  status: string;

  @ApiProperty({ description: 'List of pins', type: [MultiplePinResolutionPinDto] })
  pins: MultiplePinResolutionPinDto[];
}
