import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { MultiplePinResolutionRequest } from '../entities/multiple-pin-resolution-req.entity';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { BaseResponseWithNoCountInfo } from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums/ResponseCodeEnum';
import { AbstractRepository } from '@app/shared/database';
import { PinoLogger } from 'nestjs-pino';
import { MultiplePinResolutionTableDto } from '@app/shared/enrollment-service/dtos/multiple-pin-resolution-table.dto';
@Injectable()
export class MultiplePinResolutionRequestRepository extends AbstractRepository<MultiplePinResolutionRequest> {
  protected logger: PinoLogger;

  constructor(
    @InjectRepository(MultiplePinResolutionRequest)
    private readonly repository: Repository<MultiplePinResolutionRequest>,
    entityManager: EntityManager
  ) {
    super(repository, entityManager);
  }

  async getDataTable(filter: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<MultiplePinResolutionTableDto>> {
    const query = this.repository
      .createQueryBuilder('request')
      .leftJoinAndSelect('request.requestedBy', 'requestedBy')
      .select([
        'request.pk as "pk"',
        'request.batchId as "batchId"',
        'request.pinCount as "numberOfPins"',
        'request.createDate as "requestDate"',
        'request.status as "status"',
        'requestedBy.firstName as "firstName"',
        'requestedBy.surname as "lastName"',
        'requestedBy.emailAddress as "emailAddress"',
      ]);

    // Handle filters
    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          switch (key) {
            case 'batchId':
              query.andWhere('LOWER(request.batchId) LIKE LOWER(:batchId)', { batchId: `%${value}%` }); // Changed from requestId to batchId
              break;
            case 'createDate':
              query.andWhere("TO_CHAR(request.createDate, 'YYYY-MM-DD') = :createDate", { createDate: value });
              break;
            case 'status':
              if (Array.isArray(value) && value.length > 0) {
                query.andWhere('request.status IN (:...statuses)', { statuses: value });
              } else {
                query.andWhere('request.status = :status', { status: value });
              }
              break;
            case 'pfaCode':
              query.andWhere('LOWER(request.pfaCodes) LIKE LOWER(:pfaCode)', { pfaCode: `%${value}%` });
              break;
            case 'requestedBy':
              query.andWhere(
                '(LOWER(requestedBy.emailAddress) LIKE LOWER(:email) OR ' +
                  'LOWER(requestedBy.firstName) LIKE LOWER(:name) OR ' +
                  'LOWER(requestedBy.surname) LIKE LOWER(:name))', // Changed from lastName to surname
                { email: `%${value}%`, name: `%${value}%` }
              );
              break;
          }
        }
      });
    }
    // Always order by createDate DESC by default
    query.orderBy('request.createDate', 'DESC');

    const limit = Math.max(filter.limit || 10, 1);
    const page = Math.max(filter.page || 1, 1);
    const offset = (page - 1) * limit;

    const data = await query.offset(offset).limit(limit).getRawMany();

    // Transform the data
    const content = data.map((item) => ({
      pk: item.pk,
      batchId: item.batchId,
      numberOfPins: item.numberOfPins,
      requestDate: item.requestDate,
      firstName: item.firstName,
      lastName: item.lastName,
      requestedBy: item.emailAddress,
      status: item.status,
    }));

    // Prepare response
    const response = new BaseResponseWithNoCountInfo<MultiplePinResolutionTableDto>(ResponseCodeEnum.SUCCESS);
    response.content = content;
    response.hasNext = data.length === limit;
    response.hasPrevious = page > 1;

    return response;
  }
}
