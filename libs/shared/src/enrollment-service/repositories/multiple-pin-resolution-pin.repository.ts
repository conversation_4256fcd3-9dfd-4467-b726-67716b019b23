import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PinoLogger } from 'nestjs-pino';
import { EntityManager, Repository } from 'typeorm';
import { MultiplePinResolutionPin } from '../entities/multiple-pin-resolution-pin.entity';
import { MultiplePinResolutionStatusEnum } from '@app/shared/enrollment-service/enums/multiple-pin-resolution-status.enum';
import { MultiplePinResolutionPinDto } from '@app/shared/enrollment-service/dtos/multiple-pin-resolution-pin.dto';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { BaseResponseWithNoCountInfo } from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums';

@Injectable()
export class MultiplePinResolutionPinRepository extends AbstractRepository<MultiplePinResolutionPin> {
  constructor(
    @InjectRepository(MultiplePinResolutionPin)
    private readonly repository: Repository<MultiplePinResolutionPin>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(repository, entityManager);
  }

  serializePin(pin: MultiplePinResolutionPin): MultiplePinResolutionPinDto {
    return {
      pk: pin.pk,
      pin: pin.pin,
      firstName: pin.firstName,
      surname: pin.surname,
      isValid: pin.isValid,
      hasTransactionHistory: pin.hasTransactionHistory,
      pfaCode: pin.pfaCode,
      pfaName: pin.pfaName,
      isEnrolled: pin.isEnrolled,
      crsStatus: pin.crsStatus,
      ecrStatus: pin.ecrsStatus,
      employerCode: pin.mdaCode,
      memo: null,
      paymentConfirmationStatus: pin.paymentConfirmationStatus,
    };
  }

  async findAndUpdateValidPin(requestPk: number, validPin: string): Promise<MultiplePinResolutionPin> {
    const pinEntity = await this.repository
      .createQueryBuilder('pin')
      .innerJoinAndSelect('pin.request', 'request')
      .where('request.pk = :requestPk', { requestPk })
      .andWhere('pin.pin = :validPin', { validPin })
      .getOne();

    if (pinEntity) {
      pinEntity.isValid = true;
      return await this.repository.save(pinEntity);
    }

    return null;
  }

  async doesPinHaveActiveStatusOrReconciled(pin: string): Promise<MultiplePinResolutionPin> {
    return await this.repository
      .createQueryBuilder('pin')
      .innerJoinAndSelect('pin.request', 'request')
      .where('pin.pin = :pin', { pin })
      .andWhere('pin.isValid =:isValid', { isValid: false })
      .andWhere('request.status NOT IN (:...completedStatuses)', {
        completedStatuses: [MultiplePinResolutionStatusEnum.NDMD_REJECTED, MultiplePinResolutionStatusEnum.CANCELLED],
      })
      .getOne();
  }

  async findPinsByRequestIdAndPfaCode(requestId: string, pfaCode?: string): Promise<MultiplePinResolutionPinDto[]> {
    const query = this.repository
      .createQueryBuilder('pin')
      .innerJoin('pin.request', 'request')
      .where('request.batchId = :requestId', { requestId })
      .select([
        'pin.pk as "pk"',
        'pin.pin as "pin"',
        'pin.firstName as "firstName"',
        'pin.surname as "surname"',
        'pin.pfaCode as "pfaCode"',
        'pin.pfaName as "pfaName"',
        'pin.mdaCode as "employerCode"',
        'pin.isValid as "isValid"',
        'pin.isEnrolled as "isEnrolled"',
        'pin.crsStatus as "crsStatus"',
        'pin.ecrsStatus as "ecrsStatus"',
        'pin.hasTransactionHistory as "hasTransactionHistory"',
        'pin.eligibilityStatus as "eligibilityStatus"',
        'pin.cpaBalance as "cpaBalance"',
        'pin.validPinBalance as "validPinBalance"',
        'pin.paymentConfirmationStatus as "paymentConfirmationStatus"',
        'pin.thRequestedDate as "thRequestedDate"',
        'pin.thReceivedDate as "thReceivedDate"',
      ]);

    if (pfaCode) {
      query.andWhere('pin.pfaCode = :pfaCode', { pfaCode });
    }

    return await query.getRawMany();
  }

  async getDataTable(filter: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<MultiplePinResolutionPinDto>> {
    const query = this.repository
      .createQueryBuilder('pin')
      .innerJoin('pin.request', 'request')
      .select([
        'pin.pk as "pk"',
        'pin.pin as "pin"',
        'pin.firstName as "firstName"',
        'pin.surname as "surname"',
        'pin.pfaCode as "pfaCode"',
        'pin.pfaName as "pfaName"',
        'pin.mdaCode as "employerCode"',
        'pin.isValid as "isValid"',
        'pin.isEnrolled as "isEnrolled"',
        'pin.crsStatus as "crsStatus"',
        'pin.ecrsStatus as "ecrsStatus"',
        'pin.hasTransactionHistory as "hasTransactionHistory"',
        'pin.eligibilityStatus as "eligibilityStatus"',
        'pin.cpaBalance as "cpaBalance"',
        'pin.validPinBalance as "validPinBalance"',
        'pin.paymentConfirmationStatus as "paymentConfirmationStatus"',
        'pin.thRequestedDate as "thRequestedDate"',
        'pin.thReceivedDate as "thReceivedDate"',
        'pin.paymentRef as "paymentRef"',
        'pin.validPinPaymentRef as "validPinPaymentRef"',
        'request.status as "requestStatus"',
        'request.batchId as "batchId"',
      ]);

      query.where('pin.paymentConfirmationStatus = :status', { status: 'CONFIRMED' });

      if (filter.filters?.requestStatus == MultiplePinResolutionStatusEnum.PENDING_RECONCILIATION) {
        query.andWhere('pin.isValid = :isValid', { isValid: false });
      }

    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          switch (key) {
            case 'batchId':
              query.andWhere('LOWER(request.batchId) LIKE LOWER(:batchId)', { batchId: `%${value}%` });
              break;
            case 'employerCode':
              query.andWhere('LOWER(pin.mdaCode) LIKE LOWER(:employerCode)', { employerCode: `%${value}%` });
              break;
            case 'requestStatus':
              query.andWhere('request.status = :requestStatus', { requestStatus: value });
              break;
            default:
              query.andWhere(`LOWER(pin.${key}) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
              break;
          }
        }
      });
    }

    const limit = filter.limit || 10;
    const page = filter.page || 1;
    const offset = (page - 1) * limit;
    query.offset(offset).limit(limit);

    const rawResults = await query.getRawMany();
    const resp = new BaseResponseWithNoCountInfo<MultiplePinResolutionPinDto>(ResponseCodeEnum.SUCCESS);
    resp.content = rawResults;
    resp.hasNext = rawResults.length === limit;
    resp.hasPrevious = page > 1;

    return resp;
  }

  /**
   * Checks if a PIN has been part of a reconciled request
   * @param pin The RSA PIN to check
   * @returns The MultiplePinResolutionPin entity if found, null otherwise
   */
  async findReconciledPin(pin: string): Promise<MultiplePinResolutionPin | null> {
    return await this.repository
      .createQueryBuilder('pin')
      .innerJoinAndSelect('pin.request', 'request')
      .where('pin.pin = :pin', { pin })
      .andWhere('request.status = :status', {
        status: MultiplePinResolutionStatusEnum.RECONCILED,
      })
      .getOne();
  }
}
