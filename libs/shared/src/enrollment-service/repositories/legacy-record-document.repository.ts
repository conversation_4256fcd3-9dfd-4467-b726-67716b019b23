import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { LegacyRecordDocument } from '../entities/legacy-record-document.entity';

@Injectable()
export class LegacyRecordDocumentRepository extends AbstractRepository<LegacyRecordDocument> {
  constructor(
    @InjectRepository(LegacyRecordDocument)
    private readonly legacyRecordDocumentRepository: Repository<LegacyRecordDocument>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(legacyRecordDocumentRepository, entityManager);
  }

  async findByLegacyRecordId(legacyRecordId: number): Promise<LegacyRecordDocument> {
    return this.legacyRecordDocumentRepository.findOne({
      where: { legacyRecord: { pk: legacyRecordId } },
    });
  }
}
