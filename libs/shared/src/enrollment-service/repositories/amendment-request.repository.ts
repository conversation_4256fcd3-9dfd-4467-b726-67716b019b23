import { Injectable } from '@nestjs/common';
import { AmendmentRequest } from '../entities/amendment-request.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { BaseResponseWithNoCountInfo } from '@app/shared/dto/response/base-response-with-content';
import { AbstractRepository } from '@app/shared/database';
import { AmendmentRequestTableDto } from '@app/shared/enrollment-service/dtos/amendment-request-table.dto';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class AmendmentRequestRepository extends AbstractRepository<AmendmentRequest> {
  protected logger: PinoLogger;
  constructor(
    @InjectRepository(AmendmentRequest)
    private readonly amendmentRequestRepository: Repository<AmendmentRequest>,
    entityManager: EntityManager
  ) {
    super(amendmentRequestRepository, entityManager);
  }

  async getDataTable(filter: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<AmendmentRequestTableDto>> {
    const query = this.amendmentRequestRepository
      .createQueryBuilder('request')
      .leftJoinAndSelect('request.requestedBy', 'requestedBy')
      .select([
        'request.pk as "pk"',
        'request.rsaPin as "rsaPin"',
        'request.reason as "reason"',
        'request.comment as "comment"',
        'request.status as "status"',
        'request.createDate as "createDate"',
        'request.pfaCode as "pfaCode"',
        'requestedBy.firstName as "firstName"',
        'requestedBy.surname as "lastName"',
        'requestedBy.emailAddress as "emailAddress"',
      ]);

    // Handle filters
    if (filter.filters) {
      Object.entries(filter.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          switch (key) {
            case 'rsaPin':
              query.andWhere('LOWER(request.rsaPin) LIKE LOWER(:rsaPin)', {
                rsaPin: `%${value}%`,
              });
              break;
            case 'status':
              if (Array.isArray(value) && value.length > 0) {
                query.andWhere('request.status IN (:...statuses)', {
                  statuses: value,
                });
              } else {
                query.andWhere('request.status = :status', {
                  status: value,
                });
              }
              break;
            case 'requestedBy':
              query.andWhere('LOWER(requestedBy.emailAddress) LIKE LOWER(:email)', {
                email: `%${value}%`,
              });
              break;
            case 'createDate':
              query.andWhere("TO_CHAR(request.createDate, 'YYYY-MM-DD') = :createDate", {
                createDate: value,
              });
              break;
            case 'firstName':
              query.andWhere('LOWER(requestedBy.firstName) LIKE LOWER(:firstName)', {
                firstName: `%${value}%`,
              });
              break;
            case 'lastName':
              query.andWhere('LOWER(requestedBy.surname) LIKE LOWER(:lastName)', {
                lastName: `%${value}%`,
              });
              break;
          } 
        }
      });
    }

    // Always order by createDate DESC by default
    query.orderBy('request.createDate', 'DESC');

    const limit = Math.max(filter.limit || 10, 1);
    const page = Math.max(filter.page || 1, 1);
    const offset = (page - 1) * limit;

    // Fetch one extra record to determine if there's a next page
    const data = await query
      .offset(offset)
      .limit(limit + 1)
      .getRawMany();

    // Check if we have more records
    const hasNext = data.length > limit;
    // Remove the extra record if it exists
    const results = hasNext ? data.slice(0, limit) : data;

    // Transform the data
    const content = results.map((item) => ({
      pk: item.pk,
      rsaPin: item.rsaPin,
      reason: item.reason,
      comment: item.comment,
      status: item.status,
      createDate: item.createDate,
      pfaCode: item.pfaCode,
      requestedBy: item.emailAddress,
      firstName: item.firstName,
      lastName: item.lastName,
    }));

    const response = new BaseResponseWithNoCountInfo<AmendmentRequestTableDto>();
    response.content = content;
    response.hasNext = hasNext;
    response.hasPrevious = page > 1;

    return response;
  }
}
