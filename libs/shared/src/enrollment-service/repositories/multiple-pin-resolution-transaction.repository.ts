import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { AbstractRepository } from '@app/shared/database';
import { PinoLogger } from 'nestjs-pino';
import { MultiplePinResolutionTransactionHistory } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-transaction.entity';

@Injectable()
export class MultiplePinResolutionTransactionHistoryRepository extends AbstractRepository<MultiplePinResolutionTransactionHistory> {
  constructor(
    @InjectRepository(MultiplePinResolutionTransactionHistory)
    private readonly transactionRepository: Repository<MultiplePinResolutionTransactionHistory>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(transactionRepository, entityManager);
  }

  async saveBatch(
    transactions: MultiplePinResolutionTransactionHistory[]
  ): Promise<MultiplePinResolutionTransactionHistory[]> {
    return await this.entityManager.save(transactions, { chunk: 100 }); // Save in chunks of 100
  }

  serializeEntity(transaction: MultiplePinResolutionTransactionHistory): any {
    return {
      pk: transaction.pk,
      rsaHolderFirstName: transaction.rsaHolderFirstName,
      rsaHolderLastName: transaction.rsaHolderLastName,
    };
  }

  async findByRequestId(requestId: string): Promise<MultiplePinResolutionTransactionHistory[]> {
    return await this.transactionRepository
      .createQueryBuilder('transaction')
      .innerJoin('transaction.request', 'request')
      .where('request.batchId = :requestId', { requestId })
      .getMany();
  }
}
