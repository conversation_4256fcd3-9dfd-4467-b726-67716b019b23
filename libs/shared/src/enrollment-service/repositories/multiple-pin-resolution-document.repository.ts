import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { AbstractRepository } from '@app/shared/database';
import { PinoLogger } from 'nestjs-pino';
import { MultiplePinResolutionDocument } from '../entities/multiple-pin-resolution-document.entity';

@Injectable()
export class MultiplePinResolutionDocumentRepository extends AbstractRepository<MultiplePinResolutionDocument> {
  constructor(
    @InjectRepository(MultiplePinResolutionDocument)
    private readonly documentRepository: Repository<MultiplePinResolutionDocument>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(documentRepository, entityManager);
  }

  async findByRequestId(requestPk: number): Promise<MultiplePinResolutionDocument[]> {
    return await this.documentRepository
      .createQueryBuilder('doc')
      .where('doc.request.pk = :requestPk', { requestPk })
      .getMany();
  }

  async findByRequestIdAndProcessType(batchId: string, processType: string): Promise<MultiplePinResolutionDocument[]> {
    return await this.documentRepository
      .createQueryBuilder('doc')
      .innerJoin('doc.request', 'request')
      .leftJoinAndSelect('doc.pin', 'pin')
      .where('request.batchId = :batchId', { batchId })
      .andWhere('doc.processType = :processType', { processType })
      .getMany();
  }
}
