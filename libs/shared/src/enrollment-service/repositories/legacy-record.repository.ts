import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Entity<PERSON>anager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { LegacyRecord } from '../entities/legacy-record.entity';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { BaseResponseWithNoCountInfo } from '@app/shared/dto/response/base-response-with-content';
import { LegacyRecordDto } from '@app/shared/enrollment-service/dtos/legacy-record.dto';
import { ResponseCodeEnum } from '@app/shared/enums';

@Injectable()
export class LegacyRecordRepository extends AbstractRepository<LegacyRecord> {
  constructor(
    @InjectRepository(LegacyRecord)
    private readonly legacyRecordRepository: Repository<LegacyRecord>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(legacyRecordRepository, entityManager);
  }

  async getRecords(searchDto: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<LegacyRecordDto>> {
    const query = this.legacyRecordRepository
      .createQueryBuilder('legacyRecord')
      .select([
        'legacyRecord.rsaPin AS "rsaPin"',
        'legacyRecord.nin AS "nin"',
        'legacyRecord.firstName AS "firstName"',
        'legacyRecord.middleName AS "middleName"',
        'legacyRecord.surname AS "surname"',
        'legacyRecord.gender AS "gender"',
        'legacyRecord.dob AS "dob"',
        'legacyRecord.contactAddress AS "contactAddress"',
        'legacyRecord.contactPhone AS "contactPhone"',
        'legacyRecord.alternatePhone AS "alternatePhone"',
        'legacyRecord.email AS "email"',
        'legacyRecord.employerName AS "employerName"',
        'legacyRecord.dofa AS "dofa"',
        'legacyRecord.edor AS "edor"',
        'legacyRecord.employerCode AS "employerCode"',
      ]);

    if (searchDto.filters) {
      Object.entries(searchDto.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (['dob', 'dofa', 'edor'].includes(key)) {
            query.andWhere(`TO_CHAR(legacyRecord.${key}, 'YYYY-MM-DD') = :${key}`, { [key]: value });
          } else {
            query.andWhere(`LOWER(legacyRecord.${key}) LIKE LOWER(:${key})`, { [key]: `%${value}%` });
          }
        }
      });
    }

    const limit = Math.max(searchDto.limit || 10, 1);
    const page = Math.max(searchDto.page || 1, 1);
    const offset = (page - 1) * limit;

    const records = await query.offset(offset).limit(limit).getRawMany();

    const response = new BaseResponseWithNoCountInfo<LegacyRecordDto>(ResponseCodeEnum.SUCCESS);
    response.content = records;
    response.hasNext = records.length === searchDto.limit;
    response.hasPrevious = searchDto.page > 1;

    return response;
  }

  async existsByRsaPin(rsaPin: string): Promise<boolean> {
    return !!(await this.legacyRecordRepository
      .createQueryBuilder('legacyRecord')
      .where('legacyRecord.rsaPin = :rsaPin', { rsaPin })
      .getCount());
  }
}
