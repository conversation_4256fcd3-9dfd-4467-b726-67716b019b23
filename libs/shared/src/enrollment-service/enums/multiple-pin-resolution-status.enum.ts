export enum MultiplePinResolutionStatusEnum {
  INITIATED = 'INITIATED',
  PENDING_NDMD = 'PENDING_NDMD',
  REVIEWED = 'REVIEWED',
  PENDING_PFA_UPLOAD = 'PENDING_PFA_UPLOAD',
  COMPUTED = 'COMPUTED',
  PENDING_PAYMENT_CONFIRMATION = 'PENDING_PAYMENT_CONFIRMATION',
  PAYMENT_CONFIRMED = 'PAYMENT_CONFIRMED',
  RESOLVED = 'RESOLVED',
  CANCELLED = 'CANCELLED',
  NDMD_REJECTED = 'NDMD_REJECTED',
  AWAITING_COMPUTATION = 'AWAITING_COMPUTATION',
  PENDING_RECONCILIATION = 'PENDING_RECONCILIATION',
  PROCESSING_PAYMENT_CONFIRMATION = 'PROCESSING_PAYMENT_CONFIRMATION',
  RECONCILED = 'RECONCILED',
}
