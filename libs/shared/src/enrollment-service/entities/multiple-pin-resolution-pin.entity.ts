import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { MultiplePinResolutionRequest } from './multiple-pin-resolution-req.entity';

@Entity('TBC_MULTIPLE_PIN_RESOLUTION_PIN')
export class MultiplePinResolutionPin extends AbstractEntity<MultiplePinResolutionPin> {
  @Column({ name: 'PIN', nullable: false })
  pin: string;

  @Column({ name: 'FIRST_NAME', nullable: true })
  firstName?: string;

  @Column({ name: 'SURNA<PERSON>', nullable: true })
  surname?: string;

  @Column({ name: 'PFA_CODE', nullable: false })
  pfaCode: string;

  @Column({ name: 'PFA_NAME', nullable: true })
  pfaName?: string;

  @Column({
    name: 'IS_ENROLLED',
    type: 'number',
    precision: 1,
    nullable: false,
    default: 0,
    transformer: {
      // Transform database value (0/1) to boolean (false/true)
      from: (value: number): boolean => value === 1,
      to: (value: boolean): number => (value ? 1 : 0),
    },
  })
  isEnrolled: boolean;

  @Column({
    name: 'CRS_STATUS',
    type: 'number',
    precision: 1,
    nullable: false,
    default: 0,
    transformer: {
      from: (value: number): boolean => value === 1,
      to: (value: boolean): number => (value ? 1 : 0),
    },
  })
  crsStatus: boolean;

  @Column({
    name: 'ECRS_STATUS',
    type: 'number',
    precision: 1,
    nullable: false,
    default: 0,
    transformer: {
      from: (value: number): boolean => value === 1,
      to: (value: boolean): number => (value ? 1 : 0),
    },
  })
  ecrsStatus: boolean;

  @Column({
    name: 'IS_VALID',
    type: 'number',
    precision: 1,
    nullable: false,
    default: 0,
    transformer: {
      from: (value: number): boolean => value === 1,
      to: (value: boolean): number => (value ? 1 : 0),
    },
  })
  isValid: boolean;

  @Column({
    name: 'HAS_TRANSACTION_HISTORY',
    type: 'number',
    precision: 1,
    nullable: false,
    default: 0,
    transformer: {
      from: (value: number): boolean => value === 1,
      to: (value: boolean): number => (value ? 1 : 0),
    },
  })
  hasTransactionHistory: boolean;

  @Column({ name: 'MDA_CODE', nullable: false })
  mdaCode: string;

  @ManyToOne(() => MultiplePinResolutionRequest, (request) => request.pins, { onDelete: 'CASCADE', lazy: true })
  @JoinColumn({ name: 'REQUEST_PK' })
  request: MultiplePinResolutionRequest;

  @Column({ name: 'CPA_BALANCE', type: 'decimal', precision: 18, scale: 2, default: 0 })
  cpaBalance: number;

  @Column({ name: 'VALID_PIN_BALANCE', type: 'decimal', precision: 18, scale: 2, default: 0 })
  validPinBalance: number;

  @Column({
    name: 'ELIGIBILITY_STATUS',
    type: 'number',
    precision: 1,
    nullable: false,
    default: 0,
    transformer: {
      from: (value: number): boolean => value === 1,
      to: (value: boolean): number => (value ? 1 : 0),
    },
  })
  eligibilityStatus: boolean;

  @Column({ name: 'PAYMENT_CONFIRMED_STATUS', nullable: true })
  paymentConfirmationStatus?: string;

  @Column({ name: 'PAYMENT_CONFIRMED_BY', nullable: true })
  paymentConfirmedBy?: string;

  @Column({ name: 'PAYMENT_CONFIRMED_AT', nullable: true })
  paymentConfirmedAt?: Date;

  @Column({ name: 'PAID_TO_DATE', nullable: true })
  paidToDate?: number;

  @Column({ name: 'PAYMENT_REF', nullable: true })
  paymentRef?: string;

  @Column({ name: 'VALID_PIN_PAYMENT_REF', nullable: true })
  validPinPaymentRef?: string;

  @Column({ name: 'PENCOM_PAYMENT_CONFIRMATION_DATE', nullable: true })
  pencomPaymentConfirmationDate?: Date;

  @Column({ name: 'PENCOM_PAYMENT_CONFIRMATION_STATUS', nullable: true })
  pencomPaymentConfirmationStatus?: string;

  @Column({ name: 'COMMENT', nullable: true, length: 4000 })
  comment?: string;

  @Column({ name: 'TH_REQUESTED_DATE', nullable: true })
  thRequestedDate?: Date;

  @Column({ name: 'TH_RECEIVED_DATE', nullable: true })
  thReceivedDate?: Date;
}
