import { AbstractEntity } from '@app/shared/database';
import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { SalaryDetails } from '@app/shared/enrollment-service/interfaces/contribution-computation.interface';
import { NominalRollBandHistory } from '@app/shared/enrollment-service/entities/nominal-roll-band-history.entity';

@Entity({ name: 'TBC_NOMINAL_ROLL_BAND_DETAILS_HISTORY' })
export class NominalRollBandDetailsHistory
  extends AbstractEntity<NominalRollBandDetailsHistory>
  implements SalaryDetails
{
  @Column({ name: 'SALARY_STRUCTURE', nullable: false })
  salaryStructure: string;

  @Column({ name: 'GRADE_LEVEL', nullable: false })
  gradeLevel: string;

  @Column({ name: 'STEP', nullable: false })
  step: string;

  @Column({ name: 'SALARY_AMOUNT', nullable: true })
  salaryAmount: string;

  @ManyToOne(() => NominalRollBandHistory, (nominalRollBand) => nominalRollBand.nominalRollBandDetails, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'NOMINAL_ROLL_BAND_PK' })
  nominalRollBand: NominalRollBandHistory;
}
