import { Column, Entity, PrimaryColumn } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';

@Entity({ name: 'FLAGGED_PIN' /*, synchronize: false */ })
export class FlaggedPin extends AbstractEntity<FlaggedPin> {
  @PrimaryColumn({ name: 'RSA_PIN', unique: true, nullable: false })
  rsaPin: string;

  @Column({ name: 'REASON', nullable: false })
  reason: string;

  @Column({ name: 'FLAGGED_BY', nullable: true })
  flaggedBy: string;
}
