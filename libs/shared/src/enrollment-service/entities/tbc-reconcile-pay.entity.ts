import { Column, Entity, PrimaryColumn } from 'typeorm';
import { AbstractEntity } from '@app/shared';

@Entity({ name: 'TBC_RECONCILE_PAY' })
export class TbcReconcilePay extends AbstractEntity<TbcReconcilePay> {
  @PrimaryColumn({ name: 'PIN', type: 'varchar2', length: 30 })
  pin: string;

  @Column({ name: 'ACTOR_EMAIL', nullable: true })
  actorEmail: string;

  @Column({ name: 'LAST_PAYDATE', type: 'date', nullable: true })
  lastPaydate: Date;

  @Column({ name: 'AMOUNT', type: 'number', nullable: true })
  amount: number;

  @Column({ name: 'PAY_TYPE', type: 'varchar', nullable: true })
  payType: string;

  @Column({ name: 'GRADE', type: 'varchar', length: 10, nullable: true })
  grade: string;

  @Column({ name: 'STEP', type: 'varchar', length: 10, nullable: true })
  step: string;

  @Column({ name: 'AGENCY_CODE', type: 'varchar', length: 15, nullable: true })
  agencyCode: string;

  @Column({ name: 'MTH_EMOL', type: 'number', precision: 16, scale: 2, nullable: true })
  mthEmol: number;

  @Column({ name: 'PAID_TO_DATE', type: 'number', precision: 16, scale: 2, nullable: true })
  paidToDate: number;

  @Column({ name: 'NAME', type: 'varchar', length: 150, nullable: true })
  name: string;

  @Column({ name: 'PFACODE', type: 'varchar', length: 3, nullable: true })
  pfaCode: string;

  @Column({ name: 'PFA_NAME', type: 'varchar', nullable: true })
  pfaName: string;

  @Column({ name: 'SALARY_TYPE', type: 'varchar', length: 20, nullable: true })
  salaryType: string;
}
