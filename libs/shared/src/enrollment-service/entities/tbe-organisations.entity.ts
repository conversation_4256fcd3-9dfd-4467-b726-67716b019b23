import { Column, Entity, PrimaryColumn } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';

@Entity({ name: 'TBE_ORGANISATIONS' /* synchronize: false*/ })
export class TbeOrganisations extends AbstractEntity<TbeOrganisations> {
  @PrimaryColumn({ name: 'SECTOR_CODE', type: 'varchar', length: 10 })
  sectorCode: string;

  @PrimaryColumn({ name: 'EMPLOYER_ID', type: 'varchar', length: 20, unique: true })
  employerId: string;

  @Column({ name: 'EMPLOYER_NAME', type: 'varchar', length: 120, nullable: true })
  employerName?: string;

  @Column({ name: 'IPPIS_DATE', type: 'date', nullable: true })
  ippisDate?: Date;

  @Column({ name: 'ECRS_EMPLOYER_CODE', type: 'varchar', length: 15, nullable: true })
  ecrsEmployerCode?: string;

  @Column({ name: 'ACTOR_EMAIL', nullable: true })
  actorEmail: string;
}
