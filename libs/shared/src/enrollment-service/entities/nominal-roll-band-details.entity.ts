import { AbstractEntity } from '@app/shared/database';
import { <PERSON>umn, <PERSON>tity, Jo<PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm';
import { NominalRollBand } from '@app/shared/enrollment-service/entities/nominal-roll-band.entity';
import { SalaryDetails } from '@app/shared/enrollment-service/interfaces/contribution-computation.interface';

@Entity({ name: 'TBC_NOMINAL_ROLL_BAND_DETAILS' })
export class NominalRollBandDetails extends AbstractEntity<NominalRollBandDetails> implements SalaryDetails {
  @Column({ name: 'SALARY_STRUCTURE', nullable: false })
  salaryStructure: string;

  @Column({ name: 'GRADE_LEVEL', nullable: false })
  gradeLevel: string;

  @Column({ name: 'STEP', nullable: false })
  step: string;

  @Column({ name: 'SALARY_AMOUNT', nullable: true })
  salaryAmount: string;

  @ManyToOne(() => NominalRollBand, (nominalRollBand) => nominalRollBand.nominalRollBandDetails, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'NOMINAL_ROLL_BAND_PK' })
  nominalRollBand: NominalRollBand;
}
