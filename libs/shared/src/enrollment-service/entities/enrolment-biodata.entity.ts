import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, OneToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared';
import { EmploymentDetail } from '@app/shared/enrollment-service/entities/employment-detail.entity';
import { EmploymentDocument } from '@app/shared/enrollment-service/entities/employment-document.entity';
import { EnrolmentComment } from '@app/shared/enrollment-service/entities/enrolment-comment.entity';

@Entity('ENROLMENT_BIODATA')
export class EnrolmentBiodata extends AbstractEntity<EnrolmentBiodata> {
  @Column({ name: 'RSA_PIN', nullable: false, unique: true })
  rsaPin: string;

  @Column({ name: 'NIN', unique: true, nullable: true })
  nin: string;

  @Column({ name: 'PFA_CODE', nullable: false })
  pfaCode: string;

  @Column({ name: 'PFA_NAME' })
  pfaName: string;

  @Column({ name: '<PERSON><PERSON>LOYER_CODE', nullable: false })
  employerCode: string;

  @Colum<PERSON>({ name: 'EMPLOYER_NAME' })
  employerName: string;

  @Column({ name: 'FIRST_NAME', nullable: false })
  firstName: string;

  @Column({ name: 'SURNAME', nullable: false })
  surname: string;

  @Column({ name: 'OTHER_NAME', nullable: true })
  otherName: string;

  @Column({ name: 'CONTACT_ADDRESS', nullable: true })
  contactAddress: string;

  @Column({ name: 'EMAIL_ADDRESS', unique: true, nullable: false })
  emailAddress: string;

  @Column({ name: 'GENDER', nullable: false })
  gender: string;

  @Column({ name: 'PHONE_NUMBER', nullable: false })
  phoneNumber: string;

  @Column({ name: 'ALTERNATE_PHONE_NUMBER', nullable: true })
  alternatePhoneNumber: string;

  @Column({ name: 'DATE_OF_BIRTH', type: 'date', nullable: true })
  dateOfBirth: Date;

  @Column({ name: 'DATE_OF_DEATH', type: 'date', nullable: true })
  dateOfDeath: Date;

  @Column({ name: 'DATE_OF_FIRST_APPOINTMENT', type: 'date', nullable: true })
  dateOfFirstAppointment: Date;

  @Column({ name: 'DATE_OF_TRANSFER_SERVICE', type: 'date', nullable: true })
  dateOfTransferService: Date;

  @Column({ name: 'DATE_OF_RETIREMENT', type: 'date', nullable: true })
  dateOfRetirement: Date;

  @Column({ name: 'RETIREE_USER_TYPE' })
  retireeUserType: string;

  @Column({ name: 'MODE_OF_RETIREMENT' })
  modeOfRetirement: string;

  @Column({ name: 'REASON_FOR_EXITING', nullable: true })
  reasonForExiting: string;

  @Column({ name: 'NUMBER_OF_EMPLOYMENT', nullable: false })
  numberOfEmployment: string;

  @Column({ name: 'ALREADY_PAID', nullable: true })
  alreadyPaid: string;

  @Column({ name: 'CRITICAL_HEALTH_CHALLENGE', nullable: false })
  criticalHealthChallenge: string;

  @Column({ name: 'STAFF_ID' })
  staffId: string;

  @Column({ name: 'PDO_USER_ID', nullable: true })
  pdoUserId: string;

  @Column({ name: 'PDO_FIRST_NAME', nullable: true })
  pdoFirstName: string;

  @Column({ name: 'PDO_SURNAME', nullable: true })
  pdoSurname: string;

  @Column({ name: 'PDO_EMAIL_ADDRESS', nullable: true })
  pdoEmailAddress: string;

  @Column({ name: 'NOK_FIRST_NAME', nullable: true })
  nokFirstName: string;

  @Column({ name: 'NOK_SURNAME', nullable: true })
  nokSurname: string;

  @Column({ name: 'NOK_EMAIL_ADDRESS', nullable: true })
  nokEmailAddress: string;

  @Column({ name: 'NOK_PHONE_NUMBER', nullable: true })
  nokPhoneNumber: string;

  @Column({ name: 'CONTRIBUTION_AMOUNT', nullable: true })
  contributionAmount: string;

  @Column({ name: 'CONTRIBUTION_BATCH_ID', nullable: true })
  contributionBatchId: string;

  @Column({ name: 'ACCRUED_RIGHTS_AMOUNT', nullable: true })
  accruedRightsAmount: string;

  @Column({ name: 'ACCRUED_RIGHTS_BATCH_ID', nullable: true })
  accruedRightsBatchId: string;

  @Column({ name: 'RETIREMENT_MODE', nullable: true })
  retirementMode: string;

  @Column({ name: 'APA_VALUE_2004', nullable: true })
  apaValue2004: string;

  @Column({ name: 'PAYMENT_STATUS', nullable: true })
  paymentStatus: string;

  @Column({ name: 'PAYMENT_COMMENT', nullable: true })
  paymentComment: string;

  @Column({ name: 'TOTAL_MONTHS', nullable: true })
  totalMonths: string;

  @Column({ name: 'TOTAL_EMOLUMENT', nullable: true })
  totalEmolument: string;

  @Column({ name: 'PAID_TO_DATE', nullable: true })
  paidToDate: string;

  @Column({ name: 'TOTAL_INTEREST', nullable: true })
  totalInterest: string;

  @Column({ name: 'TOTAL_PENSION_PLUS_INTEREST', nullable: true })
  totalPensionPlusInterest: string;

  @Column({ name: 'TOTAL_PENSION', nullable: true })
  totalPension: string;

  @Column({ name: 'CONTRIBUTION_REMARKS', nullable: true })
  contributionRemarks: string;

  @Column({ name: 'ACCRUED_RIGHTS_COMPUTATION_REMARKS', nullable: true })
  accruedRightsComputationRemarks: string;

  @Column({
    name: 'PORTRAIT_OVERRIDE',
    type: 'number',
    precision: 1,
    nullable: true,
    transformer: {
      from: (value: number): boolean => value === 1,
      to: (value: boolean): number => (value ? 1 : 0),
    },
  })
  portraitOverride: boolean;

  @Column({ name: 'PORTRAIT_OVERRIDE_REASON', nullable: true })
  portraitOverrideReason: string;

  @OneToMany(() => EnrolmentComment, (comment) => comment.biodata, { cascade: true, nullable: true })
  comments: EnrolmentComment[];

  @OneToOne(() => EmploymentDocument, (documents) => documents.retiree, { cascade: true })
  @JoinColumn({ name: 'DOCUMENTS_ID' })
  documents: EmploymentDocument;

  @OneToMany(() => EmploymentDetail, (employmentDetail) => employmentDetail.enrolmentBiodata, { cascade: true })
  employmentDetails: EmploymentDetail[];
}
