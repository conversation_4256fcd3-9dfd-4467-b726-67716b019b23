import { Column, Entity, OneToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared';
import { LegacyRecordDocument } from './legacy-record-document.entity';

@Entity('LEGACY_RECORD')
export class LegacyRecord extends AbstractEntity<LegacyRecord> {
  @Column({ name: 'RSAPIN', length: 15, nullable: false, unique: true })
  rsaPin: string;

  @Column({ name: 'PFACODE', length: 10, nullable: true })
  pfaCode: string;

  @Column({ name: 'NIN', length: 15, nullable: true, unique: true })
  nin: string;

  @Column({ name: 'FIRSTNAME', length: 50, nullable: false })
  firstName: string;

  @Column({ name: 'MIDD<PERSON><PERSON><PERSON>', length: 50, nullable: true })
  middleName: string;

  @Column({ name: 'SURNAME', length: 50, nullable: false })
  surname: string;

  @<PERSON>umn({ name: '<PERSON><PERSON><PERSON>', length: 1, nullable: true })
  gender: string;

  @Column({ name: 'DOB', nullable: true })
  dob: Date;

  @Column({ name: 'CONTACT_ADDRESS', length: 300, nullable: true })
  contactAddress: string;

  @Column({ name: 'CONTACT_PHONE', length: 20, nullable: true })
  contactPhone: string;

  @Column({ name: 'ALTERNATE_PHONE', length: 20, nullable: true })
  alternatePhone: string;

  @Column({ name: 'EMAIL', length: 100, nullable: true })
  email: string;

  @Column({ name: 'EMPLOYER_NAME', length: 100, nullable: true })
  employerName: string;

  @Column({ name: 'DOFA', nullable: true })
  dofa: Date;

  @Column({ name: 'EDOR', nullable: true })
  edor: Date;

  @Column({ name: 'EMPLOYER_CODE', length: 20, nullable: true })
  employerCode: string;

  @OneToOne(() => LegacyRecordDocument, (document) => document.legacyRecord)
  document: LegacyRecordDocument;
}
