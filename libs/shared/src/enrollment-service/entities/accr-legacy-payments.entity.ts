import { Column, Entity, PrimaryColumn } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';

@Entity('TBC_ACCR_LEGACY_PAYMENTS')
export class AccrLegacyPayments extends AbstractEntity<AccrLegacyPayments> {
  @Column({ name: 'ACTOR_EMAIL', nullable: true })
  actorEmail: string;

  @Column({ name: 'FULLNAME', type: 'varchar2', length: 120, nullable: true })
  fullName: string;

  @Column({ name: 'GENDER', type: 'char', length: 1, nullable: true })
  gender: string;

  @Column({ name: 'DOR', type: 'date', nullable: true })
  dor: Date;

  @Column({ name: 'DOB', type: 'date', nullable: true })
  dob: Date;

  @Column({ name: 'DOFA', type: 'date', nullable: true })
  dofa: Date;

  @PrimaryColumn({ name: 'PIN', type: 'varchar2', length: 30, nullable: false })
  pin: string;

  @Column({ name: 'PFA_NAME', type: 'varchar2', length: 100, nullable: true })
  pfaName: string;

  @Column({ name: 'MDA_NAME', type: 'varchar2', length: 100, nullable: true })
  mdaName: string;

  @Column({ name: 'SAL_STRUC', type: 'varchar2', length: 20, nullable: true })
  salStruc: string;

  @Column({ name: 'GL_JUN2004', type: 'varchar2', length: 5, nullable: true })
  glJun2004: string;

  @Column({ name: 'STEP_JUN2004', type: 'varchar2', length: 2, nullable: true })
  stepJun2004: string;

  @PrimaryColumn({ name: 'BATCH_NAME', type: 'varchar2', length: 20, nullable: false })
  batchName: string;

  @Column({ name: 'INTRO_DATE', type: 'date', nullable: true })
  introDate: Date;

  @Column({ name: 'ANNUAL_PEN_ALLOW', type: 'number', precision: 12, scale: 2, nullable: true })
  annualPenAllow: number;

  @Column({ name: 'ACCR_BENFIT_ACTUAL', type: 'number', precision: 12, scale: 2, nullable: true })
  accrBenfitActual: number;

  @Column({ name: 'ACCR_BENFIT_APPROX', type: 'number', precision: 12, scale: 2, nullable: true })
  accrBenfitApprox: number;

  @Column({ name: 'ACCR_BENFIT_ADD', type: 'number', precision: 12, scale: 2, nullable: true })
  accrBenfitAdd: number;

  @Column({ name: 'ACCR_BENFIT_ADD_DATE', type: 'date', nullable: true })
  accrBenfitAddDate: Date;

  @Column({ name: 'ACCR_BENFIT_2ND_ADD', type: 'number', precision: 12, scale: 2, nullable: true })
  accrBenfit2ndAdd: number;

  @Column({ name: 'ACCR_BENFIT_RECOVERED', type: 'number', precision: 12, scale: 2, nullable: true })
  accrBenfitRecovered: number;

  @Column({ name: 'ACCR_BENFIT_RECOVERED_DATE', type: 'date', nullable: true })
  accrBenfitRecoveredDate: Date;

  @Column({ name: 'ACCR_BENFIT_NET', type: 'number', precision: 12, scale: 2, nullable: true })
  accrBenfitNet: number;

  @Column({ name: 'EXIT_MODE', type: 'varchar2', length: 20, nullable: true })
  exitMode: string;
}
