import { AbstractEntity } from '@app/shared/database';
import { Column, Entity, JoinC<PERSON>umn, ManyToOne, OneToMany } from 'typeorm';
import { NominalRollHistory } from '@app/shared/enrollment-service/entities/nominal-roll-history.entity';
import { NominalRollBandDetailsHistory } from '@app/shared/enrollment-service/entities/nominal-roll-band-details-history.entity';

@Entity({ name: 'TBC_NOMINAL_ROLL_BAND_HISTORY' })
export class NominalRollBandHistory extends AbstractEntity<NominalRollBandHistory> {
  @Column({ name: 'B_YEAR', nullable: false })
  year: string;

  @Column({ name: 'CONTRIBUTION_REMARKS', nullable: true })
  contributionRemarks: string;

  @Column({ name: 'TOTAL_EMOLUMENT', nullable: true })
  totalEmolument: string;

  @Column({ name: 'MONTHS_COVERED', nullable: true })
  monthsCovered: string;

  @ManyToOne(() => NominalRollHistory, (nominalRoll) => nominalRoll.bands, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'NOMINAL_ROLL_PK' })
  nominalRoll: NominalRollHistory;

  @OneToMany(() => NominalRollBandDetailsHistory, (bandDetails) => bandDetails.nominalRollBand, { cascade: true })
  nominalRollBandDetails: NominalRollBandDetailsHistory[];
}
