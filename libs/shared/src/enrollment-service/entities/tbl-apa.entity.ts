import { Column, Entity } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';

@Entity({ name: 'TBL_APA' })
export class TblApa extends AbstractEntity<TblApa> {
  @Column({ name: 'APACODE', type: 'varchar', length: 20, nullable: true })
  apaCode?: string;

  @Column({ name: 'GL', type: 'varchar', length: 5, nullable: true })
  gl?: string;

  @Column({ name: 'STEP', type: 'varchar', length: 5, nullable: true })
  step?: string;

  @Column({ name: 'INST', type: 'varchar', length: 15, nullable: true })
  inst?: string;

  @Column({ name: 'APAVALUE', type: 'decimal', precision: 16, scale: 2, nullable: true })
  apaValue?: number;

  @Column({ name: 'REMARKS', type: 'varchar', length: 15, nullable: true })
  remarks?: string;
}
