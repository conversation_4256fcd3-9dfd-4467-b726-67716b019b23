import { Column, <PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared';
import { TblBatch } from '@app/shared/enrollment-service/entities/tbl-batch';

@Entity('BATCH_DOCUMENT')
export class BatchDocuments extends AbstractEntity<BatchDocuments> {
  @OneToOne(() => TblBatch, (batchDocument) => batchDocument.documents, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'BATCH_ID' })
  batch: TblBatch;

  @Column({ name: 'SCHEDULE', type: 'blob', nullable: true })
  schedule: Buffer;

  @Column({ name: 'EXCO_MEMO', type: 'blob', nullable: true })
  excoMemo: Buffer;

  @Column({ name: 'ACCOUNT_MEMO', type: 'blob', nullable: true })
  accountMemo: Buffer;

  @Column({ name: 'EXCO_MEMO_APPENDIX', type: 'blob', nullable: true })
  excoMemoAppendix: Buffer;

  @Column({ name: 'ACCOUNT_CONFIRMATION_RECEIPT', type: 'blob', nullable: true })
  accountConfirmationReceipt: Buffer;
}
