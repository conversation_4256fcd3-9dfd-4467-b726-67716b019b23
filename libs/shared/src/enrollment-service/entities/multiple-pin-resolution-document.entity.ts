import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>T<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { MultiplePinResolutionRequest } from './multiple-pin-resolution-req.entity';
import { MultiplePinResolutionPin } from './multiple-pin-resolution-pin.entity';

@Entity('TBC_MULTIPLE_PIN_RESOLUTION_DOCUMENT')
export class MultiplePinResolutionDocument extends AbstractEntity<MultiplePinResolutionDocument> {
  @ManyToOne(() => MultiplePinResolutionRequest, (request) => request.documents, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'REQUEST_PK' })
  request: MultiplePinResolutionRequest;

  @ManyToOne(() => MultiplePinResolutionPin, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'PIN_FK' })
  pin: MultiplePinResolutionPin;

  @Column({ name: 'DOCUMENT_DATA', type: 'blob' })
  documentData: Buffer;

  @Column({ name: 'DOCUMENT_EXT', nullable: false })
  documentExt: string;

  @Column({ name: 'PROCESS_TYPE', nullable: false })
  processType: string;

  @Column({ name: 'DOCUMENT_NAME', nullable: false })
  documentName: string;
}
