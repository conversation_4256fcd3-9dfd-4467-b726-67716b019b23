import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { MultiplePinResolutionRequest } from './multiple-pin-resolution-req.entity';

@Entity('TBC_MPR_TRANSACTION_HISTORY')
export class MultiplePinResolutionTransactionHistory extends AbstractEntity<MultiplePinResolutionTransactionHistory> {
  @Column({ name: 'RSA_HOLDER_FN_NAME', length: 255 })
  rsaHolderFirstName: string;

  @Column({ name: 'RSA_HOLDER_LN_NAME', length: 255 })
  rsaHolderLastName: string;

  @Column({ name: 'EMPLOYER_NAME', length: 255, nullable: true })
  employerName?: string;

  @Column({ name: 'EMPLOYER_CODE', length: 50 })
  employerCode: string;

  @Column({ name: 'INVALID_PIN', length: 50 })
  invalidPin: string;

  @Column({ name: 'INVALID_PFA_CODE', length: 50 })
  invalidPfaCode: string;

  @Column({ name: 'PENCOM_REMIT_AMT', type: 'decimal', precision: 18, scale: 2, default: 0 })
  pencomRemitAmount: number;

  @Column({ name: 'PENCOM_INV_INCOME', type: 'decimal', precision: 18, scale: 2, default: 0 })
  pencomInvestmentIncome: number;

  @Column({ name: 'PENCOM_TOTAL', type: 'decimal', precision: 18, scale: 2, default: 0 })
  pencomTotal: number;

  @Column({ name: 'SHORTFALL_CONTRIB', type: 'decimal', precision: 18, scale: 2, default: 0 })
  shortfallContribution: number;

  @Column({ name: 'SHORTFALL_INV_INCOME', type: 'decimal', precision: 18, scale: 2, default: 0 })
  shortfallInvestmentIncome: number;

  @Column({ name: 'SHORTFALL_TOTAL', type: 'decimal', precision: 18, scale: 2, default: 0 })
  shortfallTotal: number;

  @Column({ name: 'IPPIS_AMOUNT', type: 'decimal', precision: 18, scale: 2, default: 0 })
  ippisAmount: number;

  @Column({ name: 'IPPIS_INV_INCOME', type: 'decimal', precision: 18, scale: 2, default: 0 })
  ippisInvestmentIncome: number;

  @Column({ name: 'IPPIS_TOTAL', type: 'decimal', precision: 18, scale: 2, default: 0 })
  ippisTotal: number;

  @Column({ name: 'PFA_COMMENTS', length: 1000, nullable: true })
  pfaComments?: string;

  @Column({ name: 'REMARK', length: 1000, nullable: true })
  remark?: string;

  @ManyToOne(() => MultiplePinResolutionRequest, (request) => request.transactions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'REQUEST_PK' })
  request: MultiplePinResolutionRequest;
}
