import { Column, Entity, Unique } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';

@Entity('FILE_UPLOAD_PROCESS')
@Unique(['uniqueId', 'taskId'])
export class FileUploadProcess extends AbstractEntity<FileUploadProcess> {
  @Column({ name: 'TASK_ID', nullable: false, length: 512 })
  taskId: string;

  @Column({ name: 'UNIQUE_ID', nullable: false, length: 512 })
  uniqueId: string;

  @Column({ name: 'RESULT_DESCRIPTION', nullable: false, length: 1000 })
  resultDescription: string;

  @Column({ name: 'ROW_DATA', type: 'clob', nullable: false })
  rowData: string;
}
