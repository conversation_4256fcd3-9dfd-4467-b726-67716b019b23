import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('ENROLMENT_HISTORY_VIEW', { synchronize: false })
export class EnrolmentHistoryView {
  @PrimaryColumn({ name: 'UNIQUE_ID' })
  uniqueId: string;

  @Column({ name: 'EVENT_TYPE' })
  eventType: string;

  @Column({ name: 'ACTOR_EMAIL' })
  actorEmail: string;

  @PrimaryColumn({ name: 'EVENT_TIMESTAMP' })
  eventTimestamp: Date;

  @Column({ name: 'COMMENT_TEXT', nullable: true })
  commentText?: string;

  @Column({ name: 'FIRST_NAME', nullable: true })
  firstName?: string;

  @Column({ name: 'SURNA<PERSON>', nullable: true })
  surname?: string;
}
