import { Column, <PERSON><PERSON><PERSON>, Join<PERSON>olumn, ManyToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { TbcNominalRollBatch } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch';

@Entity('TBC_BATCH_NR_ACCOUNT_MEMO_DETAILS')
export class TbcBatchNrAccountMemoDetails extends AbstractEntity<TbcBatchNrAccountMemoDetails> {
  @Column({ name: 'PFA_NAME', nullable: false })
  pfaName: string;

  @Column({ name: 'PFA_CODE', nullable: false })
  pfaCode: string;

  @Column({ name: 'PFC_NAME', nullable: false })
  pfcName: string;

  @Column({ name: 'PFC_CODE', nullable: false })
  pfcCode: string;

  @Column({ name: 'PFA_ACCOUNT_NAME', nullable: false })
  pfaAccountName: string;

  @Column({ name: 'PFA_ACCOUNT_NUMBER', nullable: false })
  pfaAccountNumber: string;

  @Column({ name: 'AMOUNT', nullable: false })
  amount: number;

  @ManyToOne(() => TbcNominalRollBatch, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'BATCH_ID' })
  batch: TbcNominalRollBatch;
}
