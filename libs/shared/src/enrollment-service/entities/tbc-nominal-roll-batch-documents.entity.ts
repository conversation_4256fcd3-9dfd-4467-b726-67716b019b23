import { Column, <PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON>, OneToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared';
import { TbcNominalRollBatch } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch';

@Entity('TBC_NOMINAL_ROLL_BATCH_DOCUMENT')
export class NominalRollBatchDocuments extends AbstractEntity<NominalRollBatchDocuments> {
  @OneToOne(() => TbcNominalRollBatch, (batchDocument) => batchDocument.documents, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'BATCH_ID' })
  batch: TbcNominalRollBatch;

  @Column({ name: 'SCHEDULE', type: 'blob', nullable: true })
  schedule: Buffer;

  @Column({ name: 'EXCO_MEMO', type: 'blob', nullable: true })
  excoMemo: Buffer;

  @Column({ name: 'EXCO_MEMO_APPENDIX', type: 'blob', nullable: true })
  excoMemoAppendix: Buffer;

  @Column({ name: 'ACCOUNT_MEMO', type: 'blob', nullable: true })
  accountMemo: Buffer;

  @Column({ name: 'ACCOUNT_CONFIRMATION_RECEIPT', type: 'blob', nullable: true })
  accountConfirmationReceipt: Buffer;
}
