import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToO<PERSON> } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { TblBatch } from '@app/shared/enrollment-service/entities/tbl-batch';

@Entity('TBC_ENROLLMENT_PFC_MEMO_DOCUMENT')
export class TbcEnrollmentPfcMemoDocument extends AbstractEntity<TbcEnrollmentPfcMemoDocument> {
  @Column({ name: 'PFC_NAME', type: 'varchar2', length: 100 })
  pfcName: string;

  @Column({ name: 'PFC_CODE', type: 'varchar2', length: 100 })
  pfcCode: string;

  @ManyToOne(() => TblBatch, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'BATCH_ID' })
  batch: TblBatch;

  @Column({ name: 'PFC_MEMO', type: 'blob', nullable: false })
  pfcMemo: B<PERSON><PERSON>;
}
