import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { TbcNominalRollBatch } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch';

@Entity('TBC_NOMINAL_ROLL_PFC_MEMO_DOCUMENT')
export class TbcNominalRollPfcMemoDocument extends AbstractEntity<TbcNominalRollPfcMemoDocument> {
  @Column({ name: 'PFC_NAME', type: 'varchar2', length: 100 })
  pfcName: string;

  @Column({ name: 'PFC_CODE', type: 'varchar2', length: 100 })
  pfcCode: string;

  @ManyToOne(() => TbcNominalRollBatch, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'BATCH_ID' })
  batch: TbcNominalRollBatch;

  @Column({ name: 'PFC_MEMO', type: 'blob', nullable: false })
  pfcMemo: Buffer;
}
