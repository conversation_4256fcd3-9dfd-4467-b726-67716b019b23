import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, Table } from 'typeorm';
import { AbstractEntity } from '../../database/abstract.entity';
import { LegacyRecord } from './legacy-record.entity';
import { truncate } from 'node:fs/promises';

@Entity('LEGACY_RECORD_DOCUMENT')
export class LegacyRecordDocument extends AbstractEntity<LegacyRecordDocument> {
  @Column({ name: 'DATA', type: 'blob', nullable: true })
  data: Buffer;

  @OneToOne(() => LegacyRecord, (legacyRecord) => legacyRecord.document)
  @JoinColumn({ name: 'LEGACY_RECORD_ID' })
  legacyRecord: LegacyRecord;
}
