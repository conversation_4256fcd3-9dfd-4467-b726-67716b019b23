import { Column, Entity, Unique } from 'typeorm';
import { AbstractEntity } from '@app/shared';

@Entity('TBL_SERIAL_NUMBER')
@Unique(['numberType', 'currentYear'])
export class SerialNumber extends AbstractEntity<SerialNumber> {
  @Column({ name: 'NUMBER_TYPE' })
  numberType: string;

  @Column({ name: 'LAST_NUMBER', default: 0 })
  lastNumber: number;

  @Column({ name: 'CURRENT_YEAR' })
  currentYear: number;
}
