import { AbstractEntity } from '@app/shared/database';
import { Colum<PERSON>, <PERSON>tity, Join<PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';
import { NominalRollBandDetails } from '@app/shared/enrollment-service/entities/nominal-roll-band-details.entity';

@Entity({ name: 'TBC_NOMINAL_ROLL_BAND' })
export class NominalRollBand extends AbstractEntity<NominalRollBand> {
  @Column({ name: 'B_YEAR', nullable: false })
  year: string;

  @Column({ name: 'CONTRIBUTION_REMARKS', nullable: true })
  contributionRemarks: string;

  @Column({ name: 'TOTAL_EMOLUMENT', nullable: true })
  totalEmolument: string;

  @Column({ name: 'MONTHS_COVERED', nullable: true })
  monthsCovered: string;

  @ManyToOne(() => NominalRoll, (nominalRoll) => nominalRoll.bands, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'NOMINAL_ROLL_PK' })
  nominalRoll: NominalRoll;

  @OneToMany(() => NominalRollBandDetails, (bandDetails) => bandDetails.nominalRollBand, { cascade: true })
  nominalRollBandDetails: NominalRollBandDetails[];
}
