import { Column, <PERSON>ti<PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { TblBatch } from '@app/shared/enrollment-service/entities/tbl-batch';

@Entity('TBC_ENROLLMENT_PFA_MEMO_DOCUMENT')
export class TbcEnrollmentPfaMemoDocument extends AbstractEntity<TbcEnrollmentPfaMemoDocument> {
  @Column({ name: 'PFA_NAME', type: 'varchar2', length: 100 })
  pfaName: string;

  @Column({ name: 'PFA_CODE', type: 'varchar2', length: 100 })
  pfaCode: string;

  @Column({ name: 'PFC_NAME', type: 'varchar2', length: 100 })
  pfcName: string;

  @Column({ name: 'PFC_CODE', type: 'varchar2', length: 100 })
  pfcCode: string;

  @ManyToOne(() => TblBatch, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'BATCH_ID' })
  batch: TblBatch;

  @Column({ name: 'PFA_MEMO', type: 'blob', nullable: false })
  pfaMemo: Buffer;

  @Column({ name: 'PFA_SCHEDULE', type: 'blob', nullable: false })
  pfaSchedule: Buffer;
}
