import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToMany, OneToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { TbcNominalRollBatchRecord } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch-record';
import { NominalRollBatchDocuments } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch-documents.entity';

@Entity('TBC_NOMINAL_ROLL_BATCH')
export class TbcNominalRollBatch extends AbstractEntity<TbcNominalRollBatch> {
  @Column({ name: 'BATCH_NAME', nullable: false, unique: true })
  batchName: string;

  @Column({ name: 'TOTAL_CONTRIBUTIONS', nullable: true })
  totalContributions: number;

  @Column({ name: 'TOTAL_REFUNDS', nullable: true })
  totalRefunds: number;

  @Column({ name: 'TOTAL_COUNT', nullable: true })
  totalCount: number;

  @Column({ name: 'STATUS', default: 'BATCHED' })
  status: string;

  @Column({ name: 'MDA_CODE', nullable: true })
  mdaCode: string;

  @Column({ name: 'PFA_CODE', nullable: true })
  pfaCode: string;

  @Column({ name: 'PAYMENT_CONFIRMED_PFA', default: '00' })
  paymentConfirmedPfa: string;

  @Column({ name: 'UPLOAD_START_DATE', type: 'date', nullable: true })
  uploadStartDate: Date;

  @Column({ name: 'PFA_NOTIFICATION_DATE', type: 'timestamp', nullable: true })
  pfaNotificationDate: Date;

  @Column({ name: 'UPLOAD_END_DATE', type: 'date', nullable: true })
  uploadEndDate: Date;

  @OneToMany(() => TbcNominalRollBatchRecord, (record) => record.batch, { cascade: true })
  records: TbcNominalRollBatchRecord[];

  @OneToOne(() => NominalRollBatchDocuments, (documents) => documents.batch, { cascade: true })
  @JoinColumn({ name: 'DOCUMENTS_ID' })
  documents: NominalRollBatchDocuments;
}
