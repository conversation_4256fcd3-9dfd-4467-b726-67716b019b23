import { AbstractEntity } from '@app/shared/database';
import { Column, <PERSON>tity, Join<PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { NominalRollBand } from '@app/shared/enrollment-service/entities/nominal-roll-band.entity';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { NominalRollStatusEnum } from '@app/shared/enums/nominal-roll-status.enum';
import { NominalRollComment } from '@app/shared/enrollment-service/entities/nominal-roll-comment.entity';

@Entity({ name: 'TBC_NOMINAL_ROLL' })
export class NominalRoll extends AbstractEntity<NominalRoll> {
  @Column({ name: 'RSAPIN', type: 'varchar', nullable: false, unique: true })
  rsaPin: string;

  @Column({ name: 'FIRSTNAME', type: 'varchar' })
  firstName: string;

  @Column({ name: '<PERSON><PERSON><PERSON><PERSON>', type: 'varchar' })
  surname: string;

  @Column({ name: 'OTHER_NAME', type: 'varchar', nullable: true })
  otherName: string;

  @Column({ name: 'AMOUNT', type: 'varchar', nullable: true })
  amount: string;

  @Column({ name: 'GENDER', nullable: false })
  gender: string;

  @Column({ name: 'DATE_OF_BIRTH', type: 'date', nullable: true })
  dateOfBirth: Date;

  @Column({ name: 'MDA_NAME', type: 'varchar', nullable: false })
  mdaName: string;

  @Column({ name: 'MDA_CODE', type: 'varchar', nullable: false })
  mdaCode: string;

  @Column({ name: 'PFA_CODE', type: 'varchar', nullable: false })
  pfaCode: string;

  @Column({ name: 'PFA_NAME', type: 'varchar', nullable: false })
  pfaName: string;

  @Column({ name: 'STATUS', nullable: false, default: NominalRollStatusEnum.UPLOADED })
  status: string;

  @Column({ name: 'BATCH_ID', nullable: true })
  batchId: string;

  @Column({ name: 'DATE_OF_FIRST_APPOINTMENT', type: 'date', nullable: true })
  dateOfFirstAppointment: Date;

  @Column({ name: 'DATE_OF_EXIT', type: 'date', nullable: false })
  dateOfExit: Date | null;

  // Additional fields needed for exit contribution calculations
  @Column({ name: 'TOTAL_MONTHS', nullable: true })
  totalMonths: string;

  @Column({ name: 'TOTAL_EMOLUMENT', nullable: true })
  totalEmolument: string;

  @Column({ name: 'PAID_TO_DATE', nullable: true })
  paidToDate: string;

  @Column({ name: 'TOTAL_INTEREST', nullable: true })
  totalInterest: string;

  @Column({ name: 'TOTAL_PENSION_PLUS_INTEREST', nullable: true })
  totalPensionPlusInterest: string;

  @Column({ name: 'TOTAL_PENSION', nullable: true })
  totalPension: string;

  @Column({ name: 'CONTRIBUTION_REMARKS', nullable: true })
  contributionRemarks: string;

  @Column({ name: 'PAYMENT_STATUS', nullable: true })
  paymentStatus: string;

  @Column({ name: 'PAYMENT_COMMENT', nullable: true })
  paymentComment: string;

  @Column({ name: 'PAYMENT_DATE', nullable: true })
  paymentDate: Date;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: false })
  @JoinColumn({ name: 'CREATED_BY', referencedColumnName: 'pk' })
  createdBy: CobraUser;

  @OneToMany(() => NominalRollBand, (band) => band.nominalRoll, { cascade: true })
  bands: NominalRollBand[];

  @OneToMany(() => NominalRollComment, (comment) => comment.nominalRoll, { cascade: true, nullable: true })
  comments: NominalRollComment[];
}
