import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, Unique } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';
import { TbcNominalRollBatch } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch';
import { RecordCategory } from '@app/shared/enrollment-service/entities/entity.types';

@Entity('TBC_NOMINAL_ROLL_BATCH_RECORD')
@Unique(['nominalRoll', 'batch'])
export class TbcNominalRollBatchRecord extends AbstractEntity<TbcNominalRollBatchRecord> {
  @ManyToOne(() => TbcNominalRollBatch, (batch) => batch.records, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'BATCH_ID' })
  batch: TbcNominalRollBatch;

  @ManyToOne(() => NominalRoll)
  @JoinColumn({ name: 'NOMINAL_ROLL_ID' })
  nominalRoll: NominalRoll;

  @Column({ name: 'AMOUNT', type: 'varchar', nullable: false })
  amount: string;

  @Column({
    type: 'varchar2',
    length: 20,
    name: 'RECORD_CATEGORY',
    nullable: false,
  })
  recordCategory: RecordCategory; // CONTRIBUTION | REFUND
}
