import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { AbstractEntity } from '@app/shared/database/abstract.entity';

export enum AmendmentRequestStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELLED = 'CANCELLED',
}

@Entity('AMENDMENT_REQUEST')
export class AmendmentRequest extends AbstractEntity<AmendmentRequest> {
  @Column({ name: 'RSA_PIN', nullable: false })
  rsaPin: string;

  @Column({ name: 'REASON', nullable: false })
  reason: string;

  @Column({ name: 'COMMENT', nullable: true })
  comment: string;

  @Column({ name: 'PFA_CODE', nullable: false })
  pfaCode: string;

  @Column({
    name: 'STATUS',
    type: 'varchar2',
    length: 50,
    enum: AmendmentRequestStatus,
    default: AmendmentRequestStatus.PENDING,
  })
  status: AmendmentRequestStatus;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: false, lazy: true })
  @JoinColumn({ name: 'REQUESTED_BY', referencedColumnName: 'pk' })
  requestedBy: CobraUser;
}
