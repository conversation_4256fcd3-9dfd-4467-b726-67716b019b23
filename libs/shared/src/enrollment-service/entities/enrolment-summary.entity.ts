import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { RegistrationStatusEnum } from '@app/shared/enums/registration-status.enum';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';

@Entity('ENROLLMENT_SUMMARY')
export class EnrolmentSummary extends AbstractEntity<EnrolmentSummary> {
  @Column({ name: 'RSA_PIN', nullable: true, unique: true })
  rsaPin: string;

  @Column({ name: 'FIRST_NAME', nullable: false })
  firstName: string;

  @Column({ name: 'SURNA<PERSON>', nullable: false })
  surname: string;

  @Column({ name: 'RETIREE_USER_TYPE', nullable: false })
  retireeUserType: string;

  @Column({ name: 'EMAIL_ADDRESS', nullable: false, unique: true })
  emailAddress: string;

  @Column({
    name: '<PERSON>NDER',
    type: 'varchar2',
    length: 1,
    transformer: {
      to: (value: boolean | string): string => {
        if (typeof value === 'boolean') {
          return value ? 'F' : 'M';
        }
        return value;
      },
      from: (value: string): boolean => value === 'F',
    },
  })
  gender: string;

  @Column({ name: 'DATE_OF_FIRST_APPOINTMENT', type: 'date', nullable: true })
  dateOfFirstAppointment: Date;

  @Column({ name: 'EXPECTED_DATE_OF_RETIREMENT', type: 'date', nullable: true })
  expectedDateOfRetirement: Date;

  @Column({ name: 'DATE_OF_TRANSFER_OF_SERVICE', type: 'date', nullable: true })
  dateOfTransferOfService: Date;

  @Column({ name: 'DATE_OF_DEATH', type: 'date', nullable: true })
  dateOfDeath: Date;

  @Column({ name: 'ENROLMENT_DATE', type: 'timestamp', nullable: true })
  enrolmentDate: Date;

  @Column({ name: 'SUBMISSION_DATE', type: 'timestamp', nullable: true })
  submissionDate: Date;

  @Column({ name: 'STATUS', nullable: false, default: RegistrationStatusEnum.REGISTERED })
  status: string;

  @Column({ name: 'ACCRUED_RIGHTS_STATUS', nullable: true })
  accruedRightsStatus: string;

  @Column({ name: 'CONTRIBUTION_STATUS', nullable: true })
  contributionStatus: string;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: true, lazy: true })
  @JoinColumn({ name: 'ENROLLED_BY', referencedColumnName: 'pk' })
  enrolledBy: CobraUser;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: true, lazy: true })
  @JoinColumn({ name: 'SUBMITTED_BY', referencedColumnName: 'pk' })
  submittedBy: CobraUser;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: true, lazy: true })
  @JoinColumn({ name: 'ASSIGNED_TO', referencedColumnName: 'pk' })
  assignedTo: CobraUser;

  @ManyToOne(() => CobraUser, (cobraUser) => cobraUser.pk, { nullable: true, lazy: true })
  @JoinColumn({ name: 'CERTIFIED_BY', referencedColumnName: 'pk' })
  certifiedBy: CobraUser;

  @Column({ name: 'ASSIGNMENT_DATE', type: 'timestamp', nullable: true })
  assignmentDate: Date;

  @Column({ name: 'VALIDATION_DATE', type: 'timestamp', nullable: true })
  validationDate: Date;

  @Column({ name: 'AUDITOR_ASSIGNED_TO', nullable: true })
  auditorAssignedTo: string;

  @Column({ name: 'AUDITOR_ASSIGNMENT_DATE', type: 'timestamp', nullable: true })
  auditorAssignmentDate: Date;

  @Column({ name: 'AUDITOR_VALIDATION_DATE', type: 'timestamp', nullable: true })
  auditorValidationDate: Date;

  @Column({ name: 'AUDITOR_CERTIFICATION_DATE', type: 'timestamp', nullable: true })
  auditCertificationDate: Date;

  @Column({ name: 'CONTRIBUTION_AMOUNT', nullable: true })
  contributionAmount: string;

  @Column({ name: 'ACCRUED_RIGHTS_AMOUNT', nullable: true })
  accruedRightsAmount: string;

  @Column({ name: 'RETIREMENT_MODE', nullable: true })
  retirementMode: string;

  @Column({ name: 'APA_VALUE_2004', nullable: true })
  apaValue2004: string;
}
