import { Column, <PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToMany, OneToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { BatchRecord } from '@app/shared/enrollment-service/entities/batch-record';
import { BatchDocuments } from '@app/shared/enrollment-service/entities/batch-documents.entity';

@Entity('TBL_BATCH')
export class TblBatch extends AbstractEntity<TblBatch> {
  @Column({ name: 'BATCH_NAME', nullable: false, unique: true })
  batchName: string;

  @Column({ name: 'BATCH_TYPE', nullable: false })
  batchType: string;

  @Column({ name: 'TOTAL_AMOUNT', nullable: true })
  totalAmount: number;

  @Column({ name: 'TOTAL_CONTRIBUTIONS', nullable: true })
  totalContributions: number;

  @Column({ name: 'TOTAL_REFUNDS', nullable: true })
  totalRefunds: number;

  @Column({ name: 'TOTAL_COUNT', nullable: true })
  totalCount: number;

  @Column({ name: 'RSA_HOLDER', nullable: false })
  rsaHolder: string;

  @Column({ name: 'ENROLLMENT_START_DATE', type: 'date', nullable: true })
  enrollmentStartDate: Date;

  @Column({ name: 'ENROLLMENT_END_DATE', type: 'date', nullable: true })
  enrollmentEndDate: Date;

  @Column({ name: 'RETIREMENT_START_DATE', type: 'date', nullable: true })
  retirementStartDate: Date;

  @Column({ name: 'RETIREMENT_END_DATE', type: 'date', nullable: true })
  retirementEndDate: Date;

  @Column({ name: 'STATUS', default: 'BATCHED' })
  status: string;

  @Column({ name: 'PFA_NOTIFICATION_DATE', type: 'timestamp', nullable: true })
  pfaNotificationDate: Date;

  @Column({ name: 'PAYMENT_CONFIRMED_PFA', default: '00' })
  paymentConfirmedPfa: string;

  @OneToMany(() => BatchRecord, (record) => record.batch, { cascade: true })
  records: BatchRecord[];

  @OneToOne(() => BatchDocuments, (documents) => documents.batch, { cascade: true })
  @JoinColumn({ name: 'DOCUMENTS_ID' })
  documents: BatchDocuments;
}
