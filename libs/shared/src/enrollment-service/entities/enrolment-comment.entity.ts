import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared';
import { EnrolmentBiodata } from './enrolment-biodata.entity';

@Entity('ENROLMENT_COMMENTS')
export class EnrolmentComment extends AbstractEntity<EnrolmentComment> {
  @Column({ name: 'COMMENT_TEXT', nullable: false })
  commentText: string;

  @Column({ name: 'AUTHOR_EMAIL', nullable: false })
  authorEmail: string;

  @Column({ name: 'AUTHOR_NAME', nullable: false })
  authorName: string;

  @ManyToOne(() => EnrolmentBiodata, (biodata) => biodata.comments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'ENROLLMENT_BIODATA_PK' })
  biodata: EnrolmentBiodata;
}
