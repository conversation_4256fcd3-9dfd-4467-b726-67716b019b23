import { Column, Entity } from 'typeorm';
import { AbstractEntity } from '@app/shared';

@Entity('ENROLMENT_DRAFT')
export class EnrolmentDraft extends AbstractEntity<EnrolmentDraft> {
  @Column({ name: 'RSA_PIN', nullable: false, unique: true })
  rsaPin: string;

  @Column({ name: 'STEP_NUMBER' })
  stepNumber: number;

  @Column({ name: 'FORM_DATA', type: 'clob' })
  formData: string;

  @Column({ name: 'ENROLMENT_STATUS', type: 'varchar', default: 'DRAFT' })
  enrolmentStatus: 'DRAFT' | 'SUBMITTED';
}
