import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared';
import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';

@Entity('TBC_NOMINAL_ROLL_COMMENT')
export class NominalRollComment extends AbstractEntity<NominalRollComment> {
  @Column({ name: 'COMMENT_TEXT', nullable: false })
  commentText: string;

  @Column({ name: 'AUTHOR_EMAIL', nullable: false })
  authorEmail: string;

  @Column({ name: 'AUTHOR_NAME', nullable: false })
  authorName: string;

  @ManyToOne(() => NominalRoll, (nominalRoll) => nominalRoll.comments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'NOMINAL_ROLL_PK' })
  nominalRoll: NominalRoll;
}
