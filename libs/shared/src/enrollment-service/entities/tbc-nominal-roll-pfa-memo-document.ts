import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { TbcNominalRollBatch } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch';

@Entity('TBC_NOMINAL_ROLL_PFA_MEMO_DOCUMENT')
export class TbcNominalRollPfaMemoDocument extends AbstractEntity<TbcNominalRollPfaMemoDocument> {
  @Column({ name: 'PFA_NAME', type: 'varchar2', length: 100 })
  pfaName: string;

  @Column({ name: 'PFA_CODE', type: 'varchar2', length: 100 })
  pfaCode: string;

  @Column({ name: 'PFC_NAME', type: 'varchar2', length: 100 })
  pfcName: string;

  @Column({ name: 'PFC_CODE', type: 'varchar2', length: 100 })
  pfcCode: string;

  @ManyToOne(() => TbcNominalRollBatch, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'BATCH_ID' })
  batch: TbcNominalRollBatch;

  @Column({ name: 'PFA_MEMO', type: 'blob', nullable: false })
  pfaMemo: Buffer;

  @Column({ name: 'PFA_SCHEDULE', type: 'blob', nullable: false })
  pfaSchedule: Buffer;
}
