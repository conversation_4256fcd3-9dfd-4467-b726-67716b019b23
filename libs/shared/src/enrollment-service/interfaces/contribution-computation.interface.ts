export interface ExitContributionEntity {
  rsaPin: string;
  dateOfFirstAppointment: Date;
  dateOfRetirement?: Date;
  dateOfExit?: Date; // For NominalRoll compatibility
  employerCode: string;

  // Fields to be updated with calculation results
  totalMonths?: string;
  totalEmolument?: string;
  paidToDate?: string;
  totalInterest?: string;
  totalPension?: string;
  totalPensionPlusInterest?: string;
  contributionRemarks?: string;

  // Method to save the entity after updates
  save(): Promise<any>;
  retrieveEmploymentPeriods(): EmploymentPeriodDetail[];
  updateEmploymentPeriod(index: number, updates: Partial<EmploymentPeriodDetail>): void;
}

// Define the salary details interface used by both entity types
export interface SalaryDetails {
  salaryStructure: string;
  gradeLevel: string;
  step: string;
}

// Define a interface for employment period details
export interface EmploymentPeriodDetail extends SalaryDetails {
  employmentYear: string;
  leaveStartDate?: Date;
  leaveEndDate?: Date;
  contributionRemarks?: string;
  totalEmolument?: string;
  monthsCovered?: string;
  salaryAmount?: string;
}
