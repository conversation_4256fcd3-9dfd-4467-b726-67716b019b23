/* eslint-disable */
import * as path from 'path';

export const SlipTemplates = {
  CERTIFIED_STAMP: {
    emailSubject: 'CERTIFIED_STAMP',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'certified-stamp.png'),
  },
  APPROVED_STAMP: {
    emailSubject: 'APPROVED_STAMP',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'approved-stamp.png'),
  },
  HEADER_BANNER: {
    emailSubject: 'HEADER_BANNER',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'slip-header-banner.png'),
  },
  ENROLMENT_SLIP: {
    emailSubject: 'ENROLMENT SLIP',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'enrolment-slip.ejs'),
  },
  ACKNOWLEDGEMENT_SLIP: {
    emailSubject: 'ACKNOWLEDGEMENT SLIP',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'acknowledgement-slip.ejs'),
  },
  EXCO_MEMO: {
    emailSubject: 'EXCO_MEMO',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'exco-accrued-rights-memo.ejs'),
  },
  EXCO_MEMO_CONTRIBUTIONS: {
    emailSubject: 'EXCO_MEMO_CONTRIBUTIONS',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'exco-contributions-memo.ejs'),
  },
  EXCO_MEMO_CONTRIBUTIONS_APPENDIX: {
    emailSubject: 'EXCO_MEMO_CONTRIBUTIONS_APPENDIX',
    messagePath: path.join(
      process.cwd(),
      'apps',
      'enrollment-ms',
      'src',
      'slips',
      'contribution-breakdown-appendix.ejs'
    ),
  },
  ACCOUNT_MEMO: {
    emailSubject: 'ACCOUNT_MEMO',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'account-accrued-rights-memo.ejs'),
  },
  ACCOUNT_MEMO_CONTRIBUTION: {
    emailSubject: 'ACCOUNT_MEMO_CONTRIBUTION',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'account-contribution-memo.ejs'),
  },
  EXIT_SCHEDULE: {
    emailSubject: 'EXIT_SCHEDULE',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'exit-schedule.ejs'),
  },
  CONTRIBUTION_SCHEDULE: {
    emailSubject: 'CONTRIBUTION_SCHEDULE',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'contribution-schedule.ejs'),
  },
  PFA_CONTRIBUTION_MEMO: {
    emailSubject: 'PFA_CONTRIBUTION_MEMO',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'pfa-contribution-memo.ejs'),
  },
  PFA_CONTRIBUTION_SCHEDULE: {
    emailSubject: 'PFA_CONTRIBUTION_SCHEDULE',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'pfa-contribution-schedule.ejs'),
  },
  PFC_CONTRIBUTION_MEMO: {
    emailSubject: 'PFC_CONTRIBUTION_MEMO',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'pfc-contribution-memo.ejs'),
  },
  PFA_ACCRUED_RIGHTS_MEMO: {
    emailSubject: 'PFA_ACCRUED_RIGHTS_MEMO',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'pfa-accrued-rights-memo.ejs'),
  },
  PFA_ACCRUED_RIGHTS_SCHEDULE: {
    emailSubject: 'PFA_ACCRUED_RIGHTS_SCHEDULE',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'pfa-accrued-rights-schedule.ejs'),
  },
  PFC_ACCRUED_RIGHTS_MEMO: {
    emailSubject: 'PFC_ACCRUED_RIGHTS_MEMO',
    messagePath: path.join(process.cwd(), 'apps', 'enrollment-ms', 'src', 'slips', 'pfc-accrued-rights-memo.ejs'),
  },
};
