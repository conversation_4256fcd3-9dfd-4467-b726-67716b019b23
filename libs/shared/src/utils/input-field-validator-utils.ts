/* eslint-disable */

import { isBefore, parseISO, startOfDay } from 'date-fns';
import { ISO_HYPHEN_DATE_FORMAT, THIRTIETH_JUNE_2007_DATE } from '@app/shared/constants';

const CUT_OFF_DATE = parseISO('2004-06-30');

export const validateDts = (dts: string, formatString: string): string | null => {
  if (!dts?.trim() || !formatString) {
    console.log('dts', dts, 'formatString', formatString);
    return null;
  }

  let finalDts = dts;
  if (formatString !== ISO_HYPHEN_DATE_FORMAT) {
    finalDts = convertDateFormatWithoutParsing(dts, formatString, ISO_HYPHEN_DATE_FORMAT);
  }

  const dtsDate = parseISO(finalDts);
  console.log('dtsDate', dtsDate, 'CUT_OFF_DATE', CUT_OFF_DATE);

  return isBefore(CUT_OFF_DATE, dtsDate) ? 'Transfer Date Not Accepted after 30 June 2004' : null;
};

export const validateDofa = (dofa: string, formatString: string): string | null => {
  if (!dofa || !dofa?.trim() || !formatString) {
    return 'Date of first appointment is required';
  }

  let finalDofa = dofa;
  if (formatString !== ISO_HYPHEN_DATE_FORMAT) {
    finalDofa = convertDateFormatWithoutParsing(dofa, formatString, ISO_HYPHEN_DATE_FORMAT);
  }

  const dofaDate = parseISO(finalDofa);
  const today = startOfDay(new Date());

  console.log('dofaDate', dofaDate, 'today', today);
  return isBefore(today, dofaDate) ? 'Date of first appointment must be less than current date' : null;
};

export function getIsoDateString(edor: string, formatString: string) {
  let finalEdor = edor;
  if (formatString !== ISO_HYPHEN_DATE_FORMAT) {
    finalEdor = convertDateFormatWithoutParsing(edor, formatString, ISO_HYPHEN_DATE_FORMAT);
  }
  return finalEdor;
}

export const validateEdor = (
  edor: string,
  dateOfBirth: string,
  dofa: string,
  dts: string,
  formatString: string
): string | null => {
  if (!edor) {
    return 'Expected Date of Retirement is required';
  }

  let edorDate = getIsoDateString(edor, formatString);
  if (isBefore(edorDate, THIRTIETH_JUNE_2007_DATE)) {
    return 'Registration process terminated. Please, contact Pension Transitional Arrangements Directorate (PTAD): EDOR is before 30th June 2007';
  }

  if (isBefore(edorDate, getIsoDateString(dateOfBirth, formatString))) {
    return 'Expected Date of Retirement must be after date of birth';
  }

  if (isBefore(edorDate, getIsoDateString(dofa, formatString))) {
    return 'Expected Date of Retirement must be after date of first appointment';
  }

  if (dts && isBefore(edor, getIsoDateString(dts, formatString))) {
    return 'Expected Date of Retirement must be after date of transfer of service';
  }
  return null;
};

/**
 * Converts a date string from one format to another without affecting the UTC offset.
 * @param dateStr - The original date string (e.g., '30/06/2004').
 * @param currentFormat - The current format of the date string (e.g., 'dd/MM/yyyy').
 * @param targetFormat - The target output format (e.g., 'dd-MM-yyyy').
 * @returns The correctly formatted date string or null if invalid.
 */
export function convertDateFormatWithoutParsing(
  dateStr: string,
  currentFormat: string,
  targetFormat: string
): string | null {
  if (!dateStr || !currentFormat || !targetFormat) {
    return null;
  }

  // Extract separators from both formats
  const currentSeparator = currentFormat.replace(/[dMy]/g, '')[0]; // e.g., '/' from 'dd/MM/yyyy'
  const targetSeparator = targetFormat.replace(/[dMy]/g, '')[0]; // e.g., '-' from 'dd-MM-yyyy'

  // Split the original date string into parts based on the current separator
  const dateParts = dateStr.split(currentSeparator);
  const formatParts = currentFormat.split(currentSeparator);
  const targetFormatParts = targetFormat.split(targetSeparator);

  if (dateParts.length !== formatParts.length) {
    return null; // Invalid format
  }

  // Create an object mapping format parts (dd, MM, yyyy) to actual values
  const dateObj: Record<string, string> = {};
  formatParts.forEach((part, index) => {
    dateObj[part] = dateParts[index];
  });

  // Construct the target format string
  return targetFormatParts.map((part) => dateObj[part]).join(targetSeparator);
}
