/* eslint-disable */
import { Response } from 'express';
import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { ResponseCodeEnum } from '@app/shared/enums';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';

export class SuccessResponse extends BaseResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  @Expose()
  success = true;

  @ApiProperty({
    description: 'Response payload',
    example: {},
  })
  @Expose()
  content: any;

  constructor(payload?: any, description?: string) {
    super(ResponseCodeEnum.SUCCESS);
    this.content = payload;
    if (description) {
      this.setDescription(description);
    }
  }

  send(res: Response, statusCode = 200) {
    return res.status(statusCode).json({
      success: this.success,
      statusCode: this.code,
      content: this.content,
      description: this.description,
    });
  }
}

export class ErrorResponse extends BaseResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: false,
  })
  @Expose()
  success = false;

  @ApiProperty({
    description: 'Error details',
    example: {},
  })
  @Expose()
  content: any;

  constructor(payload?: any, description?: string) {
    super(ResponseCodeEnum.ERROR);
    this.content = payload || {};
    if (description) {
      this.setDescription(description);
    }
  }

  send(res: Response, statusCode = 500) {
    return res.status(statusCode).json({
      success: this.success,
      statusCode: this.code,
      content: this.content,
      description: this.description,
    });
  }
}
