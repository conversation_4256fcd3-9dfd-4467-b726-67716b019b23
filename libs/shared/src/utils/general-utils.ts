/* eslint-disable */
import { MdaBiodataDto } from '@app/shared/dto/enrollment/mda-data-upload.dto';
import { MdaDeceasedDataUploadDto } from '@app/shared/dto/enrollment/mda-deceased-data-upload.dto';
import { AccruedRightsUploadDto } from '@app/shared/dto/enrollment/accrued-rights/accrued-rights-upload.dto';
import { CobraRole } from '@app/shared/user-service/entities/cobra-role.entity';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { UploadProcessTypeEnum } from '@app/shared/enums/upload-process-type-enum';
import { parse } from 'date-fns';
import { SLASH_DATE_FORMAT } from '@app/shared/constants';

const DATE_COLUMNS = [
  'DATE OF TRANSFER OF SERVICE',
  'EXPECTED DATE OF RETIREMENT',
  'DATE OF FIRST APPOINTMENT',
  'DATE OF DEATH',
  'DATE OF RETIREMENT',
  'DATE OF BIRTH',
];

export class GeneralUtils {
  // @ts-ignore
  tranformMdaRetireeBiodata<T extends MdaBiodataDto | MdaDeceasedDataUploadDto>(row: any): Partial<T> {
    const columnMapping = this.getRetireeColumnMapping();

    const transformedRow: Partial<T> = {} as Partial<T>;
    Object.keys(row).forEach((column) => {
      const mappedField = columnMapping[column]; // Convert Excel column to DTO field
      if (mappedField) {
        let value = row[column];
        // Check if the column requires date transformation
        if (DATE_COLUMNS.includes(column) && typeof value === 'number') {
          value = this.excelDateToString(value); // Convert Excel numeric date to string
        }
        transformedRow[mappedField] = value; // Assign value to correct field
      }
    });
    console.log(`transformedRow: ${JSON.stringify(transformedRow)}`);
    return transformedRow;
  }

  getRetireeColumnMapping() {
    const columnMapping: Record<string, string> = {
      RSAPIN: 'rsaPin',
      SURNAME: 'surname',
      FIRSTNAME: 'firstName',
      'STAFF ID': 'staffId',
      'EXPECTED DATE OF RETIREMENT': 'edor',
      'EMPLOYER(30 JUNE 2004)': 'employerJune2004',
      'SALARY STRUCTURE (30 JUNE 2004)': 'salaryStructureJune2004',
      'GRADE LEVEL (30 JUNE 2004)': 'gradeLevelJune2004',
      'STEP (30 JUNE 2004)': 'stepJune2004',
      'DATE OF TRANSFER OF SERVICE': 'dts',
      'DATE OF FIRST APPOINTMENT': 'dofa',
      'DATE OF DEATH': 'dateOfDeath',
    };
    return columnMapping;
  }

  excelDateToString(excelDate: number): string {
    const date = new Date((excelDate - 25569) * 86400000); // Convert Excel serial to JS date
    return date.toLocaleDateString('en-GB'); // Returns "04/04/2025"
  }
}

export const getStableNumber = (intervalMinutes: number): number => {
  return Math.floor(Date.now() / (intervalMinutes * 60 * 1000)); // This number stays the same for `intervalMinutes`
};

export const getDateOfDeath = (mdaBiodataDto: any, taskId: string): null | Date => {
  const dod = taskId.includes('DECEASED') ? (mdaBiodataDto as Partial<MdaDeceasedDataUploadDto>).dateOfDeath : null;

  if (!dod) {
    return null;
  }

  return getValidDate(dod, SLASH_DATE_FORMAT);
};

export const getValidDate = (dateString: string, formatString: string): null | Date => {
  if (!dateString) {
    return null;
  }

  return parse(dateString, formatString, new Date());
};

/*
 * converts string to date for format: MM/DD/YYYY
 * */
export const stringToDate = (dateStr: string): Date => {
  if (!dateStr) {
    return null;
  }
  const [day, month, year] = dateStr.split('/').map(Number);
  return new Date(year, month - 1, day); // Month is zero-based in JavaScript
};

export const transformAccruedRightsDto = <T extends AccruedRightsUploadDto>(row: any): Partial<T> => {
  const columnMapping: Record<string, string> = {
    RSAPIN: 'rsaPin',
    NAME: 'name',
    'DATE OF RETIREMENT': 'dateOfRetirement',
    'DATE OF BIRTH': 'dateOfBirth',
    'DATE OF FIRST APPOINTMENT': 'dateOfFirstAppointment',
    GENDER: 'gender',
    'APA VALUE': 'apaValue',
    'RETIREE TYPE': 'retireType',
  };
  const transformedRow: Partial<T> = {} as Partial<T>;
  tranformColumnMapping(row, columnMapping, transformedRow);
  return transformedRow;
};

export const excelDateToString = (excelDate: number): string => {
  const date = new Date((excelDate - 25569) * 86400000); // Convert Excel serial to JS date
  return date.toLocaleDateString('en-GB'); // Returns "04/04/2025"
};

function tranformColumnMapping<T>(row: any, columnMapping: Record<string, string>, transformedRow: Partial<T>) {
  Object.keys(row).forEach((column) => {
    const mappedField = columnMapping[column]; // Convert Excel column to DTO field
    if (mappedField) {
      let value = row[column];
      if (DATE_COLUMNS.includes(column) && typeof value === 'number') {
        value = excelDateToString(value); // Convert Excel numeric date to string
      }
      transformedRow[mappedField] = value; // Assign value to correct field
    }
  });
}

export const convertDateSlashToHyphen = (dateString: string): string => {
  if (!dateString) {
    return dateString;
  }
  const [day, month, year] = dateString.split('/');
  return `${year}-${month}-${day}`;
};

export const convertDbHyphenDateToHyphen = (dateInput: string): string => {
  if (dateInput.includes('T')) {
    return dateInput.split('T')[0]; // Extracts the YYYY-MM-DD part if it's an ISO string
  }
  return dateInput;
};

export const convertDbHyphenDateToSlash = (dateInput: string): string => {
  if (!dateInput) {
    return dateInput;
  }
  const date = new Date(dateInput); // Convert to Date object
  const day = String(date.getUTCDate()).padStart(2, '0'); // Get day (2 digits)
  const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // Get month (2 digits, Jan = 0)
  const year = date.getUTCFullYear(); // Get full year

  return `${day}/${month}/${year}`;
};

export const getRolePrivileges = (
  roles: CobraRole[]
): { privileges: Set<string>; roles: Set<string>; isAdmin: boolean } | null => {
  if (!roles) {
    console.log('No roles');
    return null;
  }

  const privileges = new Set<string>();
  const roleNames = new Set<string>();
  let isAdmin = false;
  roles.forEach((role) => {
    if (role.isAdmin) {
      isAdmin = true;
    }
    roleNames.add(role.role);
    if (role.privileges) {
      role.privileges.forEach((priv) => {
        try {
          const privilegeEntry = CobraPrivileges[priv.privilege as keyof typeof CobraPrivileges];

          if (!privilegeEntry) {
            throw new Error(`Privilege key '${priv.privilege}' does not exist in CobraPrivileges`);
          }

          privileges.add(privilegeEntry.code);
        } catch (error) {
          console.warn(
            `Error processing privilege '${priv.privilege}': ${error instanceof Error ? error.message : error}`
          );
        }
      });
    }
  });
  return { privileges, roles: roleNames, isAdmin };
};

export const normalizeExcelDateFields = (obj: Record<string, any>): Record<string, any> => {
  const newObj = { ...obj };

  for (const field of DATE_COLUMNS) {
    if (newObj[field] && typeof newObj[field] === 'number') {
      newObj[field] = excelDateToString(newObj[field]);
    }
  }
  return newObj;
};

export const extractMdaCode = (taskId: string): string | null => {
  const processTypes = Object.values(UploadProcessTypeEnum);

  // Find the matching process type in the taskId
  const matchingProcessType = processTypes.find((type) => taskId.startsWith(type));

  if (!matchingProcessType) {
    return null; // No valid process type found
  }

  // Split the taskId into parts
  const parts = taskId.split('-');

  // Dynamically determine mdaCode position based on processType word count
  const mdaCodeIndex = matchingProcessType.split('-').length; // MDA code is right after processType

  return parts[mdaCodeIndex] || null;
};

export const isBeforeOld = (before: string, after: string): boolean => {
  const parseDate = (dateStr: string): Date => {
    const [day, month, year] = dateStr.split('/').map(Number);
    return new Date(year, month - 1, day); // Month is zero-based in JS Date
  };

  const date1 = parseDate(before);
  const date2 = parseDate(after);

  return date1 < date2; // Returns true if checkFor is before checkAgainst
};

export const isBeforeToday = (dateStr: string): boolean => {
  const parseDate = (date: string): Date => {
    const [day, month, year] = date.split('/').map(Number);
    return new Date(year, month - 1, day); // Convert to JavaScript Date
  };
  const inputDate = parseDate(dateStr);
  const today = new Date();

  // Remove time part from today's date to ensure only date comparison
  today.setHours(0, 0, 0, 0);
  return inputDate < today; // Returns true if the date is before today
};

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const filterBands = (leaveStartDate: string, endDate: string, ippisDate: string | null): number[] | string => {
  const start = new Date(leaveStartDate);
  const end = new Date(endDate);

  if (start >= end) {
    return 'Start date must be before end date';
  }

  // Determine band limit year
  const limitYear = ippisDate ? new Date(ippisDate).getFullYear() : new Date().getFullYear();

  // Find the closest band limit
  let maxBandYear = 2004;
  while (maxBandYear + 3 <= limitYear) {
    maxBandYear += 3;
  }

  const bands: number[] = [];
  let year = 2004;

  // Generate bands dynamically up to the maxBandYear
  while (year <= maxBandYear) {
    bands.push(year);
    year += 3;
  }

  // Filter bands
  return bands.filter((bandYear) => {
    const bandStart = new Date(bandYear, 0, 1); // Jan 1st of the band year
    const bandEnd = new Date(bandYear + 3, 0, 1); // Jan 1st of the next band

    // Keep bands that are partially or fully before start date
    if (bandStart < start && bandEnd > start) return true;

    // Keep bands that are partially or fully after end date
    if (bandStart < end && bandEnd > end) return true;

    // Remove bands fully between start and end
    return bandStart < start || bandEnd > end;
  });
};

export function generateDigits(length: number): string {
  return Array.from({ length })
    .map(() => Math.floor(Math.random() * 10).toString())
    .join('');
}
