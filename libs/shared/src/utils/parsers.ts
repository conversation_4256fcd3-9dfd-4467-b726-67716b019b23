/**
 * Replaces the line breaks with a space.
 *
 * @param text - Some text
 */
export const removeLineBreaks = (text: string): string => {
  return text.replace(/(\r\n|\n|\r)/gm, ' ').replace(/\s+/g, ' ');
};

/**
 * returns current date.
 *
 */
export const getCurrentDate = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // Ensure two digits
  const day = String(now.getDate()).padStart(2, '0'); // Ensure two digits
  return `${year}${month}${day}`;
};
/**
 * Parses used_memory to an integer.
 *
 * @param info - Memory consumption related information
 */
export const parseUsedMemory = (info: string): number => {
  const start = info.indexOf('used_memory');
  const end = info.indexOf('used_memory_human') - 1;
  if (start < 0 || end < 0) return 0;
  return Number.parseInt(info.slice(start, end).split(':')[1], 10);
};
