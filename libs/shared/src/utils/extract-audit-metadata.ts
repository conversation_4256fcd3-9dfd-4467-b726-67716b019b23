/* eslint-disable */
import { ICustomRequest } from './custom-request-type';

export function extractAuditMetadata(req: ICustomRequest): Record<string, any> {
  return {
    user: req.user || null,
    userAgent: req.headers['user-agent'] || 'Unknown',
    referer: req.headers['referer'] || 'Unknown',
    forwardedFor: req.headers['x-forwarded-for'] || req.ip,
    method: req.method,
    path: req.url,
    extra: req.metadata?.extra || null,
  };
}
