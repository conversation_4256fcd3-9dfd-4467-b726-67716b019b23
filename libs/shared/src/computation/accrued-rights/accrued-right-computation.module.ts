import { Module } from '@nestjs/common';
import { FactorsService } from '@app/shared/computation/accrued-rights/FactorsService';
import { BaseAccruedRightsService } from '@app/shared/computation/accrued-rights/base-accrued-rights.service';
import { OthersAccruedRightsService } from '@app/shared/computation/accrued-rights/others-accrued-rights.service';
import { Professor15AccruedRightsService } from '@app/shared/computation/accrued-rights/professor-15-accrued-rights.service';
import { ProfessorLess15AccruedRightsService } from '@app/shared/computation/accrued-rights/professor-less-15-accrued-rights.service';
import { AccruedRightsFactoryService } from '@app/shared/computation/accrued-rights/accrued-rights-factory.service';

@Module({
  imports: [],
  providers: [
    FactorsService,
    {
      provide: BaseAccruedRightsService, // Abstract class
      useClass: Professor15AccruedRightsService, // Default implementation
    },
    Professor15AccruedRightsService,
    OthersAccruedRightsService,
    ProfessorLess15AccruedRightsService,
    AccruedRightsFactoryService,
  ],
  exports: [
    FactorsService,
    Professor15AccruedRightsService,
    OthersAccruedRightsService,
    ProfessorLess15AccruedRightsService,
    AccruedRightsFactoryService,
  ],
})
export class AccruedRightComputationModule {}
