import { Injectable } from '@nestjs/common';

/* eslint-disable */
@Injectable()
export class FactorsService {
  readonly annuityFactors = [
    { age: 20, male: 0.8744, female: 0.8992 },
    { age: 21, male: 0.9617, female: 0.9892 },
    { age: 22, male: 1.0579, female: 1.0881 },
    { age: 23, male: 1.1637, female: 1.1969 },
    { age: 24, male: 1.2801, female: 1.3166 },
    { age: 25, male: 1.4081, female: 1.4483 },
    { age: 26, male: 1.5489, female: 1.593 },
    { age: 27, male: 1.7038, female: 1.7524 },
    { age: 28, male: 1.8742, female: 1.9276 },
    { age: 29, male: 2.0616, female: 2.1204 },
    { age: 30, male: 2.2677, female: 2.3324 },
    { age: 31, male: 2.4945, female: 2.5656 },
    { age: 32, male: 2.744, female: 2.8222 },
    { age: 33, male: 3.0184, female: 3.1044 },
    { age: 34, male: 3.3202, female: 3.4149 },
    { age: 35, male: 3.6523, female: 3.7563 },
    { age: 36, male: 4.0175, female: 4.132 },
    { age: 37, male: 4.4192, female: 4.5452 },
    { age: 38, male: 4.8611, female: 4.9997 },
    { age: 39, male: 5.3473, female: 5.4997 },
    { age: 40, male: 5.882, female: 6.0496 },
    { age: 41, male: 6.4702, female: 6.6546 },
    { age: 42, male: 7.1172, female: 7.3201 },
    { age: 43, male: 7.8289, female: 8.0521 },
    { age: 44, male: 8.61182, female: 8.85727 },
    { age: 45, male: 9.473, female: 9.743 },
    { age: 46, male: 9.405, female: 9.694 },
    { age: 47, male: 9.333, female: 9.641 },
    { age: 48, male: 9.257, female: 9.585 },
    { age: 49, male: 9.176, female: 9.526 },
    { age: 50, male: 9.092, female: 9.463 },
    { age: 51, male: 9.003, female: 9.396 },
    { age: 52, male: 8.909, female: 9.324 },
    { age: 53, male: 8.811, female: 9.249 },
    { age: 54, male: 8.709, female: 9.169 },
    { age: 55, male: 8.601, female: 9.084 },
    { age: 56, male: 8.489, female: 8.994 },
    { age: 57, male: 8.372, female: 8.899 },
    { age: 58, male: 8.25, female: 8.799 },
    { age: 59, male: 8.124, female: 8.694 },
    { age: 60, male: 7.993, female: 8.585 },
    { age: 61, male: 7.858, female: 8.469 },
    { age: 62, male: 7.719, female: 8.349 },
    { age: 63, male: 7.576, female: 8.223 },
    { age: 64, male: 7.43, female: 8.092 },
    { age: 65, male: 7.28, female: 7.956 },
    { age: 66, male: 7.128, female: 7.816 },
    { age: 67, male: 6.974, female: 7.671 },
    { age: 68, male: 6.819, female: 7.523 },
    { age: 69, male: 6.662, female: 7.37 },
    { age: 70, male: 6.505, female: 7.214 },
    { age: 71, male: 6.349, female: 7.056 },
    { age: 72, male: 6.194, female: 6.895 },
    { age: 73, male: 6.04, female: 6.733 },
    { age: 74, male: 5.889, female: 6.57 },
  ];

  private readonly pensionAccrualTable = [
    { years: 10, rate: 0.3 },
    { years: 11, rate: 0.32 },
    { years: 12, rate: 0.34 },
    { years: 13, rate: 0.36 },
    { years: 14, rate: 0.38 },
    { years: 15, rate: 0.4 },
    { years: 16, rate: 0.42 },
    { years: 17, rate: 0.44 },
    { years: 18, rate: 0.46 },
    { years: 19, rate: 0.48 },
    { years: 20, rate: 0.5 },
    { years: 21, rate: 0.52 },
    { years: 22, rate: 0.54 },
    { years: 23, rate: 0.56 },
    { years: 24, rate: 0.58 },
    { years: 25, rate: 0.6 },
    { years: 26, rate: 0.62 },
    { years: 27, rate: 0.64 },
    { years: 28, rate: 0.66 },
    { years: 29, rate: 0.68 },
    { years: 30, rate: 0.7 },
    { years: 31, rate: 0.72 },
    { years: 32, rate: 0.74 },
    { years: 33, rate: 0.76 },
    { years: 34, rate: 0.78 },
    { years: 35, rate: 0.8 },
  ];

  getAnnuityFactor(retirementAge: number, gender: 'M' | 'F'): number {
    console.log(`Retirement age ${retirementAge}`);
    const factor = this.annuityFactors.find((f) => f.age === retirementAge);
    if (!factor) {
      throw new Error(`Annuity factor not found for age ${retirementAge}`);
    }
    return gender === 'M' ? factor.male : factor.female;
  }

  getPensionAccrualRate(years: number): number {
    const entry = this.pensionAccrualTable.reduce((prev, curr) => (years >= curr.years ? curr : prev));
    return entry.rate + (years - entry.years) * 0.02;
  }
}
