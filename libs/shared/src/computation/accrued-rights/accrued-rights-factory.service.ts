/* eslint-disable */
import { Inject, Injectable } from '@nestjs/common';
import { BaseAccruedRightsService } from '@app/shared/computation/accrued-rights/base-accrued-rights.service';
import { Professor15AccruedRightsService } from '@app/shared/computation/accrued-rights/professor-15-accrued-rights.service';
import { ProfessorLess15AccruedRightsService } from '@app/shared/computation/accrued-rights/professor-less-15-accrued-rights.service';
import { OthersAccruedRightsService } from '@app/shared/computation/accrued-rights/others-accrued-rights.service';
import { CalculationResult } from '@app/shared/computation/accrued-rights/calculation.dto';
import { AgencySector } from '@app/shared/enrollment-service/entities/agency-sector.entity';
import { EmploymentDetail } from '@app/shared/enrollment-service/entities/employment-detail.entity';
import { RetirementBenefitTypeEnum } from '@app/shared/computation/enums/RetirementBenefitTypeEnum';

@Injectable()
export class AccruedRightsFactoryService {
  constructor(
    @Inject(Professor15AccruedRightsService)
    private readonly professor15Service: Professor15AccruedRightsService,
    @Inject(ProfessorLess15AccruedRightsService)
    private readonly professorLess15Service: ProfessorLess15AccruedRightsService,
    @Inject(OthersAccruedRightsService)
    private readonly othersService: OthersAccruedRightsService
  ) {}

  getAccruedRightsService(sector: string): BaseAccruedRightsService {
    if (sector === '1001') {
      return this.professor15Service;
    } else if (sector === '1002') {
      return this.professorLess15Service;
    } else if (sector === '1003') {
      return this.othersService;
    } else {
      return null;
    }
  }

  retrieveBenefitsDetails(
    accruedRightsResult: CalculationResult,
    agencySector: AgencySector,
    employmentDetail2004: EmploymentDetail
  ): { benefit: number; retirementType: string } {
    const earlyValue = accruedRightsResult.earlyRetirementAccruedBenefit;
    const normalValue = accruedRightsResult.normalRetirementAccruedBenefit;

    const isUASS = employmentDetail2004.salaryStructure === 'UASS';
    const gradeLevel = Number(employmentDetail2004.gradeLevel);
    const ageAtRetirement = accruedRightsResult.normalRetirementYears;
    const lengthOfService = accruedRightsResult.normalRetirementYears;
    const sectorType = agencySector.sector;

    switch (sectorType) {
      case '1001':
        if (isUASS) {
          if (gradeLevel! >= 6 && ageAtRetirement === 70) {
            return {
              benefit: normalValue,
              retirementType: RetirementBenefitTypeEnum.NORMAL,
            };
          }
          if (gradeLevel! >= 6 && ageAtRetirement < 70) {
            return {
              benefit: earlyValue,
              retirementType: RetirementBenefitTypeEnum.EARLY,
            };
          }
          if (gradeLevel! <= 5 && ageAtRetirement === 65) {
            return {
              benefit: normalValue,
              retirementType: RetirementBenefitTypeEnum.NORMAL,
            };
          }
          if (gradeLevel! <= 5 && ageAtRetirement < 65) {
            return {
              benefit: earlyValue,
              retirementType: RetirementBenefitTypeEnum.EARLY,
            };
          }
        }
        if (!isUASS && ageAtRetirement < 70) {
          return {
            benefit: earlyValue,
            retirementType: RetirementBenefitTypeEnum.EARLY,
          };
        } else {
          return {
            benefit: normalValue,
            retirementType: RetirementBenefitTypeEnum.NORMAL,
          };
        }

      case '1002':
        if (ageAtRetirement === 65) {
          return {
            benefit: normalValue,
            retirementType: RetirementBenefitTypeEnum.NORMAL,
          };
        }
        if (ageAtRetirement < 65) {
          return {
            benefit: earlyValue,
            retirementType: RetirementBenefitTypeEnum.EARLY,
          };
        }
        break;

      case '1003':
        if (ageAtRetirement === 60 || (lengthOfService !== undefined && lengthOfService >= 35)) {
          return {
            benefit: normalValue,
            retirementType: RetirementBenefitTypeEnum.NORMAL,
          };
        }
        if (ageAtRetirement < 60 && (lengthOfService === undefined || lengthOfService < 35)) {
          return {
            benefit: earlyValue,
            retirementType: RetirementBenefitTypeEnum.EARLY,
          };
        }
        break;

      default:
        throw new Error(`Unknown sectorType: ${sectorType}`);
    }

    throw new Error('No matching retirement rule found');
  }
}
