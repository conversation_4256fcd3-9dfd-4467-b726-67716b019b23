/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { FactorsService } from '@app/shared/computation/accrued-rights/FactorsService';
import { BaseAccruedRightsService } from '@app/shared/computation/accrued-rights/base-accrued-rights.service';

@Injectable()
export class OthersAccruedRightsService extends BaseAccruedRightsService {
  constructor(protected readonly factorsService: FactorsService) {
    super(factorsService);
  }

  calculateAdditionalFields(C11: string, C12: string, C13: 'M' | 'F', C14: number, C15: string, C16: number) {
    return this.calculateCommonFields(C11, C12, C13, C14, C15, C16);
  }

  protected getP5(C11: string): number {
    const birthDate = this.parseDate(C11);
    const diffMs = this.P3.getTime() - birthDate.getTime();
    const years = diffMs / (1000 * 60 * 60 * 24 * 365.25);
    return Math.round(years);
  }

  protected getC23(P5: number, Q5: number): number {
    const value1 = 60 - P5;
    const value2 = 35 - Q5;
    return Math.min(value1, value2);
  }

  protected getP10(Q5: number): number {
    return this.getPValue(Q5);
  }

  protected getP11(O11: number): number {
    return this.getPValue(O11);
  }

  protected getP12(O12: number): number {
    return this.getPValue(O12);
  }

  protected getP6(): number {
    return 60; // Fixed retirement age
  }

  protected getO11(P5: number, Q5: number): number {
    return 60 - P5 + Q5;
  }
}
