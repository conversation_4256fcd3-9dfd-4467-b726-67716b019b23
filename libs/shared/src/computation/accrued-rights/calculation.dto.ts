export class CalculationResult {
  accruedBenefitsVd: number; // Accrued Benefits as at valuation date
  normalRetirementYears: number; // Number of years to retire (normal retirement)
  normalRetirementAccruedBenefit: number; // Accrued Benefits accumulated to retirement
  earlyRetirementYears: number; // Number of years to retire (early retirement)
  earlyRetirementAccruedBenefit: number; // Accrued Benefits accumulated to early retirement
}

export interface ICalculationParams {
  C11: string;
  C12: string;
  C13: 'M' | 'F';
  C14: number;
  C15: string;
  C16: number;
  type: 'OTHERS' | 'PROF15' | 'PROFLESS15';
}
