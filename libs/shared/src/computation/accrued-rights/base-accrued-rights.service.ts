/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { FactorsService } from '@app/shared/computation/accrued-rights/FactorsService';
import { CalculationResult } from '@app/shared/computation/accrued-rights/calculation.dto';

@Injectable()
export abstract class BaseAccruedRightsService {
  protected constructor(protected readonly factorsService: FactorsService) {}

  protected readonly P3 = new Date('2004-06-30');
  protected abstract getP5(C11: string): number;
  protected abstract getC23(P5: number, Q5: number): number;
  protected abstract getP10(Q5: number): number;
  protected abstract getP11(O11: number): number;
  protected abstract getP12(O12: number): number;
  protected abstract getP6(): number;
  protected abstract getO11(P5: number, Q5: number): number;

  protected parseDate(dateString: string): Date {
    const parts = dateString.split('-'); // Expecting "YYYY-MM-DD"
    if (parts.length !== 3) {
      throw new Error(`Invalid date format: ${dateString}`);
    }

    const year = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1; // Months are 0-based in JS
    const day = parseInt(parts[2], 10);

    const date = new Date(Date.UTC(year, month, day)); // Ensure UTC parsing

    if (isNaN(date.getTime())) {
      throw new Error(`Invalid date string: ${dateString}`);
    }
    return date;
  }

  protected getP7(C16: number, P5: number): number {
    const yearCalc = Math.round(C16 - 2004 + P5);
    return yearCalc < 65 ? yearCalc : this.getP6();
  }

  protected excelSerialToDate(serial: number): Date {
    const excelEpoch = new Date(1899, 11, 30); // Excel's base date (Dec 30, 1899)
    // const excelEpoch = new Date(1900, 0, 1); // Excel starts from 1-Jan-1900
    console.log(`excelSerialToDate ${serial}`);
    return new Date(excelEpoch.getTime() + serial * 86400000); // Convert days to ms
  }

  protected getQ6(C12: string): Date {
    const joinDate = this.parseDate(C12);
    const diffDays = Math.round((this.P3.getTime() - joinDate.getTime()) / (1000 * 60 * 60 * 24));
    // const diffDays = differenceInDays( this.P3, new Date(C12));
    console.log(`========= diffDays: ${diffDays} C12: ${C12} this.P3: ${this.P3}`);
    return this.excelSerialToDate(diffDays);
  }

  protected getYearDifference(date1: Date, date2: Date): number {
    return date1.getFullYear() - date2.getFullYear();
  }

  protected getDayDifference(date1: Date, date2: Date): number {
    const diffMs = date1.getTime() - date2.getTime();
    return diffMs / (1000 * 60 * 60 * 24);
  }

  protected getR6(Q6: Date): number {
    const month = Q6.getUTCMonth() + 1; // JavaScript months are 0-indexed
    const day = Q6.getUTCDate();
    return ((month - 1) * 30.41667 + day) / 365;
  }

  protected getR7(Q7: Date): number {
    const month = Q7.getUTCMonth() + 1;
    return month / 12;
  }

  protected getQ5(C12: string): number {
    const joinDate = this.parseDate(C12);
    const day = joinDate.getDate();
    const Q6 = this.getQ6(C12);
    const Q7 = this.getQ6(C12); // Q7 = Q6 per requirement

    if (day <= 2) {
      return this.getR7(Q7) + this.getS7(this.getS6(this.getQ6(C12)));
    }
    if (day < 28) {
      return this.getR6(Q6) + this.getS6(Q6);
    }
    return this.getR7(Q7) + this.getS7(this.getS6(this.getQ6(C12)));
  }

  protected getS5(C12: string): number {
    const joinDate = this.parseDate(C12);
    return this.getYearDifference(this.P3, joinDate);
  }

  protected getS6(Q6: Date): number {
    return Q6.getFullYear() - 1900;
  }

  protected getS7(S6: number): number {
    return S6; // S7 = S6
  }

  protected getT5(C12: string): number {
    const joinDate = this.parseDate(C12);
    const date2004 = new Date(2004, joinDate.getMonth(), joinDate.getDate());
    const dayDifference = this.getDayDifference(this.P3, date2004);
    return dayDifference / 365.25;
  }

  protected getO10(Q5: number): number {
    return Q5; // O10 = Q5
  }

  protected getO12(C16: number, P5: number, Q5: number, O11: number): number {
    const yearCalc = C16 - 2004 + P5;
    return yearCalc < 65 ? C16 - 2004 + Q5 : O11;
  }

  protected getR10(C13: 'M' | 'F', P5: number): number {
    return this.getAnnuityFactor(Math.floor(P5), C13);
  }

  protected getR11(C13: 'M' | 'F', P6: number): number {
    return this.getAnnuityFactor(Math.floor(P6), C13);
  }

  protected getR12(C13: 'M' | 'F', P7: number): number {
    return this.getAnnuityFactor(Math.floor(P7), C13);
  }

  protected getAnnuityFactor(age: number, gender: 'M' | 'F'): number {
    const factor = this.factorsService.annuityFactors.find((f) => f.age === age);
    if (!factor) {
      throw new Error(`Annuity factor not found for age ${age}`);
    }
    return gender === 'M' ? factor.male : factor.female;
  }

  protected getC25(C19: number, C16: number, C23: number): number {
    return C19 * Math.pow(1 + C16, C23);
  }

  protected getC30(C15: string): number {
    const earlyRetirementDate = this.parseDate(C15);
    const diffDays = this.getDayDifference(earlyRetirementDate, this.P3);
    return diffDays / 365.25;
  }

  protected getC32(C19: number, C16: number, C30: number): number {
    return C19 * Math.pow(1 + C16, C30);
  }

  protected getPValue(years: number): number {
    if (years < 10) return (years * 1.5) / 100;
    if (years >= 35) return 0.8;
    const truncated = Math.trunc(years);
    return this.factorsService.getPensionAccrualRate(truncated) + (years - truncated) * 0.02;
  }

  protected getQValue(years: number): number {
    if (years <= 5) return years * 0.2;
    if (years <= 9) return 1 + (years - 5) * 0.08;
    if (years < 10) return 1.32;
    if (years >= 35) return 3;
    return 1 + (years - 10) * 0.08;
  }

  calculateAccruedRights(
    C11: string,
    C12: string,
    C13: 'M' | 'F',
    C14: number,
    C15: string,
    C16: number
  ): CalculationResult {
    console.log(
      `requestfields :::::::::::::C11: ${C11} ,C12: ${C12} ,C13:  ${C13} ,C14:  ${C14} ,C15:  ${C15} ,C16:  ${C16}`
    );
    const result = this.calculateCommonFields(C11, C12, C13, C14, C15, C16);
    console.log(`calculateCommonFields ::::::::::::: ${JSON.stringify(result)}`);

    const calculationResult = new CalculationResult();
    calculationResult.accruedBenefitsVd = Math.round(result.C19);
    calculationResult.normalRetirementYears = Math.round(result.C23);
    calculationResult.normalRetirementAccruedBenefit = Math.round(result.C25);
    calculationResult.earlyRetirementYears = parseFloat(result.C30.toFixed(1));
    calculationResult.earlyRetirementAccruedBenefit = Math.round(result.C32);

    console.log(`calculateAccruedRights ::::::::::::: ${JSON.stringify(calculationResult)}`);
    return calculationResult;
  }

  protected calculateCommonFields(C11: string, C12: string, C13: 'M' | 'F', C14: number, C15: string, C16: number) {
    const P5 = this.getP5(C11);
    const P6 = this.getP6();
    const P7 = this.getP7(C16, P5);
    const Q6 = this.getQ6(C12);
    const Q7 = this.getQ6(C12);
    const R6 = this.getR6(Q6);
    const R7 = this.getR7(Q7);
    const Q5 = this.getQ5(C12);

    const S5 = this.getS5(C12);
    const S6 = this.getS6(Q6);
    const S7 = this.getS7(S6);
    const T5 = this.getT5(C12);
    const O10 = this.getO10(Q5);
    const O11 = this.getO11(P5, Q5);
    const O12 = this.getO12(C16, P5, Q5, O11);

    const P10 = this.getP10(Q5); // Use custom P10 if provided, else default
    const P11 = this.getP11(O11); // Use custom P11 if provided, else default
    const P12 = this.getP12(O12); // Use custom P12 if provided, else default

    const Q10 = this.getQValue(Q5);
    const Q11 = this.getQValue(O11);
    const Q12 = this.getQValue(O12);

    const R10 = this.getR10(C13, P5);
    const R11 = this.getR11(C13, P6);

    const T10 = C14 * P10 * R10;
    const U10 = C14 * Q10;

    const C19 = T10 + U10;
    const C23 = this.getC23(P5, Q5);
    const C25 = this.getC25(C19, C16, C23);
    const C30 = this.getC30(C15);
    const C32 = this.getC32(C19, C16, C30);

    return {
      P3: this.P3.toISOString().split('T')[0], // Return as string in YYYY-MM-DD format
      P5,
      P6,
      P7,
      Q6, // Return as string in YYYY-MM-DD format
      Q7,
      R6,
      R7,
      Q5,
      S5,
      S6,
      S7,
      T5,
      O10,
      O11,
      O12,
      P10,
      P11,
      P12,
      Q10,
      Q11,
      Q12,
      R10,
      R11,
      T10,
      U10,
      C19,
      C23,
      C25,
      C30,
      C32,
    };
  }
}
