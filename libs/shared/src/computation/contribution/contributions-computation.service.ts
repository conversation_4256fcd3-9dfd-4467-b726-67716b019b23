import { BadRequestException, Injectable } from '@nestjs/common';
import { format, isBefore } from 'date-fns';
import BigNumber from 'bignumber.js';
import { PinoLogger } from 'nestjs-pino';
import { EnrolmentBiodata } from '@app/shared/enrollment-service/entities/enrolment-biodata.entity';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { ResponseCodeEnum, SettingsEnumKey } from '@app/shared/enums';
import { FlaggedPinRepository } from '@app/shared/enrollment-service/repository/flagged-pin.repository';
import { SalaryTypeRepository } from '@app/shared/enrollment-service/repository/salary-type.repository';
import { TbcNocontributionAgencyRepository } from '@app/shared/enrollment-service/repository/tbc-nocontribution-agency.repository';
import { TbcReconcilePayRepository } from '@app/shared/enrollment-service/repository/tbc-reconcile-pay.repository';
import { EnrolmentBiodataRepository } from '@app/shared/enrollment-service/repository/enrolment-biodata.repository';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { ExitContributionEntityFactory } from '@app/shared/computation/adapters/contributionEntityFactory';
import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';
import { NominalRollRepository } from '@app/shared/enrollment-service/repository/nominal-roll.repository';
import { EmploymentPeriodDetail } from '@app/shared/enrollment-service/interfaces/contribution-computation.interface';
import { AS_AT_JUNE_30TH_BAND, THIRTIETH_JUNE_2007_DATE } from '@app/shared/constants';

@Injectable()
export class ContributionsComputationService {
  constructor(
    private readonly settingMsLibService: SettingMsLibService,
    private flaggedPinRepository: FlaggedPinRepository,
    private salaryTypeRepository: SalaryTypeRepository,
    private tbcNocontributionAgencyRepository: TbcNocontributionAgencyRepository,
    private tbcReconcilePayRepository: TbcReconcilePayRepository,
    private readonly enrolmentBiodataRepository: EnrolmentBiodataRepository,
    private readonly nominalRollRepository: NominalRollRepository,
    readonly logger: PinoLogger
  ) {}

  /**
   * Process exit roll for employees
   * @returns Object with processing results
   * @param entity - EnrolmentBiodata or NominalRoll entity
   */
  async calculateContributions(
    entity: EnrolmentBiodata | NominalRoll
  ): Promise<BaseResponseWithContentNoPagination<any>> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);

    // Create adapter for the entity
    const adaptedEntity = ExitContributionEntityFactory.create(entity, {
      enrolmentBiodataRepository: this.enrolmentBiodataRepository,
      nominalRollRepository: this.nominalRollRepository,
    });

    const flaggedPin = await this.flaggedPinRepository.findOne({ rsaPin: adaptedEntity.rsaPin, active: true });
    if (flaggedPin) {
      // rejected
      console.log(`PIN ${adaptedEntity.rsaPin} is flagged: ${flaggedPin.reason}`);
      adaptedEntity.contributionRemarks = `PIN ${adaptedEntity.rsaPin} is flagged: ${flaggedPin.reason}`;
      await adaptedEntity.save();
      response.setDescription(`PIN ${adaptedEntity.rsaPin} is flagged: ${flaggedPin.reason}`);
      return response;
    }

    const interestRate = await this.settingMsLibService.getSettingInt(SettingsEnumKey.EXIT_CONTRIBUTION_INTEREST_RATE);
    const dofa = adaptedEntity.dateOfFirstAppointment;
    const dor = adaptedEntity.dateOfRetirement || adaptedEntity.dateOfExit;

    if (!dor) {
      const reason = `Date of retirement/exit is missing for RSA PIN: ${adaptedEntity.rsaPin}`;
      adaptedEntity.contributionRemarks = reason;
      await adaptedEntity.save();
      response.setDescription(reason);
      return response;
    }

    if (isBefore(dor, THIRTIETH_JUNE_2007_DATE)) {
      const reason = `Date of retirement/exit is before 30th June, 2007 for RSA PIN: ${adaptedEntity.rsaPin}`;
      adaptedEntity.contributionRemarks = reason;
      await adaptedEntity.save();
      response.setDescription(reason);
      return response;
    }

    const employmentPeriods = adaptedEntity.retrieveEmploymentPeriods();

    let totalMonths = new BigNumber(0);
    let totalEmoluments = new BigNumber(0);
    let exitCalculation = false;

    // Process for each salary structure/band
    for (let i = 0; i < employmentPeriods.length; i++) {
      const period = employmentPeriods[i];
      if (period.employmentYear === AS_AT_JUNE_30TH_BAND) {
        this.logger.info('AS AT JUNE 30TH BAND - skipping computation for June 2004 band');
        continue;
      }

      const leaveInfo = this.getLeaveDetail(period);

      let salaryType = period.salaryStructure;
      let gradeLevel = period.gradeLevel;
      let step = period.step;

      gradeLevel = gradeLevel.trim().length === 1 ? `0${gradeLevel.trim()}` : gradeLevel.trim();
      step = step.trim().length === 1 ? `0${step.trim()}` : step.trim();
      salaryType = this.refineSalaryType(salaryType, period.employmentYear);

      const salary = await this.salaryTypeRepository.getSalaryDetails(salaryType, gradeLevel, step);

      if (!salary) {
        const reason = `Salary details not found for ${period.employmentYear} ${salaryType} ${gradeLevel} ${step}`;
        console.log(reason);
        adaptedEntity.updateEmploymentPeriod(i, { contributionRemarks: reason });
        adaptedEntity.contributionRemarks = reason;
        await adaptedEntity.save();
        response.setDescription(reason);
        exitCalculation = true;
        break;
      }

      // Calculate date ranges for this band
      const { startDate, endDate } = this.getStartAndEndDateFromYear(Number(period.employmentYear));
      // Adjust dates based on employee's service dates
      const { shouldCompute, adjustedStartDate, adjustedEndDate } = this.adjustDateRanges(
        dofa,
        dor,
        startDate,
        endDate
      );

      if (!shouldCompute) {
        console.log(`Skipping computation for ${period.employmentYear} due to retirement date before start date`);
        adaptedEntity.updateEmploymentPeriod(i, {
          contributionRemarks: `Skipping computation for ${period.employmentYear} due to retirement date before start date`,
        });
        await adaptedEntity.save();
        continue;
      }

      // Calculate months and adjust for leave of absence
      let months: BigNumber = this.getMonthsBetween(adjustedStartDate, adjustedEndDate);
      if (leaveInfo) {
        const { leaveStartDate, leaveEndDate } = leaveInfo;

        // If leave period overlaps with this band period
        if (this.periodsOverlap(adjustedStartDate, adjustedEndDate, leaveStartDate, leaveEndDate)) {
          // Calculate overlap period
          const overlapStart = new Date(Math.max(adjustedStartDate.getTime(), leaveStartDate.getTime()));
          const overlapEnd = new Date(Math.min(adjustedEndDate.getTime(), leaveEndDate.getTime()));

          // Subtract leave months from total
          const leaveMonths = this.getMonthsBetween(overlapStart, overlapEnd);
          months = months.minus(leaveMonths);

          // Add leave remarks
          adaptedEntity.updateEmploymentPeriod(i, {
            contributionRemarks: `Leave of Absence from: ${format(leaveStartDate, 'dd-MMM-yyyy')} to ${format(leaveEndDate, 'dd-MMM-yyyy')}`,
          });
        }
      }

      // Get salary amount
      const salaryAmount = this.sumUpSalary(salary);
      const emoluments = months.multipliedBy(salaryAmount);

      adaptedEntity.updateEmploymentPeriod(i, {
        totalEmolument: `${emoluments.toFixed(2)}`,
        monthsCovered: `adjustedStartDate: ${adjustedStartDate} - adjustedEndDate: ${adjustedEndDate} - months: ${months}`,
        salaryAmount: `${salaryAmount}`,
      });

      // Add to totals
      totalMonths = totalMonths.plus(months);
      totalEmoluments = totalEmoluments.plus(emoluments);
    }

    if (exitCalculation) {
      return response;
    }

    // Set totals
    adaptedEntity.totalMonths = `${totalMonths.toNumber()}`;
    adaptedEntity.totalEmolument = `${totalEmoluments.toFixed(2)}`;

    const noContributionAgency = await this.tbcNocontributionAgencyRepository.findOne({
      employerCode: adaptedEntity.employerCode,
    });

    // Get payments made to date
    const paidToDate = noContributionAgency ? new BigNumber(0) : await this.getPaidToDate(adaptedEntity.rsaPin);

    adaptedEntity.paidToDate = `${paidToDate.toFixed(2)}`;

    // Calculate pension amount
    const pensionAmount = totalEmoluments.minus(paidToDate);
    adaptedEntity.totalPension = `${parseFloat(pensionAmount.toFixed(2))}`;

    // Calculate interest
    const interest = pensionAmount.times(interestRate).div(100);
    adaptedEntity.totalInterest = `${parseFloat(interest.toFixed(2))}`;

    // Calculate total pension with interest
    adaptedEntity.totalPensionPlusInterest = `${parseFloat(pensionAmount.plus(interest).toFixed(2))}`;

    // Save the updated entity
    await adaptedEntity.save();

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.content = adaptedEntity.totalPensionPlusInterest;
    return response;
  }

  /**
   * Adjust date ranges based on employment dates
   */
  private adjustDateRanges(dofa: Date, dor: Date, startDate: Date, endDate: Date) {
    let shouldCompute = true;
    let adjustedStartDate = new Date(startDate);
    let adjustedEndDate = new Date(endDate);

    // If retirement date is before start date, skip computation
    if (dor < startDate) {
      return { shouldCompute: false, adjustedStartDate, adjustedEndDate };
    }

    // If retirement date is before end date, adjust end date
    if (dor < endDate) {
      adjustedEndDate = new Date(dor);
    }

    // If employment start is after structure start, adjust start date
    if (dofa > startDate) {
      adjustedStartDate = new Date(dofa);
    }

    return { shouldCompute, adjustedStartDate, adjustedEndDate };
  }

  getStartAndEndDateFromYear(year: number): { startDate: Date; endDate: Date } {
    const baseYear = 2004;
    const currentYear = new Date().getFullYear();

    // Generate all valid years: 2004, 2007, ..., up to currentYear or nearest valid past year
    const validYears: number[] = [];
    for (let y = baseYear; y <= currentYear; y += 3) {
      validYears.push(y);
    }

    if (!validYears.includes(year)) {
      throw new BadRequestException(`Invalid year: ${year}. Allowed years are: ${validYears.join(', ')}`);
    }

    const startDate = new Date(
      year,
      year === 2004 ? 6 : 0, // July for 2004 (month is 0-indexed), January for others
      1
    );

    const endDate = new Date(year + 3 - 1, 11, 31); // December 31 of the 3rd year in the cycle

    return { startDate, endDate };
  }

  /**
   * Calculate months between two dates
   */
  private getMonthsBetween(start: Date, end: Date): BigNumber {
    const yearDiff = end.getFullYear() - start.getFullYear();
    const monthDiff = end.getMonth() - start.getMonth();
    const dayDiff = end.getDate() - start.getDate();

    // Basic month calculation
    let months = yearDiff * 12 + monthDiff;

    // Adjust for partial months
    if (dayDiff < 0) {
      months -= 1;
    }

    return new BigNumber(Math.max(0, months));
  }

  /**
   * Get leave of absence details
   */
  getLeaveDetail(employmentDetail: EmploymentPeriodDetail): { leaveStartDate: Date; leaveEndDate: Date } | null {
    if (!employmentDetail.leaveStartDate || !employmentDetail.leaveEndDate) {
      return null;
    }

    return {
      leaveStartDate: employmentDetail.leaveStartDate,
      leaveEndDate: employmentDetail.leaveEndDate,
    };
  }

  /**
   * Check if two date periods overlap
   */
  private periodsOverlap(start1: Date, end1: Date, start2: Date, end2: Date): boolean {
    return start1 <= end2 && start2 <= end1;
  }

  private async getPaidToDate(rsaPin: string) {
    const paidToDate = await this.tbcReconcilePayRepository.getTotalPaidByPin(rsaPin);
    return new BigNumber(paidToDate);
  }

  private sumUpSalary(sal: any): BigNumber {
    let total = new BigNumber(0);

    try {
      if (sal) {
        const salary = new BigNumber(sal.salary ?? 0);
        const housing = new BigNumber(sal.housing ?? 0);
        const transport = new BigNumber(sal.transport ?? 0);

        total = salary.plus(housing).plus(transport);
      }
    } catch (e) {
      console.error('Error processing getAmount. The error is:', (e as Error).message);
    }

    return total;
  }

  private refineSalaryType(salaryType: string, employmentYear: string): string {
    if (salaryType.includes('TOPS')) {
      const year = parseInt(employmentYear, 10);
      if (year === 2004) {
        return 'KONS';
      } else if (year === 2007) {
        return 'TOPS';
      } else if (year > 2007) {
        return 'TOPS10';
      }
    }
    return salaryType;
  }
}
