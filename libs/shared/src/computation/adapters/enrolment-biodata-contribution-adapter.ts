import { EnrolmentBiodata } from '@app/shared/enrollment-service/entities/enrolment-biodata.entity';
import {
  EmploymentPeriodDetail,
  ExitContributionEntity,
} from '@app/shared/enrollment-service/interfaces/contribution-computation.interface';

export class EnrolmentBiodataContributionAdapter implements ExitContributionEntity {
  private entity: EnrolmentBiodata;
  private enrolmentBiodataRepository: any;

  constructor(entity: EnrolmentBiodata, repository: any) {
    this.entity = entity;
    this.enrolmentBiodataRepository = repository;
  }

  get rsaPin(): string {
    return this.entity.rsaPin;
  }

  get totalPension(): string {
    return this.entity.totalPension;
  }

  get dateOfFirstAppointment(): Date {
    return this.entity.dateOfFirstAppointment;
  }

  get dateOfRetirement(): Date {
    return this.entity.dateOfRetirement;
  }

  get employerCode(): string {
    return this.entity.employerCode;
  }

  set totalMonths(value: string) {
    this.entity.totalMonths = value;
  }

  set totalEmolument(value: string) {
    this.entity.totalEmolument = value;
  }

  set paidToDate(value: string) {
    this.entity.paidToDate = value;
  }

  set totalInterest(value: string) {
    this.entity.totalInterest = value;
  }

  set totalPensionPlusInterest(value: string) {
    this.entity.totalPensionPlusInterest = value;
  }

  set totalPension(value: string) {
    this.entity.totalPension = value;
  }

  set contributionRemarks(value: string) {
    this.entity.contributionRemarks = value;
  }

  async save(): Promise<any> {
    return this.enrolmentBiodataRepository.saveEntity(this.entity);
  }

  retrieveEmploymentPeriods(): EmploymentPeriodDetail[] {
    return this.entity.employmentDetails;
  }

  // Update specific employment period details
  updateEmploymentPeriod(index: number, updates: Partial<EmploymentPeriodDetail>): void {
    Object.assign(this.entity.employmentDetails[index], updates);
  }
}
