import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';
import {
  EmploymentPeriodDetail,
  ExitContributionEntity,
} from '@app/shared/enrollment-service/interfaces/contribution-computation.interface';

export class NominalRollContributionAdapter implements ExitContributionEntity {
  private entity: NominalRoll;
  private nominalRollRepository: any;

  constructor(entity: NominalRoll, repository: any) {
    this.entity = entity;
    this.nominalRollRepository = repository;
  }

  get rsaPin(): string {
    return this.entity.rsaPin;
  }

  get totalPension(): string {
    return this.entity.totalPension;
  }

  get dateOfFirstAppointment(): Date {
    return this.entity.dateOfFirstAppointment;
  }

  get dateOfRetirement(): Date {
    // Use dateOfExit as dateOfRetirement for NominalRoll
    return this.entity.dateOfExit;
  }

  get employerCode(): string {
    return this.entity.mdaCode;
  }

  set totalMonths(value: string) {
    // Add these fields to NominalRoll if they don't exist
    this.entity['totalMonths'] = value;
  }

  set totalEmolument(value: string) {
    this.entity['totalEmolument'] = value;
  }

  set paidToDate(value: string) {
    this.entity['paidToDate'] = value;
  }

  set totalInterest(value: string) {
    this.entity['totalInterest'] = value;
  }

  set totalPensionPlusInterest(value: string) {
    this.entity['totalPensionPlusInterest'] = value;
  }

  set totalPension(value: string) {
    this.entity['totalPension'] = value;
  }

  set contributionRemarks(value: string) {
    this.entity['contributionRemarks'] = value;
  }

  async save(): Promise<any> {
    return this.nominalRollRepository.saveEntity(this.entity);
  }

  retrieveEmploymentPeriods(): EmploymentPeriodDetail[] {
    // Transform NominalRollBand + NominalRollBandDetails into EmploymentPeriodDetail format
    return this.entity.bands.flatMap((band) => {
      return band.nominalRollBandDetails.map((detail) => {
        return {
          employmentYear: band.year,
          salaryStructure: detail.salaryStructure,
          gradeLevel: detail.gradeLevel,
          step: detail.step,
          // Add other properties as needed
        };
      });
    });
  }

  // Update specific employment period details
  updateEmploymentPeriod(index: number, updates: Partial<EmploymentPeriodDetail>): void {
    // This is more complex for NominalRoll due to the nested structure
    // Find the correct band and detail to update
    let currentIndex = 0;

    for (const band of this.entity.bands) {
      for (const detail of band.nominalRollBandDetails) {
        if (currentIndex === index) {
          // Found the right detail to update
          if (updates.contributionRemarks) {
            band['contributionRemarks'] = updates.contributionRemarks;
          }
          if (updates.totalEmolument) {
            band['totalEmolument'] = updates.totalEmolument;
          }
          if (updates.monthsCovered) {
            band['monthsCovered'] = updates.monthsCovered;
          }
          if (updates.salaryAmount) {
            detail['salaryAmount'] = updates.salaryAmount;
          }
          return;
        }
        currentIndex++;
      }
    }
  }
}
