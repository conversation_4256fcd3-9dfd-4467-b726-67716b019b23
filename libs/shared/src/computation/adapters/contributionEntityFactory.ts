import { EnrolmentBiodata } from '@app/shared/enrollment-service/entities/enrolment-biodata.entity';
import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';
import { ExitContributionEntity } from '@app/shared/enrollment-service/interfaces/contribution-computation.interface';
import { EnrolmentBiodataContributionAdapter } from '@app/shared/computation/adapters/enrolment-biodata-contribution-adapter';
import { NominalRollContributionAdapter } from '@app/shared/computation/adapters/nominal-roll-contribution-adapter';

export class ExitContributionEntityFactory {
  static create(
    entity: EnrolmentBiodata | NominalRoll,
    repositories: {
      enrolmentBiodataRepository?: any;
      nominalRollRepository?: any;
    }
  ): ExitContributionEntity {
    if (entity instanceof EnrolmentBiodata) {
      return new EnrolmentBiodataContributionAdapter(entity, repositories.enrolmentBiodataRepository);
    } else if (entity instanceof NominalRoll) {
      return new NominalRollContributionAdapter(entity, repositories.nominalRollRepository);
    }
    throw new Error('Unsupported entity type');
  }
}
