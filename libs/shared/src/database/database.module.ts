import { DynamicModule, Logger, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EntityClassOrSchema } from '@nestjs/typeorm/dist/interfaces/entity-class-or-schema.type';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const connectString = configService.get<string>('DATABASE_HOST');
        const username = configService.get<string>('DATABASE_USERNAME');
        Logger.log(`Attempting to connect to DB: ${connectString} as ${username}`, 'DatabaseModule');
        return {
          type: 'oracle',
          connectString,
          username,
          password: configService.get<string>('DATABASE_PASSWORD'),
          autoLoadEntities: configService.get<boolean>('DATABASE_AUTOLOAD_ENTITIES', false),
          logging: configService.get<boolean>('DATABASE_LOGGING', false),
          synchronize: configService.get<boolean>('DATABASE_SYNCHRONIZE', false),
          extra: {
            poolMax: configService.get<number>('DATABASE_POOL_MAX', 1000),
            queueMax: configService.get<number>('DATABASE_QUEUE_MAX', 10000),
          },
        };
      },

      inject: [ConfigService],
    }),
  ],
  exports: [TypeOrmModule],
})
export class DatabaseModule {
  static forFeature(models: EntityClassOrSchema[]): DynamicModule {
    return {
      module: DatabaseModule,
      imports: [TypeOrmModule.forFeature(models)],
      exports: [TypeOrmModule],
    };
  }
}
