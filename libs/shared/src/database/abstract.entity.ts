import { Column, CreateDate<PERSON>olumn, <PERSON><PERSON>ty, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity()
export abstract class AbstractEntity<T> {
  @PrimaryGeneratedColumn()
  pk: number;

  @Column({
    name: 'ACTIVE',
    type: 'number',
    precision: 1,
    nullable: false,
    default: 1,
    transformer: {
      // Transform database value (0/1) to boolean (false/true)
      from: (value: number): boolean => value === 1,
      // Transform boolean (false/true) to database value (0/1)
      to: (value: boolean): number => (value ? 1 : 0),
    },
  })
  active: boolean = true;

  @Column({
    name: 'DELETED',
    type: 'number',
    precision: 1,
    nullable: false,
    default: 0,
    transformer: {
      // Transform database value (0/1) to boolean (false/true)
      from: (value: number): boolean => value === 1,
      // Transform boolean (false/true) to database value (0/1)
      to: (value: boolean): number => (value ? 1 : 0),
    },
  })
  deleted: boolean = false;

  @CreateDateColumn({ name: 'CREATE_DATE', nullable: false })
  createDate: Date;

  @UpdateDateColumn({ name: 'LAST_MODIFIED', nullable: false })
  lastModified: Date;

  constructor(entity: Partial<T>) {
    Object.assign(this, entity);
  }
}
