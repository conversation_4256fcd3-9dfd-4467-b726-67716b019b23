import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { EntityManager, FindOptionsWhere, QueryRunner, Repository } from 'typeorm';
import { NotFoundException } from '@nestjs/common';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { FindManyOptions } from 'typeorm/find-options/FindManyOptions';
import { PinoLogger } from 'nestjs-pino';

export abstract class AbstractRepository<T extends AbstractEntity<T>> {
  protected abstract readonly logger: PinoLogger;

  constructor(
    protected readonly entityRepository: Repository<T>,
    protected readonly entityManager: EntityManager
  ) {}

  getEntityManager(): EntityManager {
    return this.entityManager;
  }

  getEntityRepository(): Repository<T> {
    return this.entityRepository;
  }

  async saveEntity(baseEntity: T, queryRunner?: QueryRunner): Promise<T> {
    const manager = queryRunner?.manager || this.entityManager;
    return manager.save(baseEntity);
  }

  async saveEntities(entities: T[], queryRunner?: QueryRunner): Promise<T[]> {
    const manager = queryRunner?.manager || this.entityManager;
    return manager.save(entities);
  }

  async upsertEntity(
    entity: QueryDeepPartialEntity<T>,
    conflictPaths: string[],
    queryRunner?: QueryRunner
  ): Promise<T> {
    const whereCondition = conflictPaths.reduce((acc, key) => {
      acc[key] = entity[key];
      return acc;
    }, {} as FindOptionsWhere<T>);

    const existingEntity = await this.findOne(whereCondition, queryRunner);

    const repo = queryRunner?.manager.getRepository<T>(this.entityRepository.target) || this.entityRepository;

    if (existingEntity) {
      await repo.update(whereCondition, entity);
      return this.findOne(whereCondition, queryRunner);
    } else {
      return repo.save(entity as T);
    }
  }

  async findBy(where: FindOptionsWhere<T> | FindOptionsWhere<T>[], queryRunner?: QueryRunner): Promise<T[]> {
    const repo = queryRunner?.manager.getRepository<T>(this.entityRepository.target) || this.entityRepository;
    return await repo.findBy(where);
  }

  async find(where?: FindManyOptions<T>, queryRunner?: QueryRunner): Promise<T[]> {
    const repo = queryRunner?.manager.getRepository<T>(this.entityRepository.target) || this.entityRepository;
    return await repo.find(where);
  }

  async findOne(where: FindOptionsWhere<T> | FindOptionsWhere<T>[], queryRunner?: QueryRunner): Promise<T | null> {
    const repo = queryRunner?.manager.getRepository<T>(this.entityRepository.target) || this.entityRepository;
    const entity = await repo.findOne({ where });
    return entity || null;
  }

  async findOneWithRelations(
    where: FindOptionsWhere<T> | FindOptionsWhere<T>[],
    relations: string[] = [],
    queryRunner?: QueryRunner
  ): Promise<T | null> {
    const repo = queryRunner?.manager.getRepository<T>(this.entityRepository.target) || this.entityRepository;
    return await repo.findOne({ where, relations });
  }

  async findOneAndUpdate(
    where: FindOptionsWhere<T>,
    partialEntity: QueryDeepPartialEntity<T>,
    queryRunner?: QueryRunner
  ): Promise<T> {
    const repo = queryRunner?.manager.getRepository<T>(this.entityRepository.target) || this.entityRepository;

    const updateResult = await repo.update(where, partialEntity);

    if (!updateResult.affected) {
      this.logger.warn('Entity not found with where: ', where);
      throw new NotFoundException(`Entity not found`);
    }

    return this.findOne(where, queryRunner);
  }

  async findOneAndDelete(where: FindOptionsWhere<T>, queryRunner?: QueryRunner): Promise<void> {
    const repo = queryRunner?.manager.getRepository<T>(this.entityRepository.target) || this.entityRepository;
    await repo.delete(where);
  }

  async findManyAndDelete(where: FindOptionsWhere<T>, queryRunner?: QueryRunner): Promise<void> {
    const repo = queryRunner?.manager.getRepository<T>(this.entityRepository.target) || this.entityRepository;
    await repo.delete(where);
  }

  async removeEntity(entity: T, queryRunner?: QueryRunner): Promise<void> {
    const repo = queryRunner?.manager.getRepository<T>(this.entityRepository.target) || this.entityRepository;
    await repo.remove(entity);
  }

  async removeEntities(entity: T[], queryRunner?: QueryRunner): Promise<void> {
    const repo = queryRunner?.manager.getRepository<T>(this.entityRepository.target) || this.entityRepository;
    await repo.remove(entity);
  }
}
