import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserTypeEnum } from '../enums/UserTypeEnum';
import { ALLOWED_USER_TYPES_KEY } from '../decorators/allowed-user-types.decorator';
import { CustomException } from '@app/shared/filters/exception.dto';

@Injectable()
export class AllowedUserTypesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredUserTypes = this.reflector.getAllAndOverride<UserTypeEnum[]>(ALLOWED_USER_TYPES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredUserTypes) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();

    if (!user || !user.userType) {
      throw new CustomException('User type not found');
    }

    if (!requiredUserTypes.includes(user.userType)) {
      // throw new CustomException(`Access denied! Only ${requiredUserTypes.join(', ')} users can access this resource`);
    }

    return true;
  }
}
