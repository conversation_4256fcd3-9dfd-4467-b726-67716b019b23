FROM node:20

# Install dependencies needed for Puppeteer
RUN apt-get update && apt-get install -y \
    gconf-service \
    libasound2 \
    libatk1.0-0 \
    libc6 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgcc1 \
    libgconf-2-4 \
    libgdk-pixbuf2.0-0 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    ca-certificates \
    fonts-liberation \
    libappindicator1 \
    libnss3 \
    lsb-release \
    xdg-utils \
    wget \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /usr/src/app
COPY package*.json ./
COPY apps/pdf-generator-ms/package.json apps/pdf-generator-ms/
COPY tsconfig*.json ./
COPY nest-cli.json ./

# Ensure the apps directory exists
RUN mkdir -p apps

COPY apps/pdf-generator-ms apps/pdf-generator-ms
# COPY libs libs

RUN npm install && npx puppeteer browsers install chrome
COPY . .
# COPY . .
RUN npm run build pdf-generator-ms

# CMD ["npm", "run", "start:prod"]
CMD ["node", "dist/apps/pdf-generator-ms/main"]
