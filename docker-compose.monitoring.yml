services:
  portainer:
    image: portainer/portainer-ce
    container_name: portainer
    restart: always
    ports:
      - 3001:9000
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_data:/data
    networks:
      - microservices-network

  dozzle:
    image: amir20/dozzle
    container_name: dozzle
    restart: always
    ports:
      - 3020:8080
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - microservices-network

  uptime-kuma:
    image: louislam/uptime-kuma
    container_name: uptime-kuma
    restart: always
    ports:
      - 3003:3001
    networks:
      - microservices-network

  wetty:
    image: wettyoss/wetty
    container_name: wetty
    ports:
      - "3021:3000"
    command: --ssh-host=***********
    networks:
      - microservices-network

  metabase:
    image: metabase/metabase
    restart: always
    ports:
      - 3006:3000
    volumes:
      - metabase-data:/metabase-data
      - ./plugins:/plugins
    environment:
      - MB_PLUGINS_DIR=/plugins
      - MB_DB_FILE=/metabase-data/metabase.db
      - MB_IFRAME_EMBEDDING_ALLOWED_ORIGINS=http://***********:3006
    networks:
      - microservices-network

  cloudbeaver:
    image: dbeaver/cloudbeaver:latest
    container_name: cloudbeaver
    ports:
      - "3022:8978"
    volumes:
      - /var/cloudbeaver/workspace:/opt/cloudbeaver/workspace
    restart: unless-stopped

networks:
  microservices-network:
    driver: bridge

volumes:
  portainer_data:
  metabase-data:
