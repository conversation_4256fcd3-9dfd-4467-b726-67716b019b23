FROM node:20-alpine as builder

WORKDIR /usr/src/app

COPY package*.json ./
COPY apps/setting-ms/package.json apps/setting-ms/
COPY tsconfig*.json ./
COPY nest-cli.json ./

# Ensure the apps directory exists
RUN mkdir -p apps

COPY apps/setting-ms apps/setting-ms
COPY libs libs

RUN npm install
RUN npm run build setting-ms

FROM node:20-alpine

ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}
ENV TZ=Africa/Lagos

WORKDIR /usr/src/app

# Install logrotate (Alpine Linux uses apk)
RUN apk add --no-cache logrotate tzdata

# Create log directory (mapped to host via docker-compose)
RUN mkdir -p /usr/src/app/logs

# Copy logrotate configuration
COPY ./logrotate.conf /etc/logrotate.d/applogs

# Set logrotate to run daily (Alpine uses crond)
RUN echo "0 0 * * * /usr/sbin/logrotate -f /etc/logrotate.d/applogs" >> /etc/crontabs/root

# Start cron in the background (Alpine)
RUN crond

COPY package*.json ./
COPY apps/setting-ms/package.json apps/setting-ms/
RUN npm install --production

COPY --from=builder /usr/src/app/dist ./dist

EXPOSE 3000
CMD ["node", "dist/apps/setting-ms/main"]
