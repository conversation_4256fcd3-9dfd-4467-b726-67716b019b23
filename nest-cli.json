{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/login-ms/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/login-ms/tsconfig.app.json"}, "monorepo": true, "root": "apps/login-ms", "projects": {"audit-ms": {"type": "application", "root": "apps/audit-ms", "entryFile": "main", "sourceRoot": "apps/audit-ms/src", "compilerOptions": {"tsConfigPath": "apps/audit-ms/tsconfig.app.json"}}, "cron-ms": {"type": "application", "root": "apps/cron-ms", "entryFile": "main", "sourceRoot": "apps/cron-ms/src", "compilerOptions": {"tsConfigPath": "apps/cron-ms/tsconfig.app.json"}}, "ecrs-ms": {"type": "application", "root": "apps/ecrs-ms", "entryFile": "main", "sourceRoot": "apps/ecrs-ms/src", "compilerOptions": {"tsConfigPath": "apps/ecrs-ms/tsconfig.app.json"}}, "enrollment-engine": {"type": "application", "root": "apps/enrollment-engine", "entryFile": "main", "sourceRoot": "apps/enrollment-engine/src", "compilerOptions": {"tsConfigPath": "apps/enrollment-engine/tsconfig.app.json"}}, "enrollment-ms": {"type": "application", "root": "apps/enrollment-ms", "entryFile": "main", "sourceRoot": "apps/enrollment-ms/src", "compilerOptions": {"tsConfigPath": "apps/enrollment-ms/tsconfig.app.json"}}, "login-ms": {"type": "application", "root": "apps/login-ms", "entryFile": "main", "sourceRoot": "apps/login-ms/src", "compilerOptions": {"tsConfigPath": "apps/login-ms/tsconfig.app.json"}}, "notification-ms": {"type": "application", "root": "apps/notification-ms", "entryFile": "main", "sourceRoot": "apps/notification-ms/src", "compilerOptions": {"tsConfigPath": "apps/notification-ms/tsconfig.app.json"}}, "pdf-generator-ms": {"type": "application", "root": "apps/pdf-generator-ms", "entryFile": "main", "sourceRoot": "apps/pdf-generator-ms/src", "compilerOptions": {"tsConfigPath": "apps/pdf-generator-ms/tsconfig.app.json"}}, "setting-ms": {"type": "application", "root": "apps/setting-ms", "entryFile": "main", "sourceRoot": "apps/setting-ms/src", "compilerOptions": {"tsConfigPath": "apps/setting-ms/tsconfig.app.json"}}, "shared": {"type": "library", "root": "libs/shared", "entryFile": "index", "sourceRoot": "libs/shared/src", "compilerOptions": {"tsConfigPath": "libs/shared/tsconfig.lib.json"}}, "user-ms": {"type": "application", "root": "apps/user-ms", "entryFile": "main", "sourceRoot": "apps/user-ms/src", "compilerOptions": {"tsConfigPath": "apps/user-ms/tsconfig.app.json"}}}}