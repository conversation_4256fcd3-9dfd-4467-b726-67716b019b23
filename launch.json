{"version": "0.2.0", "configurations": [{"type": "node", "request": "attach", "name": "Debug enrollment-ms", "address": "localhost", "port": 9229, "localRoot": "${workspaceFolder}", "remoteRoot": "/usr/src/app", "restart": true, "skipFiles": ["<node_internals>/**"]}, {"type": "node", "request": "attach", "name": "Debug enrollment-engine", "address": "localhost", "port": 9230, "localRoot": "${workspaceFolder}", "remoteRoot": "/usr/src/app", "restart": true, "skipFiles": ["<node_internals>/**"]}, {"type": "node", "request": "attach", "name": "Debug ecrs-ms", "address": "localhost", "port": 9231, "localRoot": "${workspaceFolder}", "remoteRoot": "/usr/src/app", "restart": true, "skipFiles": ["<node_internals>/**"]}, {"type": "node", "request": "attach", "name": "Debug login-ms", "address": "localhost", "port": 9232, "localRoot": "${workspaceFolder}", "remoteRoot": "/usr/src/app", "restart": true, "skipFiles": ["<node_internals>/**"]}, {"type": "node", "request": "attach", "name": "Debug user-ms", "address": "localhost", "port": 9233, "localRoot": "${workspaceFolder}", "remoteRoot": "/usr/src/app", "restart": true, "skipFiles": ["<node_internals>/**"]}, {"type": "node", "request": "attach", "name": "Debug notification-ms", "address": "localhost", "port": 9234, "localRoot": "${workspaceFolder}", "remoteRoot": "/usr/src/app", "restart": true, "skipFiles": ["<node_internals>/**"]}, {"type": "node", "request": "attach", "name": "Debug audit-ms", "address": "localhost", "port": 9235, "localRoot": "${workspaceFolder}", "remoteRoot": "/usr/src/app", "restart": true, "skipFiles": ["<node_internals>/**"]}, {"type": "node", "request": "attach", "name": "Debug cron-ms", "address": "localhost", "port": 9228, "localRoot": "${workspaceFolder}", "remoteRoot": "/usr/src/app", "restart": true, "skipFiles": ["<node_internals>/**"]}]}