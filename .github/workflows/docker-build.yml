name: Docker Compose Build

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

env:
  <PERSON>OGIN_SERVICE_PORT: 3005
  USER_SERVICE_PORT: 3002
  USER_SERVICE_TCP_PORT: 3013
  SETTINGS_SERVICE_PORT: 3007
  NOTIFICATION_SERVICE_PORT: 3009
  NOTIFICATION_SERVICE_TCP_PORT: 3008
  ECRS_SERVICE_PORT: 3018
  ECRS_SERVICE_TCP_PORT: 3017
  ENROLLMENT_SERVICE_HTTP_PORT: 3010
  ENROLLMENT_SERVICE_TCP_PORT: 3016
  ENROLLMENT_ENGINE_SERVICE_PORT: 3019
  PDF_SERVICE_PORT: 3023
  CRON_SERVICE_PORT: 3021
  USER_SERVICE_HOST: 127.0.0.1
  ECRS_SERVICE_HOST: 127.0.0.1
  NOTIFICATION_SERVICE_HOST: 127.0.0.1
  ENROLLMENT_SERVICE_HOST: 127.0.0.1
  CLIENT_TIME_TOLERANCE_MINUTES: 15
  AUDIT_SERVICE_PORT: 3015
  AUDIT_QUEUE_NAME: audit-queue
  DATABASE_TYPE: oracle
  DATABASE_HOST: oraclecobradb/mypdb
  DATABASE_USERNAME: cobra
  DATABASE_PASSWORD: asdf
  DATABASE_SYNCHRONIZE: true
  DATABASE_LOGGING: true
  DATABASE_AUTOLOAD_ENTITIES: true
  RABBIT_MQ_URL: redis://cobra-redis-master
  REDIS_URL: redis://cobra-redis-master
  REDIS_TTL: 300
  SMTP_HOST: test
  SMTP_PORT: 587
  SMTP_USER: test
  SMTP_PASS: test
  JWT_SECRET: your-secret-key

jobs:
  install-and-build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install root dependencies
        run: npm install

      - name: Set up Docker Compose
        run: |
          docker compose version

      - name: Build Docker images
        run: |
          docker compose -f docker-compose.yml down
