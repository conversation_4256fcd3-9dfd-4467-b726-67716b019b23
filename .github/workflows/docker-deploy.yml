name: Selective Docker Compose Build and Deploy

on:
  push:
    branches:
      - deploy-portainer

env:
  LOGIN_SERVICE_PORT: 3005
  USER_SERVICE_PORT: 3002
  USER_SERVICE_TCP_PORT: 3013
  SETTINGS_SERVICE_PORT: 3007
  NOTIFICATION_SERVICE_PORT: 3009
  NOTIFICATION_SERVICE_TCP_PORT: 3008
  ECRS_SERVICE_PORT: 3018
  ECRS_SERVICE_TCP_PORT: 3017
  ENROLLMENT_SERVICE_HTTP_PORT: 3010
  ENROLLMENT_SERVICE_TCP_PORT: 3016
  ENROLLMENT_ENGINE_SERVICE_PORT: 3019
  CRON_SERVICE_PORT: 3021
  USER_SERVICE_HOST: 127.0.0.1
  ECRS_SERVICE_HOST: 127.0.0.1
  NOTIFICATION_SERVICE_HOST: 127.0.0.1
  ENROLLMENT_SERVICE_HOST: 127.0.0.1
  CLIENT_TIME_TOLERANCE_MINUTES: 15
  AUDIT_SERVICE_PORT: 3015
  PDF_SERVICE_PORT: 3023
  AUDIT_QUEUE_NAME: audit-queue
  DATABASE_TYPE: oracle
  DATABASE_HOST: oraclecobradb/mypdb
  DATABASE_USERNAME: cobra
  DATABASE_PASSWORD: asdf
  DATABASE_SYNCHRONIZE: true
  DATABASE_LOGGING: true
  DATABASE_AUTOLOAD_ENTITIES: true
  RABBIT_MQ_URL: redis://cobra-redis-master
  REDIS_URL: redis://cobra-redis-master
  REDIS_TTL: 300
  SMTP_HOST: test
  SMTP_PORT: 587
  SMTP_USER: test
  SMTP_PASS: test
  JWT_SECRET: your-secret-key

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        run: echo "${{ secrets.GH_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin

      - name: Detect changed services and dependencies
        id: changed-services
        run: |
          # Get list of changed files
          CHANGED_FILES=$(git diff --name-only HEAD^ HEAD)

          # Detect changed services under apps/
          CHANGED_SERVICES=$(echo "$CHANGED_FILES" | grep -E '^apps/[^/]+/' | cut -d'/' -f2 | sort -u | uniq)

          # Detect if shared libraries have changed
          if echo "$CHANGED_FILES" | grep -q '^libs/'; then
            echo "Shared library changed. Rebuilding all services."
            CHANGED_SERVICES=$(docker compose config --services)
          fi

          echo "Changed services: $CHANGED_SERVICES"
          echo "services=${CHANGED_SERVICES}" >> $GITHUB_ENV

      - name: Build and push changed services
        if: env.services != ''
        run: |
          for service in ${{ env.services }}; do
            IMAGE_NAME=ghcr.io/${{ github.repository_owner }}/$service
            docker compose build --no-cache $service
            docker compose push $service
          done

      - name: Trigger Portainer Deployment for Changed Services
        if: env.services != ''
        run: |
          for service in ${{ env.services }}; do
            echo "Triggering deployment for $service..."
            curl -X POST "${{ secrets.PORTAINER_URL }}/api/stacks/${{ secrets.PORTAINER_STACK_ID }}/update?endpointId=${{ secrets.PORTAINER_ENDPOINT_ID }}" \
            -H "Authorization: Bearer ${{ secrets.PORTAINER_API_KEY }}" \
            -H "Content-Type: application/json" \
            --data '{
              "pullImage": true
            }'
          done
