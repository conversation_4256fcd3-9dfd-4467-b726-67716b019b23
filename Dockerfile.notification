FROM node:20-alpine as builder

WORKDIR /usr/src/app

COPY package*.json ./
COPY apps/notification-ms/package.json apps/notification-ms/
COPY tsconfig*.json ./
COPY nest-cli.json ./

# Ensure the apps directory exists
RUN mkdir -p apps

COPY apps/notification-ms apps/notification-ms
COPY libs libs

RUN npm install
RUN npm run build notification-ms

FROM node:20-alpine

ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}
ENV TZ=Africa/Lagos

WORKDIR /usr/src/app

# Install logrotate (Alpine Linux uses apk)
RUN apk add --no-cache logrotate tzdata

# Create log directory (mapped to host via docker-compose)
RUN mkdir -p /usr/src/app/logs

# Copy logrotate configuration
COPY ./logrotate.conf /etc/logrotate.d/applogs

# Set logrotate to run daily (Alpine uses crond)
RUN echo "0 0 * * * /usr/sbin/logrotate -f /etc/logrotate.d/applogs" >> /etc/crontabs/root

# Start cron in the background (Alpine)
RUN crond

COPY package*.json ./
COPY apps/notification-ms/package.json apps/notification-ms/
RUN npm install --production

COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/apps/notification-ms/src/emailtemplates apps/notification-ms/src/emailtemplates
COPY --from=builder /usr/src/app/apps/notification-ms/src/public apps/notification-ms/src/public

EXPOSE 3000
CMD ["node", "dist/apps/notification-ms/main"]
