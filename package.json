{"name": "login-ms", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "workspaces": ["apps/*", "libs/*"], "scripts": {"build": "nest build", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:concurrently": "concurrently \"nest start notification-ms --watch\" \"nest start login-ms --watch\" \"nest start setting-ms --watch\" \"nest start user-ms --watch\"", "start:debug": "concurrently \"nest start enrollment-ms --debug=9229 --watch\" \"nest start enrollment-engine --debug=9230 --watch\" \"nest start ecrs-ms --debug=9231 --watch\" \"nest start login-ms --debug=9232 --watch\" \"nest start user-ms --debug=9933 --watch\" \"nest start notification-ms --debug=9234 --watch\" \"nest start audit-ms --debug=9235 --watch\" \"nest start cron-ms --debug=9228 --watch\" \"nest start pdf-generator-ms --debug=9248 --watch\"", "start:debuge": "concurrently \"nest start enrollment-ms --watch\" \"nest start login-ms --watch\" \"nest start user-ms --watch\"", "start:debug:enrollment-ms": "nest start --debug=0.0.0.0:9229 --watch enrollment-ms", "start:debug:enrollment-engine": "nest start --debug=0.0.0.0:9230 --watch enrollment-engine", "start:debug:ecrs-ms": "nest start --debug=0.0.0.0:9231 --watch ecrs-ms", "start:debug:login-ms": "nest start --debug=0.0.0.0:9232 --watch login-ms", "start:debug:user-ms": "nest start --debug=0.0.0.0:9933 --watch user-ms", "start:debug:notification-ms": "nest start --debug=0.0.0.0:9234 --watch notification-ms", "start:debug:audit-ms": "nest start --debug=0.0.0.0:9235 --watch audit-ms", "start:debug:cron-ms": "nest start --debug=0.0.0.0:9228 --watch cron-ms", "start:debug:pdf-generator-ms": "nest start --debug=0.0.0.0:9248 --watch pdf-generator-ms", "start:prod": "node dist/apps/login-ms/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config apps/login-ms/test/jest-e2e.json"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^11.0.9", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.9", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/microservices": "^11.0.9", "@nestjs/platform-express": "^11.0.9", "@nestjs/platform-socket.io": "^11.0.9", "@nestjs/swagger": "^11.0.3", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.0.9", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.5", "bcryptjs": "^2.4.3", "bignumber.js": "^9.3.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^4.1.0", "ioredis": "^5.5.0", "nestjs-pino": "^4.1.0", "oracledb": "^6.7.1", "pino-http": "^10.3.0", "pino-pretty": "^11.3.0", "reflect-metadata": "^0.2.0", "rotating-file-stream": "^3.2.6", "rxjs": "^7.8.1", "typeorm": "^0.3.20"}, "devDependencies": {"@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.21.0", "@nestjs/cli": "^11.0.2", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.9", "@types/bcryptjs": "^2.4.6", "@types/bun": "latest", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.17.24", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "concurrently": "^9.1.2", "eslint": "^9.21.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.3", "globals": "^16.0.0", "jest": "^29.5.0", "prettier": "^3.5.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/", "<rootDir>/libs/"], "moduleNameMapper": {"^@app/shared(|/.*)$": "<rootDir>/libs/shared/src/$1"}}}