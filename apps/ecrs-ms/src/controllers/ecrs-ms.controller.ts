import { Body, Controller, HttpStatus, Post } from '@nestjs/common';
import { ApiBadRequestResponse, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { RetrieveUserDto, RetrieveUserWithPinDto } from '@app/shared/dto/ecrs/retrieve-user.dto';
import { EcrsMsService } from '../services/ecrs-ms.service';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { EcrsServiceClientConstant } from '@app/shared/constants/ecrs-service-client.constant';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';

@Controller('')
export class EcrsMsController {
  constructor(private readonly ecrsService: EcrsMsService) {}

  @Post('retrieve-user')
  @ApiOperation({
    summary: 'Retrieve user details',
    description:
      'Retrieves user details based on RSA PIN, surname, and user type from different tables based on user type.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User details retrieved successfully',
    schema: {
      example: {
        success: true,
        statusCode: HttpStatus.OK,
        content: {
          code: 1,
          description: 'User detail found',
          surname: 'Smith',
        },
        description: 'User details retrieved successfully',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid input parameters',
    schema: {
      example: {
        success: false,
        statusCode: 400,
        content: null,
        description: 'Invalid input parameters provided',
      },
    },
  })
  async retrieveUser(@Body() retrieveUserDto: RetrieveUserDto): Promise<EcrsUserResponseDto> {
    return await this.ecrsService.findEcrsUser(retrieveUserDto, false);
  }

  @Post('retrieve-user-without-surname')
  @ApiOperation({
    summary: 'Retrieve user details',
    description:
      'Retrieves user details based on RSA PIN, surname, and user type from different tables based on user type.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User details retrieved successfully',
    schema: {
      example: {
        success: true,
        statusCode: HttpStatus.OK,
        content: {
          code: 1,
          description: 'User detail found',
          surname: 'Smith',
        },
        description: 'User details retrieved successfully',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid input parameters',
    schema: {
      example: {
        success: false,
        statusCode: 400,
        content: null,
        description: 'Invalid input parameters provided',
      },
    },
  })
  async retrieveUserWithPin(@Body() retrieveUserWtihPinDto: RetrieveUserWithPinDto): Promise<EcrsUserResponseDto> {
    const retrieveUserDto = new RetrieveUserDto();
    retrieveUserDto.rsaPin = retrieveUserWtihPinDto.rsaPin;
    retrieveUserDto.userType = RetireeUserTypeEnum.DECEASED;

    return await this.ecrsService.findEcrsUser(retrieveUserDto, true);
  }

  @MessagePattern({ cmd: EcrsServiceClientConstant.RETRIEVE_USER })
  async retrieveUserDetails(@Payload() retrieveUserDto: RetrieveUserDto): Promise<BaseResponseDto> {
    return await this.ecrsService.findEcrsUser(retrieveUserDto, retrieveUserDto.skipSurnameCheck ?? false);
  }
}
