/* eslint-disable */
import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { EcrsMsModule } from './ecrs-ms.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { GlobalExceptionFilter } from '@app/shared/filters/global-exception.filter';
import { Logger } from 'nestjs-pino';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(EcrsMsModule);
  const configService = app.get(ConfigService);

  app.setGlobalPrefix('ecrs');
  app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
  app.useLogger(app.get(Logger));

  const port = configService.get('ECRS_SERVICE_PORT');
  const tcpPort = configService.get('ECRS_SERVICE_TCP_PORT');

  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.TCP,
    options: {
      host: '0.0.0.0',
      port: tcpPort,
    },
  });

  const config = new DocumentBuilder()
    .setTitle('Ecrs Service')
    .setDescription('Documentation API for Notification Service')
    .setVersion(configService.get('ECSR_SERVICE_VERSION') || '1.0.0')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('swagger-api', app, document);

  app.useLogger(app.get(Logger));
  app.useGlobalFilters(new GlobalExceptionFilter());
  await app.startAllMicroservices();
  app.enableCors();
  await app.listen(port);
  console.log(`ECRS service is running on port ${port}, tcp port ${tcpPort}`);
}

bootstrap();
