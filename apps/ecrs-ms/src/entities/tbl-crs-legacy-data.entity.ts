import { Column, ViewEntity } from 'typeorm';

@ViewEntity('TBL_CRS_LEGACY_DATA', { synchronize: false })
export class TblCrsLegacyData {
  @Column({ name: 'PIN' })
  pin: string;

  @Column({ name: 'LASTNAME' })
  lastName: string;

  @Column({ name: 'SEX' })
  sex: string;

  @Column({ name: 'PFA' })
  pfa: string;

  @Column({ name: 'EMPLOYER_CODE' })
  employerCode: string;

  @Column({ name: 'OTHERNAME' })
  otherName: string;

  @Column({ name: 'FIRSTNAME' })
  firstName: string;

  @Column({ name: 'PHONE', nullable: true })
  phone: string;

  @Column({ name: 'DOB', type: 'date' })
  dob: string;
}
