import { Column, ViewEntity } from 'typeorm';

@ViewEntity('TBL_CONTRIBUTOR_BIODATA', { synchronize: false })
export class TblContributorBiodata {
  @Column({ name: 'RSAPIN', unique: true })
  rsaPin: string;

  @Column({ name: 'NIN', unique: true })
  nin: string;

  @Column({ name: 'SURNAME' })
  surname: string;

  @Column({ name: 'GENDER' })
  gender: string;

  @Column({ name: 'PFACODE' })
  pfaCode: string;

  @Column({ name: 'EMP_EMPLOYER_CODE' })
  empEmployerCode: string;

  @Column({ name: 'EMPLOYER_NAME' })
  employerName: string;

  @Column({ name: 'PFA_NAME' })
  pfaName: string;

  @Column({ name: 'MIDDLENA<PERSON>', nullable: true })
  middleName: string;

  @Column({ name: 'FIRST<PERSON><PERSON>' })
  firstName: string;

  @Column({ name: 'PHONE_NO' })
  phoneNo: string;

  @Column({ name: 'DATE_OF_BIRTH', type: 'date' })
  dateOfBirth: string;
}
