import { Column, ViewEntity } from 'typeorm';

@ViewEntity('TBL_TRANSITIONALPIN_MST', { synchronize: false })
export class TblTransitionalpinMst {
  @Column({ name: 'TPIN', unique: true })
  tPin: string;

  @Column({ name: '<PERSON>UR<PERSON><PERSON>' })
  surname: string;

  @Column({ name: '<PERSON><PERSON><PERSON>' })
  gender: string;

  @Column({ name: 'PFACO<PERSON>' })
  pfaCode: string;

  @Column({ name: 'EMP_EMPLOYER_CODE' })
  empEmployerCode: string;

  @Column({ name: 'MIDDLENAME', nullable: true })
  middleName: string;

  @Column({ name: 'FIRSTNA<PERSON>' })
  firstName: string;

  @Column({ name: 'PHONE_NO' })
  phoneNo: string;

  @Column({ name: 'DATE_OF_BIRTH', type: 'date' })
  dateOfBirth: string;
}
