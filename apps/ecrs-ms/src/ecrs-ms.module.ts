import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule, LoggerModule, RedisCacheModule } from '@app/shared';
import { GlobalExceptionFilter } from '@app/shared/filters/global-exception.filter';
import { APP_FILTER, APP_GUARD } from '@nestjs/core';
import { EcrsMsController } from './controllers/ecrs-ms.controller';
import { TblContributorBiodata } from './entities/tbl-contributor-bio-data.entity';
import { TblCrsLegacyData } from './entities/tbl-crs-legacy-data.entity';
import { EcrsMsService } from './services/ecrs-ms.service';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { AuthModule } from '@app/shared/auth/auth.module';
import { AuthGuard } from '@app/shared/auth/guards/auth.guard';
import { ECRS_SERVICE } from '@app/shared/constants';
import { TblContributorBiodataRepository } from './repositories/tbl-contributor-biodata.repository';
import { TblCrsLegacyBiodataRepository } from './repositories/tbl-crs-legacy-biodata.repository';
import { HealthModule } from '@app/shared/health';
import { TblTransitionalPinMstRepository } from './repositories/tbl-transitional-pin-mst.repository';
import { TblTransitionalpinMst } from './entities/tbl-transitional-pin-mst.entity';

@Module({
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([TblContributorBiodata, TblCrsLegacyData, TblTransitionalpinMst]),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    HealthModule.registerAsync([]),
    RedisCacheModule.register(),
    LoggerModule.forRoot(ECRS_SERVICE),
    AuthModule,
  ],
  controllers: [EcrsMsController],
  providers: [
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    EcrsMsService,
    RedisCacheService,
    TblCrsLegacyBiodataRepository,
    TblContributorBiodataRepository,
    TblTransitionalPinMstRepository,
  ],
})
export class EcrsMsModule {}
