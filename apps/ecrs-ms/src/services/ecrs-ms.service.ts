/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { RetrieveUserDto } from '@app/shared/dto/ecrs/retrieve-user.dto';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import { ResponseCodeEnum } from '@app/shared/enums';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';
import { TblContributorBiodataRepository } from '../repositories/tbl-contributor-biodata.repository';
import { PinoLogger } from 'nestjs-pino';
import { TblCrsLegacyBiodataRepository } from '../repositories/tbl-crs-legacy-biodata.repository';
import { TblTransitionalPinMstRepository } from '../repositories/tbl-transitional-pin-mst.repository';

@Injectable()
export class EcrsMsService {
  constructor(
    private redisService: RedisCacheService,
    private readonly contributorRepository: TblContributorBiodataRepository,
    private readonly tblTransitionalPinMstRepository: TblTransitionalPinMstRepository,
    private readonly legacyRepository: TblCrsLegacyBiodataRepository,
    private readonly logger: PinoLogger
  ) {}

  async findEcrsUser(retrieveUserDto: RetrieveUserDto, skipSurnameCheck: boolean): Promise<EcrsUserResponseDto> {
    const { rsaPin, surname, userType } = retrieveUserDto;
    const cacheKey = EcrsMsService.getCacheKey(rsaPin, surname, userType);

    // Check cache first
    const cachedResult = await this.redisService.get(cacheKey);
    if (cachedResult) {
      return JSON.parse(cachedResult) as EcrsUserResponseDto;
    }

    // Utility function for case-insensitive surname matching
    // const isMatchingSurname = (storedSurname?: string) => storedSurname?.toLowerCase() === surname?.toLowerCase();
    const isMatchingSurname = (storedSurname?: string): boolean => {
      if (skipSurnameCheck) {
        return true;
      }
      return !!storedSurname && !!surname && storedSurname.toLowerCase() === surname.toLowerCase();
    };

    // Default response
    let result: Partial<EcrsUserResponseDto> = new EcrsUserResponseDto(ResponseCodeEnum.ERROR);
    result.setDescription('User details not found on ECRS');

    try {
      // Check contributor table
      let user = rsaPin.startsWith('PEN')
        ? await this.contributorRepository.findByRsaPin(rsaPin)
        : await this.tblTransitionalPinMstRepository.findByTPin(rsaPin);

      if (user) {
        if (isMatchingSurname(user.surname)) {
          result.ecrsStatus = true;
          result = EcrsMsService.mapUserToResponse(user, false, result);
        } else {
          result.setDescription(
            'Provided user details do not match RSAPIN details on Enhanced Contributor Registration System (ECRS)'
          );
        }
      } else if (userType === 'DECEASED') {
        // Check legacy table if userType is DECEASED
        user = await this.legacyRepository.findLegacyUserByRsaPin(rsaPin);

        if (user) {
          if (isMatchingSurname(user.surname)) {
            result = EcrsMsService.mapUserToResponse(user, true, result);
          } else {
            result.setDescription(
              'Provided user details do not match RSAPIN details on Enhanced Contributor Registration System (ECRS).'
            );
          }
        }
      }

      if (result.code === ResponseCodeEnum.SUCCESS && retrieveUserDto.checkCrsStatus) {
        if (result.legacy) {
          result.crsStatus = true;
        } else {
          const legacyUser = await this.legacyRepository.findLegacyUserByRsaPin(rsaPin);
          result.crsStatus = !!legacyUser;
        }
      }

      // Cache the successful result
      if (result.code === ResponseCodeEnum.SUCCESS) {
        await this.redisService.set(cacheKey, JSON.stringify(result), 3600);
      }
    } catch (error) {
      this.logger.error(
        `Error retrieving user data for RSAPIN: ${rsaPin} ${error instanceof Error ? error.stack : error}`
      );
    }

    return result as EcrsUserResponseDto;
  }

  private static mapUserToResponse(
    user: any,
    isLegacy: boolean,
    response: Partial<EcrsUserResponseDto>
  ): Partial<EcrsUserResponseDto> {
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.employerCode = user.employerCode;
    response.surname = user.surname;
    response.firstName = user.firstName;
    response.gender = user.gender;
    response.dateOfBirth = user.dateOfBirth;
    response.rsaPin = user.rsaPin;
    response.pfaCode = user.pfaCode;
    response.middleName = user.middleName;
    response.phoneNo = user.phoneNo;
    response.nin = user.nin;
    response.pfaName = user.pfaName;
    response.employerName = user.employerName;
    response.legacy = isLegacy;
    return response;
  }

  private static getCacheKey(rsaPin: string, surname: string, userType: RetireeUserTypeEnum): string {
    return `ecrs_user:${rsaPin}:${surname}:${userType}`;
  }
}
