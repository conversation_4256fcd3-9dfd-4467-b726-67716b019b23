import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import { TblCrsLegacyData } from '../entities/tbl-crs-legacy-data.entity';

@Injectable()
export class TblCrsLegacyBiodataRepository extends Repository<TblCrsLegacyData> {
  constructor(private dataSource: DataSource) {
    super(TblCrsLegacyData, dataSource.createEntityManager());
  }

  async findLegacyUserByRsaPin(rsaPin: string): Promise<EcrsUserResponseDto> {
    const query = this.dataSource
      .getRepository(TblCrsLegacyData)
      .createQueryBuilder('legacy')
      .leftJoin('TBL_MDA', 'mda', 'legacy.employerCode = "mda"."EMPLOYER_ID"')
      .leftJoin('TBL_PFA', 'pfa', 'legacy.pfa = "pfa"."PFACODE"')
      .select([
        'legacy.pin AS "rsaPin"',
        'legacy.lastName AS "surname"',
        'legacy.sex AS "gender"',
        'legacy.pfa AS "pfaCode"',
        'legacy.employerCode AS "employerCode"',
        'legacy.otherName AS "middleName"',
        'legacy.firstName AS "firstName"',
        'legacy.phone AS "phoneNo"',
        'legacy.dob AS "dateOfBirth"',
        '"mda"."EMPLOYER_NAME" AS "employerName"',
        '"pfa"."PFANAME" AS "pfaName"',
      ])
      .where('legacy.pin = :rsaPin', { rsaPin });

    return await query.getRawOne();
  }
}
