import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { TblContributorBiodata } from '../entities/tbl-contributor-bio-data.entity';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';

@Injectable()
export class TblContributorBiodataRepository extends Repository<TblContributorBiodata> {
  constructor(private dataSource: DataSource) {
    super(TblContributorBiodata, dataSource.createEntityManager());
  }

  async findByRsaPin(rsaPin: string): Promise<EcrsUserResponseDto> {
    const query = this.dataSource
      .getRepository(TblContributorBiodata)
      .createQueryBuilder('biodata')
      .leftJoin('TBL_MDA', 'mda', 'biodata.empEmployerCode = "mda"."EMPLOYER_ID"') // Ensure correct column name
      .leftJoin('TBL_PFA', 'pfa', 'biodata.pfaCode = "pfa"."PFACODE"')
      .select([
        'biodata.rsaPin AS "rsaPin"',
        'biodata.nin AS "nin"',
        'biodata.surname AS "surname"',
        'biodata.gender AS "gender"',
        'biodata.pfaCode AS "pfaCode"',
        'biodata.empEmployerCode AS "employerCode"',
        'biodata.middleName AS "middleName"',
        'biodata.firstName AS "firstName"',
        'biodata.phoneNo AS "phoneNo"',
        'biodata.dateOfBirth AS "dateOfBirth"',
        '"mda"."EMPLOYER_NAME" AS "employerName"',
        '"pfa"."PFANAME" AS "pfaName"',
      ])
      .where('biodata.rsaPin = :rsaPin', { rsaPin });
    return await query.getRawOne();
  }
}
