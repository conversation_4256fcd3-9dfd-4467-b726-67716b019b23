import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import { TblTransitionalpinMst } from '../entities/tbl-transitional-pin-mst.entity';

@Injectable()
export class TblTransitionalPinMstRepository extends Repository<TblTransitionalpinMst> {
  constructor(private dataSource: DataSource) {
    super(TblTransitionalpinMst, dataSource.createEntityManager());
  }

  async findByTPin(tPin: string): Promise<EcrsUserResponseDto> {
    const query = this.dataSource
      .getRepository(TblTransitionalpinMst)
      .createQueryBuilder('biodata')
      .leftJoin('TBL_MDA', 'mda', 'biodata.empEmployerCode = "mda"."EMPLOYER_ID"')
      .left<PERSON>oin('TBL_PFA', 'pfa', 'biodata.pfaCode = "pfa"."PFACODE"')
      .select([
        'biodata.tPin AS "rsaPin"',
        'biodata.surname AS "surname"',
        'biodata.gender AS "gender"',
        'biodata.pfaCode AS "pfaCode"',
        'biodata.empEmployerCode AS "employerCode"',
        'biodata.middleName AS "middleName"',
        'biodata.firstName AS "firstName"',
        'biodata.phoneNo AS "phoneNo"',
        'biodata.dateOfBirth AS "dateOfBirth"',
        '"mda"."EMPLOYER_NAME" AS "employerName"',
        '"pfa"."PFANAME" AS "pfaName"',
      ])
      .where('biodata.tPin = :tPin', { tPin });
    return await query.getRawOne();
  }
}
