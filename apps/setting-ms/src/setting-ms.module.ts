import { Module } from '@nestjs/common';
import { DatabaseModule, LoggerModule } from '@app/shared';
import { ConfigModule } from '@nestjs/config';
import { RedisCacheModule } from '@app/shared/cache';
import { SettingMsLibModule } from '@app/shared/setting-service/setting-ms-lib.module';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { HealthModule } from '@app/shared/health';
import { AuthModule } from '@app/shared/auth/auth.module';
import { APP_GUARD } from '@nestjs/core';
import { AuthGuard } from '@app/shared/auth/guards/auth.guard';
import { SETTINGS_SERVICE } from '@app/shared/constants';
import { SettingMsController } from './setting-ms.controller';
import { AuditLibModule } from '@app/shared/audit-service/audit-lib.module';

@Module({
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([]),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    LoggerModule.forRoot(SETTINGS_SERVICE),
    RedisCacheModule.register(),
    SettingMsLibModule,
    HealthModule.registerAsync([]),
    AuthModule,
    AuditLibModule,
  ],
  controllers: [SettingMsController],
  providers: [
    SettingMsLibService,
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
  ],
})
export class SettingMsModule {}
