import { Body, Controller, Patch, Post, UseInterceptors } from '@nestjs/common';
import { CreateSettingDto } from './dto/create-setting.dto';
import { GetSettingDto } from './dto/get-setting.dto';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { ApiBearerAuth, ApiOkResponse, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SettingsResponse } from '@app/shared/setting-service/dto/settings-response.dto';
import { Public } from '@app/shared/decorators/public.decorator';
import { Setting } from '@app/shared/setting-service/entities/setting.entity';
import { UpdateSettingDto } from '@app/shared/setting-service/dto/update-setting.dto';
import {
  BaseResponseWithContentNoPagination,
  BaseResponseWithNoCountInfo,
} from '@app/shared/dto/response/base-response-with-content';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { Audit } from '@app/shared/decorators/audit.decorator';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';

@ApiBearerAuth()
@UseInterceptors(AuditInterceptor)
@Controller('setting')
export class SettingMsController {
  constructor(private readonly settingService: SettingMsLibService) {}

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Audit(AuditEventTypeEnum.CREATE_SETTING_ATTEMPT)
  @ApiOperation({
    description: 'Create a new setting',
  })
  @ApiResponse({
    status: 201,
    description: 'Setting created successfully',
    type: BaseResponseWithContentNoPagination,
  })
  @Post()
  async create(@Body() createSettingDto: CreateSettingDto): Promise<Setting> {
    return await this.settingService.create(createSettingDto);
  }

  @ApiOperation({
    description: 'Retrieves all global settings',
  })
  @ApiOkResponse({
    description: 'Success',
    type: SettingsResponse,
  })
  @Public()
  @Post('/global')
  getGlobalSettings(@Body() getSettingDto: GetSettingDto): Promise<SettingsResponse> {
    return this.settingService.getGlobalSettings(getSettingDto);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.SETTING_EDIT)
  @Audit(AuditEventTypeEnum.UPDATE_SETTING_ATTEMPT)
  @ApiOperation({
    description: 'Update or create a setting',
  })
  @ApiResponse({
    status: 200,
    description: 'Setting updated successfully',
    type: BaseResponseWithContentNoPagination,
  })
  @Patch()
  async updateSetting(
    @Body() updateSettingDto: UpdateSettingDto
  ): Promise<BaseResponseWithContentNoPagination<Setting>> {
    return await this.settingService.updateSetting(updateSettingDto);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.SETTING_PAGE)
  @ApiOperation({
    description: 'Retrieves settings for data table display',
  })
  @ApiResponse({
    status: 200,
    description: 'Settings retrieved successfully',
    type: BaseResponseWithNoCountInfo,
  })
  @Post('/get-settings')
  async getSettings(@Body() filter: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<Setting>> {
    return await this.settingService.getSettings(filter);
  }
}
