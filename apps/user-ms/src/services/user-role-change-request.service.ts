import {
  RoleChangeRequestDataDto,
  RoleChangeRequestDetailedDto,
  UpdateUserRoleChangeRequestDto,
} from '@app/shared/user-service/dtos/cobra-role-change-request.dto';
import {
  CreateRoleChangeRequestDto,
  RoleChangeDocumentDto,
} from '@app/shared/user-service/dtos/cobra-role-change-request.dto';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { UserRoleChangeRequest } from '@app/shared/user-service/entities/cobra-role-change-request.entity';
import { CobraUserRoleChangeReqRepository } from '@app/shared/user-service/repositories/cobra-role-change-req.repository';
import { CustomException } from '@app/shared/filters/exception.dto';
import { ApprovalStatusEnum } from '@app/shared/enums/ApprovalStatusEnum';
import { RoleChangeDocument } from '@app/shared/user-service/entities/cobra-role-doc.entity';
import { Buffer } from 'buffer';
import { Injectable } from '@nestjs/common';
import {
  BaseResponseListWithContentNoPagination,
  BaseResponseWithContentNoPagination,
  BaseResponseWithNoCountInfo,
} from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum, SettingsEnumKey } from '@app/shared/enums';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { SendNotificationTemplateDto } from '@app/shared/dto/notification/send-notification-template.dto';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { NotificationServiceClientConstant } from '@app/shared/constants/notification-service-client.constant';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { PinoLogger } from 'nestjs-pino';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { CobraRoleRepository } from '@app/shared/user-service/repositories/cobra-role.repository';

@Injectable()
export class UserRoleChangeRequestService {
  constructor(
    @Inject('NOTIFICATION_SERVICE_CLIENT') private readonly notificationClient: ClientProxy,
    private readonly logger: PinoLogger,
    private readonly roleChangeRequestRepository: CobraUserRoleChangeReqRepository,
    private readonly userRepository: CobraUserRepository,
    private readonly settingMsLibService: SettingMsLibService,
    private readonly roleRepository: CobraRoleRepository
  ) {}

  private async getApproverRoles(): Promise<string[]> {
    const rolesString = await this.settingMsLibService.getSetting(SettingsEnumKey.ROLE_CHANGE_APPROVER_ROLES);
    return rolesString.split(',').map((role) => role.trim());
  }

  private async notifyApprovers(
    request: UserRoleChangeRequest,
    primaryUser: CobraUser,
    secondaryUser?: CobraUser
  ): Promise<void> {
    try {
      const approvers = await this.userRepository.findApproversByRoles(await this.getApproverRoles());
      if (approvers.length === 0) {
        this.logger.warn('No approvers found for role change request');
        return;
      }

      const primaryUserFullName = `${primaryUser.firstName} ${primaryUser.surname}`;
      const organization = request.mdaCode || request.pfaCode;

      // Send personalized email to each approver
      for (const approver of approvers) {
        const placeholders: Record<string, string> = {
          adminFirstName: approver.firstName,
          primaryUserFullName: primaryUserFullName,
          primaryUserEmail: primaryUser.emailAddress,
          secondaryUser: secondaryUser
            ? `${secondaryUser.firstName} ${secondaryUser.surname} (${secondaryUser.emailAddress})`
            : 'N/A',
          organization: organization,
          createDate: request.createDate.toLocaleDateString(),
        };

        const sendNotificationTemplateDto = new SendNotificationTemplateDto();
        sendNotificationTemplateDto.emailRecipients = [approver.emailAddress];
        sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.ROLE_CHANGE_APPROVAL;
        sendNotificationTemplateDto.placeholders = placeholders;

        await firstValueFrom(
          this.notificationClient.send(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            sendNotificationTemplateDto
          )
        );

        this.logger.debug(`Role change approval notification sent to: ${approver.emailAddress}`);
      }
    } catch (error) {
      this.logger.error('Failed to send role change approval notifications:', error);
      // Don't throw error to prevent disrupting the main flow
    }
  }

  private async notifyRequestorOfConfirmation(request: UserRoleChangeRequest, requestedBy: CobraUser): Promise<void> {
    try {
      const sendNotificationTemplateDto = new SendNotificationTemplateDto();
      sendNotificationTemplateDto.emailRecipients = [requestedBy.emailAddress];
      sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.ROLE_CHANGE_REQUEST_ACKNOWLEDGMENT;
      sendNotificationTemplateDto.placeholders = {
        firstName: requestedBy.firstName,
      };

      await firstValueFrom(
        this.notificationClient.send(
          NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
          sendNotificationTemplateDto
        )
      );

      this.logger.debug(`Role change request acknowledgment sent to: ${request.primaryUser.emailAddress}`);
    } catch (error) {
      this.logger.error('Failed to send role change request acknowledgment:', error);
      // Don't throw error - we don't want to roll back the request creation
    }
  }

  async createRoleChangeRequest(
    dto: CreateRoleChangeRequestDto,
    req: ICustomRequest
  ): Promise<BaseResponseListWithContentNoPagination<RoleChangeRequestDataDto>> {
    if (dto.primaryUserEmail === dto.secondaryUserEmail)
      throw new CustomException('Primary and secondary users cannot be the same');

    const primaryUser = await this.userRepository.findOne({ emailAddress: dto.primaryUserEmail });

    if (primaryUser.userType === UserTypeEnum.PENCOM || primaryUser.userType === UserTypeEnum.RETIREE) {
      throw new CustomException('Only MDA and PFA users can request role change');
    }

    if (primaryUser === null) throw new CustomException('Primary user not found');

    const secondaryUser = dto.secondaryUserEmail
      ? await this.userRepository.findOne({ emailAddress: dto.secondaryUserEmail })
      : null;

    if (dto.secondaryUserEmail && secondaryUser === null) throw new CustomException('Secondary user not found');

    // Check if secondary user has admin role using the roles array
    if (secondaryUser) {
      const hasAdminRole = secondaryUser.roles.some((role) => role.isAdmin);
      if (!hasAdminRole) {
        throw new CustomException('Secondary user must have an admin role');
      }
    } else if (this.isNumberOfAdminMaxedOut()) {
      throw new CustomException('Number of admin users has been maxed out');
    }

    // Check for pending requests for primary user
    const primaryPendingRequest = await this.roleChangeRequestRepository.findOne([
      { primaryUser: { emailAddress: dto.primaryUserEmail }, status: ApprovalStatusEnum.PENDING },
      { secondary: { emailAddress: dto.primaryUserEmail }, status: ApprovalStatusEnum.PENDING },
    ]);

    if (primaryPendingRequest) {
      throw new CustomException('Primary user already has a pending role change request');
    }

    // Check for pending requests for secondary user if exists
    if (dto.secondaryUserEmail) {
      const secondaryPendingRequest = await this.roleChangeRequestRepository.findOne([
        { primaryUser: { emailAddress: dto.secondaryUserEmail }, status: ApprovalStatusEnum.PENDING },
        { secondary: { emailAddress: dto.secondaryUserEmail }, status: ApprovalStatusEnum.PENDING },
      ]);

      if (secondaryPendingRequest) {
        throw new CustomException('Secondary user already has a pending role change request');
      }
    }

    if (
      UserTypeEnum.MDA == primaryUser.userType &&
      dto.secondaryUserEmail &&
      secondaryUser.mdaCode !== primaryUser.mdaCode
    ) {
      throw new CustomException('Primary and secondary users must belong to the same MDA');
    }

    if (
      primaryUser.userType === UserTypeEnum.PFA &&
      dto.secondaryUserEmail &&
      secondaryUser.pfaCode !== primaryUser.pfaCode
    ) {
      throw new CustomException('Primary and secondary users must belong to the same PFA');
    }

    const requestedBy = await this.userRepository.findOne({ emailAddress: req.user.email });

    let targetRole = null;
    if (!dto.secondaryUserEmail) {
      // Get the appropriate admin role name based on user type
      const roleName = await this.getAdminRoleName(req.user.userType);
      targetRole = await this.roleRepository.findOne({ role: roleName });

      if (!targetRole) {
        throw new CustomException(`Admin role ${roleName} not found`);
      }
    }

    const userReqEntity = new UserRoleChangeRequest({
      primaryUser,
      secondary: secondaryUser,
      status: ApprovalStatusEnum.PENDING,
      mdaCode: primaryUser.mdaCode,
      pfaCode: primaryUser.pfaCode,
      approvedBy: null,
      userType: primaryUser.userType,
      documents: [],
      comment: dto.comment,
      requestedBy: requestedBy,
      role: targetRole, // Add the target role if no secondary user
    });

    const documents = dto.documents.map((doc) => this.createRoleChangeDocument(doc, userReqEntity));
    userReqEntity.documents = documents;

    const savedEntity = await this.roleChangeRequestRepository.saveEntity(userReqEntity);

    // Send notifications after successful save
    await Promise.all([
      this.notifyApprovers(savedEntity, primaryUser, secondaryUser),
      this.notifyRequestorOfConfirmation(savedEntity, requestedBy),
    ]);

    const resp = new BaseResponseListWithContentNoPagination<RoleChangeRequestDataDto>(ResponseCodeEnum.SUCCESS);
    resp.content = this.roleChangeRequestRepository.serializeEntity(savedEntity);

    return resp;
  }
  async isNumberOfAdminMaxedOut(mfaCode?: string, pfaCode?: string): Promise<boolean> {
    const maxAdminCount = await this.settingMsLibService.getSettingInt(SettingsEnumKey.MAX_NUMBER_OF_ADMINS);
    const currentAdminCount = await this.userRepository.getAdminCountForOrganization(mfaCode, pfaCode);

    return currentAdminCount >= maxAdminCount;
  }

  async getAllRoleChangeRequests(
    searchParam: PaginatedSearchDto,
    req: ICustomRequest
  ): Promise<BaseResponseWithNoCountInfo<RoleChangeRequestDataDto>> {
    // Initialize filters if they don't exist
    if (!searchParam.filters) {
      searchParam.filters = {};
    }

    // Add organization-specific filters based on user type
    if (req.user.userType === UserTypeEnum.MDA) {
      searchParam.filters.mdaCode = req.user.mdaCode;
    } else if (req.user.userType === UserTypeEnum.PFA) {
      searchParam.filters.pfaCode = req.user.pfaCode;
    }
    // For PENCOM users, don't add any organization filters

    return await this.roleChangeRequestRepository.searchRequests(searchParam);
  }

  private async notifyUserOfAdminUpgrade(user: CobraUser): Promise<void> {
    try {
      const sendNotificationTemplateDto = new SendNotificationTemplateDto();
      sendNotificationTemplateDto.emailRecipients = [user.emailAddress];
      sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.ROLE_ADMIN_UPGRADE;
      sendNotificationTemplateDto.placeholders = {
        firstName: user.firstName,
      };

      await firstValueFrom(
        this.notificationClient.send(
          NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
          sendNotificationTemplateDto
        )
      );

      this.logger.debug(`Admin upgrade notification sent to: ${user.emailAddress}`);
    } catch (error) {
      this.logger.error('Failed to send admin upgrade notification:', error);
      // Don't throw error - we don't want to roll back the status update
    }
  }

  private async getAdminRoleName(userType: string): Promise<string> {
    const settingKey =
      userType === UserTypeEnum.MDA ? SettingsEnumKey.MDA_ADMIN_ROLE_NAME : SettingsEnumKey.PFA_ADMIN_ROLE_NAME;
    return await this.settingMsLibService.getSetting(settingKey);
  }

  private async swapUserRoles(primaryUser: CobraUser, secondaryUser: CobraUser): Promise<void> {
    const primaryRoles = [...primaryUser.roles];
    const secondaryRoles = [...secondaryUser.roles];

    primaryUser.roles = secondaryRoles;
    secondaryUser.roles = primaryRoles;

    await this.userRepository.saveEntity(primaryUser);
    await this.userRepository.saveEntity(secondaryUser);
  }

  private async assignAdminRole(user: CobraUser, roleChangeRequest: UserRoleChangeRequest): Promise<void> {
    if (!roleChangeRequest.role) {
      throw new CustomException('No target role found in the request');
    }

    user.roles = [await roleChangeRequest.role];
    await this.userRepository.saveEntity(user);
  }

  async updateRoleChangeStatus(
    dto: UpdateUserRoleChangeRequestDto,
    principal: string
  ): Promise<BaseResponseListWithContentNoPagination<RoleChangeRequestDataDto>> {
    const request = await this.roleChangeRequestRepository.findOneWithRelations({ pk: dto.requestId }, [
      'primaryUser',
      'primaryUser.roles',
      'secondary',
      'secondary.roles',
      'requestedBy',
    ]);

    if (request === null) throw new CustomException('Role change request not found');

    if (request.status !== ApprovalStatusEnum.PENDING) {
      throw new CustomException('Request has already been processed');
    }

    const primaryUser = await request.primaryUser;
    const secondaryUser = await request.secondary;

    request.status = dto.status;
    request.approvedBy = await this.userRepository.findOne({ emailAddress: principal });
    request.comment = dto.comment;

    if (dto.status === ApprovalStatusEnum.APPROVED) {
      if (secondaryUser) {
        // If secondary user exists, swap roles
        await this.swapUserRoles(primaryUser, secondaryUser);
      } else {
        // If no secondary user, assign admin role based on user type
        await this.assignAdminRole(primaryUser, request);
      }

      await this.notifyUserOfAdminUpgrade(primaryUser);
    }

    const update = await this.roleChangeRequestRepository.saveEntity(request);
    await this.notifyRequestorOfStatusChange(update, dto.status, primaryUser);

    const resp = new BaseResponseListWithContentNoPagination<RoleChangeRequestDataDto>(ResponseCodeEnum.SUCCESS);
    resp.content = this.roleChangeRequestRepository.serializeEntity(update);

    return resp;
  }

  private async notifyRequestorOfStatusChange(
    request: UserRoleChangeRequest,
    status: ApprovalStatusEnum,
    primaryUser: CobraUser
  ): Promise<void> {
    try {
      const requestor = await request.requestedBy;
      const sendNotificationTemplateDto = new SendNotificationTemplateDto();
      sendNotificationTemplateDto.emailRecipients = [requestor.emailAddress];
      sendNotificationTemplateDto.notificationType =
        status === ApprovalStatusEnum.APPROVED
          ? NotificatonTypeEnum.ROLE_CHANGE_STATUS_APPROVAL
          : NotificatonTypeEnum.ROLE_CHANGE_STATUS_REJECTION;

      sendNotificationTemplateDto.placeholders = {
        firstName: primaryUser.firstName,
        userEmail: primaryUser.emailAddress,
        comment: request.comment || 'No additional comments provided.',
      };

      await firstValueFrom(
        this.notificationClient.send(
          NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
          sendNotificationTemplateDto
        )
      );

      this.logger.debug(
        `Role change ${status.toLowerCase()} notification sent to: ${request.requestedBy.emailAddress}`
      );
    } catch (error) {
      this.logger.error('Failed to process role change status notification:', error);
      // Don't throw error to prevent disrupting the main flow
    }
  }

  private createRoleChangeDocument(
    docDto: RoleChangeDocumentDto,
    roleChangeRequest: UserRoleChangeRequest
  ): RoleChangeDocument {
    // Clean the base64 string by removing any existing data URI prefix
    const cleanBase64 = docDto.documentContent.replace(/^data:.*?;base64,/, '');

    const document = new RoleChangeDocument({
      roleChangeReq: roleChangeRequest,
      name: docDto.documentName,
      type: docDto.documentType,
      data: Buffer.from(cleanBase64, 'base64'),
    });
    return document;
  }

  async getDocument(
    requestId: number
  ): Promise<BaseResponseListWithContentNoPagination<{ name: string; type: string; data: string }[]>> {
    const request = await this.roleChangeRequestRepository.findOneWithRelations({ pk: requestId }, ['documents']);

    if (!request || !request.documents.length) {
      throw new CustomException('Document not found');
    }

    const documents = request.documents.map((doc) => ({
      name: doc.name,
      type: doc.type,
      data: `data:${doc.type};base64,${doc.data.toString('base64')}`,
    }));

    const resp = new BaseResponseListWithContentNoPagination<{ name: string; type: string; data: string }[]>(
      ResponseCodeEnum.SUCCESS
    );
    resp.content = documents;
    resp.setDescription('Role change request documents retrieved successfully');

    return resp;
  }

  async getRoleChangeRequestDetail(
    requestId: number
  ): Promise<BaseResponseWithContentNoPagination<RoleChangeRequestDetailedDto>> {
    const response = new BaseResponseWithContentNoPagination<RoleChangeRequestDetailedDto>(ResponseCodeEnum.SUCCESS);

    const request = await this.roleChangeRequestRepository.getRoleChangeRequestWithDetails(requestId);

    if (!request) {
      throw new CustomException('Role change request not found');
    }
    response.content = request;
    response.setDescription('Role change request details retrieved successfully');
    return response;
  }
}
