/* eslint-disable */
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import {
  CreateCobraUserDto,
  EditCobraUserDto,
  UpdateUserStatusDto,
} from '@app/shared/user-service/dtos/cobra-user.dto';
import { PinoLogger } from 'nestjs-pino';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import * as crypto from 'crypto';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { CobraRoleRepository } from '@app/shared/user-service/repositories/cobra-role.repository';
import { CobraPriviledgeRepository } from '@app/shared/user-service/repositories/cobra-priviledge.repository';
import { CobraPrivilege } from '@app/shared/user-service/entities/cobra-priviledge.entity';
import {
  BaseResponseListWithContentNoPagination,
  BaseResponseWithContent,
  BaseResponseWithContentNoPagination,
} from '@app/shared/dto/response/base-response-with-content';
import { CobraRole } from '@app/shared/user-service/entities/cobra-role.entity';
import { ResponseCodeEnum, SettingsEnumKey } from '@app/shared/enums';
import { CreateRoleDto, EditRoleDto, RoleStatusDto } from '@app/shared/user-service/dtos/cobra-role.dto';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { In } from 'typeorm';
import { UserStatusAction } from '@app/shared/enums/UserStatusActionEnum';
import { MdaRepository } from '@app/shared/user-service/repositories/mda.repository';
import { Mda } from '@app/shared/user-service/entities/mda.entity';
import { GenerateCodeDto } from '@app/shared/dto/notification/generate-code.dto';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { firstValueFrom } from 'rxjs';
import { NotificationServiceClientConstant } from '@app/shared/constants/notification-service-client.constant';
import { ClientProxy } from '@nestjs/microservices';
import { SendNotificationTemplateDto } from '@app/shared/dto/notification/send-notification-template.dto';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { EnrolmentSummary } from '@app/shared/enrollment-service/entities/enrolment-summary.entity';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { RegistrationStatusEnum } from '@app/shared/enums/registration-status.enum';
import { ChangeOldPasswordDto } from '@app/shared/dto/change-password.dto';
import * as bcrypt from 'bcryptjs';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { VerifyOtpDto } from '@app/shared/dto/notification/verify-otp.dto';
import { CustomException } from '@app/shared/filters/exception.dto';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { ReassignmentRolesDto, ReassignmentUsersDto } from '@app/shared/dto/enrollment/reassignment.dto';
import { TbeOrganisationsRepository } from '@app/shared/enrollment-service/repository/tbe-organisations.repository';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { TwoFactorAuthenticationService } from '@app/shared/ldap/services/2fa-lib.service';
import { Generate2FASecretResponse } from '@app/shared/dto/response/2fa-secret.dto';
import { LoginAccessDto } from '@app/shared/dto/login-access.dto';
import { LdapAuthService } from '@app/shared/ldap/services/ldap-auth.service';

@Injectable()
export class UserMsService {
  constructor(
    private readonly userRepository: CobraUserRepository,
    private readonly roleRepository: CobraRoleRepository,
    private readonly priviledgeRepository: CobraPriviledgeRepository,
    private readonly settingMsLibService: SettingMsLibService,
    private readonly redisCacheService: RedisCacheService,
    private readonly enrolmentSummaryRepository: EnrolmentSummaryRepository,
    private readonly tbeOrganisationsRepository: TbeOrganisationsRepository,
    private readonly mdaRepository: MdaRepository,
    private readonly pfaRepository: PfaRepository,
    private readonly ldapAuthService: LdapAuthService,
    private readonly logger: PinoLogger,
    private readonly twoFactorAuthenticationService: TwoFactorAuthenticationService,
    @Inject('NOTIFICATION_SERVICE_CLIENT') private readonly notificationClient: ClientProxy
  ) {}

  async generatePassAndCreateUser(
    dto: Omit<CreateCobraUserDto, 'password'>,
    createdBy: string,
    allowAdmin: boolean
  ): Promise<BaseResponseWithContentNoPagination<Partial<CobraUser> | null>> {
    const response = new BaseResponseWithContentNoPagination<Partial<CobraUser>>(ResponseCodeEnum.ERROR);
    if (!createdBy) {
      response.setDescription('Unable to fetch user creation details, please try again.');
      return response;
    }

    const createdByData: CobraUser | null = await this.userRepository
      .findOne({
        emailAddress: createdBy,
      })
      .catch((error) => {
        this.logger.error('Error occurred while fetching user', error);
        return Promise.resolve(null);
      });

    if (!createdByData) {
      response.setDescription('Unable to retrieve user details');
      return response;
    }

    const generatedPassword = crypto.randomBytes(8).toString('hex');

    const userDto: CreateCobraUserDto = {
      ...dto,
      password: generatedPassword,
    };

    userDto.updatePassword = true;
    userDto.createdBy = createdByData;
    userDto.active = true;

    const newUser = await this.createUser(userDto, false, allowAdmin);

    const placeholders: Record<string, string> = {};
    placeholders['username'] = newUser.surname;
    placeholders['userEmail'] = newUser.emailAddress;
    placeholders['temporaryPassword'] = generatedPassword;

    const sendNotificationTemplateDto = new SendNotificationTemplateDto();
    sendNotificationTemplateDto.emailRecipients = [newUser.emailAddress];
    sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.EXTERNAL_USER_CREATION;
    sendNotificationTemplateDto.placeholders = placeholders;

    await firstValueFrom(
      this.notificationClient.send(
        NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
        sendNotificationTemplateDto
      )
    ).catch((error) => {
      this.logger.error(
        `Error Sending user generatePassAndCreateUser notification  error:  ${
          error instanceof Error ? error.stack : error
        }`
      );
      return Promise.resolve(null);
    });

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.content = newUser;
    return response;
  }

  async createUser(
    createUserDto: CreateCobraUserDto,
    sendEmail = true,
    allowAdmin?: boolean
  ): Promise<Partial<CobraUser>> {
    return this.userRepository.getEntityManager().transaction(async (transactionalEntityManager) => {
      const userRepo = this.userRepository.getEntityRepository();

      const existingUser = await userRepo.findOne({
        where: [
          { rsaPin: createUserDto.rsaPin },
          { staffId: createUserDto.staffId },
          {
            emailAddress: createUserDto.emailAddress,
          },
        ],
      });

      if (existingUser) {
        const conflicts = [];
        if (existingUser.rsaPin === createUserDto.rsaPin) conflicts.push('RSA PIN');
        if (existingUser.staffId === createUserDto.staffId) conflicts.push('Staff ID');
        if (existingUser.emailAddress === createUserDto.emailAddress) conflicts.push('Email Address');

        const conflictFields = conflicts.join(', ');
        this.logger.debug(`User already exists with the specified ${conflictFields}`);
        throw new BadRequestException(`User already exists with the specified ${conflictFields}`);
      }

      if (createUserDto.userType === UserTypeEnum.MDA) {
        if (!createUserDto.mdaCode) {
          throw new BadRequestException('MDA code not provided');
        }

        const mdaList = await this.mdaRepository.find({
          where: { employerId: createUserDto.mdaCode },
          select: ['employerId'],
        });

        if (mdaList.length === 0) {
          throw new BadRequestException('No MDA found for the provided MDA code');
        }
        const [mda] = mdaList;
        createUserDto.mdaCode = mda.employerId;
      }

      if (createUserDto.userType === UserTypeEnum.PFA) {
        const pfaList = await this.pfaRepository.find({
          where: { pfaCode: createUserDto.pfaCode },
          select: ['pfaCode'],
        });
        if (pfaList.length === 0) {
          throw new BadRequestException('No PFA found for the provided PFA code');
        }
        const [pfa] = pfaList;
        createUserDto.pfaCode = pfa.pfaCode;
      }

      const createPayload = new CobraUser({
        ...createUserDto,
        firstTimeLogin: true,
        securityQuestions: true,
        active: true,
      } as unknown as CobraUser);

      if (createUserDto.roleFk) {
        const role = await this.roleRepository.findOne({
          pk: createUserDto.roleFk,
        });
        if (!role) {
          throw new BadRequestException('Provided role is invalid');
        }
        if (!allowAdmin && role.isAdmin) {
          throw new CustomException('You cannot create a user with an Admin role. Kindly request for a role upgrade.');
        }
        createPayload.roles = [role];
      }

      const newUser = await transactionalEntityManager.save(CobraUser, createPayload);

      if (createUserDto.userType === UserTypeEnum.RETIREE && createUserDto.mdaEmployeeBiodata) {
        const enrollmentSummary = new EnrolmentSummary({
          rsaPin: newUser.rsaPin,
          firstName: newUser.firstName,
          surname: newUser.surname,
          emailAddress: newUser.emailAddress,
          gender: newUser.gender,
          status: RegistrationStatusEnum.REGISTERED,
          dateOfFirstAppointment: createUserDto.mdaEmployeeBiodata?.dofa,
          dateOfDeath: createUserDto.mdaEmployeeBiodata?.dateOfDeath,
          expectedDateOfRetirement: createUserDto.mdaEmployeeBiodata?.edor,
          dateOfTransferOfService: createUserDto.mdaEmployeeBiodata?.dts,
          retireeUserType: createUserDto.mdaEmployeeBiodata?.retireeUserType,
        });

        this.logger.info(`About to save enrollment summary for pin: ${enrollmentSummary.rsaPin}`);
        await transactionalEntityManager.save(EnrolmentSummary, enrollmentSummary);
      }

      if (sendEmail) {
        const placeholders: Record<string, string> = {};
        placeholders['username'] = newUser.surname;

        const generateCodeDto = new GenerateCodeDto();
        generateCodeDto.email = newUser.emailAddress;
        generateCodeDto.otpRecordType = NotificatonTypeEnum.REGISTER;
        generateCodeDto.placeholders = placeholders;

        await firstValueFrom(
          this.notificationClient.send(NotificationServiceClientConstant.REQUEST_OTP, generateCodeDto)
        ).catch((error) => {
          this.logger.error(
            `Error Sending user creation notification: ${error instanceof Error ? error.stack : error}`
          );
          return Promise.resolve(null);
        });
      }

      return this.userRepository.serialize(newUser);
    });
  }

  async findUser(query: Partial<CobraUser>) {
    const user = await this.userRepository
      .findOneWithRelations({ emailAddress: query.emailAddress }, ['roles', 'roles.privileges'])
      .catch((error) => {
        this.logger.error('Error fetching role', error);
        return Promise.resolve(null);
      });

    return user ? user : null;
  }

  async findUserByEmail(query: Partial<CobraUser>) {
    const user = await this.userRepository.getUserProfile(query);

    if (user) {
      const validatorRoles = await this.settingMsLibService.getSetting(SettingsEnumKey.PENCOM_VALIDATOR_ROLE_NAME);
      const auditorRoles = await this.settingMsLibService.getSetting(SettingsEnumKey.PENCOM_AUDIT_VALIDATOR_ROLE_NAME);

      if (validatorRoles.split(',').find((role) => user.roleName?.toUpperCase() === role.trim().toUpperCase())) {
        user.isValidator = true;
        user.pendingRecordCount = await this.enrolmentSummaryRepository.getCountOfPendingRecordByAssignedTo(
          user.emailAddress
        );
      } else if (auditorRoles.split(',').find((role) => user.roleName?.toUpperCase() === role.trim().toUpperCase())) {
        user.isAuditor = true;
        user.pendingRecordCount = await this.enrolmentSummaryRepository.getCountOfPendingRecordByAuditorAssignedTo(
          user.emailAddress
        );
      }
    }
    return user ? user : null;
  }

  async getReassignmentRoles(
    reassignmentDto: ReassignmentRolesDto
  ): Promise<BaseResponseListWithContentNoPagination<String[]>> {
    const validatorRoles = await this.settingMsLibService.getSetting(SettingsEnumKey.PENCOM_VALIDATOR_ROLE_NAME);
    const auditorRoles = await this.settingMsLibService.getSetting(SettingsEnumKey.PENCOM_AUDIT_VALIDATOR_ROLE_NAME);

    const response = new BaseResponseListWithContentNoPagination<String[]>(ResponseCodeEnum.SUCCESS);
    if (validatorRoles.split(',').some((role) => reassignmentDto.userRoleName === role.trim())) {
      response.content = validatorRoles.split(',').map((role) => role.trim().toUpperCase());
      return response;
    } else if (auditorRoles.split(',').some((role) => reassignmentDto.userRoleName === role.trim())) {
      response.content = auditorRoles.split(',').map((role) => role.trim().toUpperCase());
      return response;
    }
    response.content = [];
    return response;
  }

  async findUserByRsaPin(query: Partial<CobraUser>) {
    const user = await this.userRepository.findOne({
      rsaPin: query.rsaPin,
    });
    return user ? user : null;
  }

  async findUserByStaffId(query: Partial<CobraUser>) {
    const user = await this.userRepository
      .findOneWithRelations({ staffId: query.staffId }, ['roles', 'roles.privileges'])
      .catch((error) => {
        this.logger.error('Error fetching role', error);
        return Promise.resolve(null);
      });

    return user ? user : null;
  }

  async updateUser(emailAddress: string, payload: Partial<CobraUser>): Promise<Partial<CobraUser> | null> {
    const existingUser = await this.userRepository.findOne({
      emailAddress,
    });

    if (!existingUser) {
      this.logger.error(`Unable to update user as user with email: ${emailAddress} does not exist`);
      throw new CustomException(`Unable to update user as user with email: ${emailAddress} does not exist`);
    }

    const updatedUser = await this.userRepository.findOneAndUpdate(
      {
        emailAddress,
      },
      payload
    );

    if (!updatedUser) {
      throw new CustomException('Unable to update user, please try again.');
    }

    return this.userRepository.serialize(updatedUser);
  }

  async getPrivileges(userType: string): Promise<BaseResponseWithContentNoPagination<CobraPrivilege[]>> {
    if (!userType) {
      const resp = new BaseResponseWithContentNoPagination<CobraPrivilege[]>(ResponseCodeEnum.ERROR);
      resp.setDescription('User type not specified');
      return resp;
    }

    const result = await this.priviledgeRepository.find({
      where: {
        userType,
        active: true,
      },
      select: ['privilege'],
    });

    const response = new BaseResponseWithContentNoPagination<CobraPrivilege[]>(ResponseCodeEnum.SUCCESS);
    response.content = result;
    return response;
  }

  async createRole(createRoleDto: CreateRoleDto, createdBy: string): Promise<BaseResponseDto> {
    const { roleName, description, privileges, userType } = createRoleDto;
    const refinedRoleName = roleName.toUpperCase().trim();

    const existingRole = await this.roleRepository
      .findOne({
        role: refinedRoleName,
      })
      .catch((error) => {
        this.logger.error('Error occurred while fetching role data', error);
        return Promise.resolve(null);
      });

    if (existingRole) {
      const errorResponse = new BaseResponseDto(ResponseCodeEnum.ERROR);
      errorResponse.setDescription('Role already exists');
      return errorResponse;
    }

    const createdByData = await this.userRepository
      .findOne({
        emailAddress: createdBy,
      })
      .catch((error) => {
        this.logger.error('Error occurred while fetching user', error);
        return Promise.resolve();
      });

    if (!createdByData) {
      const errorResponse = new BaseResponseDto(ResponseCodeEnum.ERROR);
      errorResponse.setDescription('Unable to retrieve user details');
      return errorResponse;
    }

    const validPrivileges = await this.priviledgeRepository.find({
      where: { privilege: In(privileges), userType: createRoleDto.userType },
    });

    if (!validPrivileges) {
      const errorResponse = new BaseResponseDto(ResponseCodeEnum.ERROR);
      errorResponse.setDescription('Role privileges does not exist');
      return errorResponse;
    }

    const newRole = new CobraRole({
      role: refinedRoleName,
      description,
      userType,
      createdBy: createdByData,
      privileges: validPrivileges,
      active: true,
      isAdmin: createRoleDto.isAdmin,
    });

    await this.roleRepository.saveEntity(newRole);

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('Success');

    return response;
  }

  async getUserByMdaOrPfa(
    userEmail: string,
    req: ICustomRequest
  ): Promise<BaseResponseListWithContentNoPagination<CobraUser[]>> {
    const isAdmin = await this.userRepository.isUserAdmin(userEmail);

    return await this.userRepository.getUserForUpgrade(req.user.mdaCode, req.user.pfaCode, isAdmin);
  }

  async deactivateRole(deactivateRoleDto: RoleStatusDto): Promise<BaseResponseDto> {
    const { roleName } = deactivateRoleDto;

    const existingRole = await this.roleRepository.findOne({ role: roleName }).catch((error) => {
      this.logger.error('Error fetching role', error);
      return Promise.resolve();
    });

    if (!existingRole) {
      const errorResponse = new BaseResponseDto(ResponseCodeEnum.ERROR);
      errorResponse.setDescription('Role does not exist');
      return errorResponse;
    }

    await this.roleRepository.findOneAndUpdate(
      { role: roleName },
      {
        active: deactivateRoleDto.active,
      }
    );

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('Role status successfully updated.');
    return response;
  }

  async editRole(editRoleDto: EditRoleDto): Promise<BaseResponseDto> {
    const { roleName, description, privileges, role, userType, isAdmin } = editRoleDto;

    const existingRole = await this.roleRepository
      .findOneWithRelations(
        {
          role,
        },
        ['privileges']
      )
      .catch((error) => {
        this.logger.error('Error fetching role', error);
        return Promise.resolve();
      });

    if (!existingRole) {
      const errorResponse = new BaseResponseDto(ResponseCodeEnum.ERROR);
      errorResponse.setDescription('Role does not exist');
      return errorResponse;
    }

    const validPrivileges = await this.priviledgeRepository.find({
      where: { privilege: In(privileges), userType: existingRole.userType },
    });
    if (!validPrivileges || validPrivileges.length === 0) {
      const errorResponse = new BaseResponseDto(ResponseCodeEnum.ERROR);
      errorResponse.setDescription('Role privileges does not exist');
      return errorResponse;
    }

    existingRole.role = roleName !== undefined ? roleName : existingRole.role;
    existingRole.userType = userType !== undefined ? userType : existingRole.userType;
    existingRole.description = description !== undefined ? description : existingRole.description;
    existingRole.privileges = validPrivileges;
    existingRole.isAdmin = isAdmin !== undefined ? isAdmin : existingRole.isAdmin;

    await this.roleRepository.saveEntity(existingRole);

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('Role updated successfully');
    return response;
  }

  async updateUserStatus(updateUserStatusDto: UpdateUserStatusDto, req: ICustomRequest): Promise<BaseResponseDto> {
    const { email, action } = updateUserStatusDto;

    const user = await this.userRepository.findOne({ emailAddress: email });

    if (!user) {
      const errorResponse = new BaseResponseDto(ResponseCodeEnum.ERROR);
      errorResponse.setDescription('User doesnt exist');
      return errorResponse;
    }

    switch (action) {
      case UserStatusAction.ACTIVATE:
        user.active = true;
        break;
      case UserStatusAction.DEACTIVATE:
        user.active = false;
        break;
      case UserStatusAction.BLACKLIST:
        user.blacklisted = true;
        break;
      case UserStatusAction.WHITELIST:
        if (req.user?.userType != UserTypeEnum.PENCOM) {
          throw new CustomException('Only PENCOM users can whitelist users');
        }
        user.blacklisted = false;
        break;
      default:
        throw new BadRequestException(`Invalid action: ${action}`);
    }

    await this.userRepository.saveEntity(user);

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('User updated successfully');
    return response;
  }

  async fetchUsers(searchParams: PaginatedSearchDto): Promise<BaseResponseWithContent<any>> {
    try {
      const paginatedResult = await this.userRepository.searchUserByType(searchParams);
      const response = new BaseResponseWithContent<CobraUser>(ResponseCodeEnum.SUCCESS);
      response.content = paginatedResult.result;
      response.total = paginatedResult.total;
      response.page = paginatedResult.page;
      response.limit = paginatedResult.limit;
      return response;
    } catch (error) {
      this.logger.error(`fetchUsers error: ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while fetching users, please try again');
      return response;
    }
  }

  async fetchUserForReassignment(
    reassignmentUsersDto: ReassignmentUsersDto
  ): Promise<BaseResponseListWithContentNoPagination<CobraUser>> {
    const paginatedResult = await this.userRepository.searchUserForReassignment(
      reassignmentUsersDto.roleName.toUpperCase(),
      reassignmentUsersDto.action,
      reassignmentUsersDto.taskOwnerEmail
    );
    const response = new BaseResponseListWithContentNoPagination<CobraUser>(ResponseCodeEnum.SUCCESS);
    response.content = paginatedResult;

    return response;
  }

  async searchMdas(filter: PaginatedSearchDto): Promise<BaseResponseListWithContentNoPagination<Mda[]>> {
    return await this.mdaRepository.searchAllMdas(filter);
  }

  async getAllOrganisations() {
    const orgCacheKey = 'ALL_ORGANISATIONS:';
    const cached = await this.redisCacheService.get(orgCacheKey);
    if (cached) {
      return JSON.parse(cached);
    }

    const organisations = await this.tbeOrganisationsRepository.getAllOrganisations();
    await this.redisCacheService.set(orgCacheKey, JSON.stringify(organisations));

    return organisations;
  }

  async getUserRoles(
    userType: UserTypeEnum,
    req: ICustomRequest
  ): Promise<BaseResponseWithContentNoPagination<{ pk: number; role: string }[]>> {
    if (req.user.userType !== UserTypeEnum.PENCOM) {
      userType = req.user.userType as UserTypeEnum;
    }

    let result;
    if (req.user.userType === UserTypeEnum.PENCOM) {
      result = await this.roleRepository.find({
        select: ['pk', 'role', 'isAdmin'],
        where: {
          userType,
          active: true,
        },
      });
    } else {
      result = await this.roleRepository.find({
        select: ['pk', 'role', 'isAdmin'],
        where: {
          userType,
          active: true,
          isAdmin: false,
        },
      });
    }

    const response = new BaseResponseWithContentNoPagination<{ pk: number; role: string; isAdmin: boolean }[]>(
      ResponseCodeEnum.SUCCESS
    );
    response.content = result.map(({ pk, role, isAdmin }) => ({
      pk,
      role,
      isAdmin,
    }));
    return response;
  }

  async editUser(editUserDto: EditCobraUserDto): Promise<BaseResponseWithContentNoPagination<Partial<CobraUser>>> {
    const existingUser = await this.userRepository.findOneWithRelations(
      {
        emailAddress: editUserDto.emailAddress,
      },
      ['roles']
    );

    if (!existingUser) {
      this.logger.error(`Unable to update user as user with email: ${editUserDto.emailAddress} does not exist`);
      throw new CustomException(`Unable to update user as user with email: ${editUserDto.emailAddress} does not exist`);
    }

    if (editUserDto.newEmailAddress && editUserDto.newEmailAddress !== existingUser.emailAddress) {
      const existingEmailUser = await this.userRepository.findOne({
        emailAddress: editUserDto.newEmailAddress,
      });

      if (existingEmailUser) {
        throw new CustomException('Email address already in use');
      }

      existingUser.emailAddress = editUserDto.newEmailAddress;
    }

    existingUser.firstName = editUserDto.firstName;
    existingUser.surname = editUserDto.surname;
    existingUser.staffId = editUserDto.staffId;
    existingUser.phoneNumber = editUserDto.phoneNumber;
    existingUser.location = editUserDto.location;
    existingUser.gender = editUserDto.gender;

    if (editUserDto.roleFk) {
      const roles = await this.roleRepository.find({
        where: { pk: editUserDto.roleFk },
      });
      if (!roles) {
        throw new BadRequestException('Provided role is invalid');
      }

      existingUser.roles = roles;
    }

    const result = await this.userRepository.saveEntity(existingUser);

    let response = new BaseResponseWithContentNoPagination<Partial<CobraUser>>(ResponseCodeEnum.SUCCESS);

    if (!result) {
      response = new BaseResponseWithContentNoPagination<Partial<CobraUser>>(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while updating user details, please try again');
    }

    response.content = this.userRepository.serialize(result);
    return response;
  }

  async changeOldUserPassword(changeOldPasswordDto: ChangeOldPasswordDto) {
    const { password, email, oldPassword } = changeOldPasswordDto;
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);

    if (password.toLowerCase() === oldPassword.toLowerCase()) {
      response.setDescription('We have seen this password before, please select a new password');
      return response;
    }

    const cobraUser: CobraUser | null = await this.userRepository
      .findOne({
        emailAddress: email,
      })
      .catch((error) => {
        this.logger.error('Error occurred while fetching user', error);
        return Promise.resolve(null);
      });

    if (!cobraUser) {
      const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('Unable to retrieve user details, please try again later');
      return response;
    }

    const isPasswordValid = await bcrypt.compare(oldPassword, cobraUser.password);
    if (!isPasswordValid) {
      const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('Invalid Old password credential provided, please check and try again.');
      return response;
    }

    const updateCobraUser = new CobraUser({
      rsaPin: cobraUser.rsaPin,
      firstName: cobraUser.firstName,
      emailAddress: cobraUser.emailAddress,
      password: await bcrypt.hash(password, 10),
      userType: cobraUser.userType,
      staffId: cobraUser.staffId,
      firstTimeLogin: false,
    });

    const result = await this.userRepository
      .findOneAndUpdate(
        {
          emailAddress: email,
        },
        updateCobraUser
      )
      .catch((error) => {
        this.logger.error('Error occurred while fetching user', error);
        return Promise.resolve(null);
      });

    if (!result) {
      response.setDescription('Unable to complete change password request, please try again');
      return response;
    }

    // send-email
    const placeholders: Record<string, string> = {};
    placeholders['username'] = cobraUser.surname;

    const notificationTemplateDto = new SendNotificationTemplateDto();
    notificationTemplateDto.emailRecipients = [cobraUser.emailAddress];
    notificationTemplateDto.notificationType = NotificatonTypeEnum.CHANGE_PASSWORD;
    notificationTemplateDto.placeholders = placeholders;

    await firstValueFrom(
      this.notificationClient.send(
        NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
        notificationTemplateDto
      )
    ).catch((error) => {
      this.logger.error(
        `Error Sending user generatePassAndCreateUser notification  error:  ${
          error instanceof Error ? error.stack : error
        }`
      );
      return Promise.resolve(null);
    });

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('User password has been successfully updated.');

    return response;
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDto): Promise<BaseResponseDto> {
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
    const cobraUser = await this.userRepository.findOne({
      emailAddress: verifyOtpDto.email,
    });

    if (!cobraUser) {
      response.setDescription('Unable to retrieve user details for OTP verification, please try again later');
      return response;
    }

    const otpResponse: BaseResponseDto | null = await firstValueFrom<BaseResponseDto>(
      this.notificationClient.send<BaseResponseDto>(NotificationServiceClientConstant.VERIFY_OTP, verifyOtpDto)
    ).catch((error) => {
      this.logger.error(`Error occurred sending notification error: ${error instanceof Error ? error.stack : error}`);
      return Promise.resolve(null);
    });

    if (!otpResponse) {
      response.setDescription('Error occurred while attempting to verify OTP, please try again later.');
      return response;
    }

    if (otpResponse.code !== 1) {
      return otpResponse;
    }

    cobraUser.firstTimeLogin = false;
    await this.userRepository.saveEntity(cobraUser);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('OTP verification successful');
    return response;
  }

  async generate2FASecret(
    loginAccessDto: LoginAccessDto
  ): Promise<BaseResponseWithContentNoPagination<Generate2FASecretResponse>> {
    const user = await this.userRepository.findOne({ staffId: loginAccessDto.username });

    if (!user) {
      throw new CustomException('User not found');
    }

    const pencomUserLoginMode = await this.settingMsLibService.getSetting(SettingsEnumKey.PENCOM_USER_LOGIN_MODE);
    if (pencomUserLoginMode === 'ACTIVE_DIRECTORY') {
      try {
        await this.ldapAuthService.login(loginAccessDto.username, loginAccessDto.password);
      } catch (error) {
        this.logger.error(`Error occurred while authenticating user with active directory`, error);
        throw new CustomException('Unable to verify username/password combination on active directory.');
      }
    } else {
      const isValid = await bcrypt.compare(loginAccessDto.password, user.password);
      if (!isValid) {
        throw new CustomException('Invalid password provided');
      }
    }

    const response = await this.twoFactorAuthenticationService.generate2FASecret(user.emailAddress);
    if (!response) {
      throw new CustomException('Error generating 2FA secret');
    }

    if (user.twoFactorSecret) {
      throw new CustomException('2FA secret already generated. Kindly contact admin');
    }

    user.twoFactorSecret = response.base32;
    await this.userRepository.saveEntity(user);

    const resp = new BaseResponseWithContentNoPagination<Generate2FASecretResponse>(ResponseCodeEnum.SUCCESS);
    resp.content = response;
    resp.setDescription('2FA secret generated successfully');

    return resp;
  }

  async reset2FASecret(email: string): Promise<BaseResponseDto> {
    const user = await this.userRepository.findOne({ emailAddress: email });

    if (!user) {
      throw new CustomException('User not found');
    }

    if (!user.twoFactorSecret) {
      throw new CustomException('User is not enrolled for 2FA');
    }

    user.twoFactorSecret = null;
    await this.userRepository.saveEntity(user);

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('2FA reset successfully');

    return response;
  }

  async agreeToTerms(email: string): Promise<BaseResponseDto> {
    const user = await this.userRepository.findOne({ emailAddress: email });

    if (!user) {
      const errorResponse = new BaseResponseDto(ResponseCodeEnum.ERROR);
      errorResponse.setDescription('User was not found');
      return errorResponse;
    }

    user.termsAndConditions = true;
    user.termsAndConditionsTimestamp = new Date();

    await this.userRepository.saveEntity(user);

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('Success');
    return response;
  }
}
