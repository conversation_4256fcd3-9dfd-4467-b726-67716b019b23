/* eslint-disable @typescript-eslint/strict-boolean-expressions */
import { Injectable, Inject } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { ClientProxy } from '@nestjs/microservices';
import {
  CreateWhitelistRequestDto,
  UpdateWhitelistRequestDto,
  WhitelistDocumentResult,
  WhitelistRequestDataDto,
} from '@app/shared/user-service/dtos/cobra-whitelist-request.dto';
import {
  BaseResponseListWithContentNoPagination,
  BaseResponseWithNoCountInfo,
} from '@app/shared/dto/response/base-response-with-content';
import { CobraWhitelistReqRepository } from '@app/shared/user-service/repositories/cobra-whitelist-req.repository';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { ResponseCodeEnum } from '@app/shared/enums';
import { CustomException } from '@app/shared/filters/exception.dto';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { ApprovalStatusEnum } from '@app/shared/enums/ApprovalStatusEnum';
import { WhitelistDocument } from '@app/shared/user-service/entities/cobra-whitelist-doc.entity';
import { UserWhitelistRequest } from '@app/shared/user-service/entities/cobra-whitelist-request.entity';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { CobraWhitelistDocRepository } from '@app/shared/user-service/repositories/cobra-whitelist-doc.repository';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { SettingsEnumKey } from '@app/shared/enums/SettingsEnum';
import { firstValueFrom } from 'rxjs';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { SendNotificationTemplateDto } from '@app/shared/dto/notification/send-notification-template.dto';
import { NotificationServiceClientConstant } from '@app/shared/constants/notification-service-client.constant';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';

@Injectable()
export class UserWhitelistRequestService {
  constructor(
    private readonly logger: PinoLogger,
    @Inject('NOTIFICATION_SERVICE_CLIENT') private readonly notificationClient: ClientProxy,
    private readonly whitelistRequestRepository: CobraWhitelistReqRepository,
    private readonly whitelistDocRepository: CobraWhitelistDocRepository,
    private readonly cobraUserRepository: CobraUserRepository,
    private readonly settingMsLibService: SettingMsLibService
  ) {}

  private generateRequestId(): string {
    const randomNum = Math.floor(Math.random() * 1000000)
      .toString()
      .padStart(6, '0');
    return `USER_WHITELIST_REQ_${randomNum}`;
  }

  private async getApproverRoles(): Promise<string[]> {
    const rolesString = await this.settingMsLibService.getSetting(SettingsEnumKey.WHITELIST_APPROVER_ROLES);
    return rolesString.split(',').map((role) => role.trim());
  }

  private async notifyApprovers(
    request: UserWhitelistRequest,
    cobraUser: CobraUser,
    requestor: CobraUser
  ): Promise<void> {
    try {
      const approverRoles = await this.getApproverRoles();
      const approvers = await this.cobraUserRepository.findApproversByRoles(approverRoles);
      const emailAddresses = approvers.map((approver) => approver.emailAddress);

      this.logger.debug(`Found approvers: ${emailAddresses.join(', ')}`);

      if (emailAddresses.length === 0) {
        this.logger.warn('No approvers found for whitelist request');
        return;
      }

      const organization = request.mdaCode || request.pfaCode;
      const placeholders: Record<string, string> = {
        userName: `${cobraUser.firstName} ${cobraUser.surname}`,
        requestedBy: `${requestor.firstName} ${requestor.surname} (${requestor.emailAddress})`,
        createDate: request.createDate.toLocaleDateString('en-NG', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        }),
        organization: organization || 'N/A',
        requestId: request.requestId,
      };

      const sendNotificationTemplateDto = new SendNotificationTemplateDto();
      sendNotificationTemplateDto.emailRecipients = emailAddresses;
      sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.WHITELIST_APPROVAL;
      sendNotificationTemplateDto.placeholders = placeholders;

      await firstValueFrom(
        this.notificationClient.send(
          NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
          sendNotificationTemplateDto
        )
      ).catch((error) => {
        this.logger.error('Failed to send approver notifications:', error);
      });

      this.logger.debug(`Notification sent to approvers: ${emailAddresses.join(', ')}`);
    } catch (error) {
      this.logger.error('Failed to send approver notifications:', error);
    }
  }

  private async notifyRequestor(
    request: UserWhitelistRequest,
    cobraUser: CobraUser,
    requestor: CobraUser
  ): Promise<void> {
    try {
      const userFullName = `${cobraUser.firstName} ${cobraUser.surname}`;
      const supervisorFirstName = requestor.firstName;

      const placeholders: Record<string, string> = {
        supervisorFirstName: supervisorFirstName,
        userFullName: userFullName,
        requestId: request.requestId,
      };

      const sendNotificationTemplateDto = new SendNotificationTemplateDto();
      sendNotificationTemplateDto.emailRecipients = [requestor.emailAddress];
      sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.WHITELIST_REQUEST_CONFIRMATION;
      sendNotificationTemplateDto.placeholders = placeholders;

      await firstValueFrom(
        this.notificationClient.send(
          NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
          sendNotificationTemplateDto
        )
      );

      this.logger.debug(`Notification sent to requestor: ${request.requestedBy.emailAddress}`);
    } catch (error) {
      this.logger.error('Failed to process requestor notification:', error);
    }
  }

  async createWhitelistRequest(
    dto: CreateWhitelistRequestDto,
    customReq: ICustomRequest
  ): Promise<BaseResponseListWithContentNoPagination<WhitelistRequestDataDto>> {
    const request = new UserWhitelistRequest({});
    request.requestId = this.generateRequestId();
    request.mdaCode = customReq.user?.mdaCode;
    request.pfaCode = customReq.user?.pfaCode;
    request.status = ApprovalStatusEnum.PENDING;

    const impactUser = await this.cobraUserRepository.findOne({ emailAddress: dto.requestUserEmail });

    if (!impactUser) {
      throw new CustomException('Request user not found');
    }

    if (!impactUser.blacklisted) {
      throw new CustomException('User is not blacklisted');
    }
    request.user = impactUser;

    //Check for pending request first
    const pendingRequest = await this.whitelistRequestRepository.findOne({
      user: { emailAddress: dto.requestUserEmail },
      status: ApprovalStatusEnum.PENDING,
    });

    if (pendingRequest) {
      throw new CustomException('A pending whitelist request already exists for this user');
    }

    const requestedBy = await this.cobraUserRepository.findOne({ emailAddress: customReq.user.email });
    request.requestedBy = requestedBy;
    request.userType = customReq.user.userType;

    // Create WhitelistDocument entities from the DTO
    const documents = dto.documents.map((doc) => {
      const document = new WhitelistDocument({});
      document.name = doc.documentName;
      document.type = doc.documentType;
      document.data = Buffer.from(doc.documentContent.replace(/^data:.*?;base64,/, ''), 'base64');
      document.whitelistReq = request;
      return document;
    });

    request.documents = documents;

    const savedRequest = await this.whitelistRequestRepository.saveEntity(request);

    // Send notifications after successful save
    await Promise.all([
      this.notifyApprovers(savedRequest, impactUser, requestedBy),
      this.notifyRequestor(savedRequest, impactUser, requestedBy),
    ]);

    const response = new BaseResponseListWithContentNoPagination<WhitelistRequestDataDto>(ResponseCodeEnum.SUCCESS);
    response.content = this.whitelistRequestRepository.mapToDto(savedRequest);
    response.setDescription('Whitelist request created successfully');
    return response;
  }

  async getAllWhitelistRequests(
    searchParam: PaginatedSearchDto
  ): Promise<BaseResponseWithNoCountInfo<WhitelistRequestDataDto>> {
    return await this.whitelistRequestRepository.searchRequests(searchParam);
  }

  async updateStatus(
    req: UpdateWhitelistRequestDto
  ): Promise<BaseResponseListWithContentNoPagination<WhitelistRequestDataDto>> {
    const request = await this.whitelistRequestRepository.findOneWithRelations({ pk: req.requestId }, [
      'approvedBy',
      'user',
      'requestedBy',
    ]);

    if (!request) {
      throw new CustomException('Whitelist request not found');
    }

    if (request.status !== ApprovalStatusEnum.PENDING) {
      throw new CustomException('Request has already been processed');
    }

    const approver = await this.cobraUserRepository.findOne({ emailAddress: req.approverEmail });

    if (!approver) {
      throw new CustomException('Approver not found');
    }

    const user = await request.user;
    user.blacklisted = req.status === ApprovalStatusEnum.APPROVED;
    request.status = req.status;
    request.comment = req.comment;
    request.approvedBy = approver;

    request.user = user;

    const updatedRequest = await this.whitelistRequestRepository.updateWhitelistRequest(request);

    const requestedBy = await request.requestedBy;

    // Send notifications to both user and supervisor
    await Promise.all([
      this.notifyRequestorOfStatus(updatedRequest, user, requestedBy),
      this.notifyOfStatus(updatedRequest, user),
    ]);

    const data = this.whitelistRequestRepository.mapToDto(updatedRequest);
    const resp = new BaseResponseListWithContentNoPagination<WhitelistRequestDataDto>(ResponseCodeEnum.SUCCESS, data);
    return resp;
  }

  async getDocument(requestId: number): Promise<BaseResponseListWithContentNoPagination<WhitelistDocumentResult>> {
    const document = await this.whitelistDocRepository.findByWhitelistReqId(requestId);

    if (!document) {
      throw new CustomException('Document not found');
    }

    const response = new BaseResponseListWithContentNoPagination<WhitelistDocumentResult>(ResponseCodeEnum.SUCCESS);
    response.content = document;
    response.setDescription('Document retrieved successfully');
    return response;
  }

  private async notifyRequestorOfStatus(
    request: UserWhitelistRequest,
    user: CobraUser,
    requestor: CobraUser
  ): Promise<void> {
    try {
      const userFullName = `${user.firstName} ${user.surname}`;
      const isApproved = request.status === ApprovalStatusEnum.APPROVED;

      const placeholders: Record<string, string> = {
        firstName: requestor.firstName,
        userFullName: userFullName,
        comment: request.comment || 'No additional comments provided.',
      };

      const sendNotificationTemplateDto = new SendNotificationTemplateDto();
      sendNotificationTemplateDto.emailRecipients = [requestor.emailAddress];
      sendNotificationTemplateDto.notificationType = isApproved
        ? NotificatonTypeEnum.WHITELIST_REQUEST_APPROVED
        : NotificatonTypeEnum.WHITELIST_REQUEST_REJECTED;
      sendNotificationTemplateDto.placeholders = placeholders;

      await firstValueFrom(
        this.notificationClient.send(
          NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
          sendNotificationTemplateDto
        )
      );

      this.logger.debug(`Status notification sent to user: ${request.user.emailAddress}`);
    } catch (error) {
      this.logger.error('Failed to process status notification:', error);
    }
  }

  private async notifyOfStatus(request: UserWhitelistRequest, user: CobraUser): Promise<void> {
    try {
      const isApproved = request.status === ApprovalStatusEnum.APPROVED;

      const userPlaceholders: Record<string, string> = {
        firstName: user.firstName,
        comment: request.comment || 'No additional comments provided.',
      };

      const userNotificationDto = new SendNotificationTemplateDto();
      userNotificationDto.emailRecipients = [user.emailAddress];
      userNotificationDto.notificationType = isApproved
        ? NotificatonTypeEnum.WHITELIST_REQUEST_USER_APPROVED
        : NotificatonTypeEnum.WHITELIST_REQUEST_USER_REJECTED;
      userNotificationDto.placeholders = userPlaceholders;

      await firstValueFrom(
        this.notificationClient.send(
          NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
          userNotificationDto
        )
      );
    } catch (error) {
      this.logger.error('Failed to process status notifications:', error);
      throw error;
    }
  }
}
