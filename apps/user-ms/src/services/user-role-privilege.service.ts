import { Inject, Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ogger } from 'nestjs-pino';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { CobraRoleRepository } from '@app/shared/user-service/repositories/cobra-role.repository';
import { CobraPriviledgeRepository } from '@app/shared/user-service/repositories/cobra-priviledge.repository';
import { MdaRepository } from '@app/shared/user-service/repositories/mda.repository';
import { ClientProxy } from '@nestjs/microservices';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { ResponseCodeEnum } from '@app/shared/enums';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { GetRoleDetail } from '@app/shared/user-service/dtos/cobra-role.dto';
import {
  BaseResponseWithContent,
  BaseResponseWithContentNoPagination,
} from '@app/shared/dto/response/base-response-with-content';

@Injectable()
export class UserRolePrivilegeService {
  constructor(
    private readonly userRepository: CobraUserRepository,
    private readonly roleRepository: CobraRoleRepository,
    private readonly priviledgeRepository: CobraPriviledgeRepository,
    private readonly mdaRepository: MdaRepository,
    private readonly pfaRepository: PfaRepository,
    private readonly logger: PinoLogger,
    @Inject('NOTIFICATION_SERVICE_CLIENT') private readonly notificationClient: ClientProxy
  ) {}

  async fetchPaginatedRoles(filter: PaginatedSearchDto) {
    try {
      const response = new BaseResponseWithContent(ResponseCodeEnum.SUCCESS);
      const result = await this.roleRepository.searchPaginatedRoles(filter);
      response.content = result.data;
      response.page = result.page;
      response.limit = result.limit;
      response.total = result.total;
      response.setDescription('Successfully fetched records');
      return response;
    } catch (error) {
      this.logger.error(`fetchPaginatedRoles error: ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while getting roles, please try again');
      return response;
    }
  }

  async getRoleByName(getRoleDetail: GetRoleDetail) {
    try {
      const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
      response.content = await this.roleRepository.getRolesByName(getRoleDetail);
      return response;
    } catch (error) {
      this.logger.error(`fetchPaginatedRoles error: ${error instanceof Error ? error.stack : error}`);
      const result = new BaseResponseDto(ResponseCodeEnum.ERROR);
      result.setDescription('Error occurred while processing request, please try again');
      return result;
    }
  }
}
