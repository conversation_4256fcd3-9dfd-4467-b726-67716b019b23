import { Injectable } from '@nestjs/common';
import { MdaRepository } from '@app/shared/user-service/repositories/mda.repository';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import {
  BaseResponseWithContent,
  BaseResponseWithContentNoPagination,
} from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums';
import { PinoLogger } from 'nestjs-pino';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import {
  CreateMdaDto,
  CreatePfaDto,
  CreatePfcDto,
  UpdateMdaDto,
  UpdatePfaDto,
  UpdatePfcDto,
} from '@app/shared/user-service/dtos/cobra-mda-pfa-pfc.dto';
import { Pfa } from '@app/shared/user-service/entities/pfa.entity';
import { PfcRepository } from '@app/shared/user-service/repositories/pfc.repository';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { Pfc } from '@app/shared/user-service/entities/pfc.entity';
import { Mda } from '@app/shared/user-service/entities/mda.entity';

@Injectable()
export class UserMdaPfaService {
  constructor(
    private readonly mdaRepository: MdaRepository,
    private readonly logger: PinoLogger,
    private readonly pfaRepository: PfaRepository,
    private readonly pfcRepository: PfcRepository
  ) {}

  async searchPfas(filter: PaginatedSearchDto) {
    try {
      const response = new BaseResponseWithContent(ResponseCodeEnum.SUCCESS);
      response.content = await this.pfaRepository.searchPfas(filter);
      return response;
    } catch (error) {
      this.logger.error(`searchPfas \n error:  ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while getting PFA List, please try again');
      return response;
    }
  }

  async searchPfcs(filter: PaginatedSearchDto) {
    try {
      const response = new BaseResponseWithContent(ResponseCodeEnum.SUCCESS);
      response.content = await this.pfcRepository.searchPfcs(filter);
      return response;
    } catch (error) {
      this.logger.error(`searchPfcs \n error:  ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while getting PFC List, please try again');
      return response;
    }
  }

  async createPfa(dto: CreatePfaDto): Promise<BaseResponseWithContentNoPagination<Pfa>> {
    const response = new BaseResponseWithContentNoPagination<Pfa>(ResponseCodeEnum.ERROR);

    const existingPfa = await this.pfaRepository.findOne({
      pfaCode: dto.pfaCode.trim(),
    });

    if (existingPfa) {
      response.setDescription('Pfa already exist');
      return response;
    }

    const retrievePfc = await this.pfcRepository.findOne({
      pfcCode: dto.pfcCode.trim(),
    });

    if (!retrievePfc) {
      response.setDescription('Pfc does not exist');
      return response;
    }

    const newPfa = new Pfa({
      ...dto,
      pfaName: dto.pfaName,
      phoneNumber: dto.phoneNumber,
      pfc: retrievePfc,
    });

    await this.pfaRepository.saveEntity(newPfa);
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Success');

    return response;
  }

  async editPfa(dto: UpdatePfaDto): Promise<BaseResponseDto> {
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);

    const existingPfa = await this.pfaRepository.findOne({
      pfaCode: dto.pfa.trim(),
    });

    if (!existingPfa) {
      response.setDescription('Pfa does not exist');
      return response;
    }

    if (dto.pfcCode) {
      const pfc = await this.pfcRepository.findOne({
        pfcCode: dto.pfcCode.trim(),
      });

      if (!pfc) {
        response.setDescription(`Pfc does not exist with code ${dto.pfcCode}`);
        return response;
      }

      existingPfa.pfc = pfc;
    }

    const updatableFields: (keyof UpdatePfaDto)[] = [
      'emailAddress',
      'pfaName',
      'phoneNumber',
      'accountName',
      'accountNumber',
      'address1',
      'address2',
      'address3',
      'address4',
      'address5',
      'domainUrl',
      'associatedPfaCode',
    ];

    for (const field of updatableFields) {
      const value = dto[field];
      if (typeof value === 'string') {
        existingPfa[field] = value.trim();
      }
    }

    if (dto.active) {
      existingPfa.active = dto.active;
    }

    await this.pfaRepository.saveEntity(existingPfa);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Pfa updated successfully');
    return response;
  }

  async retrievePfas(filter: PaginatedSearchDto): Promise<BaseResponseWithContent<any>> {
    try {
      const response = new BaseResponseWithContent<Pfa>(ResponseCodeEnum.SUCCESS);
      const paginatedResult = await this.pfaRepository.retrievePfas(filter);
      response.content = paginatedResult.result;
      response.total = paginatedResult.total;
      response.page = paginatedResult.page;
      response.limit = paginatedResult.limit;
      return response;
    } catch (error) {
      this.logger.error(`searchPfas \n error:  ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while getting PFA List, please try again');
      return response;
    }
  }

  async createPfc(dto: CreatePfcDto): Promise<BaseResponseWithContentNoPagination<Pfc>> {
    const response = new BaseResponseWithContentNoPagination<Pfc>(ResponseCodeEnum.ERROR);

    const existingPfc = await this.pfcRepository.findOne({
      pfcCode: dto.pfcCode.trim(),
    });

    if (existingPfc) {
      response.setDescription('PFC already exists');
      return response;
    }

    const newPfc = new Pfc({
      ...dto,
      pfcCode: dto.pfcCode,
      phoneNumber: dto.phoneNumber?.trim(),
      dateOfRegistration: dto.dateOfRegistration ? new Date(dto.dateOfRegistration) : null,
    });

    await this.pfcRepository.saveEntity(newPfc);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('PFC created successfully');

    return response;
  }

  async editPfc(dto: UpdatePfcDto): Promise<BaseResponseDto> {
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);

    const existingPfc = await this.pfcRepository.findOne({
      pfcCode: dto.pfc.trim(),
    });

    if (!existingPfc) {
      response.setDescription('PFC does not exist');
      return response;
    }

    const updatableFields: (keyof UpdatePfcDto)[] = [
      'pfcCode',
      'pfcName',
      'shortName',
      'contactPerson',
      'phoneNumber',
      'address1',
      'address2',
      'address3',
      'address4',
      'address5',
      'emailAddress',
    ];

    for (const field of updatableFields) {
      const value = dto[field];
      if (typeof value === 'string') {
        existingPfc[field] = value.trim();
      }
    }

    if (dto.dateOfRegistration) {
      existingPfc.dateOfRegistration = new Date(dto.dateOfRegistration);
    }

    if (dto.active) {
      existingPfc.active = dto.active;
    }

    await this.pfcRepository.saveEntity(existingPfc);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('PFC updated successfully');
    return response;
  }

  async retrievePfcs(filter: PaginatedSearchDto): Promise<BaseResponseWithContent<any>> {
    try {
      const response = new BaseResponseWithContent<Pfc>(ResponseCodeEnum.SUCCESS);
      const paginatedResult = await this.pfcRepository.retrievePfcs(filter);
      response.content = paginatedResult.result;
      response.total = paginatedResult.total;
      response.page = paginatedResult.page;
      response.limit = paginatedResult.limit;
      return response;
    } catch (error) {
      this.logger.error(`searchPfcs \n error:  ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while getting PFC List, please try again');
      return response;
    }
  }

  async createMda(dto: CreateMdaDto): Promise<BaseResponseWithContentNoPagination<Mda>> {
    const response = new BaseResponseWithContentNoPagination<Mda>(ResponseCodeEnum.ERROR);

    const existingMda = await this.mdaRepository.findOne({
      employerId: dto.employerId.trim(),
    });

    if (existingMda) {
      response.setDescription('MDA already exists');
      return response;
    }

    const newMda = new Mda({
      ...dto,
      ippisDate: dto.ippisDate ? new Date(dto.ippisDate) : null,
    });

    await this.mdaRepository.saveEntity(newMda);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Success');
    response.content = newMda;

    return response;
  }

  async editMda(dto: UpdateMdaDto): Promise<BaseResponseDto> {
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);

    const existingMda = await this.mdaRepository.findOne({
      employerId: dto.employerId.trim(),
    });

    if (!existingMda) {
      response.setDescription('MDA does not exist');
      return response;
    }

    const updatableFields: (keyof UpdateMdaDto)[] = [
      'employerId',
      'employerName',
      'ecrsEmployerCode',
      'sectorCode',
      'domainUrl',
    ];

    for (const field of updatableFields) {
      const value = dto[field];
      if (typeof value === 'string') {
        (existingMda as any)[field] = value.trim();
      }
    }

    if (dto.ippisDate) {
      existingMda.ippisDate = new Date(dto.ippisDate);
    }

    if (dto.active) {
      existingMda.active = dto.active;
    }

    await this.mdaRepository.saveEntity(existingMda);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('MDA updated successfully');
    return response;
  }

  async retrieveMdas(filter: PaginatedSearchDto): Promise<BaseResponseWithContent<any>> {
    try {
      const response = new BaseResponseWithContent<Mda>(ResponseCodeEnum.SUCCESS);
      const paginatedResult = await this.mdaRepository.retrieveMdas(filter);
      response.content = paginatedResult.result;
      response.total = paginatedResult.total;
      response.page = paginatedResult.page;
      response.limit = paginatedResult.limit;
      return response;
    } catch (error) {
      this.logger.error(`searchMdas \n error:  ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while getting MDA List, please try again');
      return response;
    }
  }

  async deletePfa(pfaCode: string): Promise<BaseResponseDto> {
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);

    if (!pfaCode) {
      response.setDescription('PFA code is required');
      return response;
    }
    const existingPfa = await this.pfaRepository.findOne({ pfaCode: pfaCode.trim() });

    if (!existingPfa) {
      response.setDescription(`PFA with code ${pfaCode} does not exist`);
      return response;
    }

    existingPfa.deleted = true;
    existingPfa.active = false;
    await this.pfaRepository.saveEntity(existingPfa);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('PFA deleted successfully');
    return response;
  }

  async deletePfc(pfcCode: string) {
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);

    if (!pfcCode) {
      response.setDescription('PFC code is required');
      return response;
    }
    const existingPfc = await this.pfcRepository.findOne({ pfcCode: pfcCode.trim() });

    if (!existingPfc) {
      response.setDescription(`PFA with code ${pfcCode} does not exist`);
      return response;
    }

    existingPfc.deleted = true;
    existingPfc.active = false;
    await this.pfcRepository.saveEntity(existingPfc);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('PFC deleted successfully');
    return response;
  }

  async deleteMda(mdaCode: string) {
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);

    if (!mdaCode) {
      response.setDescription('MDA Code code is required');
      return response;
    }
    const existingMda = await this.mdaRepository.findOne({ employerId: mdaCode.trim() });

    if (!existingMda) {
      response.setDescription(`MDA with code ${mdaCode} does not exist`);
      return response;
    }

    existingMda.deleted = true;
    existingMda.active = false;
    await this.mdaRepository.saveEntity(existingMda);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('MDA deleted successfully');
    return response;
  }
}
