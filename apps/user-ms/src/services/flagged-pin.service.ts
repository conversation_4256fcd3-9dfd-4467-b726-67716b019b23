/* eslint-disable */
import { Inject, Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { FlaggedPinRepository } from '@app/shared/enrollment-service/repository/flagged-pin.repository';
import { FlaggedPinDto } from '@app/shared/user-service/dtos/flagged-pin.dto';
import {
  BaseResponseWithContent,
  BaseResponseWithContentNoPagination,
} from '@app/shared/dto/response/base-response-with-content';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { ResponseCodeEnum } from '@app/shared/enums';
import { FlaggedPin } from '@app/shared/enrollment-service/entities/flagged-pin.entity';
import { ClientProxy } from '@nestjs/microservices';
import { RetrieveUserDto } from '@app/shared/dto/ecrs/retrieve-user.dto';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import { firstValueFrom } from 'rxjs';
import { EcrsServiceClientConstant } from '@app/shared/constants';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';

@Injectable()
export class FlaggedPinService {
  constructor(
    private readonly userRepository: CobraUserRepository,
    private readonly logger: PinoLogger,
    private flaggedPinRepository: FlaggedPinRepository,
    @Inject('ECRS_SERVICE_CLIENT') private readonly ecrsClient: ClientProxy
  ) {}

  async flagRsaHolderAccount(flaggedPinDto: FlaggedPinDto, req: ICustomRequest) {
    const response = new BaseResponseWithContentNoPagination<any>(ResponseCodeEnum.ERROR);
    const existingFlaggedPin = await this.flaggedPinRepository.findOne({ rsaPin: flaggedPinDto.rsaPin });
    if (existingFlaggedPin) {
      this.logger.warn(`RSA holder with pin ${flaggedPinDto.rsaPin} is already flagged.`);
      response.setDescription('RSA holder is already flagged.');
      return response;
    }

    // call ecrs
    const rsaPinDto = new RetrieveUserDto();
    rsaPinDto.rsaPin = flaggedPinDto.rsaPin;
    rsaPinDto.skipSurnameCheck = true;
    rsaPinDto.userType = RetireeUserTypeEnum.DECEASED;

    const ecrsUserResponseDto = (await firstValueFrom(
      this.ecrsClient.send({ cmd: EcrsServiceClientConstant.RETRIEVE_USER }, rsaPinDto)
    )) as EcrsUserResponseDto;

    if (!ecrsUserResponseDto || ecrsUserResponseDto.code !== ResponseCodeEnum.SUCCESS) {
      response.setDescription(`Failed to retrieve user data for PIN ${flaggedPinDto.rsaPin}`);
      return response;
    }

    const flaggedPin = new FlaggedPin({
      rsaPin: flaggedPinDto.rsaPin,
      reason: flaggedPinDto.reason,
      flaggedBy: req.user.email,
    });

    await this.flaggedPinRepository.saveEntity(flaggedPin);
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('RSA holder account flagged successfully.');
    return response;
  }

  async editFlaggedRsaHolderAccount(flaggedPinDto: FlaggedPinDto, req: ICustomRequest) {
    const response = new BaseResponseWithContentNoPagination<Partial<CobraUser>>(ResponseCodeEnum.ERROR);
    const existingFlaggedPin = await this.flaggedPinRepository.findOne({ rsaPin: flaggedPinDto.rsaPin });
    if (!existingFlaggedPin) {
      this.logger.warn(`RSA holder with pin ${flaggedPinDto.rsaPin} is not yet flagged.`);
      response.setDescription('RSA holder does not exist with provided pin');
      return response;
    }

    existingFlaggedPin.reason = flaggedPinDto.reason;
    existingFlaggedPin.active = flaggedPinDto.active !== undefined ? flaggedPinDto.active : existingFlaggedPin.active;
    existingFlaggedPin.flaggedBy = req.user.email;

    await this.flaggedPinRepository.saveEntity(existingFlaggedPin);
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Flagged RSA holder account updated successfully.');
    return response;
  }

  async fetchFlaggedRsaHolderAccount(paginatedSearchDto: PaginatedSearchDto) {
    try {
      const response = new BaseResponseWithContent<any>(ResponseCodeEnum.SUCCESS);
      const paginatedResult = await this.flaggedPinRepository.fetchFlaggedRsaHolderAccount(paginatedSearchDto);
      response.content = paginatedResult.result;
      response.total = paginatedResult.total;
      response.page = paginatedResult.page;
      response.limit = paginatedResult.limit;
      return response;
    } catch (error) {
      this.logger.error(`fetchFlaggedRsaHolderAccount \n error:  ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while getting FlaggedRsaHolderAccount List, please try again');
      return response;
    }
  }
}
