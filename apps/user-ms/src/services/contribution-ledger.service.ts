/* eslint-disable */
import { Inject, Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import {
  BaseResponseWithContent,
  BaseResponseWithContentNoPagination,
} from '@app/shared/dto/response/base-response-with-content';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { ResponseCodeEnum } from '@app/shared/enums';
import { ContributionLedgerDto } from '@app/shared/user-service/dtos/contribution-ledger.dto';
import { TbcReconcilePayRepository } from '@app/shared/enrollment-service/repository/tbc-reconcile-pay.repository';
import { TbcReconcilePay } from '@app/shared/enrollment-service/entities/tbc-reconcile-pay.entity';
import { RetrieveUserDto } from '@app/shared/dto/ecrs/retrieve-user.dto';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';
import { firstValueFrom } from 'rxjs';
import { EcrsServiceClientConstant } from '@app/shared/constants';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import { ClientProxy } from '@nestjs/microservices';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { Mda } from '@app/shared/user-service/entities/mda.entity';

@Injectable()
export class ContributionLedgerService {
  constructor(
    private readonly userRepository: CobraUserRepository,
    private readonly logger: PinoLogger,
    private readonly reconcilePayRepository: TbcReconcilePayRepository,
    @Inject('ECRS_SERVICE_CLIENT') private readonly ecrsClient: ClientProxy
  ) {}

  async createLedgerRecord(contributionLedgerDto: ContributionLedgerDto, req: ICustomRequest) {
    const response = new BaseResponseWithContentNoPagination<Partial<CobraUser>>(ResponseCodeEnum.ERROR);

    // call ecrs
    const rsaPinDto = new RetrieveUserDto();
    rsaPinDto.rsaPin = contributionLedgerDto.rsaPin;
    rsaPinDto.skipSurnameCheck = true;
    rsaPinDto.userType = RetireeUserTypeEnum.DECEASED;

    const ecrsUserResponseDto = (await firstValueFrom(
      this.ecrsClient.send({ cmd: EcrsServiceClientConstant.RETRIEVE_USER }, rsaPinDto)
    )) as EcrsUserResponseDto;

    if (!ecrsUserResponseDto || ecrsUserResponseDto.code !== ResponseCodeEnum.SUCCESS) {
      response.setDescription(`Failed to retrieve user data for PIN ${contributionLedgerDto.rsaPin}`);
      return response;
    }

    const existingPaidToDate = await this.reconcilePayRepository.getTotalPaidByPin(contributionLedgerDto.rsaPin);
    const existingPayDate = await this.reconcilePayRepository.getLastReconcilePayByPin(contributionLedgerDto.rsaPin);

    const lastPaydate = existingPayDate ? new Date(existingPayDate.createDate) : new Date();
    const reconcilePay = new TbcReconcilePay({
      pin: contributionLedgerDto.rsaPin,
      amount: contributionLedgerDto.amount,
      payType: contributionLedgerDto.payType,
      grade: contributionLedgerDto.grade,
      step: contributionLedgerDto.step,
      agencyCode: contributionLedgerDto.agencyCode,
      mthEmol: contributionLedgerDto.mthEmol,
      name: contributionLedgerDto.name,
      salaryType: contributionLedgerDto.salaryType,
      pfaCode: ecrsUserResponseDto.pfaCode,
      paidToDate: existingPaidToDate,
      lastPaydate,
      actorEmail: req.user.email,
    });

    await this.reconcilePayRepository.saveEntity(reconcilePay);
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Contribution ledger record created successfully.');
    return response;
  }

  async fetchContributionLedger(paginatedSearchDto: PaginatedSearchDto) {
    try {
      const response = new BaseResponseWithContent<Mda>(ResponseCodeEnum.SUCCESS);
      const paginatedResult = await this.reconcilePayRepository.fetchContributionLedger(paginatedSearchDto);
      response.content = paginatedResult.result;
      response.total = paginatedResult.total;
      response.page = paginatedResult.page;
      response.limit = paginatedResult.limit;
      return response;
    } catch (error) {
      this.logger.error(`fetchContributionLedger \n error:  ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while getting ContributionLedger List, please try again');
      return response;
    }
  }
}
