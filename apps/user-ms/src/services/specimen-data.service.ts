import { Injectable } from '@nestjs/common';
import { SpecimenData } from '@app/shared/user-service/entities/specimen-data.entity';
import {
  CreateSpecimenDataDto,
  SpecimenDataResponseDto,
  CreateSpecimenListDto,
} from '@app/shared/user-service/dtos/specimen-data.dto';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import {
  BaseResponseWithContentNoPagination,
  BaseResponseWithNoCountInfo,
} from '@app/shared/dto/response/base-response-with-content';
import { SpecimenDataRepository } from '@app/shared/user-service/repositories/specimen-data.repository';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { CustomException } from '@app/shared/filters/exception.dto';
import { PinoLogger } from 'nestjs-pino';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ResponseCodeEnum } from '@app/shared/enums';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { SettingsEnumKey } from '@app/shared/enums/SettingsEnum';

@Injectable()
export class SpecimenDataService {
  constructor(
    private readonly specimenDataRepository: SpecimenDataRepository,
    private readonly userRepository: CobraUserRepository,
    private readonly settingMsLibService: SettingMsLibService,
    private readonly logger: PinoLogger
  ) {}

  async getAllSpecimens(
    filter: PaginatedSearchDto,
    req: ICustomRequest
  ): Promise<BaseResponseWithNoCountInfo<SpecimenDataResponseDto>> {
    if (req.user.mdaCode) {
      filter.filters = {
        ...filter.filters,
        mdaCode: req.user.mdaCode,
      };
    }

    return await this.specimenDataRepository.searchSpecimens(filter);
  }

  async createSpecimens(dto: CreateSpecimenListDto, req: ICustomRequest): Promise<SpecimenDataResponseDto[]> {
    // Get max file size once for all specimens
    const maxFileSizeMB = await this.settingMsLibService.getSettingInt(SettingsEnumKey.SPECIMEN_FILE_MAX_SIZE);

    // Get creator once for all specimens
    const creator = await this.userRepository.findOne({ emailAddress: req.user.email });

    // Check for duplicates in the database
    const existingSpecimens = await this.specimenDataRepository.find({
      where: dto.specimens.map((specimen) => ({
        name: specimen.name,
        year: specimen.year,
      })),
    });

    if (existingSpecimens.length > 0) {
      const duplicates = existingSpecimens.map((specimen) => `${specimen.name} (${specimen.year})`).join(', ');
      throw new CustomException(`The following specimens already exist: ${duplicates}`);
    }

    // Validate file sizes and prepare entities
    const specimenEntities = dto.specimens.map((specimen) => {
      const documentBuffer = Buffer.from(specimen.documentData, 'base64');
      const fileSizeInMB = documentBuffer.length / (1024 * 1024);

      if (fileSizeInMB > maxFileSizeMB) {
        throw new CustomException(
          `File size (${fileSizeInMB.toFixed(2)}MB) for specimen '${specimen.name}' exceeds the maximum allowed size of ${maxFileSizeMB}MB`
        );
      }

      return new SpecimenData({
        ...specimen,
        mdaCode: req.user.mdaCode,
        createdBy: creator,
        documentData: documentBuffer,
      });
    });

    // Save all specimens in a single transaction
    const savedSpecimens = await this.specimenDataRepository.saveEntities(specimenEntities);

    return savedSpecimens.map((specimen) => this.specimenDataRepository.mapToDto(specimen));
  }

  async deleteSpecimen(id: number, req: ICustomRequest) {
    const specimen = await this.specimenDataRepository.findOne({ pk: id });

    if (specimen === null) {
      throw new CustomException('Specimen not found');
    }

    if (req.user.userType === 'MDA' && req.user.mdaCode !== specimen.mdaCode) {
      throw new CustomException('You are not authorized to delete this specimen');
    }

    await this.specimenDataRepository.findOneAndDelete({ pk: id });

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);

    return response;
  }

  async getSpecimenDocumentById(id: number) {
    const specimen = await this.specimenDataRepository.findOne({ pk: id });

    if (specimen === null) {
      throw new CustomException('Specimen not found');
    }
    const documentData = await specimen.documentData;

    const base64Data = documentData
      ?.toString('base64')
      .replace(/^data:.*?;base64,/, '')
      .replace(/^dataimage\/.*?base64/, '')
      .replace('dataapplication/pdfbase64', '');

    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);

    response.content = {
      documentData: base64Data,
      docExt: specimen.docExt,
    };

    return response;
  }
}
