/* eslint-disable */
import { Inject, Injectable } from '@nestjs/common';
import { Pi<PERSON><PERSON>ogger } from 'nestjs-pino';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import {
  BaseResponseWithContent,
  BaseResponseWithContentNoPagination,
} from '@app/shared/dto/response/base-response-with-content';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { ResponseCodeEnum } from '@app/shared/enums';
import { RetrieveUserDto } from '@app/shared/dto/ecrs/retrieve-user.dto';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';
import { firstValueFrom } from 'rxjs';
import { EcrsServiceClientConstant, HYPHEN_DATE_FORMAT } from '@app/shared/constants';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import { ClientProxy } from '@nestjs/microservices';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { Mda } from '@app/shared/user-service/entities/mda.entity';
import { AccrLegacyPaymentsDto } from '@app/shared/user-service/dtos/accrued-rights-ledger.dto';
import { AccrLegacyPaymentsRepository } from '@app/shared/enrollment-service/repository/accr-legacy-payments.repository';
import { AccrLegacyPayments } from '@app/shared/enrollment-service/entities/accr-legacy-payments.entity';
import { convertHypenToIsoDate } from '@app/shared/workflow/utils/workflow-utils';

@Injectable()
export class AccruedRightsLedgerService {
  constructor(
    private readonly userRepository: CobraUserRepository,
    private readonly logger: PinoLogger,
    private readonly accrLegacyPaymentsRepository: AccrLegacyPaymentsRepository,
    @Inject('ECRS_SERVICE_CLIENT') private readonly ecrsClient: ClientProxy
  ) {}

  async createLedgerRecord(accrLegacyPaymentsDto: AccrLegacyPaymentsDto, req: ICustomRequest) {
    const response = new BaseResponseWithContentNoPagination<Partial<CobraUser>>(ResponseCodeEnum.ERROR);

    // call ecrs
    const rsaPinDto = new RetrieveUserDto();
    rsaPinDto.rsaPin = accrLegacyPaymentsDto.rsaPin;
    rsaPinDto.surname = accrLegacyPaymentsDto.surname;
    rsaPinDto.skipSurnameCheck = false;
    rsaPinDto.userType = RetireeUserTypeEnum.DECEASED;

    const ecrsUserResponseDto = (await firstValueFrom(
      this.ecrsClient.send({ cmd: EcrsServiceClientConstant.RETRIEVE_USER }, rsaPinDto)
    )) as EcrsUserResponseDto;

    if (!ecrsUserResponseDto || ecrsUserResponseDto.code !== ResponseCodeEnum.SUCCESS) {
      response.setDescription(
        `Failed to retrieve user data for PIN ${accrLegacyPaymentsDto.rsaPin}: ${ecrsUserResponseDto?.description}`
      );
      return response;
    }

    const accrLegacyPayments = new AccrLegacyPayments({
      pin: accrLegacyPaymentsDto.rsaPin,
      batchName: accrLegacyPaymentsDto.batchName,
      fullName: `${ecrsUserResponseDto.surname} ${ecrsUserResponseDto.firstName}`,
      gender: ecrsUserResponseDto.gender,
      dor: convertHypenToIsoDate(accrLegacyPaymentsDto.dor, HYPHEN_DATE_FORMAT),
      dob: convertHypenToIsoDate(ecrsUserResponseDto.dateOfBirth, HYPHEN_DATE_FORMAT),
      dofa: convertHypenToIsoDate(accrLegacyPaymentsDto.dofa, HYPHEN_DATE_FORMAT),
      introDate: convertHypenToIsoDate(accrLegacyPaymentsDto.introDate, HYPHEN_DATE_FORMAT),
      accrBenfitAddDate: convertHypenToIsoDate(accrLegacyPaymentsDto.accrBenfitAddDate, HYPHEN_DATE_FORMAT),
      pfaName: ecrsUserResponseDto.pfaName,
      mdaName: accrLegacyPaymentsDto.mdaName,
      salStruc: accrLegacyPaymentsDto.salStruc,
      glJun2004: accrLegacyPaymentsDto.glJun2004,
      stepJun2004: accrLegacyPaymentsDto.stepJun2004,
      annualPenAllow: accrLegacyPaymentsDto.annualPenAllow,
      accrBenfitActual: accrLegacyPaymentsDto.accrBenfitActual,
      accrBenfitApprox: accrLegacyPaymentsDto.accrBenfitApprox,
      accrBenfitAdd: accrLegacyPaymentsDto.accrBenfitAdd,
      accrBenfit2ndAdd: accrLegacyPaymentsDto.accrBenfit2ndAdd,
      accrBenfitRecovered: accrLegacyPaymentsDto.accrBenfitRecovered,
      accrBenfitRecoveredDate: convertHypenToIsoDate(accrLegacyPaymentsDto.accrBenfitRecoveredDate, HYPHEN_DATE_FORMAT),
      accrBenfitNet: accrLegacyPaymentsDto.accrBenfitNet,
      exitMode: accrLegacyPaymentsDto.exitMode,
      actorEmail: req.user.email,
    });

    await this.accrLegacyPaymentsRepository.saveEntity(accrLegacyPayments);
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Accrued Rights ledger record created successfully.');
    return response;
  }

  async fetchAccruedRightsLedger(paginatedSearchDto: PaginatedSearchDto) {
    try {
      const response = new BaseResponseWithContent<Mda>(ResponseCodeEnum.SUCCESS);
      const paginatedResult = await this.accrLegacyPaymentsRepository.accruedRightsLedger(paginatedSearchDto);
      response.content = paginatedResult.result;
      response.total = paginatedResult.total;
      response.page = paginatedResult.page;
      response.limit = paginatedResult.limit;
      return response;
    } catch (error) {
      this.logger.error(`fetchAccruedRightsLedger \n error:  ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while getting fetchAccruedRightsLedger List, please try again');
      return response;
    }
  }
}
