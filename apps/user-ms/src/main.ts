/* eslint-disable */
import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { UserMsModule } from './user-ms.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { GlobalExceptionFilter } from '@app/shared/filters/global-exception.filter';
import * as bodyParser from 'body-parser';

async function bootstrap() {
  const app = await NestFactory.create(UserMsModule, {
    bufferLogs: true,
  });

  // Increase payload size limits
  app.use(bodyParser.json({ limit: '20mb' }));

  app.setGlobalPrefix('user');
  app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
  app.useLogger(app.get(Logger));

  const configService = app.get(ConfigService);
  const port = configService.get('USER_SERVICE_PORT');
  const tcpPort = configService.get('USER_SERVICE_TCP_PORT');

  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.TCP,
    options: {
      host: '0.0.0.0',
      port: tcpPort,
    },
  });

  const config = new DocumentBuilder()
    .setTitle('User Service')
    .setDescription('Documentation API for Notification Service')
    .setVersion(configService.get('USER_SERVICE_VERSION') || '1.0.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('swagger-api', app, document);

  app.useGlobalFilters(new GlobalExceptionFilter());
  await app.startAllMicroservices();
  app.enableCors();
  await app.listen(port);
  console.log(`User service is running on port ${tcpPort}, httpPort ${port}`);
}

bootstrap();
