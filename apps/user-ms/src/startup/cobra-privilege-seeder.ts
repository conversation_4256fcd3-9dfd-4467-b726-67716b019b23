import { Injectable, OnModuleInit } from '@nestjs/common';
import { CobraPriviledgeRepository } from '@app/shared/user-service/repositories/cobra-priviledge.repository';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class CobraPrivilegeSeeder implements OnModuleInit {
  constructor(
    private readonly cobraPrivilegeRepository: CobraPriviledgeRepository,
    private readonly logger: PinoLogger
  ) {}

  async onModuleInit() {
    this.logger.info('onModuleInit about to seed privileges');
    await this.cobraPrivilegeRepository.seedPrivileges();
    this.logger.info('onModuleInit done seeding privileges');
  }
}
