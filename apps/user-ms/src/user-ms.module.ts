/* eslint-disable */
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UserMsController } from './controllers/user-ms.controller';
import { UserMsService } from './services/user-ms.service';
import { DatabaseModule, LoggerModule, RedisCacheModule } from '@app/shared';
import { GlobalExceptionFilter } from '@app/shared/filters/global-exception.filter';
import { APP_FILTER, APP_GUARD } from '@nestjs/core';
import { HealthModule } from '@app/shared/health';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { AuthGuard } from '@app/shared/auth/guards/auth.guard';
import { AuthModule } from '@app/shared/auth/auth.module';
import { Pfa } from '@app/shared/user-service/entities/pfa.entity';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { Mda } from '@app/shared/user-service/entities/mda.entity';
import { MdaRepository } from '@app/shared/user-service/repositories/mda.repository';
import { AuditLibModule } from '@app/shared/audit-service/audit-lib.module';
import {
  AUDIT_QUEUE_NAME,
  ECRS_SERVICE,
  ECRS_SERVICE_HOST,
  ECRS_SERVICE_TCP_PORT,
  NOTIFICATION_SERVICE_HOST,
  NOTIFICATION_SERVICE_TCP_PORT,
  NOTIFICATIONS_SERVICE,
  USER_SERVICE,
} from '@app/shared/constants';
import { CobraRole } from '@app/shared/user-service/entities/cobra-role.entity';
import { CobraPrivilege } from '@app/shared/user-service/entities/cobra-priviledge.entity';
import { CobraRoleRepository } from '@app/shared/user-service/repositories/cobra-role.repository';
import { CobraPriviledgeRepository } from '@app/shared/user-service/repositories/cobra-priviledge.repository';
import { ClientProxyFactory, Transport } from '@nestjs/microservices';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { SettingMsLibModule } from '@app/shared/setting-service/setting-ms-lib.module';
import { UserMsMessageController } from './controllers/user-ms-message.controller';
import { UserRolePrivilegeService } from './services/user-role-privilege.service';
import { UserMdaPfaService } from './services/user-mda-pfa.service';
import { CobraPrivilegeSeeder } from './startup/cobra-privilege-seeder';
import { EnrolmentSummary } from '@app/shared/enrollment-service/entities/enrolment-summary.entity';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { UserRoleChangeRequest } from '@app/shared/user-service/entities/cobra-role-change-request.entity';
import { UserRoleChangeRequestService } from './services/user-role-change-request.service';
import { CobraRoleChangeReqDocRepository } from '@app/shared/user-service/repositories/cobra-role-change-req-doc.repository';
import { CobraUserRoleChangeReqRepository } from '@app/shared/user-service/repositories/cobra-role-change-req.repository';
import { RoleChangeDocument } from '@app/shared/user-service/entities/cobra-role-doc.entity';
import { UserRoleChangeRequestController } from './controllers/user-role-privilege.controller';
import { UserWhitelistRequest } from '@app/shared/user-service/entities/cobra-whitelist-request.entity';
import { WhitelistDocument } from '@app/shared/user-service/entities/cobra-whitelist-doc.entity';
import { CobraWhitelistReqRepository } from '@app/shared/user-service/repositories/cobra-whitelist-req.repository';
import { UserWhitelistRequestController } from './controllers/user-whitelist-request.controller';
import { UserWhitelistRequestService } from './services/user-whitelist-request.service';
import { CobraWhitelistDocRepository } from '@app/shared/user-service/repositories/cobra-whitelist-doc.repository';
import { SpecimenData } from '@app/shared/user-service/entities/specimen-data.entity';
import { SpecimenDataController } from './controllers/specimen-data.controller';
import { SpecimenDataService } from './services/specimen-data.service';
import { SpecimenDataRepository } from '@app/shared/user-service/repositories/specimen-data.repository';
import { Pfc } from '@app/shared/user-service/entities/pfc.entity';
import { PfcRepository } from '@app/shared/user-service/repositories/pfc.repository';
import { TbeOrganisationsRepository } from '@app/shared/enrollment-service/repository/tbe-organisations.repository';
import { TbeOrganisations } from '@app/shared/enrollment-service/entities/tbe-organisations.entity';
import { LdapModule } from '@app/shared/ldap/ldap.module';
import { FlaggedPinController } from './controllers/flagged-pin.controller';
import { FlaggedPinService } from './services/flagged-pin.service';
import { FlaggedPinRepository } from '@app/shared/enrollment-service/repository/flagged-pin.repository';
import { FlaggedPin } from '@app/shared/enrollment-service/entities/flagged-pin.entity';
import { ContributionLedgerController } from './controllers/contribution-ledger.controller';
import { ContributionLedgerService } from './services/contribution-ledger.service';
import { TbcReconcilePayRepository } from '@app/shared/enrollment-service/repository/tbc-reconcile-pay.repository';
import { TbcReconcilePay } from '@app/shared/enrollment-service/entities/tbc-reconcile-pay.entity';
import { AccruedRightLedgerController } from './controllers/accrued-ledger.controller';
import { AccruedRightsLedgerService } from './services/accrued-rights-ledger.service';
import { AccrLegacyPaymentsRepository } from '@app/shared/enrollment-service/repository/accr-legacy-payments.repository';
import { AccrLegacyPayments } from '@app/shared/enrollment-service/entities/accr-legacy-payments.entity';

@Module({
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([
      CobraUser,
      Pfa,
      Pfc,
      Mda,
      CobraRole,
      FlaggedPin,
      TbcReconcilePay,
      AccrLegacyPayments,
      CobraPrivilege,
      TbeOrganisations,
      EnrolmentSummary,
      UserRoleChangeRequest,
      RoleChangeDocument,
      UserWhitelistRequest,
      WhitelistDocument,
      SpecimenData,
    ]),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    HealthModule.registerAsync(
      [
        { name: NOTIFICATIONS_SERVICE, host: NOTIFICATION_SERVICE_HOST, port: NOTIFICATION_SERVICE_TCP_PORT },
        { name: ECRS_SERVICE, host: ECRS_SERVICE_HOST, port: ECRS_SERVICE_TCP_PORT },
      ],
      AUDIT_QUEUE_NAME
    ),
    LoggerModule.forRoot(USER_SERVICE),
    RedisCacheModule.register(),
    AuthModule,
    AuditLibModule,
    LdapModule,
    SettingMsLibModule,
  ],
  controllers: [
    UserMsController,
    UserMsMessageController,
    UserRoleChangeRequestController,
    ContributionLedgerController,
    AccruedRightLedgerController,
    UserWhitelistRequestController,
    SpecimenDataController,
    FlaggedPinController,
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: 'NOTIFICATION_SERVICE_CLIENT',
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: configService.get('NOTIFICATION_SERVICE_HOST', '127.0.0.1'),
            port: configService.get('NOTIFICATION_SERVICE_TCP_PORT', 3008),
          },
        });
      },
    },
    {
      provide: 'ECRS_SERVICE_CLIENT',
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: configService.get('ECRS_SERVICE_HOST', '0.0.0.0'),
            port: configService.get('ECRS_SERVICE_TCP_PORT', 3017),
          },
        });
      },
    },
    UserMsService,
    ContributionLedgerService,
    AccruedRightsLedgerService,
    UserRolePrivilegeService,
    CobraPrivilegeSeeder,
    FlaggedPinService,
    UserMdaPfaService,
    SettingMsLibService,
    CobraUserRepository,
    PfaRepository,
    MdaRepository,
    PfcRepository,
    CobraRoleRepository,
    CobraPriviledgeRepository,
    TbcReconcilePayRepository,
    TbeOrganisationsRepository,
    CobraRoleChangeReqDocRepository,
    CobraUserRoleChangeReqRepository,
    EnrolmentSummaryRepository,
    UserRoleChangeRequestService,
    CobraWhitelistReqRepository,
    CobraWhitelistDocRepository,
    UserWhitelistRequestService,
    SpecimenDataService,
    SpecimenDataRepository,
    FlaggedPinRepository,
    AccrLegacyPaymentsRepository,
  ],
})
export class UserMsModule {}
