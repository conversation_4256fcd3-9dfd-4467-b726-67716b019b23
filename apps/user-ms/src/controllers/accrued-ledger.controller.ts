import { Body, Controller, Post, Req, UseInterceptors } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { Audit } from '@app/shared/decorators/audit.decorator';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { AccruedRightsLedgerService } from '../services/accrued-rights-ledger.service';
import { AccrLegacyPaymentsDto } from '@app/shared/user-service/dtos/accrued-rights-ledger.dto';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';

@ApiBearerAuth()
@ApiTags('Accrued Rights Ledger Controller')
@Controller('accrued-right-ledger')
@UseInterceptors(AuditInterceptor)
export class AccruedRightLedgerController {
  constructor(private readonly accruedRightsLedgerService: AccruedRightsLedgerService) {}

  @Audit(AuditEventTypeEnum.ACCRUED_RIGHTS_LEDGER_INSERT, (req) => {
    return {
      extra: {
        rsaPin: req.body.rsaPin,
        payType: req.body.payType,
        name: req.body.name,
        amount: req.body.amount,
      },
    };
  })
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.CREATE_ACCRUED_RIGHTS_LEDGER)
  @Post('create-record')
  @ApiOperation({
    summary: 'Insert ACCRUED RIGHTS Ledger Record',
    description: 'Insert ACCRUED RIGHTS Ledger Record',
  })
  @ApiResponse({
    status: 201,
    description: 'Success',
    schema: {
      example: {
        code: 1,
        description: 'Accrued Rights ledger record created successfully.',
        content: null,
      },
    },
  })
  async createLedgerRecord(@Body() accrLegacyPaymentsDto: AccrLegacyPaymentsDto, @Req() req: ICustomRequest) {
    return await this.accruedRightsLedgerService.createLedgerRecord(accrLegacyPaymentsDto, req);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.RETRIEVE_ACCRUED_RIGHTS_LEDGER)
  @Post('fetch-records')
  @ApiOperation({
    summary: 'Retrieve Accrued Rights Ledger',
    description: 'Retrieve Accrued Rights Ledger',
  })
  @ApiResponse({
    status: 201,
    description: 'Success',
    schema: {
      example: {
        code: 1,
        description: 'Success.',
        content: null,
      },
    },
  })
  async fetchAccruedRightsLedger(@Body() paginatedSearchDto: PaginatedSearchDto) {
    return await this.accruedRightsLedgerService.fetchAccruedRightsLedger(paginatedSearchDto);
  }
}
