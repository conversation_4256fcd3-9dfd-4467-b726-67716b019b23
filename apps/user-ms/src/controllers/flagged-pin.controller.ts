/* eslint-disable */
import { Body, Controller, Post, Req, UseInterceptors } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { Audit } from '@app/shared/decorators/audit.decorator';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { FlaggedPinService } from '../services/flagged-pin.service';
import { FlaggedPinDto } from '@app/shared/user-service/dtos/flagged-pin.dto';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';

@ApiBearerAuth()
@ApiTags('Flagged Controller')
@Controller('flagged-pin')
@UseInterceptors(AuditInterceptor)
export class FlaggedPinController {
  constructor(private readonly flaggedPinService: FlaggedPinService) {}

  @Audit(AuditEventTypeEnum.FLAG_RSA_HOLDER, (req) => {
    return {
      extra: {
        rsaPin: req.body.rsaPin,
        reason: req.body.reason,
        status: req.body.status,
      },
    };
  })
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.FLAG_RSA_HOLDER)
  @Post('flag-rsa-holder')
  @ApiOperation({
    summary: 'Flag RSA holder Account',
    description: 'Flags an RSA holder account for review',
  })
  @ApiResponse({
    status: 201,
    description: 'Success',
    schema: {
      example: {
        code: 1,
        description: 'RSA holder account flagged successfully.',
        content: null,
      },
    },
  })
  async flagRsaHolderAccount(@Body() flaggedPinDto: FlaggedPinDto, @Req() req: ICustomRequest) {
    return await this.flaggedPinService.flagRsaHolderAccount(flaggedPinDto, req);
  }

  @Audit(AuditEventTypeEnum.EDIT_FLAG_RSA_HOLDER, (req) => {
    return {
      extra: {
        rsaPin: req.body.rsaPin,
        reason: req.body.reason,
        status: req.body.status,
      },
    };
  })
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.EDIT_FLAG_RSA_HOLDER)
  @Post('edit-flag-rsa-holder')
  @ApiOperation({
    summary: 'Edit Flag RSA holder Account',
    description: 'Edit Flags an RSA holder account for review',
  })
  @ApiResponse({
    status: 201,
    description: 'Success',
    schema: {
      example: {
        code: 1,
        description: 'Flagged RSA holder account updated successfully.',
        content: null,
      },
    },
  })
  async editFlaggedRsaHolderAccount(@Body() flaggedPinDto: FlaggedPinDto, @Req() req: ICustomRequest) {
    return await this.flaggedPinService.editFlaggedRsaHolderAccount(flaggedPinDto, req);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.RETRIEVE_FLAG_RSA_HOLDER)
  @Post('fetch-flagged-rsas')
  @ApiOperation({
    summary: 'Retrieve Flagged RSA holder Accounts',
    description: 'Retrieve Flagged RSA holder account for review',
  })
  @ApiResponse({
    status: 201,
    description: 'Success',
    schema: {
      example: {
        code: 1,
        description: 'Deactivate RSA holder account flagged successfully.',
        content: null,
      },
    },
  })
  async fetchFlaggedRsaHolderAccount(@Body() paginatedSearchDto: PaginatedSearchDto) {
    return await this.flaggedPinService.fetchFlaggedRsaHolderAccount(paginatedSearchDto);
  }
}
