import { Controller, Get, Post, Delete, Body, Param, Req, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  SpecimenDataResponseDto,
  CreateSpecimenListDto,
  DownloadDocument,
} from '@app/shared/user-service/dtos/specimen-data.dto';
import { SpecimenDataService } from './../services/specimen-data.service';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { BaseResponseListWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';

@ApiBearerAuth()
@ApiTags('Specimen Data')
@Controller('specimen-data')
export class SpecimenDataController {
  constructor(private readonly specimenDataService: SpecimenDataService) {}

  @RequirePrivileges(CobraPrivileges.SPECIMEN_CREATION)
  @AllowedUserTypes(UserTypeEnum.PENCOM, UserTypeEnum.MDA)
  @Post()
  @ApiOperation({ summary: 'Create multiple specimens' })
  @ApiResponse({
    status: 201,
    description: 'Specimens created successfully',
    type: [SpecimenDataResponseDto],
  })
  async createSpecimens(
    @Body() createDto: CreateSpecimenListDto,
    @Req() req: ICustomRequest
  ): Promise<SpecimenDataResponseDto[]> {
    return await this.specimenDataService.createSpecimens(createDto, req);
  }

  @RequirePrivileges(CobraPrivileges.SPECIMEN_PAGE)
  @AllowedUserTypes(UserTypeEnum.PENCOM, UserTypeEnum.MDA, UserTypeEnum.PFA)
  @Post('get-specimens')
  @ApiOperation({ summary: 'Get all specimen data' })
  @ApiResponse({
    status: 200,
    description: 'List of specimen data',
    type: BaseResponseListWithContentNoPagination<SpecimenDataResponseDto>,
  })
  async getAllSpecimens(@Body() filter: PaginatedSearchDto, @Req() req: ICustomRequest) {
    return await this.specimenDataService.getAllSpecimens(filter, req);
  }

  @RequirePrivileges(CobraPrivileges.SPECIMEN_PAGE)
  @AllowedUserTypes(UserTypeEnum.PENCOM, UserTypeEnum.MDA, UserTypeEnum.PFA)
  @Post('document')
  @ApiOperation({ summary: 'Get specimen data by ID' })
  @ApiResponse({
    status: 200,
    description: 'Specimen data',
    type: SpecimenDataResponseDto,
  })
  async getSpecimenById(@Body() downloadDto: DownloadDocument) {
    return await this.specimenDataService.getSpecimenDocumentById(downloadDto.pk);
  }

  @RequirePrivileges(CobraPrivileges.SPECIMEN_DELETION)
  @AllowedUserTypes(UserTypeEnum.PENCOM, UserTypeEnum.MDA)
  @Delete(':id')
  @ApiOperation({ summary: 'Delete specimen data' })
  @ApiResponse({
    status: 200,
    description: 'Specimen data deleted successfully',
    type: BaseResponseDto,
  })
  async deleteSpecimen(@Param('id') id: number, @Req() req: ICustomRequest) {
    return await this.specimenDataService.deleteSpecimen(id, req);
  }
}
