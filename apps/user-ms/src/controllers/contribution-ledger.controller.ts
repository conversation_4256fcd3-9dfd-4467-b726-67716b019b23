/* eslint-disable */
import { Body, Controller, Post, Req, UseInterceptors } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { Audit } from '@app/shared/decorators/audit.decorator';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { ContributionLedgerService } from '../services/contribution-ledger.service';
import { ContributionLedgerDto } from '@app/shared/user-service/dtos/contribution-ledger.dto';

@ApiBearerAuth()
@ApiTags('Contribution Ledger Controller')
@Controller('contribution-ledger')
@UseInterceptors(AuditInterceptor)
export class ContributionLedgerController {
  constructor(private readonly contributionLedgerService: ContributionLedgerService) {}

  @Audit(AuditEventTypeEnum.CONTRIBUTION_LEDGER_INSERT, (req) => {
    return {
      extra: {
        rsaPin: req.body.pin,
        payType: req.body.payType,
        name: req.body.name,
        amount: req.body.amount,
      },
    };
  })
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.CREATE_CONTRIBUTION_LEDGER)
  @Post('create-record')
  @ApiOperation({
    summary: 'Insert Contribution Ledger Record',
    description: 'Insert Contribution Ledger Record',
  })
  @ApiResponse({
    status: 201,
    description: 'Success',
    schema: {
      example: {
        code: 1,
        description: 'Contribution ledger record created successfully.',
        content: null,
      },
    },
  })
  async createLedgerRecord(@Body() contributionLedgerDto: ContributionLedgerDto, @Req() req: ICustomRequest) {
    return await this.contributionLedgerService.createLedgerRecord(contributionLedgerDto, req);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.RETRIEVE_CONTRIBUTION_LEDGER)
  @Post('fetch-contribution-ledger')
  @ApiOperation({
    summary: 'Retrieve Contribution Ledger',
    description: 'Retrieve  Contribution Ledger',
  })
  @ApiResponse({
    status: 201,
    description: 'Success',
    schema: {
      example: {
        code: 1,
        description: 'Success.',
        content: null,
      },
    },
  })
  async fetchContributionLedger(@Body() paginatedSearchDto: PaginatedSearchDto) {
    return await this.contributionLedgerService.fetchContributionLedger(paginatedSearchDto);
  }
}
