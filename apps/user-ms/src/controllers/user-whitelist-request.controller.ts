import { Controller, Get, Post, Body, Patch, Param, Request, Query, UseInterceptors } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserWhitelistRequestService } from '../services/user-whitelist-request.service';
import {
  CreateWhitelistRequestDto,
  UpdateWhitelistRequestDto,
  WhitelistRequestDataDto,
} from '@app/shared/user-service/dtos/cobra-whitelist-request.dto';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import {
  BaseResponseListWithContentNoPagination,
  BaseResponseWithNoCountInfo,
} from '@app/shared/dto/response/base-response-with-content';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { Audit } from '@app/shared/decorators/audit.decorator';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';

@ApiBearerAuth()
@UseInterceptors(AuditInterceptor)
@ApiTags('User Whitelist Requests')
@Controller('whitelist-request')
export class UserWhitelistRequestController {
  constructor(protected readonly whitelistRequestService: UserWhitelistRequestService) {}

  @AllowedUserTypes(UserTypeEnum.MDA, UserTypeEnum.PFA)
  @RequirePrivileges(CobraPrivileges.WHITELIST_REQUEST_CREATE)
  @Audit(AuditEventTypeEnum.CREATE_USER_WHITELIST_REQUEST)
  @ApiOperation({ summary: 'Create a whitelist request' })
  @ApiResponse({
    status: 201,
    description: 'Whitelist request created successfully',
    type: BaseResponseListWithContentNoPagination<WhitelistRequestDataDto>,
  })
  @Post()
  async createWhitelistRequest(
    @Body() dto: CreateWhitelistRequestDto,
    @Request() req: ICustomRequest
  ): Promise<BaseResponseListWithContentNoPagination<WhitelistRequestDataDto>> {
    return await this.whitelistRequestService.createWhitelistRequest(dto, req);
  }

  @RequirePrivileges(CobraPrivileges.WHITELIST_REQUEST_PAGE)
  @ApiOperation({ summary: 'Get all whitelist requests' })
  @ApiResponse({
    status: 200,
    description: 'List of whitelist requests',
    type: BaseResponseWithNoCountInfo<WhitelistRequestDataDto>,
  })
  @Post('get-whitelist-requests')
  async getAllWhitelistRequests(
    @Body() searchParam: PaginatedSearchDto
  ): Promise<BaseResponseWithNoCountInfo<WhitelistRequestDataDto>> {
    return await this.whitelistRequestService.getAllWhitelistRequests(searchParam);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.WHITELIST_REQUEST_ADMIN)
  @Audit(AuditEventTypeEnum.UPDATE_USER_WHITELIST_REQUEST)
  @ApiOperation({ summary: 'Approve or reject a whitelist request' })
  @ApiResponse({
    status: 200,
    description: 'Whitelist request updated successfully',
    type: BaseResponseListWithContentNoPagination<WhitelistRequestDataDto>,
  })
  @Patch(':id/status')
  async updateWhitelistStatus(
    @Param('id') requestId: number,
    @Body() dto: UpdateWhitelistRequestDto,
    @Request() req: ICustomRequest
  ): Promise<BaseResponseListWithContentNoPagination<WhitelistRequestDataDto>> {
    return await this.whitelistRequestService.updateStatus({ ...dto, requestId, approverEmail: req.user.email });
  }

  @ApiOperation({ summary: 'Get whitelist request document' })
  @ApiResponse({
    status: 200,
    description: 'Whitelist request document',
  })
  @Get(':id/document')
  async getDocument(@Param('id') requestId: number) {
    return await this.whitelistRequestService.getDocument(requestId);
  }
}
