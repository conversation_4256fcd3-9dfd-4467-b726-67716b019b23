/* eslint-disable */
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { ErrorResponse, SuccessResponse } from '@app/shared/utils/response';
import {
  AgreeToTermsDto,
  CreateCobraUserDto,
  CreateUserWithoutPasswordDto,
  EditCobraUserDto,
  SaveSignatureDto,
  UpdateUserStatusDto,
  UploadProfilePictureDto,
} from '@app/shared/user-service/dtos/cobra-user.dto';
import { UserMsService } from '../services/user-ms.service';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { Audit } from '@app/shared/decorators/audit.decorator';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { CreateRoleDto, EditRoleDto, GetRoleDetail, RoleStatusDto } from '@app/shared/user-service/dtos/cobra-role.dto';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { UserRolePrivilegeService } from '../services/user-role-privilege.service';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { UserMdaPfaService } from '../services/user-mda-pfa.service';
import {
  BaseResponseListWithContentNoPagination,
  BaseResponseWithContentNoPagination,
} from '@app/shared/dto/response/base-response-with-content';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { VerifyOtpDto } from '@app/shared/dto/notification/verify-otp.dto';
import { Public } from '@app/shared/decorators/public.decorator';
import { ResponseCodeEnum } from '@app/shared/enums';
import { AppendUserFilters } from '@app/shared/interceptors/user-filters.interceptor';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { ReassignmentRolesDto, ReassignmentUsersDto } from '@app/shared/dto/enrollment/reassignment.dto';
import {
  CreateMdaDto,
  CreatePfaDto,
  CreatePfcDto,
  UpdateMdaDto,
  UpdatePfaDto,
  UpdatePfcDto,
} from '@app/shared/user-service/dtos/cobra-mda-pfa-pfc.dto';
import { Reset2FaSecret } from '@app/shared/dto/response/2fa-secret.dto';
import { LoginAccessDto } from '@app/shared/dto/login-access.dto';

@ApiBearerAuth()
@ApiTags('User Service Controller')
@Controller('')
@UseInterceptors(AuditInterceptor)
export class UserMsController {
  constructor(
    private readonly userService: UserMsService,
    private readonly userRolePrivilegeService: UserRolePrivilegeService,
    private readonly userMdaPfaService: UserMdaPfaService
  ) {}

  @Audit(AuditEventTypeEnum.CREATE_USER_ATTEMPT)
  @AllowedUserTypes(UserTypeEnum.PENCOM, UserTypeEnum.MDA, UserTypeEnum.PFA)
  @RequirePrivileges(CobraPrivileges.USER_MANAGEMENT_CREATE)
  @Post('create-user')
  @ApiOperation({
    summary: 'Create a new user',
    description: 'Creates a new user account in the system',
  })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    schema: {
      example: {
        success: true,
        statusCode: 201,
        content: {
          rsaPin: 'RSA123456',
          staffId: 'STAFF001',
          firstname: 'John',
          surname: 'Doe',
        },
        description: 'User created successfully',
      },
    },
  })
  async createUser(@Body() createUserDto: CreateCobraUserDto, @Res() res: Response, @Req() req: ICustomRequest) {
    let allowAdminRole = req.user.userType == UserTypeEnum.PENCOM;

    const outcome = await this.userService.createUser(createUserDto, allowAdminRole);

    return new SuccessResponse(outcome, 'User created successfully').send(res, HttpStatus.CREATED);
  }

  @Post('create-user-without-password')
  @ApiOperation({
    summary: 'Create a new user',
    description: 'Creates a new user account in the system',
  })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    schema: {
      example: {
        success: true,
        statusCode: 201,
        content: {
          rsaPin: 'RSA123456',
          staffId: 'STAFF001',
          firstname: 'John',
          surname: 'Doe',
        },
        description: 'User created successfully',
      },
    },
  })
  @Audit(AuditEventTypeEnum.CREATE_USER_WITHOUT_PASSWORD_ATTEMPT)
  async generatePassAndCreateUser(@Body() createUserDto: CreateUserWithoutPasswordDto, @Req() req: ICustomRequest) {
    let allowAdmin = true;

    if (UserTypeEnum.PENCOM !== req.user.userType) {
      if (req.user.userType !== createUserDto.userType) {
        const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
        response.setDescription(
          `Your user type ${req.user.userType} is not allowed to create a user with user type ${createUserDto.userType} `
        );
        return response;
      }
      allowAdmin = false;
      if (UserTypeEnum.PFA == createUserDto.userType) {
        createUserDto.pfaCode = req.user.pfaCode;
        createUserDto.mdaCode = '';
      } else if (UserTypeEnum.MDA == createUserDto.userType) {
        createUserDto.mdaCode = req.user.mdaCode;
        createUserDto.pfaCode = '';
      }
    }

    return await this.userService.generatePassAndCreateUser(createUserDto, req.user?.email, allowAdmin);
  }

  @Get('email/:email')
  @ApiOperation({
    summary: 'Find a user by email',
    description: 'Find a user account by email address',
  })
  @ApiResponse({
    status: 200,
    description: 'User found',
    schema: {
      example: {
        success: true,
        statusCode: 200,
        content: {
          rsaPin: 'RSA123456',
          staffId: 'STAFF001',
          firstname: 'John',
          surname: 'Doe',
        },
        description: 'User found successfully',
      },
    },
  })
  @Audit(AuditEventTypeEnum.GET_USER_BY_EMAIL_ATTEMPT)
  async getUserByEmail(
    @Param('email') email: string,
    @Query('userType') userType: UserTypeEnum,
    @Res() res: Response,
    @Req() req: ICustomRequest
  ) {
    const outcome = await this.userService.findUserByEmail({
      emailAddress: email,
      userType,
    });
    return outcome
      ? new SuccessResponse(outcome, 'User found successfully').send(res)
      : new ErrorResponse(outcome, 'User not found').send(res);
  }

  @Post('upload-profile-picture')
  @ApiOperation({
    summary: 'Upload user profile picture',
    description: "Upload and save a user's profile picture as base64 string",
  })
  @ApiResponse({
    status: 200,
    description: 'Profile picture uploaded successfully',
    schema: {
      example: {
        success: true,
        statusCode: 200,
        content: {
          avatarUrl: 'https://example.com/avatars/user123.jpg',
        },
        description: 'Profile picture uploaded successfully',
      },
    },
  })
  @Audit(AuditEventTypeEnum.UPLOAD_PROFILE_PICTURE_ATTEMPT)
  async uploadProfilePicture(
    @Body() uploadDto: UploadProfilePictureDto,
    @Res() res: Response,
    @Req() req: ICustomRequest
  ) {
    const outcome = await this.userService.updateUser(uploadDto.emailAddress, uploadDto);
    return new SuccessResponse(outcome, 'Profile picture uploaded successfully').send(res);
  }

  @Post('save-signature')
  @ApiOperation({
    summary: 'Save user signature',
    description: "Upload and save a user's signature as base64 string",
  })
  @ApiResponse({
    status: 200,
    description: 'Signature saved successfully',
    schema: {
      example: {
        success: true,
        statusCode: 200,
        content: {
          signatureUrl: 'https://example.com/signatures/user123.png',
        },
        description: 'Signature saved successfully',
      },
    },
  })
  @Audit(AuditEventTypeEnum.SAVE_SIGNATURE_ATTEMPT)
  async saveSignature(@Body() saveSignatureDto: SaveSignatureDto, @Res() res: Response, @Req() req: ICustomRequest) {
    const outcome = await this.userService.updateUser(saveSignatureDto.emailAddress, saveSignatureDto);

    return new SuccessResponse(outcome, 'Signature saved successfully').send(res);
  }

  @Get('get-privileges')
  async getPrivileges(@Query('userType') userType: string) {
    return await this.userService.getPrivileges(userType);
  }

  @Post('get-role')
  async getRole(@Body() filter: PaginatedSearchDto) {
    return await this.userRolePrivilegeService.fetchPaginatedRoles(filter);
  }

  @Post('get-role-detail')
  async getRoleByName(@Body() getRoleDetail: GetRoleDetail) {
    return await this.userRolePrivilegeService.getRoleByName(getRoleDetail);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.ROLE_MANAGEMENT_CREATE)
  @Audit(AuditEventTypeEnum.CREATE_ROLE_ATTEMPT)
  @ApiOperation({
    summary: 'Create a role',
    description: 'Creates a new role in the system',
    requestBody: {
      content: {
        'application/json': {
          schema: {
            example: {
              role: 'Admin',
              description: 'Admin role',
              privileges: ['PRIVILEGE_1', 'PRIVILEGE_2'],
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Role created successfully',
    type: BaseResponseDto,
  })
  @Post('create-role')
  async createRole(@Body() createRoleDto: CreateRoleDto, @Req() req: ICustomRequest) {
    return await this.userService.createRole(createRoleDto, req.user.email);
  }

  @ApiOperation({ summary: 'Get user by MDA or PFA' })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: BaseResponseListWithContentNoPagination,
  })
  @Get('upgrade/:userEmail')
  async getUserByMdaOrPfa(@Param('userEmail') userEmail: string, @Req() req: ICustomRequest) {
    return await this.userService.getUserByMdaOrPfa(userEmail, req);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.ROLE_MANAGEMENT_EDIT)
  @Audit(AuditEventTypeEnum.EDIT_ROLE_ATTEMPT)
  @ApiResponse({
    status: 200,
    description: 'Role edited successfully',
    type: BaseResponseDto,
  })
  @Patch('edit-role')
  async editRole(@Body() editRoleDto: EditRoleDto) {
    return await this.userService.editRole(editRoleDto);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.ROLE_MANAGEMENT_DEACTIVATE)
  @Audit(AuditEventTypeEnum.DEACTIVATE_ROLE_ATTEMPT)
  @ApiOperation({
    summary: 'Deactivate a role',
    description: 'Deactivates a role based on the provided role name',
    requestBody: {
      content: {
        'application/json': {
          schema: {
            example: { roleName: 'Admin' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Role deactivated successfully',

    schema: {
      example: {
        success: true,
        statusCode: 200,
        description: 'Role deactivated successfully',
      },
    },
  })
  @Patch('deactivate-role')
  async deactivateRole(@Body() deactivateRoleDto: RoleStatusDto, @Req() req: ICustomRequest) {
    return await this.userService.deactivateRole(deactivateRoleDto);
  }

  @RequirePrivileges()
  @Audit(AuditEventTypeEnum.UPDATE_USER_STATUS)
  @ApiResponse({
    status: 200,
    description: 'Role activated successfully',
    type: BaseResponseDto,
  })
  @Patch('update-user-status')
  async updateUserStatus(@Body() updateUserStatusDto: UpdateUserStatusDto, @Req() req: ICustomRequest) {
    return await this.userService.updateUserStatus(updateUserStatusDto, req);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM, UserTypeEnum.MDA, UserTypeEnum.PFA)
  @RequirePrivileges(
    CobraPrivileges.PENCOM_USER_MANAGEMENT,
    CobraPrivileges.MDA_USER_MANAGEMENT,
    CobraPrivileges.PFA_USER_MANAGEMENT
  )
  @Post('get-users')
  async getUsers(@Body() query: PaginatedSearchDto, @AppendUserFilters(['userType']) filters: any) {
    return await this.userService.fetchUsers(query);
  }

  @ApiOperation({ summary: 'Get users for reassignment' })
  @ApiResponse({
    status: 200,
    description: 'List of users',
    type: BaseResponseListWithContentNoPagination,
  })
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.REASSIGN_TASK)
  @Post('/reassignment')
  async getUsersWithoutPagination(@Body() reassignmentDto: ReassignmentUsersDto) {
    return await this.userService.fetchUserForReassignment(reassignmentDto);
  }

  @ApiOperation({ summary: 'Get reassignment roles' })
  @ApiResponse({
    status: 200,
    description: 'List of reassignment roles',
    type: BaseResponseWithContentNoPagination,
  })
  @Post('roles/reassignment')
  async getReassignmentRoles(@Body() reassignmentDto: ReassignmentRolesDto) {
    return await this.userService.getReassignmentRoles(reassignmentDto);
  }

  @Public()
  @ApiOperation({ summary: 'Validates the generated OTP' })
  @ApiBody({
    description: 'Payload to verify OTP',
    type: VerifyOtpDto,
    required: true,
  })
  @ApiOkResponse({
    description: 'OTP verification successful',
    type: BaseResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid OTP or verification failure',
    type: BaseResponseDto,
  })
  @Post('/verify-otp')
  verifyOtp(@Body() verifyOtpDto: VerifyOtpDto): Promise<BaseResponseDto> {
    return this.userService.verifyOtp(verifyOtpDto);
  }

  @Post('get-mdas')
  async getMdas(@Body() filter: PaginatedSearchDto) {
    return await this.userService.searchMdas(filter);
  }

  @Get('get-all-organisations')
  async getAllOrganisations() {
    return await this.userService.getAllOrganisations();
  }

  @Post('get-pfas')
  async getPfas(@Body() filter: PaginatedSearchDto) {
    return await this.userMdaPfaService.searchPfas(filter);
  }

  @Post('get-pfcs')
  async getPfcs(@Body() filter: PaginatedSearchDto) {
    return await this.userMdaPfaService.searchPfcs(filter);
  }

  @ApiOperation({ summary: 'Get roles based on user type' })
  @ApiResponse({
    status: 200,
    description: 'List of roles',
    type: BaseResponseWithContentNoPagination,
  })
  @ApiQuery({
    name: 'userType',
    required: true,
    enum: UserTypeEnum,
    description: 'Type of user (e.g., PENCOM, MDA, PFA)',
  })
  @Get('get-user-roles')
  async getUserRole(@Query('userType') userType: UserTypeEnum, @Req() req: ICustomRequest) {
    return await this.userService.getUserRoles(userType, req);
  }

  @Audit(AuditEventTypeEnum.EDIT_USER_ATTEMPT, (req) => {
    return {
      extra: {
        emailAddress: req.body.emailAddress,
        newEmailAddress: req.body.newEmailAddress,
      },
    };
  })
  @RequirePrivileges(CobraPrivileges.USER_MANAGEMENT_EDIT)
  @ApiOperation({ summary: 'Edit a user' })
  @Patch('edit-user')
  async editUser(@Body() editUserDto: EditCobraUserDto) {
    return await this.userService.editUser(editUserDto);
  }

  @RequirePrivileges(CobraPrivileges.RETRIEVE_PFA)
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Post('retrieve-pfas')
  async retrievePfas(@Body() filter: PaginatedSearchDto) {
    return await this.userMdaPfaService.retrievePfas(filter);
  }

  @Public()
  @Post('2fa/setup')
  async generate(@Body() loginAccessDto: LoginAccessDto) {
    return this.userService.generate2FASecret(loginAccessDto);
  }

  @Audit(AuditEventTypeEnum.RESET_2FA_SECRET_ATTEMPT, (req) => {
    return {
      extra: {
        email: req.body.email,
      },
    };
  })
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.RESET_2FA_SECRET)
  @Patch('2fa/reset')
  async reset(@Body() reset2FaSecretDto: Reset2FaSecret): Promise<BaseResponseDto> {
    return this.userService.reset2FASecret(reset2FaSecretDto.email);
  }

  @Audit(AuditEventTypeEnum.CREATE_PFA, (req) => {
    return {
      extra: {
        pfaCode: req.body.pfaCode,
        pfaName: req.body.pfaName,
      },
    };
  })
  @RequirePrivileges(CobraPrivileges.CREATE_PFA)
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Post('create-pfa')
  async createPfa(@Body() createPfaDto: CreatePfaDto) {
    return await this.userMdaPfaService.createPfa(createPfaDto);
  }

  @Audit(AuditEventTypeEnum.DELETE_PFA, (req) => {
    return {
      extra: {
        pfaCode: req.params.pfaCode,
      },
    };
  })
  @RequirePrivileges(CobraPrivileges.DELETE_PFA)
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Delete('delete-pfa/:pfaCode')
  async deletePfa(@Param('pfaCode') pfaCode: string) {
    return await this.userMdaPfaService.deletePfa(pfaCode);
  }

  @Audit(AuditEventTypeEnum.EDIT_PFA, (req) => {
    return {
      extra: {
        pfaCode: req.body.pfaCode,
      },
    };
  })
  @RequirePrivileges(CobraPrivileges.EDIT_PFA)
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Patch('edit-pfa')
  async editPfa(@Body() editPfaDto: UpdatePfaDto) {
    return await this.userMdaPfaService.editPfa(editPfaDto);
  }

  @RequirePrivileges(CobraPrivileges.RETRIEVE_PFC)
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Post('retrieve-pfcs')
  async retrievePfcs(@Body() filter: PaginatedSearchDto) {
    return await this.userMdaPfaService.retrievePfcs(filter);
  }

  @Audit(AuditEventTypeEnum.CREATE_PFC, (req) => {
    return {
      extra: {
        pfcCode: req.body.pfcCode,
        pfcName: req.body.pfcName,
      },
    };
  })
  @RequirePrivileges(CobraPrivileges.CREATE_PFC)
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Post('create-pfc')
  async createPfc(@Body() createPfcDto: CreatePfcDto, @Req() req: ICustomRequest) {
    return await this.userMdaPfaService.createPfc(createPfcDto);
  }

  @Audit(AuditEventTypeEnum.EDIT_PFC, (req) => {
    return {
      extra: {
        pfcCode: req.body.pfcCode,
      },
    };
  })
  @RequirePrivileges(CobraPrivileges.EDIT_PFC)
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Patch('edit-pfc')
  async editPfc(@Body() editPfcDto: UpdatePfcDto) {
    return await this.userMdaPfaService.editPfc(editPfcDto);
  }

  @Audit(AuditEventTypeEnum.DELETE_PFC, (req) => {
    return {
      extra: {
        pfcCode: req.params.pfcCode,
      },
    };
  })
  @RequirePrivileges(CobraPrivileges.DELETE_PFC)
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Delete('delete-pfc/:pfcCode')
  async deletePfc(@Param('pfcCode') pfcCode: string) {
    return await this.userMdaPfaService.deletePfc(pfcCode);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.RETRIEVE_MDA)
  @Post('retrieve-mdas')
  async retrieveMdas(@Body() filter: PaginatedSearchDto) {
    return await this.userMdaPfaService.retrieveMdas(filter);
  }

  @Audit(AuditEventTypeEnum.CREATE_MDA, (req) => {
    return {
      extra: {
        employerId: req.body.employerId,
        employerName: req.body.employerName,
      },
    };
  })
  @RequirePrivileges(CobraPrivileges.CREATE_MDA)
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Post('create-mda')
  async createMda(@Body() createMdaDto: CreateMdaDto) {
    return await this.userMdaPfaService.createMda(createMdaDto);
  }

  @Audit(AuditEventTypeEnum.EDIT_MDA, (req) => {
    return {
      extra: {
        employerId: req.body.employerId,
      },
    };
  })
  @RequirePrivileges(CobraPrivileges.EDIT_MDA)
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Patch('edit-mda')
  async editMda(@Body() editMdaDto: UpdateMdaDto) {
    return await this.userMdaPfaService.editMda(editMdaDto);
  }

  @Audit(AuditEventTypeEnum.DELETE_MDA, (req) => {
    return {
      extra: {
        mdaCode: req.params.mdaCode,
      },
    };
  })
  @RequirePrivileges(CobraPrivileges.DELETE_MDA)
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Delete('delete-mda/:mdaCode')
  async deleteMda(@Param('mdaCode') mdaCode: string) {
    return await this.userMdaPfaService.deleteMda(mdaCode);
  }

  @Post('agree-to-terms')
  async agreeToTerms(@Body() agreeToTermsDto: AgreeToTermsDto) {
    return await this.userService.agreeToTerms(agreeToTermsDto.email);
  }
}
