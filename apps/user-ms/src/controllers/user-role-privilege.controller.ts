import { Controller, Get, Post, Body, Patch, Param, Request, Query, UseInterceptors } from '@nestjs/common';
import { UserRoleChangeRequestService } from '../services/user-role-change-request.service';
import {
  CreateRoleChangeRequestDto,
  RoleChangeRequestDataDto,
  UpdateUserRoleChangeRequestDto,
  RoleChangeRequestDetailedDto,
} from '@app/shared/user-service/dtos/cobra-role-change-request.dto';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import {
  BaseResponseListWithContentNoPagination,
  BaseResponseWithContentNoPagination,
  BaseResponseWithNoCountInfo,
} from '@app/shared/dto/response/base-response-with-content';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { Audit } from '@app/shared/decorators/audit.decorator';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';

@ApiBearerAuth()
@UseInterceptors(AuditInterceptor)
@ApiTags('User Role Change Requests')
@Controller('role-change-request')
export class UserRoleChangeRequestController {
  constructor(protected readonly userRoleChangeRequestService: UserRoleChangeRequestService) {}

  @AllowedUserTypes(UserTypeEnum.MDA, UserTypeEnum.PFA)
  @RequirePrivileges(CobraPrivileges.UPGRADE_REQUEST_CREATE)
  @Audit(AuditEventTypeEnum.CREATE_ROLE_CHANGE_REQUEST)
  @ApiOperation({ summary: 'Create a role change request' })
  @ApiResponse({
    status: 201,
    description: 'Role change request created successfully',
    type: BaseResponseListWithContentNoPagination<RoleChangeRequestDataDto>,
  })
  @Post()
  async createRoleChangeRequest(
    @Body() dto: CreateRoleChangeRequestDto,
    @Request() req: ICustomRequest
  ): Promise<BaseResponseListWithContentNoPagination<RoleChangeRequestDataDto>> {
    return await this.userRoleChangeRequestService.createRoleChangeRequest(dto, req);
  }

  @RequirePrivileges(CobraPrivileges.UPGRADE_REQUEST_PAGE)
  @ApiOperation({ summary: 'Get all role change requests' })
  @ApiResponse({
    status: 200,
    description: 'List of role change requests',
    type: BaseResponseWithNoCountInfo<RoleChangeRequestDataDto>,
  })
  @Post('get-role-change-requests')
  async getAllRoleChangeRequests(
    @Body() searchParam: PaginatedSearchDto,
    @Request() req: ICustomRequest
  ): Promise<BaseResponseWithNoCountInfo<RoleChangeRequestDataDto>> {
    return await this.userRoleChangeRequestService.getAllRoleChangeRequests(searchParam, req);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.UPGRADE_REQUEST_ADMIN)
  @Audit(AuditEventTypeEnum.UPDATE_ROLE_CHANGE_REQUEST)
  @ApiOperation({ summary: 'Approve or reject a role change request' })
  @ApiResponse({
    status: 200,
    description: 'Role change request updated successfully',
    type: BaseResponseListWithContentNoPagination<RoleChangeRequestDataDto>,
  })
  @Patch(':id/status')
  async updateRoleChangeStatus(
    @Param('id') requestId: number,
    @Body() dto: UpdateUserRoleChangeRequestDto,
    @Request() req: ICustomRequest
  ): Promise<BaseResponseListWithContentNoPagination<RoleChangeRequestDataDto>> {
    return this.userRoleChangeRequestService.updateRoleChangeStatus({ ...dto, requestId }, req.user.email);
  }

  @ApiOperation({ summary: 'Get role change request document' })
  @ApiResponse({
    status: 200,
    description: 'Role change request document',
  })
  @Get(':id/document')
  async getDocument(
    @Param('id') requestId: number
  ): Promise<BaseResponseListWithContentNoPagination<{ name: string; type: string; data: string }[]>> {
    return await this.userRoleChangeRequestService.getDocument(requestId);
  }

  @ApiOperation({ summary: 'Get detailed role change request information' })
  @ApiResponse({
    status: 200,
    description: 'Detailed role change request information',
    type: RoleChangeRequestDetailedDto,
  })
  @Get('/:id/detail')
  async getRoleChangeRequestDetail(
    @Param('id') requestId: number,
    @Request() req: ICustomRequest
  ): Promise<BaseResponseWithContentNoPagination<RoleChangeRequestDetailedDto>> {
    return await this.userRoleChangeRequestService.getRoleChangeRequestDetail(requestId);
  }
}
