/* eslint-disable */
import { NestFactory } from '@nestjs/core';
import { EnrollmentEngineModule } from './enrollment-engine.module';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { ENROLLMENT_QUEUE } from '@app/shared/constants';

async function bootstrap() {
  const app = await NestFactory.create(EnrollmentEngineModule);

  app.setGlobalPrefix('enrollment-engine');
  const configService = app.get(ConfigService);
  const port = configService.get('ENROLLMENT_ENGINE_SERVICE_PORT');
  const rabbitMqUrl = configService.get('RABBIT_MQ_URL');
  console.log(`rabbitMqUrl: ${rabbitMqUrl}`);
  app.enableCors();
  await app.listen(port);

  const microservice = await NestFactory.createMicroservice<MicroserviceOptions>(EnrollmentEngineModule, {
    transport: Transport.RMQ,
    options: {
      urls: [rabbitMqUrl],
      queue: ENROLLMENT_QUEUE,
      queueOptions: { durable: true },
      prefetchCount: 5,
    },
  });

  await microservice.listen();
  console.log(`Enrollment engine started ${port}`);
}

bootstrap();
