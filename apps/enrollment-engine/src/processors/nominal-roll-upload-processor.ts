/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { INominalRollValidation } from '@app/shared/dto/enrollment/interfaces/nominal-roll-validation.interface';
import { ProcessedResultDto } from '@app/shared/dto/enrollment/mda-data-upload-result.dto';
import { NrDtoValidator } from '../validators/nominal-roll/nr-dto-validator.service';
import { IEmployeeDataDto } from '@app/shared/dto/enrollment/interfaces/nominal-roll-employee.interface';
import { NrEcrsValidator } from '../validators/nominal-roll/nr-ecrs-validator.service';
import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';
import { NrCurrentUserValidator } from '../validators/nominal-roll/nr-current-user-validator';
import { NominalRollRepository } from '@app/shared/enrollment-service/repository/nominal-roll.repository';
import { NominalRollBand } from '@app/shared/enrollment-service/entities/nominal-roll-band.entity';
import { NominalRollBandDetails } from '@app/shared/enrollment-service/entities/nominal-roll-band-details.entity';
import { PinoLogger } from 'nestjs-pino';
import { NrMdaValidator } from '../validators/nominal-roll/nr-mda-validator';
import { NrPfaValidator } from '../validators/nominal-roll/nr-pfa-validator';
import { NrBandValidator } from '../validators/nominal-roll/nr-band-validator';
import { NrExistingUserValidator } from '../validators/nominal-roll/nr-existing-user-validator';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { SettingsEnumKey } from '@app/shared/enums';
import { NominalRollHistory } from '@app/shared/enrollment-service/entities/nominal-roll-history.entity';
import { NominalRollBandHistory } from '@app/shared/enrollment-service/entities/nominal-roll-band-history.entity';
import { NominalRollBandDetailsHistory } from '@app/shared/enrollment-service/entities/nominal-roll-band-details-history.entity';
import { NrExistingNominalRollValidator } from '../validators/nominal-roll/nr-existing-nominal-roll-validator.service';

@Injectable()
export class NominalRollUploadProcessor {
  private readonly validators: INominalRollValidation<any>[];

  constructor(
    nominalRollDtoValidator: NrDtoValidator<IEmployeeDataDto>,
    nominalRollEcrsValidator: NrEcrsValidator<IEmployeeDataDto>,
    mdaValidator: NrMdaValidator<IEmployeeDataDto>,
    pfaValidator: NrPfaValidator<IEmployeeDataDto>,
    nrCurrentUserValidator: NrCurrentUserValidator<IEmployeeDataDto>,
    bandValidator: NrBandValidator<IEmployeeDataDto>,
    nrExistingUserValidator: NrExistingUserValidator<IEmployeeDataDto>,
    nrExistingNominalRollValidator: NrExistingNominalRollValidator<IEmployeeDataDto>,
    private readonly nominalRollRepository: NominalRollRepository,
    private readonly settingMsLibService: SettingMsLibService,
    private readonly logger: PinoLogger
  ) {
    this.validators = [
      nrExistingUserValidator,
      nrCurrentUserValidator,
      nominalRollDtoValidator,
      nominalRollEcrsValidator,
      mdaValidator,
      pfaValidator,
      nrExistingNominalRollValidator,
      bandValidator,
    ];
  }

  /**
   * verify rsaPin against ECRS
   * verify each band against the salary structures, grade level and steps match static table
   * verify band doesn't exceed current year
   *
   */
  async processNominalRoll<T extends IEmployeeDataDto>(taskId: string, userEmail: string, data: Partial<T>) {
    const result = new ProcessedResultDto();
    result.id = data.rsaPin;
    const context: Record<string, any> = {};
    context.taskId = taskId;
    context.rsaPin = data.rsaPin;
    context.userEmail = userEmail;

    const existingNominalRoll = await this.nominalRollRepository.findOne({ rsaPin: data.rsaPin });
    const nominalRollUploadState = await this.settingMsLibService.getSettingList(
      SettingsEnumKey.NOMINAL_ROLL_UPLOAD_STATE
    );
    if (existingNominalRoll && !nominalRollUploadState.includes(existingNominalRoll.status)) {
      result.description = `Nominal roll already exists with status: ${existingNominalRoll.status}`;
      return result;
    }

    for (const validator of this.validators) {
      const error = await validator.validate(data, context);
      if (error) {
        result.description = error;
        return result;
      }
    }

    const entityManager = this.nominalRollRepository.getEntityManager();

    await entityManager.transaction(async (transactionalEntityManager) => {
      try {
        const ecrsResponse = context.ecrsResponse;
        let nominalRoll: NominalRoll;
        if (existingNominalRoll) {
          if (['CR_FINALISED'].includes(existingNominalRoll.status)) {
            const fullNr = await this.nominalRollRepository.findOneWithRelations({ rsaPin: data.rsaPin }, [
              'bands',
              'bands.nominalRollBandDetails',
              'createdBy',
            ]);
            const nominalRollHistory = this.mapRollToHistory(fullNr);
            await transactionalEntityManager.save(NominalRollHistory, nominalRollHistory);
          }
          // Update existing NominalRoll
          nominalRoll = transactionalEntityManager.merge(NominalRoll, existingNominalRoll, {
            rsaPin: data.rsaPin,
            firstName: ecrsResponse.firstName,
            surname: data.surname,
            otherName: ecrsResponse.middleName,
            dateOfBirth: new Date(ecrsResponse.dateOfBirth),
            gender: ecrsResponse.gender,
            mdaCode: context.mdaCode,
            mdaName: context.mdaName,
            pfaCode: context.pfaCode,
            pfaName: context.pfaName,
            dateOfFirstAppointment: data.dateOfFirstAppointment ? new Date(data.dateOfFirstAppointment) : null,
            dateOfExit: new Date(context.ippisDate),
            createdBy: typeof context.cobraUser === 'string' ? JSON.parse(context.cobraUser) : context.cobraUser,
          });
        } else {
          // Create fresh NominalRoll
          nominalRoll = transactionalEntityManager.create(NominalRoll, {
            rsaPin: data.rsaPin,
            firstName: ecrsResponse.firstName,
            surname: data.surname,
            otherName: ecrsResponse.middleName,
            dateOfBirth: new Date(ecrsResponse.dateOfBirth),
            gender: ecrsResponse.gender,
            mdaCode: context.mdaCode,
            mdaName: context.mdaName,
            pfaCode: context.pfaCode,
            pfaName: context.pfaName,
            dateOfFirstAppointment: data.dateOfFirstAppointment ? new Date(data.dateOfFirstAppointment) : null,
            dateOfExit: new Date(context.ippisDate),
            createdBy: typeof context.cobraUser === 'string' ? JSON.parse(context.cobraUser) : context.cobraUser,
          });
        }

        await transactionalEntityManager.save(nominalRoll);

        // Remove existing bands and their details to prevent duplicates
        if (existingNominalRoll) {
          await transactionalEntityManager.delete(NominalRollBand, { nominalRoll: { pk: nominalRoll.pk } });
        }

        // Add new Bands and Details
        for (const band of data.bandData) {
          const nominalRollBand = transactionalEntityManager.create(NominalRollBand, {
            year: band.year.toString(),
            nominalRoll: nominalRoll,
          });

          await transactionalEntityManager.save(nominalRollBand);

          const bandDetail = transactionalEntityManager.create(NominalRollBandDetails, {
            salaryStructure: band.salaryStructure,
            gradeLevel: band.gl.toString(),
            step: band.step.toString(),
            nominalRollBand: nominalRollBand,
          });

          await transactionalEntityManager.save(bandDetail);
        }

        result.description = 'Successfully Processed';
      } catch (error) {
        result.description = 'Error while attempting to process Nominal roll request, please try again';
        this.logger.error(
          `error occurred when saving Nominal roll for PIN ${data.rsaPin}: ${error instanceof Error ? error.stack : error}`
        );
      }
    });
    return result;
  }

  private mapRollToHistory(nominalRoll: NominalRoll): NominalRollHistory {
    const history = new NominalRollHistory({});
    history.rsaPin = nominalRoll.rsaPin;
    history.firstName = nominalRoll.firstName;
    history.surname = nominalRoll.surname;
    history.otherName = nominalRoll.otherName;
    history.amount = nominalRoll.amount;
    history.gender = nominalRoll.gender;
    history.dateOfBirth = nominalRoll.dateOfBirth;
    history.mdaName = nominalRoll.mdaName;
    history.mdaCode = nominalRoll.mdaCode;
    history.pfaCode = nominalRoll.pfaCode;
    history.pfaName = nominalRoll.pfaName;
    history.status = nominalRoll.status;
    history.batchId = nominalRoll.batchId;
    history.dateOfFirstAppointment = nominalRoll.dateOfFirstAppointment;
    history.dateOfExit = nominalRoll.dateOfExit;
    history.totalMonths = nominalRoll.totalMonths;
    history.totalEmolument = nominalRoll.totalEmolument;
    history.paidToDate = nominalRoll.paidToDate;
    history.totalInterest = nominalRoll.totalInterest;
    history.totalPensionPlusInterest = nominalRoll.totalPensionPlusInterest;
    history.totalPension = nominalRoll.totalPension;
    history.contributionRemarks = nominalRoll.contributionRemarks;
    history.paymentStatus = nominalRoll.paymentStatus;
    history.paymentComment = nominalRoll.paymentComment;
    history.createdBy = nominalRoll.createdBy;

    history.bands = nominalRoll.bands.map((band) => {
      const bandHistory = new NominalRollBandHistory({});
      bandHistory.year = band.year;
      bandHistory.contributionRemarks = band.contributionRemarks;
      bandHistory.totalEmolument = band.totalEmolument;
      bandHistory.monthsCovered = band.monthsCovered;
      bandHistory.nominalRoll = history;

      // Map band details
      bandHistory.nominalRollBandDetails = band.nominalRollBandDetails.map((detail) => {
        const bandDetailHistory = new NominalRollBandDetailsHistory({});
        bandDetailHistory.salaryStructure = detail.salaryStructure;
        bandDetailHistory.gradeLevel = detail.gradeLevel;
        bandDetailHistory.step = detail.step;
        bandDetailHistory.salaryAmount = detail.salaryAmount;
        bandDetailHistory.nominalRollBand = bandHistory;
        return bandDetailHistory;
      });

      return bandHistory;
    });

    return history;
  }
}
