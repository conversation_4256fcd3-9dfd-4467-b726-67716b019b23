/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { ProcessedResultDto } from '@app/shared/dto/enrollment/mda-data-upload-result.dto';
import { MdaEmployeeBiodataRepository } from '@app/shared/login-service/repositories/mda-employee-biodata.repository';
import { convertDateSlashToHyphen, GeneralUtils, stringToDate } from '@app/shared/utils';
import { PinoLogger } from 'nestjs-pino';
import { IAccruedRights } from '@app/shared/dto/enrollment/interfaces/accrued-rights-validation.interface';
import { AccruedRightsUploadDto } from '@app/shared/dto/enrollment/accrued-rights/accrued-rights-upload.dto';
import { ProfessorLess15AccruedRightsService } from '@app/shared/computation/accrued-rights/professor-less-15-accrued-rights.service';
import { Professor15AccruedRightsService } from '@app/shared/computation/accrued-rights/professor-15-accrued-rights.service';
import { OthersAccruedRightsService } from '@app/shared/computation/accrued-rights/others-accrued-rights.service';
import { CalculationResult } from '@app/shared/computation/accrued-rights/calculation.dto';
import { AccruedRightsResult } from '@app/shared/dto/enrollment/entities/accrued-rights-result.entity';
import { AccruedRightsResultRepository } from '@app/shared/dto/enrollment/repositories/accrued-rights-result.repository';

@Injectable()
export class AccruedRightsProcessor {
  private validators: IAccruedRights<any>[];

  constructor(
    private readonly mdaEmployeeBiodataRepository: MdaEmployeeBiodataRepository,
    private readonly utils: GeneralUtils,
    private readonly logger: PinoLogger,
    private readonly professorLess15AccruedRightsService: ProfessorLess15AccruedRightsService,
    private readonly professor15AccruedRightsService: Professor15AccruedRightsService,
    private readonly othersAccruedRightsService: OthersAccruedRightsService,
    private readonly accruedRightsResultRepository: AccruedRightsResultRepository
  ) {
    this.validators = [];
  }

  /**
   *
   *
   */
  async processBiodata<T extends AccruedRightsUploadDto>(
    accruedRightsDto: Partial<T>,
    taskId: string
  ): Promise<ProcessedResultDto> {
    const result = new ProcessedResultDto();
    result.id = accruedRightsDto.rsaPin;
    const context: Record<string, any> = {}; // Shared memory between validators
    context.taskId = taskId;
    console.log(`============ processBiodata : ${JSON.stringify(accruedRightsDto)} ============`);

    // for (const validator of this.validators) {
    //   const error = await validator.validate(mdaBiodataDto, context);
    //   if (error) {
    //     result.description = error;
    //     return result; // Stop processing on first error
    //   }
    // }
    let accruedRightsResult: CalculationResult;
    switch (accruedRightsDto.retireType) {
      case 'OTHERS': {
        accruedRightsResult = this.othersAccruedRightsService.calculateAccruedRights(
          convertDateSlashToHyphen(accruedRightsDto.dateOfBirth),
          convertDateSlashToHyphen(accruedRightsDto.dateOfFirstAppointment),
          accruedRightsDto.gender === 'M' ? 'M' : 'F',
          Number(accruedRightsDto.apaValue),
          convertDateSlashToHyphen(accruedRightsDto.dateOfRetirement),
          0.05
        );
        break;
      }
      case 'PROF15': {
        accruedRightsResult = this.professor15AccruedRightsService.calculateAccruedRights(
          convertDateSlashToHyphen(accruedRightsDto.dateOfBirth),
          convertDateSlashToHyphen(accruedRightsDto.dateOfFirstAppointment),
          accruedRightsDto.gender === 'M' ? 'M' : 'F',
          Number(accruedRightsDto.apaValue),
          convertDateSlashToHyphen(accruedRightsDto.dateOfRetirement),
          0.05
        );

        break;
      }
      case 'PROFLESS15': {
        accruedRightsResult = this.professorLess15AccruedRightsService.calculateAccruedRights(
          convertDateSlashToHyphen(accruedRightsDto.dateOfBirth),
          convertDateSlashToHyphen(accruedRightsDto.dateOfFirstAppointment),
          accruedRightsDto.gender === 'M' ? 'M' : 'F',
          Number(accruedRightsDto.apaValue),
          convertDateSlashToHyphen(accruedRightsDto.dateOfRetirement),
          0.05
        );

        break;
      }
      default: {
        result.description = `Unknown user type provided: ${accruedRightsDto.retireType}`;
      }
    }

    if (accruedRightsResult) {
      console.log(`========= accruedRightsResult: ${JSON.stringify(accruedRightsResult)}`);
      const accruedRightsResultEntity = new AccruedRightsResult({
        rsaPin: accruedRightsDto.rsaPin,
        name: accruedRightsDto.name,
        gender: accruedRightsDto.gender,
        retireType: accruedRightsDto.retireType,
        apaValue: accruedRightsDto.apaValue,
        dateOfBirth: stringToDate(accruedRightsDto.dateOfBirth),
        dateOfRetirement: stringToDate(accruedRightsDto.dateOfRetirement),
        dateOfFirstAppointment: stringToDate(accruedRightsDto.dateOfFirstAppointment),
        accruedBenefitsVd: accruedRightsResult.accruedBenefitsVd?.toString(),
        normalRetirementYears: accruedRightsResult.normalRetirementYears?.toString(),
        normalRetirementAccruedBenefit: accruedRightsResult.normalRetirementAccruedBenefit?.toString(),
        earlyRetirementYears: accruedRightsResult.earlyRetirementYears?.toString(),
        earlyRetirementAccruedBenefit: accruedRightsResult.earlyRetirementAccruedBenefit?.toString(),
        entryPoint: 'ENDPOINT',
      });

      try {
        console.log(`========= accruedRightsResultEntity : ${JSON.stringify(accruedRightsResultEntity)}`);
        await this.accruedRightsResultRepository.saveEntity(accruedRightsResultEntity);
        result.description = 'Successfully Processed';
      } catch (error) {
        result.description = 'Error while attempting to process request, please try again';
        this.logger.error(
          `error occurred when saving ACCRUED_RIGHTS_RESULT for PIN ${accruedRightsDto.rsaPin}: ${error instanceof Error ? error.stack : error}`
        );
      }
    } else {
      console.log('accruedRightsResult is not valid');
      result.description = 'Error while processing access rights';
    }
    return result;
  }
}
