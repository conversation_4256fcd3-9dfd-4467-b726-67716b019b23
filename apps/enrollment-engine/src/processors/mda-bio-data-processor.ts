/* eslint-disable */
import { IBiodataValidation } from '@app/shared/dto/enrollment/interfaces/bio-data-validation.interface';
import { RegisteredUserValidator } from '../validators/registered-user-validator';
import { DtoValidator } from '../validators/dto-validator';
import { EcrsValidator } from '../validators/ecrs-validator';
import { Injectable } from '@nestjs/common';
import { ProcessedResultDto } from '@app/shared/dto/enrollment/mda-data-upload-result.dto';
import { FieldValidator } from '../validators/field-validator';
import { MdaEmployeeBiodata } from '@app/shared/login-service/entities/employee-biodata.entity';
import { MdaEmployeeBiodataRepository } from '@app/shared/login-service/repositories/mda-employee-biodata.repository';
import { GeneralUtils, getDateOfDeath, getValidDate } from '@app/shared/utils';
import { MdaValidator } from '../validators/mda-validator';
import { PfaValidator } from '../validators/pfa-validator';
import { PinoLogger } from 'nestjs-pino';
import { CurrentUserValidator } from '../validators/current-user-validator';
import { IMdaBaseBioData } from '@app/shared/dto/enrollment/interfaces/mda-base-bio-data.interface';
import { MdaBiodataDto } from '@app/shared/dto/enrollment/mda-data-upload.dto';
import { MdaDeceasedDataUploadDto } from '@app/shared/dto/enrollment/mda-deceased-data-upload.dto';
import { EdorValidator } from '../validators/edor-validator.service';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { SLASH_DATE_FORMAT } from '@app/shared/constants';
import { SalaryStructureValidator } from '../validators/salary-structure-validator';

@Injectable()
export class MdaBioDataProcessor {
  private validators: IBiodataValidation<any>[];

  constructor(
    currentUserValidator: CurrentUserValidator<IMdaBaseBioData>,
    dtoValidation: DtoValidator<IMdaBaseBioData>,
    registeredUserValidation: RegisteredUserValidator<IMdaBaseBioData>,
    ecrsValidation: EcrsValidator<IMdaBaseBioData>,
    fieldValidator: FieldValidator<IMdaBaseBioData>,
    mdaValidator: MdaValidator<IMdaBaseBioData>,
    pfaValidator: PfaValidator<IMdaBaseBioData>,
    dodValidator: EdorValidator<IMdaBaseBioData>,
    salaryStructureValidator: SalaryStructureValidator<IMdaBaseBioData>,
    private readonly mdaEmployeeBiodataRepository: MdaEmployeeBiodataRepository,
    private readonly utils: GeneralUtils,
    private readonly logger: PinoLogger
  ) {
    this.validators = [
      currentUserValidator,
      dtoValidation,
      registeredUserValidation,
      ecrsValidation,
      fieldValidator,
      dodValidator,
      mdaValidator,
      pfaValidator,
      salaryStructureValidator,
    ];
  }

  /**
   * confirm pin has not been registered
   * call ECRS to validate pin
   * if ecrsResponse is empty return result is not found
   * retrieve retiree from TBL_MDA_UPLOAD TABLE
   * check if already paid table for user and  update user data
   * IF exists then update
   * if not exist, then create
   * for DECEASED {
        - DOD is mandatory
        - DOD > DOB and DTS and DFA
        - if DOD < 30 June 2004, -- “Registration process terminated. Please, contact Pension Transitional Arrangements Directorate (PTAD)”
        - if DOD < 30 June 2007 && EDOR > 30 June 2007, continue else ***
    }
   * ensure that DOFA > current date -- “DOFA must be less than current date”
   * ensure that DTS > 30 June 2004, -- “Transfer Date Not Accepted after 30 June 2004”
   * ensure that EDOR > DOB and DTS
   * if EDOR <  30 June 2004 -- Registration process terminated. Please, contact Pension Transitional Arrangements Directorate (PTAD)”.
   * validate employerJune2004, salaryStructureJune2004, glJune2004, stepJune2004 against respective table
   */
  async processBiodata<T extends MdaBiodataDto | MdaDeceasedDataUploadDto>(
    mdaBiodataDto: Partial<T>,
    taskId: string
  ): Promise<ProcessedResultDto> {
    const result = new ProcessedResultDto();
    result.id = mdaBiodataDto.rsaPin;

    const context: Record<string, any> = {}; // Shared memory between validators
    context.taskId = taskId;
    context.rsaPin = mdaBiodataDto.rsaPin;

    for (const validator of this.validators) {
      const error = await validator.validate(mdaBiodataDto, context);
      if (error) {
        result.description = error;
        return result; // Stop processing on first error
      }
    }

    const createdBy = new CobraUser({
      pk: JSON.parse(context.cobraUser).pk,
    });

    const employeeInfo = new MdaEmployeeBiodata({
      rsaPin: mdaBiodataDto.rsaPin,
      firstName: mdaBiodataDto.firstName,
      surname: mdaBiodataDto.surname,
      staffId: `${mdaBiodataDto.staffId}`,
      dts: getValidDate(mdaBiodataDto.dts, SLASH_DATE_FORMAT),
      edor: getValidDate(mdaBiodataDto.edor, SLASH_DATE_FORMAT),
      dofa: getValidDate(mdaBiodataDto.dofa, SLASH_DATE_FORMAT),
      employerJune2004: mdaBiodataDto.employerJune2004,
      salaryStructureJune2004: mdaBiodataDto.salaryStructureJune2004,
      gradeLevelJune2004: `${mdaBiodataDto.gradeLevelJune2004}`,
      stepJune2004: `${mdaBiodataDto.stepJune2004}`,
      dateOfDeath: getDateOfDeath(mdaBiodataDto, taskId),
      gender: context.gender,
      dateOfBirth: getValidDate(context.dateOfBirth, SLASH_DATE_FORMAT),
      mdaCode: context.mdaCode,
      mdaName: context.mdaName,
      pfaCode: context.pfaCode,
      pfaName: context.pfaName,
      retireeUserType: context.retireeUserType,
      createdBy,
    });

    try {
      await this.mdaEmployeeBiodataRepository.upsertEntity(employeeInfo, ['rsaPin']);
      result.description = 'Successfully Uploaded';
    } catch (error) {
      result.description = 'Error while attempting to process request, please try again';
      this.logger.error(
        `error occurred when saving TBL_MDA_EMPLOYEE_BIODATA for PIN ${mdaBiodataDto.rsaPin}: 
        \n ${JSON.stringify(employeeInfo)}
        \n ${error instanceof Error ? error.stack : error}`
      );
    }

    return result;
  }
}
