/* eslint-disable */
import { Inject, Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { ResponseCodeEnum } from '@app/shared/enums';
import { EnrollmentServiceClientConstant } from '@app/shared/constants/enrollment-service-client.constant';
import { NominalRollRepository } from '@app/shared/enrollment-service/repository/nominal-roll.repository';
import { NominalRollStatusEnum } from '@app/shared/enums/nominal-roll-status.enum';
import { ContributionsComputationService } from '@app/shared/computation/contribution/contributions-computation.service';

@Injectable()
export class RecurringNominalRollContributionCalculator {
  constructor(
    private readonly logger: PinoLogger,
    private readonly nominalRollRepository: NominalRollRepository,
    private readonly computationService: ContributionsComputationService,
    @Inject('ENROLLMENT_SERVICE_CLIENT')
    private readonly enrollmentServiceClient: ClientProxy
  ) {}

  async calculateNominalRoll(rsaPin: string): Promise<void> {
    const nominalRoll = await this.nominalRollRepository.findOneWithRelations({ rsaPin: rsaPin }, [
      'bands',
      'bands.nominalRollBandDetails',
    ]);
    this.logger.error(`nominalRoll, ${JSON.stringify(nominalRoll)}`);
    if (!nominalRoll) {
      this.logger.error(`Queued RSA Pin ${rsaPin} not found in nominalRoll: skipping processing `);
      return;
    }

    if (nominalRoll.status !== NominalRollStatusEnum.AWAITING_COMPUTATION) {
      this.logger.error(
        `Queued RSA Pin ${rsaPin} with status ${nominalRoll.status} is not allowed to be processed: skipping processing `
      );
      return;
    }

    const contributionsResponse = await this.computationService.calculateContributions(nominalRoll);
    if (contributionsResponse.code !== ResponseCodeEnum.SUCCESS) {
      this.logger.error(
        `Unable to calculate exit contributions for rsaPin: ${rsaPin}:: ${contributionsResponse.description}`
      );
      return;
    }

    const contributions = contributionsResponse.content;
    try {
      const payload: Record<string, string> = {
        rsaPin: rsaPin,
        action: 'COMPUTATION_ANALYSIS',
        email: 'SYSTEM_COMPUTATION',
        contribution: `${contributions}`,
      };
      await this.emitProgressUpdate(payload);
    } catch (error) {
      this.logger.error(
        `error occurred when saving RECURRING NR CONTRIBUTION for PIN ${rsaPin}: ${error instanceof Error ? error.stack : error}`
      );
    }
  }

  async emitProgressUpdate(payload: Record<string, string>) {
    try {
      return await firstValueFrom(
        this.enrollmentServiceClient.send(EnrollmentServiceClientConstant.UPDATE_NOMINAL_ROLL_COMPUTATION, payload)
      );
    } catch (error) {
      this.logger.error(
        `Error occurred while updating progress on enrollment service client : ${error instanceof Error ? error.stack : error}`
      );
      return new BaseResponseDto(ResponseCodeEnum.ERROR);
    }
  }
}
