/* eslint-disable */
import { Inject, Injectable } from '@nestjs/common';
import { MdaEmployeeBiodataRepository } from '@app/shared/login-service/repositories/mda-employee-biodata.repository';
import { GeneralUtils } from '@app/shared/utils';
import { PinoLogger } from 'nestjs-pino';
import { AccruedRightsResultRepository } from '@app/shared/dto/enrollment/repositories/accrued-rights-result.repository';
import { RegistrationStatusEnum } from '@app/shared/enums/registration-status.enum';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { EnrolmentBiodataRepository } from '@app/shared/enrollment-service/repository/enrolment-biodata.repository';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { AS_AT_JUNE_30TH_BAND, ISO_HYPHEN_DATE_FORMAT, THIRTIETH_JUNE_2004_DATE } from '@app/shared/constants';
import { format, isBefore } from 'date-fns';
import { AgencySectorRepository } from '@app/shared/enrollment-service/repository/agency-sector.repository';
import { TblApaRepository } from '@app/shared/enrollment-service/repository/tbl-apa.repository';
import { EmploymentDetail } from '@app/shared/enrollment-service/entities/employment-detail.entity';
import { AccruedRightsFactoryService } from '@app/shared/computation/accrued-rights/accrued-rights-factory.service';
import { AccruedRightsResult } from '@app/shared/dto/enrollment/entities/accrued-rights-result.entity';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { ResponseCodeEnum, SettingsEnumKey } from '@app/shared/enums';
import { EnrollmentServiceClientConstant } from '@app/shared/constants/enrollment-service-client.constant';
import { ContributionsComputationService } from '@app/shared/computation/contribution/contributions-computation.service';
import { EnrolmentBiodata } from '@app/shared/enrollment-service/entities/enrolment-biodata.entity';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';

@Injectable()
export class ExitAccruedRightsProcessor {
  constructor(
    private readonly redisCacheService: RedisCacheService,
    private readonly mdaEmployeeBiodataRepository: MdaEmployeeBiodataRepository,
    private readonly utils: GeneralUtils,
    private readonly logger: PinoLogger,
    private readonly agencySectorRepository: AgencySectorRepository,
    private readonly tblApaRepository: TblApaRepository,
    private readonly accruedRightsFactoryService: AccruedRightsFactoryService,
    private readonly contributionsComputationService: ContributionsComputationService,
    private readonly accruedRightsResultRepository: AccruedRightsResultRepository,
    private readonly enrolmentSummaryRepository: EnrolmentSummaryRepository,
    private readonly enrolmentBiodataRepository: EnrolmentBiodataRepository,
    private readonly settingService: SettingMsLibService,
    @Inject('ENROLLMENT_SERVICE_CLIENT')
    private readonly enrollmentServiceClient: ClientProxy
  ) {}

  async processExitAccruedRights(rsaPin: string): Promise<void> {
    const enrolmentSummary = await this.enrolmentSummaryRepository.findOne({ rsaPin });
    this.logger.error(`enrolmentSummary, ${JSON.stringify(enrolmentSummary)}`);
    if (!enrolmentSummary) {
      this.logger.error(`Queued RSA Pin ${rsaPin} not found in enrolment summary: skipping processing `);
      return;
    }

    if (enrolmentSummary.status !== RegistrationStatusEnum.VALIDATED) {
      this.logger.error(
        `Queued RSA Pin ${rsaPin} with status ${enrolmentSummary.status} is not allowed to be processed: skipping processing `
      );
      return;
    }

    const enrolmentBiodata = await this.enrolmentBiodataRepository.fetchEnrolmentBiodataWithDetails(rsaPin);
    this.logger.error(`enrolmentBiodata, ${JSON.stringify(enrolmentBiodata)}`);
    if (!enrolmentBiodata) {
      this.logger.error(`Queued RSA Pin ${rsaPin} is not found in enrolmentBiodata: skipping processing `);
      return;
    }

    console.log('enrolmentBiodata::: ', enrolmentBiodata);
    const accruedBenefits = await this.handleAccruedRightsComputation(enrolmentBiodata);
    if (accruedBenefits.code !== ResponseCodeEnum.SUCCESS) {
      this.logger.error(`Unable to compute exit accrued rights for rsaPin: ${rsaPin}:: ${accruedBenefits.description}`);
      return;
    }

    const contributionsResponse = await this.contributionsComputationService.calculateContributions(enrolmentBiodata);
    if (contributionsResponse.code !== ResponseCodeEnum.SUCCESS) {
      this.logger.error(
        `Unable to calculate exit contributions for rsaPin: ${rsaPin}:: ${contributionsResponse.description}`
      );
      return;
    }
    const contributions = contributionsResponse.content;

    try {
      const payload: Record<string, string> = {
        rsaPin: enrolmentBiodata.rsaPin,
        action: 'COMPUTATION_ANALYSIS',
        email: 'SYSTEM_COMPUTATION',
        accruedBenefit: `${accruedBenefits.content.benefit}`,
        contribution: contributions,
        apaValue: `${accruedBenefits.content.apaValue}`,
        retirementType: `${accruedBenefits.content.retirementType}`,
      };
      await this.emitProgressUpdate(payload);
    } catch (error) {
      this.logger.error(
        `error occurred when saving ACCRUED_RIGHTS_RESULT for PIN ${enrolmentBiodata.rsaPin}: ${error instanceof Error ? error.stack : error}`
      );
    }
  }

  async emitProgressUpdate(payload: Record<string, string>) {
    try {
      return await firstValueFrom(
        this.enrollmentServiceClient.send(EnrollmentServiceClientConstant.UPDATE_EXIT_ACCRUED_COMPUTATION, payload)
      );
    } catch (error) {
      this.logger.error(
        `Error occurred while updating progress on enrollment service client : ${error instanceof Error ? error.stack : error}`
      );
      return new BaseResponseDto(ResponseCodeEnum.ERROR);
    }
  }

  private async getApaValue(employmentDetail2004: EmploymentDetail): Promise<number | null> {
    const apaValueDetail = await this.tblApaRepository.findOne({
      remarks: employmentDetail2004.salaryStructure,
      gl: employmentDetail2004.gradeLevel,
      step: employmentDetail2004.step,
    });
    return apaValueDetail?.apaValue;
  }

  private async handleAccruedRightsComputation(
    enrolmentBiodata: EnrolmentBiodata
  ): Promise<BaseResponseWithContentNoPagination<any>> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);

    const employmentDetail2004 = enrolmentBiodata?.employmentDetails.find(
      (detail) => detail.employmentYear === AS_AT_JUNE_30TH_BAND
    );

    this.logger.error(`employmentDetail2004, ${JSON.stringify(employmentDetail2004)}`);

    const dateOfFirstAppointment = format(enrolmentBiodata.dateOfFirstAppointment, ISO_HYPHEN_DATE_FORMAT);
    if (!employmentDetail2004 && isBefore(dateOfFirstAppointment, THIRTIETH_JUNE_2004_DATE)) {
      const errorMessage = 'Unable to find 2004 employment detail';
      this.logger.error(errorMessage);
      enrolmentBiodata.accruedRightsComputationRemarks = errorMessage;
      await this.enrolmentBiodataRepository.saveEntity(enrolmentBiodata);

      response.setDescription(errorMessage);
      return response;
    }

    if (!employmentDetail2004) {
      //means dofa is after 30th June 2004
      response.setResponseCode(ResponseCodeEnum.SUCCESS);
      response.content = {
        benefit: 0,
        apaValue: 0,
        retirementType: 'INELIGIBLE',
      };
      enrolmentBiodata.accruedRightsComputationRemarks = 'INELIGIBLE FOR EXIT ACCRUED RIGHTS COMPUTATION';
      await this.enrolmentBiodataRepository.saveEntity(enrolmentBiodata);
      return response;
    }

    const employmentType = await this.agencySectorRepository.findOne({
      employerCode: employmentDetail2004.employerCode,
    });
    this.logger.error(`employmentType, ${JSON.stringify(employmentType)}`);
    if (!employmentType) {
      const errorMessage = `Unable to find 2004 employmentType on agency sector for rsaPin: ${enrolmentBiodata.rsaPin} employerCode ${employmentDetail2004.employerCode}`;
      this.logger.error(errorMessage);
      enrolmentBiodata.accruedRightsComputationRemarks = errorMessage;
      await this.enrolmentBiodataRepository.saveEntity(enrolmentBiodata);
      response.setDescription(errorMessage);
      return response;
    }

    const apaValue = await this.getApaValue(employmentDetail2004);
    this.logger.error(`apaValue, ${JSON.stringify(apaValue)}`);
    if (!apaValue) {
      const errorMessage = `Unable to find 2004 apaValueDetail  for rsaPin: ${enrolmentBiodata.rsaPin}, salaryStructure: ${employmentDetail2004.salaryStructure} , gradeLevel: ${employmentDetail2004.gradeLevel} , step: ${employmentDetail2004.step} `;
      this.logger.error(errorMessage);
      enrolmentBiodata.accruedRightsComputationRemarks = errorMessage;
      await this.enrolmentBiodataRepository.saveEntity(enrolmentBiodata);
      response.setDescription(errorMessage);
      return response;
    }

    const accruedRightsService = this.accruedRightsFactoryService.getAccruedRightsService(employmentType.sector);
    const dateOfBirth = format(enrolmentBiodata.dateOfBirth, ISO_HYPHEN_DATE_FORMAT);
    const dateOfRetirement = ExitAccruedRightsProcessor.getDor(
      enrolmentBiodata.dateOfRetirement,
      enrolmentBiodata.dateOfDeath,
      enrolmentBiodata.retireeUserType
    );
    const accruedRightsInterestRate = await this.settingService.getSettingInt(
      SettingsEnumKey.ACCRUED_RIGHTS_INTEREST_RATE
    );
    const gender: 'M' | 'F' = ['male', 'm'].includes(enrolmentBiodata.gender.toLowerCase())
      ? 'M'
      : ['female', 'f'].includes(enrolmentBiodata.gender.toLowerCase())
        ? 'F'
        : 'M';

    const accruedRightsResult = accruedRightsService.calculateAccruedRights(
      dateOfBirth,
      dateOfFirstAppointment,
      gender,
      apaValue,
      dateOfRetirement,
      accruedRightsInterestRate
    );

    this.logger.error(`accruedRightsResult, ${JSON.stringify(accruedRightsResult)}`);
    if (!accruedRightsResult) {
      const errorMessage = `Unable to generate accruedRightsResult for rsaPin: ${enrolmentBiodata.rsaPin}`;
      this.logger.error(errorMessage);
      enrolmentBiodata.accruedRightsComputationRemarks = errorMessage;
      await this.enrolmentBiodataRepository.saveEntity(enrolmentBiodata);
      response.setDescription(errorMessage);
      return response;
    }

    const { benefit, retirementType } = this.accruedRightsFactoryService.retrieveBenefitsDetails(
      accruedRightsResult,
      employmentType,
      employmentDetail2004
    );

    const accruedRightsResultEntity = new AccruedRightsResult({
      rsaPin: enrolmentBiodata.rsaPin,
      name: `${enrolmentBiodata.firstName} ${enrolmentBiodata.surname}`,
      gender: enrolmentBiodata.gender.toLowerCase() === 'male' ? 'M' : 'F',
      retireType: `${employmentType.sector} - ${retirementType} - benefit: ${benefit}`,
      apaValue: `${apaValue}`,
      dateOfBirth: enrolmentBiodata.dateOfBirth,
      dateOfRetirement: enrolmentBiodata.dateOfRetirement,
      dateOfFirstAppointment: enrolmentBiodata.dateOfFirstAppointment,
      accruedBenefitsVd: accruedRightsResult.accruedBenefitsVd?.toString(),
      normalRetirementYears: accruedRightsResult.normalRetirementYears?.toString(),
      normalRetirementAccruedBenefit: accruedRightsResult.normalRetirementAccruedBenefit?.toString(),
      earlyRetirementYears: accruedRightsResult.earlyRetirementYears?.toString(),
      earlyRetirementAccruedBenefit: accruedRightsResult.earlyRetirementAccruedBenefit?.toString(),
      entryPoint: 'EXIT-COMPUTATION',
    });
    await this.accruedRightsResultRepository.saveEntity(accruedRightsResultEntity);

    const content = {
      benefit: benefit,
      apaValue: apaValue,
      retirementType: retirementType,
    };

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.content = content;
    return response;
  }

  private static getDor(dateOfRetirement: Date, dateOfDeath: Date, retireeUserType: string) {
    if (retireeUserType !== RetireeUserTypeEnum.DECEASED) {
      return format(dateOfRetirement, ISO_HYPHEN_DATE_FORMAT);
    }

    if (dateOfDeath && dateOfRetirement) {
      return format(dateOfDeath < dateOfRetirement ? dateOfDeath : dateOfRetirement, ISO_HYPHEN_DATE_FORMAT);
    }

    return format(dateOfDeath || dateOfRetirement, ISO_HYPHEN_DATE_FORMAT);
  }
}
