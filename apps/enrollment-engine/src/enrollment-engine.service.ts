/* eslint-disable */
import { Inject, Injectable } from '@nestjs/common';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { PinoLogger } from 'nestjs-pino';
import { ExcelUploadTasksGateway } from '@app/shared/tasks/excel-upload.gateway';
import { ProcessedResultDto } from '@app/shared/dto/enrollment/mda-data-upload-result.dto';
import { GeneralUtils, normalizeExcelDateFields, transformAccruedRightsDto } from '@app/shared/utils';
import { firstValueFrom } from 'rxjs';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ClientProxy } from '@nestjs/microservices';
import { ResponseCodeEnum, SettingsEnumKey } from '@app/shared/enums';
import { UpdateProgressDto } from '@app/shared/dto/enrollment/update-progress.dto';
import { FileUploadRepository } from '@app/shared/enrollment-service/repository/file-upload.repository';
import { FileUploadProcess } from '@app/shared/enrollment-service/entities/file-upload.entity';
import { MdaBioDataProcessor } from './processors/mda-bio-data-processor';
import { MdaBiodataDto } from '@app/shared/dto/enrollment/mda-data-upload.dto';
import { MdaDeceasedDataUploadDto } from '@app/shared/dto/enrollment/mda-deceased-data-upload.dto';
import { NominalRollUploadProcessor } from './processors/nominal-roll-upload-processor';
import { EmployeeDataDto } from '@app/shared/dto/enrollment/nominalroll/nominal-roll-employee.dto';
import { AccruedRightsUploadDto } from '@app/shared/dto/enrollment/accrued-rights/accrued-rights-upload.dto';
import { AccruedRightsProcessor } from './processors/accrued-rights-processor';
import { CronJobNamesConstant } from '@app/shared/constants/cron.constants';
import { ExitAccruedRightsProcessor } from './processors/exit-accrued-rights-processor';
import { EnrollmentServiceClientConstant } from '@app/shared/constants/enrollment-service-client.constant';
import { RecurringNominalRollContributionCalculator } from './processors/recurring-nominal-roll-contribution-calculator';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { MultiplePinCalculatorService } from 'apps/enrollment-engine/src/services/multiple-pin-calculator.service';

@Injectable()
export class EnrollmentEngineService {
  constructor(
    private readonly redisCacheService: RedisCacheService,
    private readonly logger: PinoLogger,
    private readonly tasksGateway: ExcelUploadTasksGateway,
    private readonly fileUploadRepository: FileUploadRepository,
    private readonly mdaBioDataProcessor: MdaBioDataProcessor,
    private readonly exitAccruedRightsProcessor: ExitAccruedRightsProcessor,
    private readonly recurringNominalRollContributionCalculator: RecurringNominalRollContributionCalculator,
    private readonly accruedRightsProcessor: AccruedRightsProcessor,
    private readonly nominalRollUploadProcessor: NominalRollUploadProcessor,
    private readonly settingMsLibService: SettingMsLibService,
    private readonly utils: GeneralUtils,
    private readonly multiplePinCalculatorService: MultiplePinCalculatorService,
    @Inject('ECRS_SERVICE_CLIENT') private readonly ecrsClient: ClientProxy,
    @Inject('ENROLLMENT_SERVICE_CLIENT')
    private readonly enrollmentServiceClient: ClientProxy
  ) {}

  async handleMdaEmployeeDataUpload(queueData: string): Promise<void> {
    const data = JSON.parse(queueData) as { taskId: string; row: any };
    const taskId: string = data.taskId;
    try {
      await this.emitProgressUpdate(taskId, 0);
      const newRow = normalizeExcelDateFields(data.row);
      const mdaBioDataDto: Partial<MdaBiodataDto | MdaDeceasedDataUploadDto> =
        this.utils.tranformMdaRetireeBiodata(newRow);
      const result: ProcessedResultDto = await this.mdaBioDataProcessor.processBiodata(mdaBioDataDto, taskId);
      await this.handleResultUpdate({ taskId, row: newRow }, result);
    } catch (error) {
      this.logger.error(
        `Error processing row:, ${data.row}, \n error:  ${error instanceof Error ? error.stack : error}`
      );
      const result = new ProcessedResultDto();
      result.id = data.taskId;
      result.description = 'Unable to process requests, please try again';
      await this.handleResultUpdate(data, result); // Reject the message and requeue it
    } finally {
      await this.updateProgressPercentage(taskId);
    }
  }

  private async updateProgressPercentage(taskId: string) {
    const timeout = await this.settingMsLibService.getSettingInt(SettingsEnumKey.MDA_EXCEL_RESULT_TIMEOUT);
    await new Promise((resolve) => setTimeout(resolve, timeout));
    await this.redisCacheService.incr(`task:${taskId}:processed`);
    const total = await this.redisCacheService.get(`task:${taskId}:total`);
    const processed = await this.redisCacheService.get(`task:${taskId}:processed`);
    const percentage = (parseInt(processed) / parseInt(total)) * 100;
    await this.emitProgressUpdate(taskId, percentage);

    if (percentage === 100) {
      await this.redisCacheService.del(taskId);
    }
  }

  async handleNominalRollUpload(queueData: string): Promise<void> {
    const data = JSON.parse(queueData) as { taskId: string; userEmail: string; employeeData: any };
    const taskId: string = data.taskId;
    try {
      const userEmail: string = data.userEmail;
      const employeeDataDto: EmployeeDataDto = data.employeeData as EmployeeDataDto;
      await this.emitProgressUpdate(taskId, 0);
      const result = await this.nominalRollUploadProcessor.processNominalRoll(taskId, userEmail, employeeDataDto);
      this.logger.error(`Result ${result.id}: ${JSON.stringify(result)}%`);
      await this.handleNrResultUpdate(data, result);
    } catch (error) {
      this.logger.error(
        `handleNominalRollUpload Error processing row:, ${data.taskId}, \n error:  ${error instanceof Error ? error.stack : error}`
      );
      const result = new ProcessedResultDto();
      result.id = data.taskId;
      result.description = 'Unable to process requests, please try again';
      await this.handleNrResultUpdate(data, result);
    } finally {
      await this.updateProgressPercentage(taskId);
    }
  }

  async emitProgressUpdate(taskId: string, percentage: number) {
    try {
      const requestPayload = new UpdateProgressDto();
      requestPayload.taskId = taskId;
      requestPayload.percentage = percentage;

      return await firstValueFrom(
        this.enrollmentServiceClient.send(EnrollmentServiceClientConstant.UPDATE_PROGRESS, requestPayload)
      );
    } catch (error) {
      this.logger.error(
        `Error occurred while updating progress on enrollment service client taskId::, ${taskId}, \n error:  ${error instanceof Error ? error.stack : error}`
      );

      return new BaseResponseDto(ResponseCodeEnum.ERROR);
    }
  }

  async handleAccruedRights(queueData): Promise<void> {
    const data = JSON.parse(queueData) as { taskId: string; row: any };
    console.log(`queueData ${JSON.stringify(queueData)}`);
    const taskId: string = data.taskId;
    try {
      const accruedRightsDto: Partial<AccruedRightsUploadDto> = transformAccruedRightsDto(data.row);
      console.log(`accruedRightsDto ${JSON.stringify(accruedRightsDto)}`);
      const result: ProcessedResultDto = await this.accruedRightsProcessor.processBiodata(accruedRightsDto, taskId);
      this.logger.error(`Result ${result.id}: ${JSON.stringify(result)}%`);
      await this.handleResultUpdate(data, result);
    } catch (error) {
      this.logger.error(
        `Error processing handleAccruedRights row:, ${data.row}, \n error:  ${error instanceof Error ? error.stack : error}`
      );
    } finally {
      await this.updateProgressPercentage(taskId);
    }
    return Promise.resolve(undefined);
  }

  async handleExitArContributionQueue(data) {
    this.logger.debug(`handleExitArContributionQueue, ${JSON.stringify(data)}`);
    const rsaPin = data.rsaPin;
    const cacheKey = `${CronJobNamesConstant.EXIT_ACCRUED_RIGHTS_CONTRIBUTION_JOB}:${rsaPin}`;

    await this.exitAccruedRightsProcessor.processExitAccruedRights(rsaPin);

    await this.redisCacheService.del(cacheKey);
  }

  async handleNominalRollContributionQueue(data) {
    this.logger.debug(`handleNominalRollContributionQueue,  ${JSON.stringify(data)}`);
    const rsaPin = data.rsaPin;
    const cacheKey = `${CronJobNamesConstant.NOMINAL_ROLL_CONTRIBUTION_JOB}:${rsaPin}`;

    await this.recurringNominalRollContributionCalculator.calculateNominalRoll(rsaPin);

    await this.redisCacheService.del(cacheKey);
  }

  async handleNominalRollPfaConfirmationEscalation(data) {
    this.logger.debug(`handleNominalRollPfaConfirmationEscalation, ${JSON.stringify(data)}`);
    const rsaPin = data.rsaPin;
    const cacheKey = `${CronJobNamesConstant.NOMINAL_ROLL_CONTRIBUTION_JOB}:${rsaPin}`;

    await this.enrollmentServiceClient.emit(EnrollmentServiceClientConstant.PFA_NON_CONFIRMATION_ESCALATION_JOB, {
      rsaPin,
      pfaCode: data.pfaCode,
      requestType: data.requestType,
      action: data.action,
    });

    await this.redisCacheService.del(cacheKey);
  }

  private async handleResultUpdate(data: { taskId: string; row: any }, result: ProcessedResultDto) {
    const resultEntity = new FileUploadProcess({
      taskId: data.taskId,
      rowData: JSON.stringify(data.row),
      uniqueId: result.id,
      resultDescription: result.description,
    });

    try {
      await this.fileUploadRepository.upsertEntity(resultEntity, ['taskId', 'uniqueId']);
    } catch (error) {
      this.logger.error(
        `Error occurred while trying to save FileUploadProcess uniqueId ${result.id}: ${error instanceof Error ? error.stack : error}`
      );
    }
  }

  private async handleNrResultUpdate(data: { taskId: string; employeeData: any }, result: ProcessedResultDto) {
    const resultEntity = new FileUploadProcess({
      taskId: data.taskId,
      rowData: JSON.stringify(data.employeeData),
      uniqueId: result.id,
      resultDescription: result.description,
    });

    try {
      this.logger.error(`about to save FileUploadProcess for uniqueId  ${result.id} taskId: ${data.taskId}`);
      await this.fileUploadRepository.upsertEntity(resultEntity, ['taskId', 'uniqueId']);
    } catch (error) {
      this.logger.error(
        `Error occurred while trying to save handleNrResultUpdate FileUploadProcess uniqueId ${result.id}: ${error instanceof Error ? error.stack : error}`
      );
    }
  }

  async handleMultiplePinCalculation(data: { batchId: string }): Promise<void> {
    this.logger.debug(`Processing multiple PIN calculation for batch ID: ${data.batchId}`);

    try {
      // Existing calculation logic...
      const success = await this.multiplePinCalculatorService.calculateMultiplePinBalances(data.batchId);

      if (success) {
        // Notify enrollment service that computation is completed
        await this.enrollmentServiceClient.emit(EnrollmentServiceClientConstant.PROCESS_MULTIPLE_PIN_WORKFLOW, {
          batchId: data.batchId,
          action: 'COMPUTE',
        });

        this.logger.debug(`Successfully processed multiple PIN calculation for batch ID: ${data.batchId}`);
      } else {
        this.logger.error(`Failed to process multiple PIN calculation for batch ID: ${data.batchId}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing multiple PIN calculation for batch ID ${data.batchId}: ${error.message}`,
        error.stack
      );
    }
  }
}
