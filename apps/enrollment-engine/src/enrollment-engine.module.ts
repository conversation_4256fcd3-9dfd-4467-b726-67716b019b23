/* eslint-disable */
import { Module } from '@nestjs/common';
import { EnrollmentEngineController } from './enrollment-engine.controller';
import { EnrollmentEngineService } from './enrollment-engine.service';
import { DatabaseModule, LoggerModule, RedisCacheModule } from '@app/shared';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HealthModule } from '@app/shared/health';
import { TasksModule } from '@app/shared/tasks/tasks.module';
import { ClientProxyFactory, Transport } from '@nestjs/microservices';
import { GeneralUtils } from '@app/shared/utils';
import { ExcelUploadTasksGateway } from '@app/shared/tasks/excel-upload.gateway';
import { SettingMsLibModule } from '@app/shared/setting-service/setting-ms-lib.module';
import { FileUploadRepository } from '@app/shared/enrollment-service/repository/file-upload.repository';
import { FileUploadProcess } from '@app/shared/enrollment-service/entities/file-upload.entity';
import { MdaEmployeeBiodataRepository } from '@app/shared/login-service/repositories/mda-employee-biodata.repository';
import { MdaEmployeeBiodata } from '@app/shared/login-service/entities/employee-biodata.entity';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { MdaBioDataProcessor } from './processors/mda-bio-data-processor';
import { DtoValidator } from './validators/dto-validator';
import { EcrsValidator } from './validators/ecrs-validator';
import { RegisteredUserValidator } from './validators/registered-user-validator';
import { FieldValidator } from './validators/field-validator';
import { AccrLegacyPaymentsRepository } from '@app/shared/enrollment-service/repository/accr-legacy-payments.repository';
import { AccrLegacyPayments } from '@app/shared/enrollment-service/entities/accr-legacy-payments.entity';
import { AlreadyPaidValidator } from './validators/already-paid-validator';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { MdaRepository } from '@app/shared/user-service/repositories/mda.repository';
import { Pfa } from '@app/shared/user-service/entities/pfa.entity';
import { Mda } from '@app/shared/user-service/entities/mda.entity';
import { MdaValidator } from './validators/mda-validator';
import { PfaValidator } from './validators/pfa-validator';
import { CurrentUserValidator } from './validators/current-user-validator';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { EdorValidator } from './validators/edor-validator.service';
import {
  ECRS_SERVICE,
  ECRS_SERVICE_HOST,
  ECRS_SERVICE_TCP_PORT,
  ENROLLMENT_ENGINE_SERVICE,
  ENROLLMENT_MS_SERVICE,
  ENROLLMENT_QUEUE,
  ENROLLMENT_SERVICE_HOST,
  ENROLLMENT_SERVICE_TCP_PORT,
} from '@app/shared/constants';
import { NominalRollUploadProcessor } from './processors/nominal-roll-upload-processor';
import { NrDtoValidator } from './validators/nominal-roll/nr-dto-validator.service';
import { NrEcrsValidator } from './validators/nominal-roll/nr-ecrs-validator.service';
import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';
import { NominalRollBand } from '@app/shared/enrollment-service/entities/nominal-roll-band.entity';
import { NominalRollBandDetails } from '@app/shared/enrollment-service/entities/nominal-roll-band-details.entity';
import { NominalRollRepository } from '@app/shared/enrollment-service/repository/nominal-roll.repository';
import { NrCurrentUserValidator } from './validators/nominal-roll/nr-current-user-validator';
import { AccruedRightComputationModule } from '@app/shared/computation';
import { AccruedRightsResultRepository } from '@app/shared/dto/enrollment/repositories/accrued-rights-result.repository';
import { AccruedRightsResult } from '@app/shared/dto/enrollment/entities/accrued-rights-result.entity';
import { AccruedRightsProcessor } from './processors/accrued-rights-processor';
import { CobraRole } from '@app/shared/user-service/entities/cobra-role.entity';
import { CobraPrivilege } from '@app/shared/user-service/entities/cobra-priviledge.entity';
import { ExitAccruedRightsProcessor } from './processors/exit-accrued-rights-processor';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { EnrolmentSummary } from '@app/shared/enrollment-service/entities/enrolment-summary.entity';
import { EnrolmentBiodataRepository } from '@app/shared/enrollment-service/repository/enrolment-biodata.repository';
import { EnrolmentBiodata } from '@app/shared/enrollment-service/entities/enrolment-biodata.entity';
import { EnrolmentComment } from '@app/shared/enrollment-service/entities/enrolment-comment.entity';
import { EmploymentDocument } from '@app/shared/enrollment-service/entities/employment-document.entity';
import { EmploymentDetail } from '@app/shared/enrollment-service/entities/employment-detail.entity';
import { AgencySectorRepository } from '@app/shared/enrollment-service/repository/agency-sector.repository';
import { AgencySector } from '@app/shared/enrollment-service/entities/agency-sector.entity';
import { TblApaRepository } from '@app/shared/enrollment-service/repository/tbl-apa.repository';
import { TblApa } from '@app/shared/enrollment-service/entities/tbl-apa.entity';
import { FlaggedPinRepository } from '@app/shared/enrollment-service/repository/flagged-pin.repository';
import { SalaryTypeRepository } from '@app/shared/enrollment-service/repository/salary-type.repository';
import { TbcNocontributionAgencyRepository } from '@app/shared/enrollment-service/repository/tbc-nocontribution-agency.repository';
import { TbcReconcilePayRepository } from '@app/shared/enrollment-service/repository/tbc-reconcile-pay.repository';
import { TbcReconcilePay } from '@app/shared/enrollment-service/entities/tbc-reconcile-pay.entity';
import { TbcNoContributionAgency } from '@app/shared/enrollment-service/entities/tbc-nocontribution-agency.entity';
import { SalaryType } from '@app/shared/enrollment-service/entities/salary-type.entity';
import { FlaggedPin } from '@app/shared/enrollment-service/entities/flagged-pin.entity';
import { SalaryAllowance } from '@app/shared/enrollment-service/entities/salary-allowance.entity';
import { NrMdaValidator } from './validators/nominal-roll/nr-mda-validator';
import { NrPfaValidator } from './validators/nominal-roll/nr-pfa-validator';
import { NrBandValidator } from './validators/nominal-roll/nr-band-validator';
import { TbeOrganisationsRepository } from '@app/shared/enrollment-service/repository/tbe-organisations.repository';
import { TbeOrganisations } from '@app/shared/enrollment-service/entities/tbe-organisations.entity';
import { NrExistingUserValidator } from './validators/nominal-roll/nr-existing-user-validator';
import { RecurringNominalRollContributionCalculator } from './processors/recurring-nominal-roll-contribution-calculator';
import { ContributionsComputationService } from '@app/shared/computation/contribution/contributions-computation.service';
import { NominalRollComment } from '@app/shared/enrollment-service/entities/nominal-roll-comment.entity';
import { MultiplePinCalculatorService } from 'apps/enrollment-engine/src/services/multiple-pin-calculator.service';
import { MultiplePinResolutionPinRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-pin.repository';
import { MultiplePinResolutionRequestRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-req.repository';
import { MultiplePinResolutionRequest } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-req.entity';
import { MultiplePinResolutionPin } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-pin.entity';
import { MultiplePinResolutionTransactionHistoryRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-transaction.repository';
import { MultiplePinResolutionTransactionHistory } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-transaction.entity';
import { MultiplePinResolutionDocument } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-document.entity';
import { Pfc } from '@app/shared/user-service/entities/pfc.entity';
import { SalaryStructureValidator } from './validators/salary-structure-validator';
import { LegacyRecordRepository } from '@app/shared/enrollment-service/repositories/legacy-record.repository';
import { LegacyRecord } from '@app/shared/enrollment-service/entities/legacy-record.entity';
import { LegacyRecordDocument } from '@app/shared/enrollment-service/entities/legacy-record-document.entity';
import { NominalRollHistory } from '@app/shared/enrollment-service/entities/nominal-roll-history.entity';
import { NominalRollBandHistory } from '@app/shared/enrollment-service/entities/nominal-roll-band-history.entity';
import { NominalRollBandDetailsHistory } from '@app/shared/enrollment-service/entities/nominal-roll-band-details-history.entity';
import { NominalRollHistoryRepository } from '@app/shared/enrollment-service/repository/nominal-roll-history.repository';
import { NrExistingNominalRollValidator } from './validators/nominal-roll/nr-existing-nominal-roll-validator.service';

@Module({
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([
      FileUploadProcess,
      CobraUser,
      CobraRole,
      TbcReconcilePay,
      SalaryType,
      SalaryAllowance,
      FlaggedPin,
      TbcNoContributionAgency,
      CobraPrivilege,
      MdaEmployeeBiodata,
      AccrLegacyPayments,
      TbeOrganisations,
      Pfa,
      Pfc,
      Mda,
      NominalRoll,
      LegacyRecord,
      LegacyRecordDocument,
      NominalRollComment,
      NominalRollBand,
      NominalRollBandDetails,
      NominalRollHistory,
      NominalRollBandHistory,
      NominalRollBandDetailsHistory,
      AccruedRightsResult,
      EnrolmentSummary,
      EnrolmentBiodata,
      EnrolmentComment,
      EmploymentDocument,
      EmploymentDetail,
      AgencySector,
      TblApa,
      MultiplePinResolutionRequest,
      MultiplePinResolutionDocument,
      MultiplePinResolutionPin,
      MultiplePinResolutionTransactionHistory,
    ]),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    LoggerModule.forRoot(ENROLLMENT_ENGINE_SERVICE),
    RedisCacheModule.register(),
    HealthModule.registerAsync(
      [
        { name: ENROLLMENT_MS_SERVICE, host: ENROLLMENT_SERVICE_HOST, port: ENROLLMENT_SERVICE_TCP_PORT },
        { name: ECRS_SERVICE, host: ECRS_SERVICE_HOST, port: ECRS_SERVICE_TCP_PORT },
      ],
      ENROLLMENT_QUEUE
    ),
    SettingMsLibModule,
    TasksModule,
    EnrollmentEngineModule,
    AccruedRightComputationModule,
  ],
  controllers: [EnrollmentEngineController],
  providers: [
    EnrollmentEngineService,
    ContributionsComputationService,
    ExcelUploadTasksGateway,
    FlaggedPinRepository,
    TbcNocontributionAgencyRepository,
    TbcReconcilePayRepository,
    SalaryTypeRepository,
    FileUploadRepository,
    NominalRollRepository,
    EnrolmentSummaryRepository,
    TblApaRepository,
    AgencySectorRepository,
    EnrolmentBiodataRepository,
    MdaEmployeeBiodataRepository,
    AccrLegacyPaymentsRepository,
    TbeOrganisationsRepository,
    NominalRollHistoryRepository,
    CobraUserRepository,
    MultiplePinResolutionRequestRepository,
    MultiplePinResolutionPinRepository,
    MultiplePinResolutionTransactionHistoryRepository,
    PfaRepository,
    MdaRepository,
    LegacyRecordRepository,
    AccruedRightsResultRepository,
    AccruedRightsProcessor,
    NominalRollUploadProcessor,
    ExitAccruedRightsProcessor,
    NrExistingNominalRollValidator,
    NrExistingUserValidator,
    NrDtoValidator,
    NrBandValidator,
    NrMdaValidator,
    NrPfaValidator,
    NrEcrsValidator,
    NrCurrentUserValidator,
    MdaBioDataProcessor,
    CurrentUserValidator,
    DtoValidator,
    EcrsValidator,
    RegisteredUserValidator,
    SalaryStructureValidator,
    EdorValidator,
    FieldValidator,
    AlreadyPaidValidator,
    MdaValidator,
    PfaValidator,
    GeneralUtils,
    RecurringNominalRollContributionCalculator,
    MultiplePinCalculatorService,

    {
      provide: 'ECRS_SERVICE_CLIENT',
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: configService.get('ECRS_SERVICE_HOST', '0.0.0.0'),
            port: configService.get('ECRS_SERVICE_TCP_PORT', 3017),
          },
        });
      },
    },
    {
      provide: 'ENROLLMENT_SERVICE_CLIENT',
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: configService.get('ENROLLMENT_SERVICE_HOST', '0.0.0.0'),
            port: configService.get('ENROLLMENT_SERVICE_TCP_PORT', 3016),
          },
        });
      },
    },
  ],
})
export class EnrollmentEngineModule {}
