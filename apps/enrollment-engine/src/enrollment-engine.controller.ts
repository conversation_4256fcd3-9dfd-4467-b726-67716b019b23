/* eslint-disable */
import { Controller, Get, Query } from '@nestjs/common';
import { EnrollmentEngineService } from './enrollment-engine.service';
import { EventPattern } from '@nestjs/microservices';
import { EnrollmentEngineServiceClientConstant } from '@app/shared/constants';

@Controller('/enrollment-engine')
export class EnrollmentEngineController {
  constructor(private readonly enrollmentEngineService: EnrollmentEngineService) {}

  @EventPattern(EnrollmentEngineServiceClientConstant.MDA_DATA_UPLOAD)
  async handleEmployeeBioDataRow(data): Promise<void> {
    return await this.enrollmentEngineService.handleMdaEmployeeDataUpload(data);
  }

  @EventPattern(EnrollmentEngineServiceClientConstant.MDA_NOMINAL_ROLL_UPLOAD)
  async handleRow(data): Promise<void> {
    return await this.enrollmentEngineService.handleNominalRollUpload(data);
  }

  @EventPattern(EnrollmentEngineServiceClientConstant.EXIT_AR_CONTRIBUTION_COMPUTATION)
  async handleExitArContributionQueue(data): Promise<void> {
    return await this.enrollmentEngineService.handleExitArContributionQueue(data);
  }

  @EventPattern(EnrollmentEngineServiceClientConstant.NOMINAL_ROLL_CONTRIBUTION_JOB)
  async handleNominalRollContributionQueue(data): Promise<void> {
    return await this.enrollmentEngineService.handleNominalRollContributionQueue(data);
  }

  @EventPattern(EnrollmentEngineServiceClientConstant.PFA_NON_CONFIRMATION_ESCALATION_JOB)
  async handleNominalRollPfaConfirmationEscalation(data): Promise<void> {
    return await this.enrollmentEngineService.handleNominalRollPfaConfirmationEscalation(data);
  }

  @EventPattern(EnrollmentEngineServiceClientConstant.ACCRUED_RIGHTS_COMPUTATION)
  async handleAccruedRights(data): Promise<void> {
    return await this.enrollmentEngineService.handleAccruedRights(data);
  }

  @EventPattern(EnrollmentEngineServiceClientConstant.MULTIPLE_PIN_CALCULATION_JOB)
  async handleMultiplePinCalculationQueue(data): Promise<void> {
    return await this.enrollmentEngineService.handleMultiplePinCalculation(data);
  }

  @Get('update-progress')
  async updateProgress(
    @Query('taskId') taskId: string,
    @Query('percentage') percentage: number
  ): Promise<{ message: string }> {
    await this.enrollmentEngineService.emitProgressUpdate(taskId, percentage);
    return {
      message: `Progress update emitted for task ${taskId}: ${percentage}%`,
    };
  }
}
