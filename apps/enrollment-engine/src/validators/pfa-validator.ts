import { IBiodataValidation } from '@app/shared/dto/enrollment/interfaces/bio-data-validation.interface';
import { Injectable } from '@nestjs/common';
import { IMdaBaseBioData } from '@app/shared/dto/enrollment/interfaces/mda-base-bio-data.interface';
import { AbstractValidator } from './abstract-validator';

@Injectable()
export class PfaValidator<T extends IMdaBaseBioData> extends AbstractValidator<T> implements IBiodataValidation<T> {
  async validate(dto: Partial<T>, context: Record<string, any>): Promise<string | null> {
    return this.validatePfa(context);
  }
}
