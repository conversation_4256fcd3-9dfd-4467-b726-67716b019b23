/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { IMdaBaseBioData } from '@app/shared/dto/enrollment/interfaces/mda-base-bio-data.interface';
import { IBiodataValidation } from '@app/shared/dto/enrollment/interfaces/bio-data-validation.interface';
import { validateEdor } from '@app/shared/utils/input-field-validator-utils';
import { SLASH_DATE_FORMAT } from '@app/shared/constants';

@Injectable()
export class EdorValidator<T extends IMdaBaseBioData> implements IBiodataValidation<T> {
  async validate(mdaBiodataDto: Partial<T>, context: Record<string, any>): Promise<string | null> {
    if (!mdaBiodataDto.edor) {
      return 'Expected Date of Retirement is required';
    }

    const edorValidation = validateEdor(
      mdaBiodataDto.edor,
      context.dateOfBirth,
      mdaBiodataDto.dofa,
      mdaBiodataDto.dts,
      SLASH_DATE_FORMAT
    );
    if (edorValidation) {
      return edorValidation;
    }

    return null; // No errors
  }
}
