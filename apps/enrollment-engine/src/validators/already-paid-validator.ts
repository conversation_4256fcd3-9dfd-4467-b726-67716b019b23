/* eslint-disable */
import { IBiodataValidation } from '@app/shared/dto/enrollment/interfaces/bio-data-validation.interface';
import { Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { AccrLegacyPaymentsRepository } from '@app/shared/enrollment-service/repository/accr-legacy-payments.repository';
import { IMdaBaseBioData } from '@app/shared/dto/enrollment/interfaces/mda-base-bio-data.interface';

@Injectable()
export class AlreadyPaidValidator<T extends IMdaBaseBioData> implements IBiodataValidation<T> {
  constructor(
    private readonly logger: PinoLogger,
    private readonly accrLegacyPaymentsRepository: AccrLegacyPaymentsRepository
  ) {}

  async validate(dto: Partial<T>, context: Record<string, any>): Promise<string | null> {
    try {
      const retireeType = this.accrLegacyPaymentsRepository.findOne({
        pin: dto.rsaPin,
      });
      context.alreadyPaid = !!retireeType;
    } catch (error) {
      this.logger.error(
        `Error occurred while attempting to verify already paid: ${error instanceof Error ? error.stack : error}`
      );
      return 'Error occurred while attempting to verify already paid';
    }

    return null;
  }
}
