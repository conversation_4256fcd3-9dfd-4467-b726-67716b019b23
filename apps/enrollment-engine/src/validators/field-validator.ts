/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { IMdaBaseBioData } from '@app/shared/dto/enrollment/interfaces/mda-base-bio-data.interface';
import { IBiodataValidation } from '@app/shared/dto/enrollment/interfaces/bio-data-validation.interface';
import { validateDofa, validateDts } from '@app/shared/utils/input-field-validator-utils';
import { SLASH_DATE_FORMAT } from '@app/shared/constants';

@Injectable()
export class FieldValidator<T extends IMdaBaseBioData> implements IBiodataValidation<T> {
  constructor() {}

  async validate(mdaBiodataDto: Partial<T>, context: Record<string, any>): Promise<string | null> {
    if (context.retireeUserType !== 'DECEASED' && mdaBiodataDto.rsaPin.startsWith('TPEN')) {
      return 'TPEN is only allowed for DECEASED employees';
    }

    const dofaValidation = validateDofa(mdaBiodataDto.dofa, SLASH_DATE_FORMAT);
    if (dofaValidation) {
      return dofaValidation;
    }

    const dtsValidation = validateDts(mdaBiodataDto.dts, SLASH_DATE_FORMAT);
    if (dtsValidation) {
      return dtsValidation;
    }

    return null; // No errors
  }
}
