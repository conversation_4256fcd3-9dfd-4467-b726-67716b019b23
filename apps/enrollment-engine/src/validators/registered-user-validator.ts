/* eslint-disable */
import { IBiodataValidation } from '@app/shared/dto/enrollment/interfaces/bio-data-validation.interface';
import { Injectable } from '@nestjs/common';
import { MdaEmployeeBiodataRepository } from '@app/shared/login-service/repositories/mda-employee-biodata.repository';
import { PinoLogger } from 'nestjs-pino';
import { IMdaBaseBioData } from '@app/shared/dto/enrollment/interfaces/mda-base-bio-data.interface';
import { FlaggedPinRepository } from '@app/shared/enrollment-service/repository/flagged-pin.repository';
import { LegacyRecordRepository } from '@app/shared/enrollment-service/repositories/legacy-record.repository';

@Injectable()
export class RegisteredUserValidator<T extends IMdaBaseBioData> implements IBiodataValidation<T> {
  constructor(
    private readonly logger: PinoLogger,
    private readonly mdaEmployeeBiodataRepository: MdaEmployeeBiodataRepository,
    private flaggedPinRepository: FlaggedPinRepository,
    private readonly legacyRecordRepository: LegacyRecordRepository
  ) {}

  async validate(mdaBiodataDto: Partial<T>): Promise<string | null> {
    try {
      const prevBiodata = await this.mdaEmployeeBiodataRepository.findOne({
        rsaPin: mdaBiodataDto.rsaPin,
      });
      if (prevBiodata && (await this.checkIfRegistered(mdaBiodataDto.rsaPin))) {
        return 'User is already registered';
      }
    } catch (error) {
      this.logger.error(
        `Error occurred while validating employee details rsaPin ${mdaBiodataDto.rsaPin}, error: ${error}`
      );
      return 'Error occurred while validating employee details rsaPin';
    }

    const flaggedPin = await this.flaggedPinRepository.findOne({ rsaPin: mdaBiodataDto.rsaPin, active: true });
    if (flaggedPin) {
      return `User is flagged for suspicious activity, please contact COBRA team, Reason: ${flaggedPin.reason}`;
    }
    return null;
  }

  async checkIfRegistered(rsaPin: string) {
    const legacyRecord = await this.legacyRecordRepository.findOne({ rsaPin: rsaPin });
    if (legacyRecord) {
      console.log('Legacy record found for RSA Pin:', rsaPin, legacyRecord);
      return true;
    }
    return false;
  }
}
