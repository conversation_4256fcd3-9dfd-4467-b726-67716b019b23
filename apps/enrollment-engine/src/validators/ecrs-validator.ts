/* eslint-disable */
import { IBiodataValidation } from '@app/shared/dto/enrollment/interfaces/bio-data-validation.interface';
import { Injectable } from '@nestjs/common';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import { IMdaBaseBioData } from '@app/shared/dto/enrollment/interfaces/mda-base-bio-data.interface';
import { AbstractValidator } from './abstract-validator';
import { convertDbHyphenDateToSlash } from '@app/shared/utils';

@Injectable()
export class EcrsValidator<T extends IMdaBaseBioData> extends AbstractValidator<T> implements IBiodataValidation<T> {
  async validate(dto: Partial<T>, context: Record<string, any>): Promise<string | null> {
    const retireeType = context.retireeUserType;
    const ecrsResponse: EcrsUserResponseDto = await this.callEcrsService(dto.rsaPin, dto.surname, retireeType);

    this.logger.error(`ecrsResponse: ${JSON.stringify(ecrsResponse)}`);
    if (ecrsResponse.code !== 1) {
      return ecrsResponse.description;
    }

    context.ecrsResponse = ecrsResponse;
    context.gender = ecrsResponse.gender;
    context.dateOfBirth = convertDbHyphenDateToSlash(ecrsResponse.dateOfBirth);
    context.pfaCode = ecrsResponse.pfaCode;
    return null;
  }
}
