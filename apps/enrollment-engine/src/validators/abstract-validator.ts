/* eslint-disable */
import { Inject, Injectable } from '@nestjs/common';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';
import { PinoLogger } from 'nestjs-pino';
import { ClientProxy } from '@nestjs/microservices';
import { RetrieveUserDto } from '@app/shared/dto/ecrs/retrieve-user.dto';
import { firstValueFrom } from 'rxjs';
import { EcrsServiceClientConstant } from '@app/shared/constants/ecrs-service-client.constant';
import { ResponseCodeEnum } from '@app/shared/enums';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { extractMdaCode } from '@app/shared/utils';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { MdaRepository } from '@app/shared/user-service/repositories/mda.repository';

@Injectable()
export class AbstractValidator<T> {
  constructor(
    readonly logger: PinoLogger,
    @Inject('ECRS_SERVICE_CLIENT') private readonly ecrsClient: ClientProxy,
    private readonly cobraUserRepository: CobraUserRepository,
    private readonly pfaRepository: PfaRepository,
    private readonly mdaRepository: MdaRepository,
    private readonly redisCacheService: RedisCacheService
  ) {}

  async callEcrsService(
    rsaPin: string,
    surname: string,
    retireeType: RetireeUserTypeEnum
  ): Promise<EcrsUserResponseDto> {
    const requestPayload = new RetrieveUserDto();
    requestPayload.rsaPin = rsaPin;
    requestPayload.surname = surname;
    requestPayload.userType = retireeType;

    try {
      return await firstValueFrom(
        this.ecrsClient.send({ cmd: EcrsServiceClientConstant.RETRIEVE_USER }, requestPayload)
      );
    } catch (error) {
      this.logger.error(
        `Error occurred while retrieving COBRA User data from ECRS ms ${error instanceof Error ? error.stack : error}`
      );
      return new EcrsUserResponseDto(ResponseCodeEnum.ERROR);
    }
  }

  async getCobraUser(userEmail: string): Promise<CobraUser> {
    const cacheKey = `USER_VALIDATION_${userEmail}`;
    const cobraUser = await this.redisCacheService.get(cacheKey);
    if (cobraUser) {
      return cobraUser;
    }

    const dbCobraUser = await this.cobraUserRepository.findOne({
      emailAddress: userEmail,
    });

    await this.redisCacheService.set(cacheKey, JSON.stringify(dbCobraUser), 3600);
    return dbCobraUser;
  }

  async validateMda(context: Record<string, any>): Promise<string | null> {
    try {
      const ecrsResponseCtx = context.ecrsResponse;
      const rsaPin = context.rsaPin;
      if (!ecrsResponseCtx) {
        this.logger.error(`rsaPin: ${rsaPin}, Unable to retrieve user information from ECRS`);
        return 'Unable to retrieve user information from ECRS';
      }

      const ecrsResponse = ecrsResponseCtx as EcrsUserResponseDto;
      const uploaderMdaCode = extractMdaCode(context.taskId);

      if (!uploaderMdaCode || !ecrsResponse.employerCode) {
        this.logger.error(
          `rsaPin: ${rsaPin}, uploaderMdaCode ${uploaderMdaCode}, ecrsResponseMda ${ecrsResponse.employerCode} is not defined`
        );
        return 'Unable to verify Employer details';
      }

      if (uploaderMdaCode !== ecrsResponse.employerCode) {
        this.logger.error(
          `rsaPin: ${rsaPin}, uploaderMdaCode ${uploaderMdaCode}, ecrsResponseMda ${ecrsResponse.employerCode} does not match`
        );
        return 'Employer code does not match the employee Employer Code';
      }

      const mda = await this.mdaRepository.findOne({
        employerId: ecrsResponse.employerCode,
      });

      if (!mda) {
        this.logger.error(`uploaderMdaCode ${uploaderMdaCode}, not found in MDA table`);
        return 'Unable to get employer details';
      }

      context.mdaCode = mda.employerId;
      context.mdaName = mda.employerName;
    } catch (error) {
      this.logger.error(
        `Error occurred while attempting to verify already paid: ${error instanceof Error ? error.stack : error}`
      );
      return 'Error occurred while attempting to verify MDA';
    }

    return null;
  }

  async validatePfa(context: Record<string, any>): Promise<string | null> {
    try {
      const ecrsResponse = context.ecrsResponse as EcrsUserResponseDto;
      const pfa = await this.pfaRepository.findPfaByCodeAndAssociatedCode(ecrsResponse.pfaCode);
      if (!pfa) {
        return `Unable to validate PFA details : ${context.pfaCode}, please try again`;
      }

      context.pfaCode = pfa.pfaCode;
      context.pfaName = pfa.pfaName;
    } catch (error) {
      this.logger.error(
        `Error occurred while attempting to verify PFA: ${error instanceof Error ? error.stack : error}`
      );
      return 'Error occurred while attempting to verify PFA';
    }

    return null;
  }
}
