import { IBiodataValidation } from '@app/shared/dto/enrollment/interfaces/bio-data-validation.interface';
import { Injectable } from '@nestjs/common';
import { IMdaBaseBioData } from '@app/shared/dto/enrollment/interfaces/mda-base-bio-data.interface';
import { RetrieveCompleteEmployerDetailsDto } from '@app/shared/dto/enrollment/enrolment-draft.dto';
import { THIRTIETH_JUNE_2004_DATE } from '@app/shared/constants/enrolment-services-contants';
import { SLASH_DATE_FORMAT } from '@app/shared/constants';
import { getValidDate } from '@app/shared/utils';
import { TbeOrganisationsRepository } from '@app/shared/enrollment-service/repository/tbe-organisations.repository';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class SalaryStructureValidator<T extends IMdaBaseBioData> implements IBiodataValidation<T> {
  constructor(
    private readonly logger: PinoLogger,
    private readonly tbeOrganisationsRepository: TbeOrganisationsRepository
  ) {}
  async validate(mdaBiodataDto: Partial<T>, context: Record<string, any>): Promise<string | null> {
    const dofa = getValidDate(mdaBiodataDto.dofa, SLASH_DATE_FORMAT);
    if (!dofa) {
      return 'Date of First Appointment (DOFA) is required and must be a valid date.';
    }

    const dofaIsBeforeOrOnJune2004 = dofa <= THIRTIETH_JUNE_2004_DATE;
    if (
      !dofaIsBeforeOrOnJune2004 &&
      (mdaBiodataDto.employerJune2004 ||
        mdaBiodataDto.salaryStructureJune2004 ||
        mdaBiodataDto.gradeLevelJune2004 ||
        mdaBiodataDto.stepJune2004)
    ) {
      return 'If the date of first appointment is after June 30, 2004, you should not provide employer and salary structure for June 2004. Please remove these fields or ensure the date of first appointment is before or on June 30, 2004.';
    }

    if (!dofaIsBeforeOrOnJune2004) {
      return null;
    }

    if (
      dofaIsBeforeOrOnJune2004 &&
      (!mdaBiodataDto.employerJune2004 ||
        !mdaBiodataDto.salaryStructureJune2004 ||
        !mdaBiodataDto.gradeLevelJune2004 ||
        !mdaBiodataDto.stepJune2004)
    ) {
      return 'Kindly provide the employer and salary structure for June 2004 as the date of first appointment is before or on June 30, 2004.';
    }

    const retrieveEmployerDetailsDto: RetrieveCompleteEmployerDetailsDto = new RetrieveCompleteEmployerDetailsDto();
    retrieveEmployerDetailsDto.employerCode = mdaBiodataDto.employerJune2004;
    retrieveEmployerDetailsDto.year = '2004';
    retrieveEmployerDetailsDto.salaryStructure = mdaBiodataDto.salaryStructureJune2004;
    retrieveEmployerDetailsDto.gradelevel = `${mdaBiodataDto.gradeLevelJune2004}`;
    retrieveEmployerDetailsDto.step = `${mdaBiodataDto.stepJune2004}`;
    return this.validateSalaryStructure(retrieveEmployerDetailsDto, dofa);
  }

  async validateSalaryStructure(
    retrieveEmployerDetailsDto: RetrieveCompleteEmployerDetailsDto,
    dofa: Date
  ): Promise<string | null> {
    try {
      const bandMatch =
        await this.tbeOrganisationsRepository.getEmployerCompleteSalaryDetails(retrieveEmployerDetailsDto);
      if (!bandMatch || bandMatch.length === 0) {
        return 'Salary details not found with the provided details';
      }

      if (bandMatch[0].ippisDate && dofa > new Date(bandMatch[0].ippisDate)) {
        return 'You are not eligible for this scheme as your Date of First Appointment (DOFA) is after the IPPIS date of your employer. Please contact your PTAD further assistance.';
      }
    } catch (error) {
      this.logger.error(
        `Error occurred while attempting to verify salary structure: ${error instanceof Error ? error.stack : error}`
      );
      return 'Error occurred while attempting to verify salary details';
    }
    return null;
  }
}
