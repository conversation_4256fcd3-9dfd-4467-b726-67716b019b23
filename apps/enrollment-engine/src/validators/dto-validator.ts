/* eslint-disable */
import { IBiodataValidation } from '@app/shared/dto/enrollment/interfaces/bio-data-validation.interface';
import { MdaBiodataDto } from '@app/shared/dto/enrollment/mda-data-upload.dto';
import { Injectable } from '@nestjs/common';
import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';
import { MdaDeceasedDataUploadDto } from '@app/shared/dto/enrollment/mda-deceased-data-upload.dto';
import { PinoLogger } from 'nestjs-pino';
import { IMdaBaseBioData } from '@app/shared/dto/enrollment/interfaces/mda-base-bio-data.interface';

@Injectable()
export class DtoValidator<T extends IMdaBaseBioData> implements IBiodataValidation<T> {
  constructor(private readonly logger: PinoLogger) {}

  async validate(dtoData: Partial<T>, context: Record<string, any>): Promise<string | null> {
    const taskId = context.taskId;
    const dtoMap: Record<string, { pojoType: new () => IMdaBaseBioData; retireeType: RetireeUserTypeEnum }> = {
      RETIREE: {
        pojoType: MdaBiodataDto,
        retireeType: RetireeUserTypeEnum.RETIREE,
      },
      DECEASED: {
        pojoType: MdaDeceasedDataUploadDto,
        retireeType: RetireeUserTypeEnum.DECEASED,
      },
    };

    const key = Object.keys(dtoMap).find((k) => taskId.includes(k));
    if (!key) {
      this.logger.error(`Unknown user type provided in request extracted from taskId: ${taskId}`);
      return 'Unknown user type provided in request';
    }

    const { pojoType, retireeType } = dtoMap[key];
    context.retireeUserType = retireeType;

    // Transform and validate the correct DTO
    const dtoInstance = plainToInstance(pojoType, dtoData);
    const errors = await validate(dtoInstance);

    if (errors.length > 0) {
      return errors.map((err) => Object.values(err.constraints || {}).join(', ')).join('; ');
    }

    return null;
  }
}
