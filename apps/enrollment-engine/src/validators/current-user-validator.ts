/* eslint-disable */
import { IBiodataValidation } from '@app/shared/dto/enrollment/interfaces/bio-data-validation.interface';
import { Injectable } from '@nestjs/common';
import { IMdaBaseBioData } from '@app/shared/dto/enrollment/interfaces/mda-base-bio-data.interface';
import { AbstractValidator } from './abstract-validator';

@Injectable()
export class CurrentUserValidator<T extends IMdaBaseBioData>
  extends AbstractValidator<T>
  implements IBiodataValidation<T>
{
  async validate(dto: Partial<T>, context: Record<string, any>): Promise<string | null> {
    try {
      const cobraUser = await this.getCobraUser(context.userEmail);
      if (!cobraUser) {
        return 'Unable to validate user attempting to submit request, please try again';
      }
      context.cobraUser = cobraUser;
    } catch (error) {
      this.logger.error(
        `Unable to validate user attempting to submit request,: ${error instanceof Error ? error.stack : error}`
      );
      return 'Error occurred while attempting to verify <PERSON><PERSON>';
    }

    return null;
  }
}
