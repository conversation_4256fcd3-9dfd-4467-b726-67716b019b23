import { Injectable } from '@nestjs/common';
import { IEmployeeDataDto } from '@app/shared/dto/enrollment/interfaces/nominal-roll-employee.interface';
import { INominalRollValidation } from '@app/shared/dto/enrollment/interfaces/nominal-roll-validation.interface';
import { PinoLogger } from 'nestjs-pino';
import { TbeOrganisationsRepository } from '@app/shared/enrollment-service/repository/tbe-organisations.repository';
import { NominalRollRepository } from '@app/shared/enrollment-service/repository/nominal-roll.repository';

@Injectable()
export class NrExistingNominalRollValidator<T extends IEmployeeDataDto> implements INominalRollValidation<T> {
  constructor(
    private readonly tbeOrganisationsRepository: TbeOrganisationsRepository,
    private readonly nominalRollRepository: NominalRollRepository,
    readonly logger: PinoLogger
  ) {}

  async validate(dto: Partial<T>, context: Record<string, any>): Promise<string | null> {
    try {
      const organisations = await this.tbeOrganisationsRepository.findOne({ employerId: `${context.mdaCode}` });
      if (!organisations) {
        return 'Unable to verify organisation details, please try again.';
      }

      const existingNominalRoll = await this.nominalRollRepository.findOne({ rsaPin: context.rsaPin });
      if (existingNominalRoll && organisations.ippisDate) {
        return 'Record has been initially uploaded and processed. Please contact the administrator for further assistance.';
      }

      return null;
    } catch (error) {
      this.logger.error(
        `Unable to verify if Nominal Roll already exist in the exit process: ${error instanceof Error ? error.stack : error}`
      );
      return 'Error occurred while attempting to verify Nominal Roll details';
    }
  }
}
