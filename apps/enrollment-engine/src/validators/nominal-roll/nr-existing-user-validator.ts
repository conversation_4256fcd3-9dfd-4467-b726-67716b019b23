import { Injectable } from '@nestjs/common';
import { IEmployeeDataDto } from '@app/shared/dto/enrollment/interfaces/nominal-roll-employee.interface';
import { INominalRollValidation } from '@app/shared/dto/enrollment/interfaces/nominal-roll-validation.interface';
import { PinoLogger } from 'nestjs-pino';
import { MdaEmployeeBiodataRepository } from '@app/shared/login-service/repositories/mda-employee-biodata.repository';

@Injectable()
export class NrExistingUserValidator<T extends IEmployeeDataDto> implements INominalRollValidation<T> {
  constructor(
    private readonly mdaEmployeeBiodataRepository: MdaEmployeeBiodataRepository,
    readonly logger: PinoLogger
  ) {}

  async validate(dto: Partial<T>, context: Record<string, any>): Promise<string | null> {
    try {
      const existingUser = await this.mdaEmployeeBiodataRepository.findOne({ rsaPin: dto.rsaPin });
      if (existingUser) {
        return 'User already exists in the exit process, kindly go and complete the exit.';
      }

      return null;
    } catch (error) {
      this.logger.error(
        `Unable to verify if user already exist in the exit process: ${error instanceof Error ? error.stack : error}`
      );
      return 'Error occurred while attempting to verify Existing user details';
    }
  }
}
