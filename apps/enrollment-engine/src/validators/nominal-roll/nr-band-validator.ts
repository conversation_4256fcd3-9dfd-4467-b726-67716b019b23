/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { IEmployeeDataDto } from '@app/shared/dto/enrollment/interfaces/nominal-roll-employee.interface';
import { INominalRollValidation } from '@app/shared/dto/enrollment/interfaces/nominal-roll-validation.interface';
import { TbeOrganisationsRepository } from '@app/shared/enrollment-service/repository/tbe-organisations.repository';
import { RetrieveCompleteEmployerDetailsDto } from '@app/shared/dto/enrollment/enrolment-draft.dto';
import { PinoLogger } from 'nestjs-pino';
import { getIsoDateString } from '@app/shared/utils/input-field-validator-utils';
import { SLASH_DATE_FORMAT } from '@app/shared/constants';
import { parseISO } from 'date-fns';

@Injectable()
export class NrBandValidator<T extends IEmployeeDataDto> implements INominalRollValidation<T> {
  constructor(
    private readonly tbeOrganisationsRepository: TbeOrganisationsRepository,
    readonly logger: PinoLogger
  ) {}

  async validate(dto: Partial<T>, context: Record<string, any>): Promise<string | null> {
    try {
      const organisations = await this.tbeOrganisationsRepository.findOne({ employerId: `${context.mdaCode}` });
      if (!organisations) {
        return 'Unable to verify organisation details, please try again.';
      }

      const ippisDate = organisations.ippisDate ? new Date(organisations.ippisDate) : new Date();
      context.ippisDate = ippisDate;
      const validateBandDetails = this.validateBands(dto.dateOfFirstAppointment, dto.bandData, ippisDate);
      if (validateBandDetails) {
        return validateBandDetails;
      }
      let errorMessage = '';
      for (const [index, band] of dto.bandData.entries()) {
        const isLast = index === dto.bandData.length - 1;
        if (isLast && band.employer !== context.mdaCode) {
          errorMessage = `Employer of year: ${band.year} must be the current employer of the employee`;
          this.logger.warn(errorMessage);
          break;
        }

        if (band.year > ippisDate.getFullYear()) {
          errorMessage = `Band year ${band.year} is after than IPPIS year ${ippisDate.getFullYear()}`;
          this.logger.warn(errorMessage);
          break;
        }
        const retrieveCompleteEmployerDetailsDto = new RetrieveCompleteEmployerDetailsDto();
        retrieveCompleteEmployerDetailsDto.year = `${band.year}`;
        retrieveCompleteEmployerDetailsDto.salaryStructure = `${band.salaryStructure}`;
        retrieveCompleteEmployerDetailsDto.gradelevel = `${band.gl}`;
        retrieveCompleteEmployerDetailsDto.step = `${band.step}`;
        retrieveCompleteEmployerDetailsDto.employerCode = `${band.employer}`;

        const bandDetails = await this.tbeOrganisationsRepository.getEmployerCompleteSalaryDetails(
          retrieveCompleteEmployerDetailsDto
        );

        if (!bandDetails || bandDetails.length === 0) {
          errorMessage = `Unable find band details for mdaCode: ${band.employer} year ${band.year}, salary structure ${band.salaryStructure}, grade level ${band.gl}, step ${band.step}`;
          this.logger.warn(errorMessage);
          break;
        }
      }

      if (errorMessage) {
        return errorMessage;
      }

      return null;
    } catch (error) {
      this.logger.error(
        `Unable to validate user attempting to submit request,: ${error instanceof Error ? error.stack : error}`
      );
      return 'Error occurred while attempting to verify Band details';
    }
  }

  validateBands(dateOfFirstAppointment: string, bands: any, ippisDate: Date): string | null {
    const dofaYear = parseISO(getIsoDateString(dateOfFirstAppointment, SLASH_DATE_FORMAT)).getFullYear();
    const doeYear = ippisDate.getFullYear();
    const validBandYears = this.generateValidBandYears();

    // Find the valid band year immediately before or equal to dofaYear
    const previousBandYear =
      validBandYears.filter((year) => year <= dofaYear).sort((a, b) => b - a)[0] || validBandYears[0]; // Get the latest year <= dofaYear

    // Include all valid band years from previousBandYear to doeYear
    const expectedBands = validBandYears.filter((year) => year >= previousBandYear && year <= doeYear);
    const submittedBandYears = bands.map((band) => band.year).sort((a, b) => a - b);
    const expectedBandYears = expectedBands.sort((a, b) => a - b);

    const areEqual =
      submittedBandYears.length === expectedBandYears.length &&
      submittedBandYears.every((year, i) => year === expectedBandYears[i]);

    if (!areEqual) {
      return `Submitted bands [${submittedBandYears.join(', ')}] do not match expected bands [${expectedBandYears.join(', ')}] based on appointment (${dofaYear}) and exit (${doeYear}) dates.`;
    }

    return null;
  }

  generateValidBandYears(startYear = 2004): number[] {
    const currentYear = new Date().getFullYear();
    const bands: number[] = [];
    for (let year = startYear; year <= currentYear + 1; year += 3) {
      bands.push(year);
    }
    return bands;
  }
}
