/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { IEmployeeDataDto } from '@app/shared/dto/enrollment/interfaces/nominal-roll-employee.interface';
import { INominalRollValidation } from '@app/shared/dto/enrollment/interfaces/nominal-roll-validation.interface';
import { AbstractValidator } from '../abstract-validator';

@Injectable()
export class NrCurrentUserValidator<T extends IEmployeeDataDto>
  extends AbstractValidator<T>
  implements INominalRollValidation<T>
{
  async validate(dto: Partial<T>, context: Record<string, any>): Promise<string | null> {
    try {
      const cobraUser = await this.getCobraUser(context.userEmail);
      if (!cobraUser) {
        return 'Unable to validate user attempting to process Nominal roll, please try again';
      }
      context.cobraUser = cobraUser;
    } catch (error) {
      this.logger.error(
        `Unable to validate user attempting to submit request,: ${error instanceof Error ? error.stack : error}`
      );
      return 'Error occurred while attempting to verify <PERSON><PERSON>';
    }

    return null;
  }
}
