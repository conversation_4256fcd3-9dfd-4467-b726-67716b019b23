/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { INominalRollValidation } from '@app/shared/dto/enrollment/interfaces/nominal-roll-validation.interface';
import { IEmployeeDataDto } from '@app/shared/dto/enrollment/interfaces/nominal-roll-employee.interface';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import { AbstractValidator } from '../abstract-validator';

@Injectable()
export class NrEcrsValidator<T extends IEmployeeDataDto>
  extends AbstractValidator<T>
  implements INominalRollValidation<T>
{
  async validate(dto: Partial<T>, context: Record<string, any>): Promise<string | null> {
    const retireeType = RetireeUserTypeEnum.RETIREE;
    const ecrsResponse: EcrsUserResponseDto = await this.callEcrsService(dto.rsaPin, dto.surname, retireeType);

    if (ecrsResponse.code !== 1) {
      return 'Unable to verify employee details, please try again or contact support';
    }

    context.ecrsResponse = ecrsResponse;
    return null;
  }
}
