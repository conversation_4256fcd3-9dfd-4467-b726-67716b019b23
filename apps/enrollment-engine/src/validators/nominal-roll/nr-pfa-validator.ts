import { Injectable } from '@nestjs/common';
import { AbstractValidator } from '../abstract-validator';
import { IEmployeeDataDto } from '@app/shared/dto/enrollment/interfaces/nominal-roll-employee.interface';
import { INominalRollValidation } from '@app/shared/dto/enrollment/interfaces/nominal-roll-validation.interface';

@Injectable()
export class NrPfaValidator<T extends IEmployeeDataDto>
  extends AbstractValidator<T>
  implements INominalRollValidation<T>
{
  async validate(dto: Partial<T>, context: Record<string, any>): Promise<string | null> {
    return this.validatePfa(context);
  }
}
