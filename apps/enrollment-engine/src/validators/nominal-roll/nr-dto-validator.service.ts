/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { validate } from 'class-validator';
import { PinoLogger } from 'nestjs-pino';
import { INominalRollValidation } from '@app/shared/dto/enrollment/interfaces/nominal-roll-validation.interface';
import { IEmployeeDataDto } from '@app/shared/dto/enrollment/interfaces/nominal-roll-employee.interface';

@Injectable()
export class NrDtoValidator<T extends IEmployeeDataDto> implements INominalRollValidation<T> {
  constructor(private readonly logger: PinoLogger) {}

  async validate(dto: Partial<T>, context: Record<string, any>): Promise<string | null> {
    const taskId = context.taskId;

    const errors = await validate(dto);
    if (errors.length > 0) {
      return errors.map((err) => Object.values(err.constraints || {}).join(', ')).join('; ');
    }

    return null;
  }
}
