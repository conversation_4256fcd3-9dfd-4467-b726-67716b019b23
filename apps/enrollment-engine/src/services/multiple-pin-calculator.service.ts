import { Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { MultiplePinResolutionRequestRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-req.repository';
import { MultiplePinResolutionPinRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-pin.repository';
import { TbcReconcilePayRepository } from '@app/shared/enrollment-service/repository/tbc-reconcile-pay.repository';
import { MultiplePinResolutionTransactionHistoryRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-transaction.repository';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { SettingsEnumKey } from '@app/shared/enums/SettingsEnum';

@Injectable()
export class MultiplePinCalculatorService {
  constructor(
    private readonly logger: PinoLogger,
    private readonly multiplePinRequestRepository: MultiplePinResolutionRequestRepository,
    private readonly multiplePinResolutionPinRepository: MultiplePinResolutionPinRepository,
    private readonly reconcilePayRepository: TbcReconcilePayRepository,
    private readonly transactionHistoryRepository: MultiplePinResolutionTransactionHistoryRepository,
    private readonly settingMsService: SettingMsLibService
  ) {
    this.logger.setContext(MultiplePinCalculatorService.name);
  }

  async calculateMultiplePinBalances(batchId: string): Promise<boolean> {
    this.logger.debug(`Calculating multiple PIN balances for batch ID: ${batchId}`);

    try {
      // Get settings for calculations
      const contributionThresholdPercent =
        (await this.settingMsService.getSettingInt(SettingsEnumKey.MULTIPLE_PIN_CONTRIBUTION_THRESHOLD_PERCENT)) / 100;
      const cpaInterestRatePercent =
        (await this.settingMsService.getSettingInt(SettingsEnumKey.MULTIPLE_PIN_CPA_INTEREST_RATE_PERCENT)) / 100;
      const validPinInterestRatePercent = (100 - cpaInterestRatePercent) / 100;

      const multiplePinRequest = await this.multiplePinRequestRepository.findOne({
        batchId: batchId,
      });

      if (!multiplePinRequest) {
        this.logger.error(`Multiple PIN request with batch ID ${batchId} not found`);
        return false;
      }

      // Get the valid PIN from the request
      const pins = await multiplePinRequest.pins;
      const validPin = pins.find((pin) => pin.isValid);
      if (!validPin) {
        this.logger.error(`No valid PIN found for batch ID ${batchId}`);
        return false;
      }

      // Get all invalid PINs
      const invalidPins = pins.filter((pin) => !pin.isValid);

      // Calculate totals using direct query for better performance
      const validPinTotal = await this.reconcilePayRepository.getTotalPaidByPin(validPin.pin);

      validPin.paidToDate = validPinTotal;

      await this.multiplePinResolutionPinRepository.saveEntity(validPin);

      // Calculate totals for invalid PINs
      for (const invalidPin of invalidPins) {
        const transaction = await this.transactionHistoryRepository.findOne({
          invalidPin: invalidPin.pin,
          request: { pk: multiplePinRequest.pk },
        });
        const total = transaction.pencomTotal; // + transaction.ippisTotal + transaction.shortfallTotal;

        // Calculate balances based on business rules
        if (total >= validPinTotal * contributionThresholdPercent) {
          // If invalid PIN contributions are at least threshold % of valid PIN
          invalidPin.cpaBalance =
            transaction.pencomRemitAmount + transaction.pencomInvestmentIncome * cpaInterestRatePercent;
          invalidPin.validPinBalance =
            transaction.shortfallTotal +
            transaction.ippisTotal +
            transaction.pencomInvestmentIncome * validPinInterestRatePercent;
          invalidPin.eligibilityStatus = true;
        } else {
          // If invalid PIN contributions are less than threshold % of valid PIN
          invalidPin.cpaBalance = total;
          invalidPin.validPinBalance = transaction.shortfallTotal + transaction.ippisTotal;
          invalidPin.eligibilityStatus = false;
        }
        invalidPin.paidToDate = await this.reconcilePayRepository.getTotalPaidByPin(invalidPin.pin);
        // Save the updated PIN
        await this.multiplePinResolutionPinRepository.saveEntity(invalidPin);
      }

      await this.multiplePinResolutionPinRepository.saveEntity(validPin);

      this.logger.debug(`Successfully calculated multiple PIN balances for batch ID: ${batchId}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Error calculating multiple PIN balances for batch ID ${batchId}: ${error.message}`,
        error.stack
      );
      return false;
    }
  }
}
