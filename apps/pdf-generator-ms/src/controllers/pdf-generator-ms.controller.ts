import { Body, Controller, HttpStatus, Post, Res } from '@nestjs/common';
import { PdfGeneratorMsService } from '../services/pdf-generator-ms.service';
import { Response } from 'express';
import { PdfGenerationOptions, PdfService } from '../services/pdf-ms.service';

@Controller('')
export class PdfGeneratorMsController {
  constructor(
    private readonly pdfGeneratorMsService: PdfGeneratorMsService,
    private readonly pdfService: PdfService
  ) {}

  @Post('generate2')
  async generatePdf(@Body('html') html: string, @Res() res: Response) {
    try {
      const pdfBuffer = await this.pdfGeneratorMsService.generatePdf(html);
      res.set({
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="generated.pdf"',
        'Content-Length': pdfBuffer.length,
      });
      res.send(pdfBuffer);
    } catch (error) {
      res.status(500).send({ error: 'Failed to generate PDF' });
    }
  }

  @Post('generate')
  async generatePdf2(@Body() body: PdfGenerationOptions, @Res() res: Response) {
    try {
      const pdfBuffer = await this.pdfService.generatePdf(body);

      res.set({
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename=document.pdf',
        'Content-Length': pdfBuffer.length,
      });

      res.status(HttpStatus.OK).send(pdfBuffer);
    } catch (error) {
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: 'Failed to generate PDF',
        error: error.message,
      });
    }
  }
}
