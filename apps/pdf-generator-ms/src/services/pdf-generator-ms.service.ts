import { Injectable } from '@nestjs/common';
import * as puppeteer from 'puppeteer';

@Injectable()
export class PdfGeneratorMsService {
  // async generatePdf(html: string): Promise<Buffer> {
  //   const browser = await puppeteer.launch({ args: ['--no-sandbox', '--disable-setuid-sandbox'], headless: true });
  //   try {
  //     const page = await browser.newPage();
  //     await page.setContent(html, { waitUntil: 'networkidle0' });
  //     const pdfBuffer = await page.pdf({ format: 'A4', printBackground: true });
  //     return Buffer.from(pdfBuffer);
  //   } finally {
  //     await browser.close();
  //   }
  // }

  async generatePdf(html: string): Promise<Buffer> {
    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      headless: true,
    });
    try {
      const page = await browser.newPage();
      await page.setContent(html, { waitUntil: 'networkidle0' });
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        displayHeaderFooter: true,
        footerTemplate: `
          <div style="font-size: 10px; width: 100%; text-align: center; margin: 0 20px;">
            <span>Confidential</span>
            <span style="float: right;">
              Page <span class="pageNumber"></span> of <span class="totalPages"></span>
            </span>
          </div>`,
        headerTemplate: '<div></div>',
        margin: {
          top: '10mm',
          bottom: '20mm',
          left: '10mm',
          right: '10mm',
        },
      });
      return Buffer.from(pdfBuffer);
    } finally {
      await browser.close();
    }
  }
}
