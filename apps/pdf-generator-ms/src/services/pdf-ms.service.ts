import { Injectable } from '@nestjs/common';
import * as puppeteer from 'puppeteer';

export interface PdfGenerationOptions {
  html: string;
  footer?: string;
  showPageNumber?: boolean;
  borders?: boolean;
  format?: puppeteer.PaperFormat;
  orientation?: 'portrait' | 'landscape';
  margin?: {
    top?: string;
    bottom?: string;
    left?: string;
    right?: string;
  };
}

@Injectable()
export class PdfService {
  async generatePdf(options: PdfGenerationOptions): Promise<Buffer> {
    const {
      html,
      footer,
      showPageNumber = false,
      borders = false,
      format = 'A4',
      orientation = 'portrait',
      margin = {
        top: '10mm',
        bottom: footer || showPageNumber ? '20mm' : '10mm',
        left: '10mm',
        right: '10mm',
      },
    } = options;

    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      headless: true,
    });

    try {
      const page = await browser.newPage();

      // Apply borders styling if requested
      const styledHtml = borders ? this.addBordersToContent(html) : html;

      await page.setContent(styledHtml, { waitUntil: 'networkidle0' });

      // Generate footer template based on options
      const footerTemplate = this.generateFooterTemplate(footer, showPageNumber);

      const pdfBuffer = await page.pdf({
        format,
        landscape: orientation === 'landscape',
        printBackground: true,
        displayHeaderFooter: Boolean(footer || showPageNumber),
        footerTemplate,
        headerTemplate: '<div></div>',
        margin,
      });

      return Buffer.from(pdfBuffer);
    } finally {
      await browser.close();
    }
  }

  private generateFooterTemplate(footer?: string, showPageNumber?: boolean): string {
    if (!footer && !showPageNumber) {
      return '<div></div>';
    }

    const footerText = footer || '';
    const pageNumberHtml = showPageNumber
      ? `<span style="float: right;">Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>`
      : '';

    return `
      <div style="font-size: 10px; width: 100%; text-align: center; margin: 0 20px; display: flex; justify-content: space-between; align-items: center;">
        <span>${footerText}</span>
        ${pageNumberHtml}
      </div>
    `;
  }

  private addBordersToContent(html: string): string {
    // Add CSS for borders - you can customize this styling as needed
    const borderStyles = `
      <style>
        body {
          border: 2px solid #333;
          padding: 20px;
          margin: 0;
        }
        
        /* Add borders to common elements */
        table {
          border-collapse: collapse;
          border: 1px solid #333;
        }
        
        table th, table td {
          border: 1px solid #333;
          padding: 8px;
        }
        
        .bordered-section {
          border: 1px solid #333;
          padding: 10px;
          margin: 10px 0;
        }
        
        /* You can add more border styles as needed */
        h1, h2, h3, h4, h5, h6 {
          border-bottom: 1px solid #333;
          padding-bottom: 5px;
        }
      </style>
    `;

    // Insert the styles into the HTML
    if (html.includes('<head>')) {
      return html.replace('<head>', `<head>${borderStyles}`);
    } else if (html.includes('<html>')) {
      return html.replace('<html>', `<html><head>${borderStyles}</head>`);
    } else {
      return `<html><head>${borderStyles}</head><body>${html}</body></html>`;
    }
  }

  // Alternative method for more advanced border customization
  async generatePdfWithAdvancedOptions(
    options: PdfGenerationOptions & {
      borderColor?: string;
      borderWidth?: string;
      borderStyle?: 'solid' | 'dashed' | 'dotted';
    }
  ): Promise<Buffer> {
    const { borderColor = '#333', borderWidth = '1px', borderStyle = 'solid', ...baseOptions } = options;

    if (baseOptions.borders) {
      // Customize border appearance
      const customBorderHtml = this.addCustomBordersToContent(baseOptions.html, borderColor, borderWidth, borderStyle);
      baseOptions.html = customBorderHtml;
    }

    return this.generatePdf(baseOptions);
  }

  private addCustomBordersToContent(html: string, color: string, width: string, style: string): string {
    const borderStyles = `
      <style>
        body {
          border: ${width} ${style} ${color};
          padding: 20px;
          margin: 0;
        }
        
        table {
          border-collapse: collapse;
          border: ${width} ${style} ${color};
        }
        
        table th, table td {
          border: ${width} ${style} ${color};
          padding: 8px;
        }
        
        .bordered-section {
          border: ${width} ${style} ${color};
          padding: 10px;
          margin: 10px 0;
        }
        
        h1, h2, h3, h4, h5, h6 {
          border-bottom: ${width} ${style} ${color};
          padding-bottom: 5px;
        }
      </style>
    `;

    if (html.includes('<head>')) {
      return html.replace('<head>', `<head>${borderStyles}`);
    } else if (html.includes('<html>')) {
      return html.replace('<html>', `<html><head>${borderStyles}</head>`);
    } else {
      return `<html><head>${borderStyles}</head><body>${html}</body></html>`;
    }
  }
}
