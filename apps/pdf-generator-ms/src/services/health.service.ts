import { Injectable } from '@nestjs/common';
import { HealthCheck, HealthCheckService, HealthCheckStatus, HealthIndicatorResult } from '@nestjs/terminus';
import { Public } from '@app/shared/decorators/public.decorator';
import path from 'path';
import * as fs from 'fs';

@Injectable()
export class HealthService {
  constructor(private health: HealthCheckService) {}

  @Public()
  @HealthCheck()
  async checkHealth() {
    const checks = [async () => this.checkPuppeteer()];

    try {
      return await this.health.check(checks);
    } catch (error: any) {
      return {
        status: (error.response?.status as HealthCheckStatus) ?? ('error' as HealthCheckStatus),
        info: error.response?.info || {},
        error: error.response?.error || {},
        details: error.response?.details || {},
      };
    }
  }

  private async checkPuppeteer(): Promise<HealthIndicatorResult> {
    const chromePath = path.join('/root/.cache/puppeteer', 'chrome');

    const isChromePresent = fs.existsSync(chromePath);
    return {
      puppeteer: {
        status: isChromePresent ? 'up' : 'down',
        message: isChromePresent ? 'Puppeteer Chrome binary is available.' : 'Puppeteer Chrome binary is missing.',
        pathChecked: chromePath,
      },
    };
  }
}
