import { Module } from '@nestjs/common';
import { PdfGeneratorMsController } from './controllers/pdf-generator-ms.controller';
import { PdfGeneratorMsService } from './services/pdf-generator-ms.service';
import { PdfService } from './services/pdf-ms.service';
import { ConfigModule } from '@nestjs/config';
import { HealthController } from './controllers/health.controller';
import { HealthService } from './services/health.service';
import { TerminusModule } from '@nestjs/terminus';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TerminusModule,
  ],
  controllers: [PdfGeneratorMsController, HealthController],
  providers: [PdfGeneratorMsService, PdfService, HealthService],
})
export class PdfGeneratorMsModule {}
