import { NestFactory } from '@nestjs/core';
import { PdfGeneratorMsModule } from './pdf-generator-ms.module';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as bodyParser from 'body-parser';

async function bootstrap() {
  const app = await NestFactory.create(PdfGeneratorMsModule);
  app.setGlobalPrefix('pdf-generator');
  app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
  app.use(bodyParser.json({ limit: '50mb' }));
  app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

  const configService = app.get(ConfigService);
  const port = configService.get('PDF_SERVICE_PORT') || 3023;

  const config = new DocumentBuilder()
    .setTitle('PDF Service')
    .setDescription('Documentation API for PDF Service')
    .setVersion(configService.get('PDF_SERVICE_VERSION') || '1.0.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('swagger-api', app, document);

  app.enableCors();
  await app.listen(port);
  console.log(`PDF Generator service running on port ${port}`);
}
bootstrap();
