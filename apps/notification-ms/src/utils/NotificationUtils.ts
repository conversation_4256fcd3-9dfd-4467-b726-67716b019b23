import { format } from 'date-fns';

export class NotificationUtils {
  generateDigits(length: number): string {
    return Array.from({ length })
      .map(() => Math.floor(Math.random() * 10).toString())
      .join('');
  }

  generateAlphabets(length: number): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    return Array.from({ length })
      .map(() => characters.charAt(Math.floor(Math.random() * characters.length)))
      .join('');
  }

  generateToken(length: number): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    return Array.from({ length })
      .map(() => characters.charAt(Math.floor(Math.random() * characters.length)))
      .join('');
  }

  formatDate(date: Date = new Date()): string {
    return format(date, 'dd-MM-yyyy');
  }
}
