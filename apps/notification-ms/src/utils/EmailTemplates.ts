/* eslint-disable */
import * as path from 'path';

const EMAIL_TEMPLATES_DIR = path.join(process.cwd(), 'apps', 'notification-ms', 'src', 'emailtemplates');

const createTemplate = (subject: string, fileName: string) => ({
  emailSubject: subject,
  messagePath: path.join(EMAIL_TEMPLATES_DIR, fileName),
});

export const EmailTemplates = {
  VIEWS_INCLUDE_PATH: createTemplate('PATH', 'includes'),
  RESET_PASSWORD: createTemplate('Password Reset', 'reset-password.ejs'),
  REGISTER: createTemplate('Welcome to COBRA!', 'register.ejs'),
  CHANGE_PASSWORD: createTemplate('Your Password Has Been Successfully Changed', 'change-password.ejs'),
  EXTERNAL_USER_CREATION: createTemplate('Welcome to COBRA! Here Are Your Login Details', 'external-user-creation.ejs'),
  ROLE_CHANGE_APPROVAL: createTemplate('Role Change Request Review Required', 'role-change-approval.ejs'),
  WHITELIST_APPROVAL: createTemplate('Whitelist Request Approval Required', 'whitelist-approval.ejs'),
  ANSWERED_SECURITY_QUESTIONS: createTemplate('Securing your account on COBRA!', 'answered-security-questions.ejs'),
  ROLE_ADMIN_UPGRADE: createTemplate('Your Account Has Been Upgraded to Admin', 'role-upgraded-user.ejs'),
  WHITELIST_REQUEST_CONFIRMATION: createTemplate(
    'Whitelisting Request Submitted',
    'whitelist-request-confirmation.ejs'
  ),
  ROLE_CHANGE_REQUEST_ACKNOWLEDGMENT: createTemplate(
    'Your Request Has Been Submitted for Review',
    'role-change-request-acknowledgment.ejs'
  ),
  ROLE_CHANGE_STATUS_APPROVAL: createTemplate('Role Change Request Approved', 'role-change-status-approval.ejs'),
  ROLE_CHANGE_STATUS_REJECTION: createTemplate('Role Change Request Rejected', 'role-change-status-rejection.ejs'),
  WHITELIST_REQUEST_USER_APPROVED: createTemplate(
    'Your Account Has Been Whitelisted',
    'whitelist-request-user-approved.ejs'
  ),
  WHITELIST_REQUEST_USER_REJECTED: createTemplate('Whitelisting Request Denied', 'whitelist-request-user-rejected.ejs'),
  WHITELIST_REQUEST_APPROVED: createTemplate('Whitelisting Request Approved', 'whitelist-request-approved.ejs'),
  WHITELIST_REQUEST_REJECTED: createTemplate('Whitelisting Request Denied', 'whitelist-request-rejected.ejs'),
  SUPERVISOR_REVIEW_REQUEST: createTemplate('Record Available for Your Review', 'pre-enrollment-notification.ejs'),
  RETIREE_ENROLMENT_NOTIFICATION: createTemplate(
    'Your Record Has Been Validated and Submitted for Further Review',
    'exit-retiree-enrolment-notification.ejs'
  ),
  VALIDATOR_REVIEW_REQUEST: createTemplate(
    'Record Available for Your Review',
    'exit-enrolment-validator-notification.ejs'
  ),
  AUDIT_VALIDATOR_REVIEW_REQUEST: createTemplate(
    'Record Available for Your Review',
    'exit-enrolment-audit-validator-notification.ejs'
  ),
  AUDIT_SUPERVISOR_REVIEW_REQUEST: createTemplate(
    'Record Available for Your Review',
    'exit-enrolment-audit-supervisor-notification.ejs'
  ),
  AUDIT_VALIDATOR_NR_REVIEW_REQUEST: createTemplate(
    'Record Available for Your Review',
    'nr-contribution-audit-validator-notification.ejs'
  ),
  AUDIT_SUPERVISOR_NR_REVIEW_REQUEST: createTemplate(
    'Record Available for Your Review',
    'nr-contribution-audit-supervisor-notification.ejs'
  ),
  VALIDATOR_REQUEST_REJECTION: createTemplate(
    'Action Required: Record Returned by PENCOM for Corrections',
    'exit-validator-request-rejection-notification.ejs'
  ),
  VALIDATOR_REQUEST_REJECTION_RETIREE_NOTIFICATION: createTemplate(
    'Action Required: Record Returned by PENCOM for Corrections',
    'exit-validator-request-rejection-retiree-notification.ejs'
  ),
  AUDIT_VALIDATOR_REQUEST_REJECTION: createTemplate(
    'Rejection of Retiree Record – Action Required',
    'exit-audit-validator-request-rejection-notification.ejs'
  ),
  AUDIT_VALIDATOR_CONTRIBUTION_REQUEST_REJECTION: createTemplate(
    'Rejection of Retiree Record – Action Required',
    'nominal-roll-audit-validator-contribution-request-rejection-notification.ejs'
  ),
  NDMD_RSA_PIN_REVIEW: createTemplate('Request to Review Validity of RSA PIN', 'ndmd-rsa-pin-review.ejs'),
  NDMD_RSA_PIN_REVIEW_COMPLETED: createTemplate(
    'PIN Validity Review Completed Successfully',
    'ndmd-rsa-pin-review-completed.ejs'
  ),
  NDMD_RSA_PIN_REVIEW_REJECTED: createTemplate(
    'PIN Validity Review Request Rejected',
    'ndmd-rsa-pin-review-rejected.ejs'
  ),
  AMENDMENT_REQUEST_REVIEW: createTemplate('Amendment Request for RSA PIN', 'amendment-request-review.ejs'),
  AMENDMENT_REQUEST_CANCELLATION_RETIREE: createTemplate(
    'Amendment Request Cancelled',
    'amendment-request-cancellation-retiree.ejs'
  ),
  AMENDMENT_REQUEST_CANCELLATION_PENCOM: createTemplate(
    'Amendment Request Cancelled',
    'amendment-request-cancellation-pencom.ejs'
  ),
  AMENDMENT_REQUEST_CANCELLATION_PFA: createTemplate(
    'Amendment Request Cancelled',
    'amendment-request-cancellation-pfa.ejs'
  ),
  AMENDMENT_REQUEST_APPROVAL_RETIREE: createTemplate(
    'Amendment Request Approved for Your Record',
    'amendment-request-approval-retiree.ejs'
  ),
  AMENDMENT_REQUEST_APPROVAL_PFA: createTemplate('Amendment Request Approved', 'amendment-request-approval-pfa.ejs'),
  AMENDMENT_REQUEST_REJECTION_RETIREE: createTemplate(
    'Amendment Request Rejected for Your Record',
    'amendment-request-rejection-retiree.ejs'
  ),
  AMENDMENT_REQUEST_REJECTION_PFA: createTemplate('Amendment Request Rejected', 'amendment-request-rejection-pfa.ejs'),
  AMENDMENT_REQUEST_INITIATED_PFA: createTemplate(
    'Amendment Request Initiated by Your PFA',
    'amendment-request-initiated-pfa.ejs'
  ),
  AMENDMENT_REQUEST_INITIATED_RETIREE: createTemplate(
    'Amendment Request Initiated for Your Record',
    'amendment-request-initiated-retiree.ejs'
  ),
  TRANSACTION_HISTORY_MEMO_NOTIFICATION: createTemplate(
    'Action Required – Memo Requesting Transaction History Documents',
    'transaction-history-memo-notification.ejs'
  ),
  MULTIPLE_PIN_RESOLUTION_COMPUTATION: createTemplate(
    'Multiple RSA PINS Computation Completed- System',
    'multiple-rsa-pins-computation-completed.ejs'
  ),
  MPR_PAYMENT_REQUEST_PFA: createTemplate(
    'Request to Process Payment for Invalid RSA PIN',
    'mpr-payment-request-pfa.ejs'
  ),
  MULTIPLE_PIN_VALID_PIN_PAYMENT_CONFIRMATION: createTemplate(
    'Payment Confirmation for Invalid RSA PIN',
    'mpr-valid-pin-payment-confirmation.ejs'
  ),
  MULTIPLE_PIN_PAYMENT_CONFIRMATION_PENCOM: createTemplate(
    'Payment Receipt Confirmation for Batch ID',
    'multiple-pin-payment-confirmation-pencom.ejs'
  ),
  EXCO_APPROVAL_REQUEST: createTemplate('Request for Exco Approval', 'exco-approval-request-notification.ejs'),
  EXCO_APPROVAL_GRANTED: createTemplate('ExCo Approval Granted', 'exco-approval-granted-notification.ejs'),
  ACCOUNT_PAYMENT_REQUESTED: createTemplate(
    'Remittance for Approved Payment',
    'account-payment-request-notification.ejs'
  ),
  ACCOUNT_PAYMENT_CONFIRMED: createTemplate('ACCOUNT Payment Confirmed', 'account-payment-confirmed-notification.ejs'),
  PFA_PAYMENT_CONFIRMED: createTemplate('PFA Payment Confirmed', 'pfa-payment-confirmed-notification.ejs'),
  PFA_PAYMENT_MADE: createTemplate('Payment Made for Batch', 'pfa-payment-made-notification.ejs'),
  PFC_PAYMENT_MADE: createTemplate('Payment Made for Batch', 'pfc-payment-made-notification.ejs'),
  PFA_NON_PAYMENT_CONFIRMATION: createTemplate(
    'Payment Confirmation Required',
    'pfa-payment-confirmation-required-notification.ejs'
  ),
  HOD_CERTIFICATION_NOTIFICATION: createTemplate(
    'Record Certification',
    'exit-enrolment-hod-certification-notification.ejs'
  ),
  MULTIPLE_PIN_TRANSACTION_HISTORY_REJECTION: createTemplate(
    'Transaction History Rejection',
    'mpr-transaction-history-rejection.ejs'
  ),
};
