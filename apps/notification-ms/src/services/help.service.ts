import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HelpTemplate } from '../entities/help-template.entity';
import { HelpResponseDTO, HelpItemDTO } from '@app/shared/dto/help/help-response.dto';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { ResponseCodeEnum } from '@app/shared/enums';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { CreateHelpTemplateDTO } from '@app/shared/dto/help/create-help-template.dto';

@Injectable()
export class HelpService {
  constructor(
    @InjectRepository(HelpTemplate)
    private readonly helpRepository: Repository<HelpTemplate>
  ) {}

  async getHelpContentForUserType(userTypeCode: UserTypeEnum): Promise<HelpResponseDTO> {
    const helpItems = await this.helpRepository.find({
      where: {
        userType: userTypeCode,
        active: true,
        deleted: false,
      },
    });

    const response = new HelpResponseDTO();
    response.code = ResponseCodeEnum.SUCCESS;
    response.description = 'Help resources retrieved successfully';
    response.content = {};

    // Group items by section
    helpItems.forEach((item) => {
      if (!response.content[item.section]) {
        response.content[item.section] = [];
      }

      const helpItemDTO: HelpItemDTO = {
        title: item.title,
        description: item.description,
        type: item.type,
        link: item.link,
      };

      response.content[item.section].push(helpItemDTO);
    });

    return response;
  }

  async createHelp(createHelpDto: CreateHelpTemplateDTO): Promise<BaseResponseWithContentNoPagination<HelpTemplate>> {
    const help = new HelpTemplate({});
    Object.assign(help, {
      title: createHelpDto.title,
      description: createHelpDto.description,
      section: createHelpDto.section,
      type: createHelpDto.type,
      link: createHelpDto.link,
      userType: createHelpDto.userType,
    });

    const savedHelp = await this.helpRepository.save(help);

    const response = new BaseResponseWithContentNoPagination<HelpTemplate>(ResponseCodeEnum.SUCCESS);
    response.content = savedHelp;
    response.setDescription('Help item created successfully');
    return response;
  }
}
