import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { MailAttachment } from '../entities/mail-attachment.entity';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class MailAttachmentRepository extends AbstractRepository<MailAttachment> {
  constructor(
    @InjectRepository(MailAttachment)
    private mailAttachmentRepository: Repository<MailAttachment>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(mailAttachmentRepository, entityManager);
  }
}
