import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { MailHistory } from '../entities/mail-history.entity';
import { PinoLogger } from 'nestjs-pino';

import { ResponseCodeEnum } from '@app/shared/enums';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import {
  BaseResponseWithContentNoPagination,
  BaseResponseWithNoCountInfo,
} from '@app/shared/dto/response/base-response-with-content';
import { MailAttachment } from 'apps/notification-ms/src/entities/mail-attachment.entity';

@Injectable()
export class MailHistoryRepository extends AbstractRepository<MailHistory> {
  constructor(
    @InjectRepository(MailHistory)
    private readonly mailHistoryRepository: Repository<MailHistory>,
    readonly logger: PinoLogger,
    entityManager: EntityManager
  ) {
    super(mailHistoryRepository, entityManager);
  }

  async saveMailHistory(
    toEmail: string[],
    subject: string,
    message: string,
    status: string,
    attachments?: { filename: string; content: any; contentType: string }[]
  ): Promise<MailHistory> {
    const mailHistory = new MailHistory({
      receiver: toEmail.join(', '),
      subject: subject,
      message: message,
      messageStatus: status,
    });

    if (attachments && attachments.length > 0) {
      mailHistory.attachments = attachments.map((attachment) => {
        // Handle buffer content conversion
        let bufferContent: Buffer;
        if (
          attachment.content &&
          typeof attachment.content === 'object' &&
          attachment.content.type === 'Buffer' &&
          Array.isArray(attachment.content.data)
        ) {
          bufferContent = Buffer.from(attachment.content.data);
        } else if (typeof attachment.content === 'string') {
          bufferContent = Buffer.from(attachment.content, 'base64');
        } else {
          bufferContent = attachment.content;
        }

        return new MailAttachment({
          filename: attachment.filename,
          contentType: attachment.contentType || 'application/pdf',
          content: bufferContent,
          mailHistory: mailHistory,
        });
      });
    }

    return await this.saveEntity(mailHistory);
  }

  async getDataTable(searchDto: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<any>> {
    const queryBuilder = this.mailHistoryRepository.createQueryBuilder('mail_history');

    // Select specific fields, excluding the message field
    queryBuilder.select([
      'mail_history.pk',
      'mail_history.receiver',
      'mail_history.subject',
      'mail_history.messageStatus',
      'mail_history.createDate',
      'mail_history.lastModified',
    ]);

    // Apply filters if provided
    if (searchDto.filters) {
      if (searchDto.filters.receiver) {
        queryBuilder.andWhere('mail_history.receiver LIKE :receiver', {
          receiver: `%${searchDto.filters.receiver}%`,
        });
      }

      if (searchDto.filters.subject) {
        queryBuilder.andWhere('mail_history.subject LIKE :subject', {
          subject: `%${searchDto.filters.subject}%`,
        });
      }

      if (searchDto.filters.messageStatus) {
        queryBuilder.andWhere('mail_history.messageStatus = :messageStatus', {
          messageStatus: searchDto.filters.messageStatus,
        });
      }
    }

    const { page = 1, limit = 10 } = searchDto;
    const pageSize = limit;

    // Apply sorting
    queryBuilder.orderBy(`mail_history.createDate`, 'DESC');

    // Apply pagination
    queryBuilder.skip((page - 1) * pageSize).take(pageSize);

    const [result, total] = await queryBuilder.getManyAndCount();

    const response = new BaseResponseWithNoCountInfo<any>(ResponseCodeEnum.SUCCESS);
    response.content = result;
    response.hasPrevious = page > 1;
    response.hasNext = page < Math.ceil(total / pageSize);

    return response;
  }

  async findAllMailInPks(pk: number[]) {
    return await this.mailHistoryRepository
      .createQueryBuilder('mail_history')
      .where('mail_history.pk IN (:...pks)', { pks: pk })
      .getMany();
  }
}
