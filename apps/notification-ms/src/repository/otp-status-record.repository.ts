import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { OtpStatusRecord } from '../entities/otp-status-record.entity';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class OtpStatusRecordRepository extends AbstractRepository<OtpStatusRecord> {
  constructor(
    @InjectRepository(OtpStatusRecord)
    otpStatusRecordRepository: Repository<OtpStatusRecord>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(otpStatusRecordRepository, entityManager);
  }
}
