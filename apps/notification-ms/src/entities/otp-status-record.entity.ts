import { Column, CreateDateColumn, Entity } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';

@Entity('OTP_STATUS_RECORD')
export class OtpStatusRecord extends AbstractEntity<OtpStatusRecord> {
  @Column({ name: 'OTP', nullable: false })
  otp: string;

  @Column({
    name: 'OTP_USED',
    type: 'number',
    precision: 1,
    nullable: false,
    default: 0,
    transformer: {
      // Transform database value (0/1) to boolean (false/true)
      from: (value: number): boolean => value === 1,
      // Transform boolean (false/true) to database value (0/1)
      to: (value: boolean): number => (value ? 1 : 0),
    },
  })
  otpUsed: boolean;

  @Column({ name: 'PHONE-NUMBER', nullable: true })
  phoneNumber: string;

  @Column({ name: 'OTP-STATUS-RECORD-TYPE-ENUM', nullable: false })
  otpStatusRecordTypeEnum: string;

  @CreateDateColumn({ name: 'TIME-GENERATED', nullable: false })
  timeGenerated: Date;

  @CreateDateColumn({ name: 'TIME-USED', nullable: true })
  timeUsed: Date;

  @Column({ name: 'EXPIRATION-TIME', nullable: false })
  expirationTime: Date;

  @Column({ name: 'EMAIL', nullable: false })
  email: string;
}
