import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { Column, Entity } from 'typeorm';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { HelpResourceType } from '@app/shared/dto/help/resource-type.enum';

@Entity({ name: 'HELP_TEMPLATE' })
export class HelpTemplate extends AbstractEntity<HelpTemplate> {
  @Column({ name: 'TITLE', nullable: false, length: 255 })
  title: string;

  @Column({ name: 'DESCRIPTION', type: 'clob', nullable: true })
  description: string;

  @Column({ name: 'TYPE', type: 'varchar', length: 20, nullable: false })
  type: HelpResourceType;

  @Column({ name: 'SECTION', type: 'varchar', length: 100, nullable: false })
  section: string;

  @Column({ name: 'USER_TYPE', type: 'varchar', length: 20, nullable: false })
  userType: UserTypeEnum;

  @Column({ name: 'LINK', nullable: false, length: 2000 })
  link: string;
}
