import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { MailHistory } from './mail-history.entity';

@Entity('MAIL_ATTACHMENT')
export class MailAttachment extends AbstractEntity<MailAttachment> {
  @ManyToOne(() => MailHistory, (mailHistory) => mailHistory.attachments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'MAIL_HISTORY_ID' })
  mailHistory: MailHistory;

  @Column({ name: 'FILENAME', nullable: false, length: 512 })
  filename: string;

  @Column({ name: 'CONTENT_TYPE', nullable: false, length: 128 })
  contentType: string;

  @Column({ name: 'CONTENT', type: 'blob', nullable: false })
  content: Buffer;
}
