import { Column, Entity, OneToMany } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';
import { MailAttachment } from './mail-attachment.entity';

@Entity('MAIL_HISTORY')
export class MailHistory extends AbstractEntity<MailHistory> {
  @Column({ name: 'RECEIVER', nullable: false, length: 512 })
  receiver: string;

  @Column({ name: 'SUBJECT', nullable: false, length: 512 })
  subject: string;

  @Column({ name: 'MESSAGE', nullable: false, type: 'clob' })
  message: string;

  @Column({ name: 'MESSAGE_STATUS', nullable: false, length: 512 })
  messageStatus: string;

  @OneToMany(() => MailAttachment, (attachment) => attachment.mailHistory, { lazy: true, cascade: true })
  attachments: MailAttachment[];
}
