<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <style>
        body, table, td, a {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
        }
        body {
            margin: 0;
            padding: 0;
            width: 100% !important;
            line-height: 100% !important;
            background: white;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
            background: #F9F9F9;
        }
        .header {
            background: #2d572c;
            color: white;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
            min-height: 60px;
        }
        .header img {
            width: 100%;
            height: 100%;
        }
        .content {
            font-size: 16px;
            line-height: 1.6;
            text-align: left;
            padding: 20px;
        }
        .code {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            background: #f8f8f8;
            padding: 15px;
            margin: 20px 0;
        }
        .footer {
            font-size: 12px;
            color: #555555;
            text-align: center;
            padding: 20px;
        }
        .footer a {
            color: #007BFF;
            text-decoration: none;
        }
        .footer a:hover {
            text-decoration: underline;
        }
        @media only screen and (max-width: 600px) {
            .header {
                font-size: 16px;
            }
            .content {
                font-size: 14px;
            }
            .footer {
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
<center>
    <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">
        <tr>
            <td align="center" valign="top">
                <div class="header">
                    <img src=' <%= notificationBaseUrl %>/notification/images/cobra-email-header.jpg'
                         alt="Contribution & Bond Redemption Application">
                </div>
                <div class='email-container'>
