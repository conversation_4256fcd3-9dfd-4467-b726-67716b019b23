import { Controller } from '@nestjs/common';
import { NotificationMsService } from '../notification-ms.service';
import { SendNotificationDto } from '@app/shared/dto/notification/send-notification.dto';
import { GenerateCodeDto } from '@app/shared/dto/notification/generate-code.dto';
import { VerifyOtpDto } from '@app/shared/dto/notification/verify-otp.dto';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { NotificationServiceClientConstant } from '@app/shared/constants/notification-service-client.constant';
import { SendNotificationTemplateDto } from '@app/shared/dto/notification/send-notification-template.dto';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';

@Controller()
export class NotificationMsMessageController {
  constructor(private readonly notificationMsService: NotificationMsService) {}

  @MessagePattern(NotificationServiceClientConstant.SEND_NOTIFICATION)
  async sendNotificationTcp(@Payload() sendNotificationDto: SendNotificationDto): Promise<BaseResponseDto> {
    return this.notificationMsService.sendNotification(sendNotificationDto);
  }

  @MessagePattern(NotificationServiceClientConstant.REQUEST_OTP)
  async generateAndSendCodeTcp(@Payload() generateCodeDto: GenerateCodeDto): Promise<BaseResponseDto> {
    return this.notificationMsService.generateAndSendCode(generateCodeDto);
  }

  @MessagePattern(NotificationServiceClientConstant.VERIFY_OTP)
  async verifyOtpTcp(@Payload() verifyOtpDto: VerifyOtpDto): Promise<BaseResponseDto> {
    return this.notificationMsService.verifyOtp(verifyOtpDto);
  }

  @MessagePattern(NotificationServiceClientConstant.VERIFY_USED_OTP)
  async verifyUsedOtp(@Payload() verifyOtpDto: VerifyOtpDto): Promise<BaseResponseDto> {
    return this.notificationMsService.verifyUsedOtp(verifyOtpDto);
  }

  @MessagePattern(NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE)
  async sendEmailNotificationTemplate(
    @Payload() sendNotificationTemplateDto: SendNotificationTemplateDto
  ): Promise<BaseResponseDto> {
    return this.notificationMsService.sendEmailNotificationTemplate(sendNotificationTemplateDto);
  }
}
