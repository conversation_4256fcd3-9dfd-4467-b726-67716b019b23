import { Body, Controller, Get, Post, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { HelpService } from '../services/help.service';
import { HelpResponseDTO } from '@app/shared/dto/help/help-response.dto';
import { CreateHelpTemplateDTO } from '@app/shared/dto/help/create-help-template.dto';
import { HelpTemplate } from '../entities/help-template.entity';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';

@ApiTags('Help Resources')
@ApiBearerAuth()
@Controller('help')
export class HelpController {
  constructor(private readonly helpService: HelpService) {}

  @Get()
  @ApiOperation({ summary: 'Get help resources by user type' })
  @ApiResponse({
    status: 200,
    description: 'Help resources retrieved successfully',
    type: HelpResponseDTO,
  })
  async getHelpResources(@Req() req: ICustomRequest): Promise<HelpResponseDTO> {
    const userType = req.user.userType as UserTypeEnum;
    return await this.helpService.getHelpContentForUserType(userType);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new help item' })
  @ApiBody({ type: CreateHelpTemplateDTO })
  @ApiResponse({
    status: 201,
    description: 'Help item created successfully',
    type: BaseResponseWithContentNoPagination,
  })
  async createHelp(
    @Body() createHelpDto: CreateHelpTemplateDTO
  ): Promise<BaseResponseWithContentNoPagination<HelpTemplate>> {
    return await this.helpService.createHelp(createHelpDto);
  }
}
