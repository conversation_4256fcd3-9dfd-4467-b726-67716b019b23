import { <PERSON>, Get, Param, Res } from '@nestjs/common';
import { Response } from 'express';
import path from 'path';
import { Public } from '@app/shared/decorators/public.decorator';

@Controller('images')
export class NotificationImagesController {
  @Public()
  @Get(':imageName')
  getImage(@Param('imageName') imageName: string, @Res() res: Response) {
    const imagePath = path.join(process.cwd(), 'apps', 'notification-ms', 'src', 'public', 'images', imageName);

    return res.sendFile(imagePath);
  }
}
