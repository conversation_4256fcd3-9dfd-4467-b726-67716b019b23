import { Body, Controller, Post } from '@nestjs/common';
import { NotificationMsService } from '../notification-ms.service';
import { SendNotificationDto } from '@app/shared/dto/notification/send-notification.dto';
import { GenerateCodeDto } from '@app/shared/dto/notification/generate-code.dto';
import { VerifyOtpDto } from '@app/shared/dto/notification/verify-otp.dto';
import { ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { Public } from '@app/shared/decorators/public.decorator';

@Controller('')
export class NotificationMsController {
  constructor(private readonly notificationMsService: NotificationMsService) {}

  @ApiOperation({
    description: 'Sends notifications to the specified email',
  })
  @Post('/send-notification')
  async sendNotification(@Body() sendNotificationDto: SendNotificationDto): Promise<BaseResponseDto> {
    return this.notificationMsService.sendNotification(sendNotificationDto);
  }

  @Public()
  @ApiOperation({
    description: 'Generates OTP based on process type',
  })
  @Post('/request-otp')
  async generateAndSendCode(@Body() generateCodeDto: GenerateCodeDto): Promise<BaseResponseDto> {
    return this.notificationMsService.generateAndSendCode(generateCodeDto);
  }

  @ApiOperation({ summary: 'Validates the generated OTP' })
  @ApiOkResponse({
    description: 'Validates the generated OTP',
  })
  @Post('/verify-otp')
  verifyOtp(@Body() verifyOtpDto: VerifyOtpDto): Promise<BaseResponseDto> {
    return this.notificationMsService.verifyOtp(verifyOtpDto);
  }
}
