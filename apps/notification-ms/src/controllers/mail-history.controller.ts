import { Body, Controller, Post, UseInterceptors } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { MailHistoryRepository } from '../repository/mail-history.repository';
import { NotificationMsService } from '../notification-ms.service';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { BaseResponseWithNoCountInfo } from '@app/shared/dto/response/base-response-with-content';
import { ResendEmailDto } from '@app/shared/dto/notification/email-notification.dto';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { Audit } from '@app/shared/decorators/audit.decorator';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';

@ApiBearerAuth()
@UseInterceptors(AuditInterceptor)
@ApiTags('Mail History')
@Controller('mail-history')
export class MailHistoryController {
  constructor(
    private readonly mailHistoryRepository: MailHistoryRepository,
    private readonly notificationMsService: NotificationMsService
  ) {}

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.MAIL_HISTORY_PAGE)
  @Post()
  @ApiOperation({ summary: 'Get mail history entries sorted by date' })
  @ApiResponse({
    status: 200,
    description: 'Mail history entries retrieved successfully',
  })
  async getMailHistory(@Body() searchDto: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<any>> {
    return this.mailHistoryRepository.getDataTable(searchDto);
  }
  @Audit(AuditEventTypeEnum.MAIL_HISTORY_RESEND, (req) => {
    return {
      extra: {
        pk: req.body.pk,
      },
    };
  })
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.MAIL_HISTORY_RESEND)
  @Post('resend')
  @ApiOperation({ summary: 'Resend an email from history' })
  @ApiResponse({
    status: 200,
    description: 'Email resent successfully',
    type: BaseResponseDto,
  })
  async resendEmail(@Body() resendDto: ResendEmailDto): Promise<BaseResponseDto> {
    return this.notificationMsService.resendEmail(resendDto);
  }
}
