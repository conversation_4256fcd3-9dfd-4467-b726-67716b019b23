import { Module } from '@nestjs/common';
import { NotificationMsController } from './controllers/notification-ms.controller';
import { NotificationMsService } from './notification-ms.service';
import { DatabaseModule, LoggerModule } from '@app/shared';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisCacheModule } from '@app/shared/cache';
import { SettingMsLibModule } from '@app/shared/setting-service/setting-ms-lib.module';
import { MailerModule } from '@nestjs-modules/mailer';
import { MailHistoryRepository } from './repository/mail-history.repository';
import { MailHistory } from './entities/mail-history.entity';
import { NotificationUtils } from './utils/NotificationUtils';
import { OtpStatusRecord } from './entities/otp-status-record.entity';
import { OtpStatusRecordRepository } from './repository/otp-status-record.repository';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { HealthModule } from '@app/shared/health';
import { APP_GUARD } from '@nestjs/core';
import { AuthGuard } from '@app/shared/auth/guards/auth.guard';
import { AuthModule } from '@app/shared/auth/auth.module';
import { NOTIFICATIONS_SERVICE } from '@app/shared/constants';
import { NotificationMsMessageController } from './controllers/notification-ms-message.controller';
import { NotificationImagesController } from './controllers/notification-images-controller';
import { HelpController } from './controllers/help.controller';
import { HelpService } from './services/help.service';
import { HelpTemplate } from './entities/help-template.entity';
import { MailHistoryController } from 'apps/notification-ms/src/controllers/mail-history.controller';
import { MailAttachment } from './entities/mail-attachment.entity';
import { MailAttachmentRepository } from './repository/mail-attachment.repository';
import { AuditLibModule } from '@app/shared/audit-service/audit-lib.module';

@Module({
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([MailHistory, OtpStatusRecord, HelpTemplate, MailAttachment]),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    LoggerModule.forRoot(NOTIFICATIONS_SERVICE),
    RedisCacheModule.register(),
    HealthModule.registerAsync([]),
    NotificationMsModule,
    SettingMsLibModule,
    AuthModule,
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        transport: {
          host: configService.get('SMTP_HOST'),
          port: configService.get('SMTP_PORT'),
          auth: {
            user: configService.get('SMTP_USER'),
            pass: configService.get('SMTP_PASS'),
          },
        },
      }),
      inject: [ConfigService],
    }),
    AuditLibModule,
  ],
  controllers: [
    NotificationMsController,
    NotificationMsMessageController,
    NotificationImagesController,
    HelpController,
    MailHistoryController,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    NotificationMsService,
    SettingMsLibService,
    MailHistoryRepository,
    OtpStatusRecordRepository,
    NotificationUtils,
    HelpService,
    MailAttachmentRepository,
  ],
  exports: [NotificationMsService],
})
export class NotificationMsModule {}
