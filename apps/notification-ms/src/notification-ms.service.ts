/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { SendNotificationDto } from '@app/shared/dto/notification/send-notification.dto';
import { MailerService } from '@nestjs-modules/mailer';
import { MailHistoryRepository } from './repository/mail-history.repository';
import { ResponseCodeEnum, SettingsEnumKey } from '@app/shared/enums';
import { PinoLogger } from 'nestjs-pino';
import { GenerateCodeDto } from '@app/shared/dto/notification/generate-code.dto';
import { NotificationUtils } from './utils/NotificationUtils';
import { OtpStatusRecord } from './entities/otp-status-record.entity';
import { OtpStatusRecordRepository } from './repository/otp-status-record.repository';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import * as fs from 'fs';
import { EmailTemplates } from './utils/EmailTemplates';
import { InternalServerErrorException } from '@nestjs/common/exceptions/internal-server-error.exception';
import { VerifyOtpDto } from '@app/shared/dto/notification/verify-otp.dto';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { SendNotificationTemplateDto } from '@app/shared/dto/notification/send-notification-template.dto';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import * as ejs from 'ejs';
import { ResendEmailDto } from '@app/shared/dto/notification/email-notification.dto';

@Injectable()
export class NotificationMsService {
  constructor(
    private readonly settingMsService: SettingMsLibService,
    private readonly mailerService: MailerService,
    private readonly mailHistoryRepository: MailHistoryRepository,
    private readonly otpStatusRecordRepository: OtpStatusRecordRepository,
    private readonly utils: NotificationUtils,
    private readonly logger: PinoLogger
  ) {}

  async sendNotification(sendNotificationDto: SendNotificationDto): Promise<BaseResponseDto> {
    let status = 'NOT_SENT';
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
    try {
      let result;
      if (sendNotificationDto.attachments && sendNotificationDto.attachments.length > 0) {
        result = await this.sendEmailWithAttachment(
          sendNotificationDto.email,
          sendNotificationDto.subject,
          sendNotificationDto.content,
          sendNotificationDto.attachments
        );
      } else {
        result = await this.sendEmail(
          sendNotificationDto.email,
          sendNotificationDto.subject,
          sendNotificationDto.content
        );
      }
      status = result.length !== 0 ? 'SENT' : 'FAILED';
      this.logger.error(`mail has been sent to:  ${sendNotificationDto.email}`);
      response.setResponseCode(ResponseCodeEnum.SUCCESS);
      await this.mailHistoryRepository.saveMailHistory(
        sendNotificationDto.email,
        sendNotificationDto.subject,
        sendNotificationDto.content,
        status,
        sendNotificationDto.attachments
      );
      return response;
    } catch (error) {
      this.logger.error(`sendNotification error: \n error:  ${error instanceof Error ? error.stack : error}`);
      return response;
    }
  }

  async sendEmail(toEmails: string[], subject: string, content: string) {
    const fromEmail = await this.settingMsService.getSetting(SettingsEnumKey.FROM_EMAIL);
    return await Promise.all(
      toEmails.map((email) =>
        this.mailerService.sendMail({
          from: fromEmail,
          to: email,
          subject: subject,
          text: content,
          headers: { 'Content-Type': 'text/html' }, // Explicitly set Content-Type
        })
      )
    );
  }

  async sendEmailWithAttachment(
    toEmails: string[],
    subject: string,
    content: string,
    attachments: { filename: string; content: any; contentType: string }[]
  ) {
    const fromEmail = await this.settingMsService.getSetting(SettingsEnumKey.FROM_EMAIL);

    const normalizedAttachments = attachments.map((attachment) => {
      const { filename, content, contentType } = attachment;

      // Handle buffer content conversion
      let bufferContent: Buffer;
      if (content && typeof content === 'object' && content.type === 'Buffer' && Array.isArray(content.data)) {
        bufferContent = Buffer.from(content.data);
      } else if (typeof content === 'string') {
        // If content is base64 string
        bufferContent = Buffer.from(content, 'base64');
      } else {
        bufferContent = content;
      }

      return {
        filename,
        content: bufferContent,
        contentType: contentType || 'application/pdf', // default to PDF if not specified
      };
    });

    return await Promise.all(
      toEmails.map((email) =>
        this.mailerService.sendMail({
          from: fromEmail,
          to: email,
          subject,
          text: content,
          html: content,
          attachments: normalizedAttachments,
        })
      )
    );
  }

  async generateAndSendCode(generateCodeDto: GenerateCodeDto): Promise<BaseResponseDto> {
    const otpLength = await this.settingMsService.getSettingInt(SettingsEnumKey.OTP_LENGTH);
    const otpType = await this.settingMsService.getSetting(SettingsEnumKey.OTP_TYPE);
    let otpToken = '';
    switch (otpType) {
      case 'ALPHABETS':
        otpToken = this.utils.generateAlphabets(otpLength);
        break;
      case 'DIGITS':
        otpToken = this.utils.generateDigits(otpLength);
        break;
      case 'ALPHANUMERIC':
        otpToken = this.utils.generateToken(otpLength);
        break;
      default:
        otpToken = this.utils.generateDigits(otpLength);
    }

    const expirationTimeSetting = await this.settingMsService.getSettingInt(
      SettingsEnumKey.OTP_EXPIRATION_TIME_IN_MINUTES
    );
    const timeGenerated = new Date();
    const otpExpirationInMilliseconds = expirationTimeSetting * 60 * 1000;
    const expirationTime = new Date(timeGenerated.getTime() + otpExpirationInMilliseconds);

    const otpStatusRecord = new OtpStatusRecord({
      email: generateCodeDto.email,
      otp: otpToken,
      otpStatusRecordTypeEnum: generateCodeDto.otpRecordType,
      expirationTime,
    });

    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);

    try {
      await this.otpStatusRecordRepository.saveEntity(otpStatusRecord);
      this.logger.info(`OTP Status record created for email ${generateCodeDto.email}`);
    } catch (error) {
      this.logger.error(`Error while generating Saving otpStatusRecord ${error}`);
      response.setDescription('Error occurred while processing request.');
      return response;
    }

    const placeholders: Record<string, string> = {};
    placeholders['username'] = generateCodeDto.placeholders?.username ?? 'User';
    placeholders['accessCode'] = otpToken;

    const { emailTemplate, message } = await this.generateMessage(generateCodeDto.otpRecordType, placeholders);

    const notification = new SendNotificationDto();
    notification.email = [generateCodeDto.email];
    notification.content = message;
    notification.subject = emailTemplate.emailSubject;

    await this.sendNotification(notification);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('OTP has been successfully created and sent to the attached email.');
    return response;
  }

  async verifyUsedOtp(verifyOtpDto: VerifyOtpDto): Promise<BaseResponseDto> {
    return this.verifyOtp(verifyOtpDto, true);
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDto, otpUsed: boolean = false): Promise<BaseResponseDto> {
    const { processType: otpStatusRecordTypeEnum, otp, email } = verifyOtpDto;

    const response = new BaseResponseDto(ResponseCodeEnum.INVALID);
    if (!otp || !email) {
      return response;
    }

    const otps = await this.otpStatusRecordRepository.find({
      where: {
        email,
        otpUsed,
        otpStatusRecordTypeEnum,
      },
      order: {
        timeGenerated: 'DESC', // Sort by `timeGenerated` in descending order
      },
      take: 1, // Limit to 1 record, similar to `setMaxResults(1)`
    });

    if (!otps || otps.length === 0) {
      response.setResponseCode(ResponseCodeEnum.ERROR);
      return response;
    }

    const otpStatusRecord = otps[0];

    if (otpStatusRecord.otp !== otp) {
      return response;
    }

    otpStatusRecord.active = false;

    const currentTime = new Date().getTime();
    if (currentTime > new Date(otpStatusRecord.expirationTime).getTime()) {
      response.setResponseCode(ResponseCodeEnum.EXPIRED);
      return response;
    }

    otpStatusRecord.otpUsed = true;
    otpStatusRecord.timeUsed = new Date();
    otpStatusRecord.lastModified = new Date();

    try {
      await this.otpStatusRecordRepository.findOneAndUpdate({ pk: otpStatusRecord.pk }, otpStatusRecord);
      otpStatusRecord.otp === otp
        ? response.setResponseCode(ResponseCodeEnum.SUCCESS)
        : response.setResponseCode(ResponseCodeEnum.INVALID);
    } catch (error) {
      this.logger.error('Error Updating otpStatusRecord', error);
    }

    return response;
  }

  private async generateMessage(
    otpRecordType: string,
    placeholders: Record<string, string>
  ): Promise<{ emailTemplate; message }> {
    let emailTemplate = EmailTemplates[otpRecordType] || EmailTemplates.RESET_PASSWORD;
    let message = '';

    switch (otpRecordType) {
      case NotificatonTypeEnum.REGISTER: {
        emailTemplate = EmailTemplates.REGISTER;
        break;
      }
      case NotificatonTypeEnum.EXTERNAL_USER_CREATION: {
        emailTemplate = EmailTemplates.EXTERNAL_USER_CREATION;
        break;
      }
      case NotificatonTypeEnum.ANSWERED_SECURITY_QUESTIONS: {
        emailTemplate = EmailTemplates.ANSWERED_SECURITY_QUESTIONS;
        break;
      }
    }

    try {
      message = fs.readFileSync(emailTemplate.messagePath, 'utf8');
    } catch (error) {
      this.logger.error(
        `Template file not found at: ${emailTemplate.messagePath} \n error:  ${error instanceof Error ? error.stack : error}`
      );
      throw new InternalServerErrorException(`Error occurred while processing request`);
    }

    if (emailTemplate.messagePath.endsWith('.ejs')) {
      placeholders['title'] = emailTemplate.emailSubject;
      placeholders['notificationBaseUrl'] = await this.settingMsService.getSetting(
        SettingsEnumKey.NOTIFICATION_BASE_URL
      );
      placeholders['loginLink'] = await this.settingMsService.getSetting(SettingsEnumKey.COBRA_BASE_URL);
      placeholders['supportEmail'] = await this.settingMsService.getSetting(SettingsEnumKey.SUPPORT_EMAIL_ADDRESS);
      message = ejs.render(message, placeholders, { views: [EmailTemplates.VIEWS_INCLUDE_PATH.messagePath] });
    } else {
      // Replace all placeholders dynamically
      for (const [placeholder, value] of Object.entries(placeholders)) {
        message = message.replace(new RegExp(placeholder, 'g'), value);
      }
    }

    return { emailTemplate, message };
  }

  async sendEmailNotificationTemplate(
    sendNotificationTemplateDto: SendNotificationTemplateDto
  ): Promise<BaseResponseDto> {
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);

    const { emailTemplate, message } = await this.generateMessage(
      sendNotificationTemplateDto.notificationType,
      sendNotificationTemplateDto.placeholders
    );

    if (!emailTemplate || !message) {
      throw new InternalServerErrorException(
        `${sendNotificationTemplateDto.notificationType} email template not found`
      );
    }

    const notification = new SendNotificationDto();
    notification.email = sendNotificationTemplateDto.emailRecipients;
    notification.content = message;
    notification.subject = emailTemplate.emailSubject;
    notification.attachments = sendNotificationTemplateDto.attachments;

    await this.sendNotification(notification);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription(`${sendNotificationTemplateDto.notificationType} email sent successfully.`);
    return response;
  }

  async resendEmail(resendDto: ResendEmailDto): Promise<BaseResponseDto> {
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);

    // Find the mail history entry
    const mailHistory = await this.mailHistoryRepository.findAllMailInPks(resendDto.pk);

    if (!mailHistory || mailHistory.length === 0) {
      response.setDescription('Mail history entry not found');
      return response;
    }

    for (const mail of mailHistory) {
      // Parse the receiver string back to an array
      const emails = mail.receiver.split(', ');
      const mailAttachments = await mail.attachments;
      const attachments =
        mailAttachments?.map((attachment) => ({
          filename: attachment.filename,
          content: attachment.content,
          contentType: attachment.contentType,
        })) || [];

      // Resend the email using the notification service
      await this.sendNotification({
        email: emails,
        subject: mail.subject,
        content: mail.message,
        attachments: attachments,
      });
    }

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Email sent successfully');
    return response;
  }
}
