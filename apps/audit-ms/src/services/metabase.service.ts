import { Injectable, NotFoundException } from '@nestjs/common';
import { ReportDashboardRepository } from '@app/shared/audit-service/repositories/report-dashboard.repository';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import * as jwt from 'jsonwebtoken';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums/ResponseCodeEnum';
import { CobraReportDashboard } from '@app/shared/audit-service/entities/report-dashboard.entity';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { CustomException } from '@app/shared/filters/exception.dto';
import { CreateReportDashboardDto } from '@app/shared/audit-service/dtos/report-dashboard.dto';
import { SettingsEnumKey } from '@app/shared/enums';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';

@Injectable()
export class MetaBaseService {
  constructor(
    private readonly reportDashboardRepository: ReportDashboardRepository,
    private readonly settingMsLibService: SettingMsLibService,
    private readonly cobraUserRepository: CobraUserRepository
  ) {}

  async createReportDashboard(createReportDashboardDto: CreateReportDashboardDto): Promise<BaseResponseDto> {
    const existing = await this.reportDashboardRepository.findByResourceType(
      createReportDashboardDto.resourceType.toUpperCase()
    );

    if (existing) {
      throw new CustomException(
        `Dashboard configuration already exists for resource type: ${createReportDashboardDto.resourceType}`
      );
    }

    const dashboard = new CobraReportDashboard({
      resourceType: createReportDashboardDto.resourceType,
      resourceConfig: createReportDashboardDto.resourceConfig,
      mdaParams: createReportDashboardDto.mdaParams ? JSON.stringify(createReportDashboardDto.mdaParams) : null,
      pfaParams: createReportDashboardDto.pfaParams ? JSON.stringify(createReportDashboardDto.pfaParams) : null,
      pencomParams: createReportDashboardDto.pencomParams
        ? JSON.stringify(createReportDashboardDto.pencomParams)
        : null,
    });

    await this.reportDashboardRepository.saveEntity(dashboard);

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.description = 'Report dashboard configuration created successfully';
    return response;
  }

  async getAllReportDashboards(): Promise<BaseResponseDto> {
    const dashboards = await this.reportDashboardRepository.find({
      where: { active: true, deleted: false },
    });

    const response = new BaseResponseWithContentNoPagination<any>(ResponseCodeEnum.SUCCESS);
    response.description = 'Report dashboards retrieved successfully';
    response.content = dashboards.map((dashboard) => ({
      resourceType: dashboard.resourceType,
      resourceConfig: dashboard.resourceConfig,
      createDate: dashboard.createDate,
      lastModified: dashboard.lastModified,
    }));
    return response;
  }

  private async getParamsForUserType(
    dashboard: CobraReportDashboard,
    userType: string,
    userCode: string,
    req: ICustomRequest
  ): Promise<Record<string, any>> {
    let params = {};

    const isAdmin = await this.cobraUserRepository.isUserAdmin(req.user.email);

    switch (userType.toUpperCase()) {
      case 'MDA':
        if (dashboard.mdaParams) {
          params = this.applyUserParams(
            dashboard.mdaParams as unknown as Record<string, any>,
            'mda_code',
            userCode,
            isAdmin ? undefined : req.user.email
          );
        }
        break;
      case 'PFA':
        if (dashboard.pfaParams) {
          params = this.applyUserParams(
            dashboard.pfaParams as unknown as Record<string, any>,
            'pfa_code',
            userCode,
            isAdmin ? undefined : req.user.email
          );
        }
        break;
      case 'PENCOM':
        if (dashboard.pencomParams) {
          params = isAdmin
            ? dashboard.pencomParams
            : this.applyUserParams(
                dashboard.pencomParams as unknown as Record<string, any>,
                'pencom_code',
                null,
                req.user.email
              );
        }
        break;
    }

    return params;
  }

  private applyUserParams(
    paramTemplate: Record<string, any>,
    codeField: string,
    userCode: string,
    userId?: string
  ): Record<string, any> {
    const params = { ...paramTemplate };
    if (params[codeField]) {
      params[codeField] = [userCode];
    }
    // If user is not admin (user_id is provided), set the user_id
    // If user is admin (user_id is undefined), remove the user_id field completely
    if ('user_id' in params && userId) {
      params['user_id'] = [userId];
    }
    return params;
  }

  async getMetaBaseUrl(
    resourceType: string,
    req: ICustomRequest
  ): Promise<BaseResponseWithContentNoPagination<string>> {
    const dashboard = await this.reportDashboardRepository.findByResourceType(resourceType);

    if (!dashboard) {
      throw new NotFoundException(`No dashboard configuration found for resource type: ${resourceType}`);
    }

    const baseUrl = await this.settingMsLibService.getSetting(SettingsEnumKey.METABASE_BASE_URL);
    const secretKey = await this.settingMsLibService.getSetting(SettingsEnumKey.METABASE_SECRET_KEY);
    const duration = await this.settingMsLibService.getSettingInt(SettingsEnumKey.METABASE_TOKEN_DURATION);

    const userCode = req.user.userType === 'MDA' ? req.user.mdaCode : req.user.pfaCode;
    const params = await this.getParamsForUserType(dashboard, req.user.userType, userCode, req);

    const auditPayload = {
      resource: dashboard.resourceConfig,
      params: params,
      exp: Math.round(Date.now() / 1000) + (duration || 10) * 60,
    };

    const token = jwt.sign(auditPayload, secretKey);
    const url = `${baseUrl}/embed/dashboard/${token}#bordered=true&titled=true`;

    const resp = new BaseResponseWithContentNoPagination<string>(ResponseCodeEnum.SUCCESS);
    resp.content = url;

    return resp;
  }
}
