/* eslint-disable */
import { NestFactory } from '@nestjs/core';
import { AuditMsModule } from './audit-ms.module';
import { ValidationPipe } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { GlobalExceptionFilter } from '@app/shared/filters/global-exception.filter';
import { AUDIT_QUEUE_NAME } from '@app/shared/constants';

async function bootstrap() {
  const app = await NestFactory.create(AuditMsModule, {
    bufferLogs: true,
  });

  const configService = app.get(ConfigService);

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    })
  );
  app.useLogger(app.get(Logger));

  const httpPort = configService.get<number>('AUDIT_SERVICE_PORT', 4000);
  const rabbitmqUrl = configService.get<string>('RABBIT_MQ_URL', 'amqp://localhost:5672');
  const queueName = AUDIT_QUEUE_NAME;

  app.setGlobalPrefix('audit');

  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [rabbitmqUrl],
      queue: queueName,
      queueOptions: {
        durable: true,
      },
    },
  });

  const config = new DocumentBuilder()
    .setTitle('Audit Service')
    .setDescription('Documentation API for Audit Service')
    .setVersion(configService.get('AUDIT_SERVICE_VERSION', '1.0.0'))
    .addTag('audit')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('swagger-api', app, document);

  app.useGlobalFilters(new GlobalExceptionFilter());
  app.enableCors({
    origin: configService.get<string>('CORS_ORIGINS', '*'),
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
  });

  //app.setGlobalPrefix("api/v1");

  try {
    await app.startAllMicroservices();
    await app.listen(httpPort);

    const logger = app.get(Logger);
    logger.log(`🚀 Audit service HTTP server is running on port ${httpPort}`);

    logger.log(`📝 Swagger documentation available at /swagger-api`);
    logger.log(`🐰 RabbitMQ consumer connected to ${rabbitmqUrl}`);
    logger.log(`📬 Listening to queue: ${queueName}`);
  } catch (error) {
    const logger = app.get(Logger);
    logger.error('Failed to start the application', error);
    process.exit(1);
  }
}

bootstrap().catch((err) => {
  console.error('Failed to bootstrap the application', err);
  process.exit(1);
});
