import { AuditLog } from '@app/shared/audit-service/entities/audit-logs.entity';
import { IAuditLog } from '@app/shared/dto/audit/audit-log.dto';
import { Controller, Injectable } from '@nestjs/common';
import { EventPattern } from '@nestjs/microservices';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
@Controller('')
export class AuditConsumer {
  constructor(
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
    private readonly logger: PinoLogger
  ) {}

  @EventPattern('audit.log')
  async handleLog(data: IAuditLog): Promise<void> {
    try {
      const auditLog = this.auditLogRepository.create({
        ...data,
        metadata: data.metadata ? JSON.stringify(data.metadata) : null,
      });

      await this.auditLogRepository.save(auditLog);
      this.logger.debug(`Audit log saved: ${JSON.stringify(auditLog)}`);
    } catch (error) {
      this.logger.error(`Failed to save audit log: ${error}`);
      throw error;
    }
  }
}
