/* eslint-disable */
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { LoginModule } from './login.module';
import { Logger, LoggerErrorInterceptor } from 'nestjs-pino';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(LoginModule);

  app.setGlobalPrefix('login');
  app.useGlobalPipes(new ValidationPipe());
  app.useLogger(app.get(Logger));
  app.useGlobalInterceptors(new LoggerErrorInterceptor());

  const configService = app.get(ConfigService);
  const port = configService.get('LOGIN_SERVICE_PORT') || 3005;

  const config = new DocumentBuilder()
    .setTitle('Login Service')
    .setDescription('Documentation API for Login Service')
    .setVersion(configService.get('LOGIN_SERVICE_VERSION') || '1.0.0')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('swagger-api', app, document);

  app.enableCors();
  await app.listen(port);
  console.log(`Login service running on port ${port}`);
}

bootstrap();
