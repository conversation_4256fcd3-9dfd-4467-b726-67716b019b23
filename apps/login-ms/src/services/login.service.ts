/* eslint-disable */
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ResponseCodeEnum, SettingsEnumKey } from '@app/shared/enums';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';
import { LoginRegisterDto } from '@app/shared/dto/login-register.dto';
import {
  GetSecurityQuestionsResponseDto,
  GetUserSecurityQuestionRequestDto,
  SaveSecurityQuestionsRequestDto,
  VerifySecurityQuestionsRequestDto,
} from '@app/shared/dto/security-questions-payload.dto';
import { MdaEmployeeBiodata } from '@app/shared/login-service/entities/employee-biodata.entity';
import { MdaEmployeeBiodataRepository } from '@app/shared/login-service/repositories/mda-employee-biodata.repository';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { LoginAccessDto, LoginAccessResponseDto } from '@app/shared/dto/login-access.dto';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import * as bcrypt from 'bcryptjs';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { ChangeOldPasswordDto, ChangePasswordDto, ResetPasswordDto } from '@app/shared/dto/change-password.dto';
import { VerifyOtpDto } from '@app/shared/dto/notification/verify-otp.dto';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { getRolePrivileges, PasswordEncryption } from '@app/shared/utils';
import { UserServiceClientConstant } from '@app/shared/constants/user-service-client.constant';
import { RetrieveUserDto } from '@app/shared/dto/ecrs/retrieve-user.dto';
import { NotificationServiceClientConstant } from '@app/shared/constants/notification-service-client.constant';
import { EcrsServiceClientConstant } from '@app/shared/constants/ecrs-service-client.constant';
import { AuthLibService } from '@app/shared/auth/services/auth-lib.service';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { LdapAuthService } from '@app/shared/ldap/services/ldap-auth.service';
import { SecurityQuestionAnswer } from '@app/shared/login-service/entities/security-question-answer.entity';
import { SecurityQuestionsAnswerRepository } from '@app/shared/login-service/repositories/security-questions-answer.repository';
import { RedisCacheConstant } from '@app/shared/cache/redis-cache.constant';
import { SecurityQuestionsRepository } from '@app/shared/login-service/repositories/security-questions.repository';
import { CreateCobraUserDto } from '@app/shared/user-service/dtos/cobra-user.dto';
import { SendNotificationTemplateDto } from '@app/shared/dto/notification/send-notification-template.dto';
import { PasswordResetTypeEnum } from '@app/shared/enums/PasswordResetTypeEnum';
import { MdaRepository } from '@app/shared/user-service/repositories/mda.repository';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { PinoLogger } from 'nestjs-pino';
import { CaptchaService } from './captcha.service';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { Verify2FACode } from '@app/shared/dto/response/2fa-secret.dto';
import { TwoFactorAuthenticationService } from '@app/shared/ldap/services/2fa-lib.service';

@Injectable()
export class LoginService {
  private readonly cacheTTL: number;

  constructor(
    private readonly logger: PinoLogger,
    private readonly mdaRepository: MdaRepository,
    private readonly pfaRepository: PfaRepository,
    private readonly mdaEmployeeBiodataRepository: MdaEmployeeBiodataRepository,
    private readonly securityQuestionsAnswerRepository: SecurityQuestionsAnswerRepository,
    private readonly securityQuestionsRepository: SecurityQuestionsRepository,
    private readonly ldapAuthService: LdapAuthService,
    private readonly redisCacheService: RedisCacheService,
    private readonly settingMsLibService: SettingMsLibService,
    private readonly configService: ConfigService,
    private readonly authLibService: AuthLibService,
    private readonly captchaService: CaptchaService,
    private readonly twoFactorAuthenticationService: TwoFactorAuthenticationService,
    @Inject('USER_SERVICE_CLIENT') private readonly userClient: ClientProxy,
    @Inject('ECRS_SERVICE_CLIENT') private readonly ecrsClient: ClientProxy,
    @Inject('NOTIFICATION_SERVICE_CLIENT') private readonly notificationClient: ClientProxy
  ) {
    this.cacheTTL = this.configService.get<number>('CACHE_TTL', 3600);
  }

  async registerRetiree(checkRsaPinPayload: LoginRegisterDto): Promise<BaseResponseDto> {
    const { rsaPin, email, password, surname } = checkRsaPinPayload;

    let existingCobraUser: CobraUser | null;
    try {
      existingCobraUser = await firstValueFrom(this.userClient.send(UserServiceClientConstant.GET_BY_RSAPIN, rsaPin));
    } catch (error) {
      this.logger.error(
        `registerRetiree: Error fetching COBRA User data error:  ${error instanceof Error ? error.stack : error}`
      );
      const response = new LoginAccessResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while processing request, please try again.');
      return response;
    }

    if (existingCobraUser) {
      const response = new LoginAccessResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('User already exist, please go proceed to login.');
      return response;
    }

    const mdaEmployeeBiodata: MdaEmployeeBiodata | null = await this.mdaEmployeeBiodataRepository
      .findOne({ rsaPin })
      .catch((error) => {
        this.logger.error(
          `Error occurred while fetching RSA PIN data  error:  ${error instanceof Error ? error.stack : error}`
        );
        return null;
      });

    if (!mdaEmployeeBiodata) {
      const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription(
        'Registration not successful, please liaise with your MDA to include your details in the list of Retirees for the period or contact support'
      );
      return response;
    }

    const userType: RetireeUserTypeEnum = !mdaEmployeeBiodata.dateOfDeath
      ? RetireeUserTypeEnum.RETIREE
      : RetireeUserTypeEnum.DECEASED;

    const requestPayload: RetrieveUserDto = {
      rsaPin: mdaEmployeeBiodata.rsaPin,
      surname,
      userType,
    };

    const ecrsResponse: EcrsUserResponseDto = await this.callEcrsService(requestPayload);
    if (ecrsResponse.code !== 1) {
      const response = new BaseResponseDto(ecrsResponse.code);
      response.setDescription(ecrsResponse.description);
      return response;
    }

    const newCobraUser = new CreateCobraUserDto();
    newCobraUser.rsaPin = mdaEmployeeBiodata.rsaPin;
    newCobraUser.firstName = mdaEmployeeBiodata.firstName;
    newCobraUser.surname = mdaEmployeeBiodata.surname;
    newCobraUser.emailAddress = email;
    newCobraUser.password = password;
    newCobraUser.userType = UserTypeEnum.RETIREE;
    newCobraUser.staffId = mdaEmployeeBiodata.staffId;
    newCobraUser.gender = mdaEmployeeBiodata.gender;
    newCobraUser.mdaEmployeeBiodata = mdaEmployeeBiodata;

    try {
      await firstValueFrom(this.userClient.send(UserServiceClientConstant.CREATE, newCobraUser));
    } catch (error) {
      this.logger.error(
        `Error occurred while creating Cobra user  error:  ${error instanceof Error ? error.stack : error}`
      );
      const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while processing registration request, please try again later.');
      return response;
    }

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('Registration successful');
    return response;
  }

  async callEcrsService(requestPayload: RetrieveUserDto): Promise<EcrsUserResponseDto> {
    try {
      return await firstValueFrom(
        this.ecrsClient.send({ cmd: EcrsServiceClientConstant.RETRIEVE_USER }, requestPayload)
      );
    } catch (error) {
      this.logger.error(
        `Error occurred while retrieving COBRA User data from ECRS ms ${error instanceof Error ? error.stack : error}`
      );
      return new EcrsUserResponseDto(ResponseCodeEnum.ERROR);
    }
  }

  async saveSecurityQuestions(saveSecurityQuestionsPayload: SaveSecurityQuestionsRequestDto): Promise<BaseResponseDto> {
    const { email, securityQuestions } = saveSecurityQuestionsPayload;

    const cobraUser = await firstValueFrom(this.userClient.send(UserServiceClientConstant.GET_BY_EMAIL, email)).catch(
      (error) => {
        this.logger.error('Error occurred while fetching COBRA User data', error);
        return null;
      }
    );

    if (!cobraUser) {
      const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('Unable to retrieve user details, please try again later');
      return response;
    }

    try {
      await this.securityQuestionsAnswerRepository.findOneAndDelete({
        cobraUser,
      });

      const placeholders: Record<string, string> = {};
      placeholders['username'] = cobraUser.surname;
      for (const [index, value] of securityQuestions.entries()) {
        const newSecurityQuestion = new SecurityQuestionAnswer({
          cobraUser: cobraUser,
          question: value.question,
          answer: value.answer,
        });

        await this.securityQuestionsAnswerRepository.saveEntity(newSecurityQuestion);
        placeholders[`question${index + 1}`] = value.question;
      }

      const updateCobraUser: Partial<CreateCobraUserDto> = {
        emailAddress: cobraUser.emailAddress,
        securityQuestions: false,
      };

      await firstValueFrom(
        this.userClient.send(UserServiceClientConstant.UPDATE_COBRA_USER_FOR_OTP, updateCobraUser)
      ).catch((error) => {
        this.logger.error('Error occurred while updating COBRA User data', error);
        return null;
      });

      const sendNotificationTemplateDto = new SendNotificationTemplateDto();
      sendNotificationTemplateDto.emailRecipients = [cobraUser.emailAddress];
      sendNotificationTemplateDto.notificationType = NotificatonTypeEnum.ANSWERED_SECURITY_QUESTIONS;
      sendNotificationTemplateDto.placeholders = placeholders;

      await firstValueFrom(
        this.notificationClient.send(
          NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
          sendNotificationTemplateDto
        )
      ).catch((error) => {
        this.logger.error(
          `Error Sending user generatePassAndCreateUser notification  error:  ${
            error instanceof Error ? error.stack : error
          }`
        );
        return Promise.resolve(null);
      });

      const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
      response.setDescription('Security Question saved successfully');
      return response;
    } catch (error) {
      this.logger.error('Error occurred while saving security questions', error);
      const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('Error saving security questions');
      return response;
    }
  }

  async verfiySecurityQuestions(
    verfiySecurityQuestionsPayload: VerifySecurityQuestionsRequestDto
  ): Promise<BaseResponseDto> {
    const { emailAddress, questions } = verfiySecurityQuestionsPayload;

    const user = await firstValueFrom(this.userClient.send(UserServiceClientConstant.GET_BY_EMAIL, emailAddress)).catch(
      (error) => {
        this.logger.error('Error occurred while fetching COBRA User data', error);
        return Promise.resolve(null);
      }
    );

    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
    if (!user) {
      response.setDescription('Unable to get associated user details with provide email');
      return response;
    }

    // Fetch the stored security questions for the user
    const storedQuestions = await this.securityQuestionsAnswerRepository.findBy({
      cobraUser: { pk: user.pk },
    });

    if (!storedQuestions.length) {
      response.setDescription('No security questions found for user');
      return response;
    }

    // Create a map of stored questions and answers for quick lookup
    const storedMap = new Map(storedQuestions.map((q) => [q.question.toLowerCase(), q.answer.toLowerCase()]));

    let matchCount = 0;

    let answersMatch = false;
    for (const question of questions) {
      const storedAnswer = storedMap.get(question.question.toLowerCase());
      if (storedAnswer && storedAnswer === question.answer?.toLowerCase()) {
        matchCount++;
      }
      if (matchCount >= 2) {
        answersMatch = true; // Early return if at least 2 matches are found
      }
    }

    if (!answersMatch) {
      response.setDescription('Security questions/answers do not match');
      return response;
    }

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Security questions match');
    return response;
  }

  async getSecurityQuestions(): Promise<GetSecurityQuestionsResponseDto> {
    try {
      const cachedQuestions = await this.redisCacheService.get(RedisCacheConstant.SECURITY_QUESTIONS);

      if (cachedQuestions) {
        this.logger.debug('Retrieved security questions from cache');
        return this.createSuccessResponse(JSON.parse(cachedQuestions));
      }

      const questionsFromDb = await this.securityQuestionsRepository.find();

      if (!questionsFromDb?.length) {
        this.logger.warn('No security questions found in database');
        return this.createErrorResponse('Security Questions not found');
      }

      const questions = questionsFromDb.map((q) => q.question);
      await this.redisCacheService.set(RedisCacheConstant.SECURITY_QUESTIONS, JSON.stringify(questions), this.cacheTTL);

      this.logger.debug('Stored security questions in cache');
      return this.createSuccessResponse(questions);
    } catch (error) {
      this.logger.error(
        'Error occurred while fetching security questions',
        error instanceof Error ? error.stack : error
      );
      return this.createErrorResponse('Error fetching security questions');
    }
  }

  async getUserSecurityQuestions(
    getSecurityQuestionsPayload: GetUserSecurityQuestionRequestDto
  ): Promise<GetSecurityQuestionsResponseDto> {
    const { email } = getSecurityQuestionsPayload;
    const userQuestions = await this.securityQuestionsAnswerRepository.find({
      relations: ['cobraUser'],
      where: {
        cobraUser: {
          emailAddress: email,
        },
      },
    });

    if (!userQuestions?.length) {
      this.logger.warn('No security questions found in database');
      return this.createErrorResponse('Security Questions not found');
    }
    const questions = userQuestions.map((q) => q.question);
    return this.createSuccessResponse(questions);
  }

  async loginAccess(
    loginAccessPayload: LoginAccessDto,
    isTokenValidated: boolean = false,
    providedCobraUser?: CobraUser
  ): Promise<LoginAccessResponseDto> {
    const { username, userType, password } = loginAccessPayload;

    let pattern: { cmd: string };
    switch (userType) {
      case UserTypeEnum.MDA:
      case UserTypeEnum.PFA:
        pattern = UserServiceClientConstant.GET_BY_EMAIL;
        break;
      case UserTypeEnum.RETIREE:
        pattern = UserServiceClientConstant.GET_BY_RSAPIN;
        break;
      case UserTypeEnum.PENCOM:
        pattern = UserServiceClientConstant.GET_BY_STAFFID;
        break;
      default:
        const response = new LoginAccessResponseDto(ResponseCodeEnum.ERROR);
        response.setDescription(`Unknown user type: ${userType} specified, please verify and try again.`);
        return response;
    }

    if (
      userType !== UserTypeEnum.PENCOM &&
      (await this.settingMsLibService.getSettingBoolean(SettingsEnumKey.ENABLE_CAPTCHA))
    ) {
      if (!loginAccessPayload.captcha) {
        const response = new LoginAccessResponseDto(ResponseCodeEnum.ERROR);
        response.setDescription('Captcha is required');
        return response;
      }

      const isCaptchaValid = await this.captchaService.validateCaptcha(loginAccessPayload.captcha);
      if (!isCaptchaValid) {
        const response = new LoginAccessResponseDto(ResponseCodeEnum.ERROR);
        response.setDescription('Your session could not be verified for security. Please refresh and try again.');
        return response;
      }
    }

    let cobraUser: CobraUser | null;
    try {
      cobraUser = providedCobraUser || (await firstValueFrom(this.userClient.send(pattern, username)));
    } catch (error) {
      this.logger.error(`Error fetching COBRA User data   error:  ${error instanceof Error ? error.stack : error}`);
      const response = new LoginAccessResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while verifying user, please try again.');
      return response;
    }

    if (!cobraUser) {
      const response = new LoginAccessResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('Unknown user specified, please verify and try again.');
      return response;
    }

    if (userType !== cobraUser.userType) {
      const response = new LoginAccessResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('Provided user type is incorrect.');
      return response;
    }

    const pencomUserLoginMode = await this.settingMsLibService.getSetting(SettingsEnumKey.PENCOM_USER_LOGIN_MODE);

    if (userType === UserTypeEnum.PENCOM && pencomUserLoginMode === 'ACTIVE_DIRECTORY') {
      try {
        const ldapUser: { staffName: string; department: string } = await this.ldapAuthService.login(
          username,
          password
        );
        this.logger.debug(`User ${username}: ${ldapUser.staffName}: ${ldapUser.department} found`);
      } catch (error) {
        console.log('error occurred while authenticating user with active directory', error);
        const response = new LoginAccessResponseDto(ResponseCodeEnum.ERROR);
        response.setDescription('Unable to verify username/password combination on active directory.');
        return response;
      }
      if (await this.settingMsLibService.getSettingBoolean(SettingsEnumKey.ENABLE_2FA_FOR_PENCOM_USERS)) {
        if (!isTokenValidated && cobraUser.twoFactorSecret) {
          const response = new LoginAccessResponseDto(ResponseCodeEnum.SUCCESS);
          response.setDescription('Please proceed to verify 2FA');
          response.twoFactorAuthRequired = true;
          return response;
        }
        if (!isTokenValidated && !cobraUser.twoFactorSecret) {
          const response = new LoginAccessResponseDto(ResponseCodeEnum.SUCCESS);
          response.setDescription('Please proceed to setup 2FA');
          response.twoFactorAuthOnboardingRequired = true;
          return response;
        }
      }
    } else {
      const isPasswordValid = await bcrypt.compare(password, cobraUser.password);
      if (!isPasswordValid) {
        const response = new LoginAccessResponseDto(ResponseCodeEnum.ERROR);
        response.setDescription('Username and password combination is incorrect');
        return response;
      }
    }

    if (!cobraUser.active) {
      const response = new LoginAccessResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('User account is not currently active, kindly contact your Admin.');
      return response;
    }

    if (cobraUser.blacklisted) {
      const response = new LoginAccessResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('User account is currently blacklisted, kindly contact the COBRA team.');
      return response;
    }

    const { privileges: privilegeSet, roles: roleSet, isAdmin } = getRolePrivileges(cobraUser.roles);
    const privileges = [...privilegeSet];
    const roles = [...roleSet];

    const payload = {
      userId: cobraUser.pk,
      userType,
      isAdmin,
      email: cobraUser.emailAddress,
      roles,
      privileges,
      mdaCode: cobraUser.mdaCode,
      organisationName: await this.getUserOrganisationName(cobraUser),
      pfaCode: cobraUser.pfaCode,
    };

    const accessToken = this.authLibService.generateToken(payload);

    const response = new LoginAccessResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('success');
    response.firstTimeLogin = cobraUser.firstTimeLogin;
    response.updatePassword = cobraUser.updatePassword;
    response.securityQuestions = cobraUser.securityQuestions;
    response.token = accessToken;

    return response;
  }

  async generateNewToken(token: string): Promise<BaseResponseWithContentNoPagination<string>> {
    try {
      const decoded = this.authLibService.verifyToken(token);

      const { exp, iat, nbf, ...cleanPayload } = decoded;

      const newToken = this.authLibService.generateToken(cleanPayload);

      const response = new BaseResponseWithContentNoPagination<string>(ResponseCodeEnum.SUCCESS);
      response.setDescription('success');
      response.content = newToken;
      return response;
    } catch (error) {
      this.logger.error(`Error in generateNewToken: ${error instanceof Error ? error.stack : error}`);

      const response = new BaseResponseWithContentNoPagination<string>(ResponseCodeEnum.ERROR);
      response.setDescription('Failed to generate token');
      return response;
    }
  }

  async getUserOrganisationName(cobraUser: CobraUser) {
    if (cobraUser.userType === UserTypeEnum.MDA) {
      return this.mdaRepository.findMdaNameByCode(cobraUser.mdaCode);
    }

    if (cobraUser.userType === UserTypeEnum.PFA) {
      return this.pfaRepository.findPfaNameByCode(cobraUser.pfaCode);
    }

    if (cobraUser.userType === UserTypeEnum.PENCOM) {
      return 'PENCOM';
    }

    return '';
  }

  async changePassword(changePasswordDto: ChangePasswordDto): Promise<BaseResponseDto> {
    const { newPassword, otp, email, resetType } = changePasswordDto;

    if (resetType === PasswordResetTypeEnum.RESET_LINK) {
      if (!otp) {
        const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
        response.setDescription('OTP is required for password reset');
        return response;
      }

      const verifyOtpPayload: VerifyOtpDto = {
        otp,
        email,
        processType: NotificatonTypeEnum.RESET_PASSWORD,
      };

      const isOtpValidationResponse: BaseResponseDto | null = await firstValueFrom<BaseResponseDto>(
        this.notificationClient.send<BaseResponseDto>(
          NotificationServiceClientConstant.VERIFY_USED_OTP,
          verifyOtpPayload
        )
      ).catch((error) => {
        this.logger.error(`Error occurred sending notification error: ${error instanceof Error ? error.stack : error}`);
        return null;
      });

      if (!isOtpValidationResponse) {
        const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
        response.setDescription('Error occurred while attempting to verify OTP, please try again later');
        return response;
      }

      if (isOtpValidationResponse.code !== 1) {
        return isOtpValidationResponse;
      }
    }

    const cobraUser = await firstValueFrom(this.userClient.send(UserServiceClientConstant.GET_BY_EMAIL, email)).catch(
      (error) => {
        this.logger.error(
          `Error occurred while fetching COBRA User data  error:  ${error instanceof Error ? error.stack : error}`
        );
        return null;
      }
    );

    if (!cobraUser) {
      const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('Unable to retrieve user details, please try again later');
      return response;
    }

    const hashedPassword = PasswordEncryption.encryptPassword(newPassword);

    const updateCobraUser: Partial<CreateCobraUserDto> = {
      rsaPin: cobraUser.rsaPin,
      firstName: cobraUser.firstName,
      surname: cobraUser.surname,
      emailAddress: email,
      password: hashedPassword,
      userType: cobraUser.userType,
      staffId: cobraUser.staffId,
      gender: cobraUser.gender,
    };

    await firstValueFrom(
      this.userClient.send(UserServiceClientConstant.UPDATE_COBRA_USER_DETAILS, updateCobraUser)
    ).catch((error) => {
      this.logger.error(
        `Error occurred while updating COBRA User data  error:  ${error instanceof Error ? error.stack : error}`
      );
      return null;
    });

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('User information successfully updated');
    return response;
  }

  async changeOldPassword(changeOldPasswordDto: ChangeOldPasswordDto): Promise<BaseResponseDto> {
    return (await firstValueFrom(
      this.userClient.send(UserServiceClientConstant.CHANGE_OLD_PASSWORD, changeOldPasswordDto)
    ).catch((error) => {
      this.logger.error(
        `Error occurred while fetching COBRA User data  error:  ${error instanceof Error ? error.stack : error}`
      );
      return new BaseResponseDto(ResponseCodeEnum.ERROR);
    })) as BaseResponseDto;
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<BaseResponseDto> {
    const { email, userType } = resetPasswordDto;

    const cobraUser = await firstValueFrom(this.userClient.send(UserServiceClientConstant.GET_BY_EMAIL, email)).catch(
      (error) => {
        this.logger.error(
          `Error occurred while fetching COBRA User data  error:  ${error instanceof Error ? error.stack : error}`
        );
        return Promise.resolve(null);
      }
    );

    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
    if (!cobraUser) {
      response.setDescription('Unable to retrieve user details, please try again later');
      return response;
    }

    if (userType !== cobraUser.userType) {
      response.setDescription('Provided user type is incorrect.');
      return response;
    }

    return await firstValueFrom<BaseResponseDto>(
      this.notificationClient.send(NotificationServiceClientConstant.REQUEST_OTP, {
        email,
        otpRecordType: NotificatonTypeEnum.RESET_PASSWORD,
      })
    ).catch((error) => {
      this.logger.error(`Error occurred sending notification  error:  ${error instanceof Error ? error.stack : error}`);
      response.setDescription('Error occurred while sending OTP, please try again later');
      return response;
    });
  }

  async verify2FACode(verify2FACodeDto: Verify2FACode): Promise<BaseResponseDto> {
    const cobraUser = await firstValueFrom(
      this.userClient.send(UserServiceClientConstant.GET_BY_STAFFID, verify2FACodeDto.username)
    ).catch((error) => {
      this.logger.error(
        `Error occurred while fetching COBRA User data  error:  ${error instanceof Error ? error.stack : error}`
      );
      return Promise.resolve(null);
    });

    if (!cobraUser) {
      const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('Unable to retrieve user details, please try again later');
      return response;
    }

    const isValid = await this.twoFactorAuthenticationService.verify2FACode(
      cobraUser.twoFactorSecret,
      verify2FACodeDto.twoFactorToken,
      verify2FACodeDto.clientTime
    );

    if (isValid === false) {
      const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('Invalid 2FA code');
      return response;
    }

    const loginAccessPayload = new LoginAccessDto();
    loginAccessPayload.username = verify2FACodeDto.username;
    loginAccessPayload.userType = verify2FACodeDto.userType;
    loginAccessPayload.password = verify2FACodeDto.password;
    loginAccessPayload.clientTime = verify2FACodeDto.clientTime;
    loginAccessPayload.captcha = verify2FACodeDto.captcha;

    return this.loginAccess(loginAccessPayload, true, cobraUser);
  }

  private createSuccessResponse(questions: string[]): GetSecurityQuestionsResponseDto {
    const response = new GetSecurityQuestionsResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('success');
    response.questions = questions;
    return response;
  }

  private createErrorResponse(description: string): GetSecurityQuestionsResponseDto {
    const response = new GetSecurityQuestionsResponseDto(ResponseCodeEnum.ERROR);
    response.setResponseCode(-1);
    response.setDescription(description);
    return response;
  }
}
