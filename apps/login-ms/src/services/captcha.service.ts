import { SettingsEnumKey } from '@app/shared/enums/SettingsEnum';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class CaptchaService {
  constructor(
    private readonly logger: <PERSON><PERSON>Logger,
    private readonly settingService: SettingMsLibService
  ) {}

  async validateCaptcha(captcha: string): Promise<boolean> {
    if (!captcha) {
      return false;
    }

    try {
      const secretKey = await this.settingService.getSetting(SettingsEnumKey.CAPTCHA_SECRET_KEY);
      const url = await this.settingService.getSetting(SettingsEnumKey.CAPTCHA_VALIDATION_URL);
      const response = await fetch(`${url}?secret=${secretKey}&response=${captcha}`);
      const data = await response.json();

      const validThreshold = await this.settingService.getSettingInt(SettingsEnumKey.CAPTCHA_VALID_THRESHOLD);
      console.log(`captcha data: ${JSON.stringify(data)}`);
      if (!data.success || data.score < validThreshold) {
        return false;
      }

      return true;
    } catch (error) {
      console.log('Error validating captcha:', error);
    }

    return false;
  }
}
