/* eslint-disable */
import {
  Body,
  Controller,
  Get,
  Headers,
  Param,
  Post,
  Req,
  UnauthorizedException,
  UseInterceptors,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { LoginRegisterDto } from '@app/shared/dto/login-register.dto';
import {
  SaveSecurityQuestionsRequestDto,
  VerifySecurityQuestionsRequestDto,
} from '@app/shared/dto/security-questions-payload.dto';
import { LoginService } from './services/login.service';
import { LoginAccessDto } from '@app/shared/dto/login-access.dto';
import { Public } from '@app/shared/decorators/public.decorator';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ResponseCodeEnum } from '@app/shared/enums';
import { ChangeOldPasswordDto, ChangePasswordDto } from '@app/shared/dto/change-password.dto';
import { AuthLibService } from '@app/shared/auth/services/auth-lib.service';
import { Audit } from '@app/shared/decorators/audit.decorator';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { isValidEmail } from '@app/shared/utils';
import { Verify2FACode } from '@app/shared/dto/response/2fa-secret.dto';

@Controller('')
@UseInterceptors(AuditInterceptor)
export class LoginMsController {
  constructor(
    private readonly loginService: LoginService,
    private readonly authLibService: AuthLibService
  ) {}

  @Public()
  @Post('/register')
  @Audit(AuditEventTypeEnum.LOGIN_REGISTER_ATTEMPT)
  async register(@Body() loginRegisterDto: LoginRegisterDto, @Req() req: ICustomRequest) {
    req.user = { email: loginRegisterDto.email };
    req.metadata.user = {
      ...loginRegisterDto,
    };
    return await this.loginService.registerRetiree(loginRegisterDto);
  }

  @Post('/save-security-questions')
  @Audit(AuditEventTypeEnum.SAVE_SECURITY_QUESTIONS_ATTEMPT)
  async saveSecurityQuestions(
    @Body() saveSecurityQuestionsDto: SaveSecurityQuestionsRequestDto,
    @Req() req: ICustomRequest
  ) {
    return await this.loginService.saveSecurityQuestions(saveSecurityQuestionsDto);
  }

  @Public()
  @Post('/verify-security-questions')
  @Audit(AuditEventTypeEnum.VERIFY_SECURITY_QUESTIONS_ATTEMPT)
  async verifySecurityQuestions(
    @Body() verifySecurityQuestionsDto: VerifySecurityQuestionsRequestDto,
    @Req() req: ICustomRequest
  ) {
    return await this.loginService.verfiySecurityQuestions(verifySecurityQuestionsDto);
  }

  @Public()
  @Get('/get-security-questions')
  @Audit(AuditEventTypeEnum.GET_SECURITY_QUESTIONS_ATTEMPT)
  async getSecurityQuestions(@Req() req: ICustomRequest) {
    return await this.loginService.getSecurityQuestions();
  }

  @Public()
  @Get('/get-user-security-questions/:email')
  @Audit(AuditEventTypeEnum.GET_USER_SECURITY_QUESTIONS_ATTEMPT)
  async getUserSecurityQuestions(@Param('email') email: string, @Req() req: ICustomRequest) {
    return await this.loginService.getUserSecurityQuestions({
      email,
    });
  }

  @Public()
  @Post('/access')
  @UsePipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      validationError: { target: false },
    })
  )
  @Audit(AuditEventTypeEnum.LOGIN_ATTEMPT)
  async access(@Body() loginAccessDto: LoginAccessDto, @Req() req: ICustomRequest) {
    req.user = { email: loginAccessDto.username };
    req.metadata.user = {
      username: loginAccessDto.username,
      userType: loginAccessDto.userType,
    };

    return await this.loginService.loginAccess(loginAccessDto);
  }

  @Post('refresh-token')
  async generateNewToken(@Headers('authorization') authHeader: string) {
    if (!authHeader) {
      throw new UnauthorizedException('No token provided');
    }

    const token = authHeader.replace('Bearer ', '');

    return this.loginService.generateNewToken(token);
  }

  @Public()
  @Post('signout')
  @Audit(AuditEventTypeEnum.SIGNOUT_ATTEMPT)
  async signOut(@Headers('authorization') authHeader: string, @Req() req: ICustomRequest) {
    if (!authHeader) {
      throw new UnauthorizedException('No token provided');
    }

    const token = authHeader.replace('Bearer ', '');
    const isInvalidToken = await this.authLibService.isTokenInvalidated(token);
    if (isInvalidToken) {
      return new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    }
    await this.authLibService.invalidateToken(token);

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('success');

    return response;
  }

  @Public()
  @Post('/change-password')
  @Audit(AuditEventTypeEnum.CHANGE_PASSWORD_ATTEMPT)
  async changePassword(@Body() changePasswordDto: ChangePasswordDto, @Req() req: ICustomRequest) {
    req.user = { email: changePasswordDto.email };
    req.metadata.user = {
      username: changePasswordDto.email,
    };

    const result = await this.loginService.changePassword(changePasswordDto);
    return result;
  }

  @Post('/change-old-password')
  @Audit(AuditEventTypeEnum.CHANGE_OLD_PASSWORD_SUCCESS)
  async changeOldPassword(@Body() changeOldPasswordDto: ChangeOldPasswordDto, @Req() req: ICustomRequest) {
    return await this.loginService.changeOldPassword(changeOldPasswordDto);
  }

  @Public()
  @Get('/reset-password/:email/:userType')
  @Audit(AuditEventTypeEnum.RESET_PASSWORD_ATTEMPT)
  async resetPassword(@Param('email') email: string, @Param('userType') userType: string, @Req() req: ICustomRequest) {
    if (!email || !isValidEmail(email) || !userType) {
      const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
      response.setDescription('Invalid email or user type');
      return response;
    }

    req.user = { email: email };
    req.metadata.user = {
      username: email,
      userType: userType,
    };

    return await this.loginService.resetPassword({ email, userType });
  }

  @Public()
  @Post('/verify-2fa-code')
  @Audit(AuditEventTypeEnum.VERIFY_OTP_ATTEMPT)
  async verifyOtp(@Body() verifyOtpDto: Verify2FACode, @Req() req: ICustomRequest) {
    req.user = { email: verifyOtpDto.username, userType: verifyOtpDto.userType };
    return await this.loginService.verify2FACode(verifyOtpDto);
  }
}
