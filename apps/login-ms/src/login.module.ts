/* eslint-disable */
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DatabaseModule, LoggerModule } from '@app/shared';
import { LoginService } from './services/login.service';
import { RedisCacheModule } from '@app/shared/cache';
import { SettingMsLibModule } from '@app/shared/setting-service/setting-ms-lib.module';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { LoginMsController } from './login.controller';
import { MdaEmployeeBiodata } from '@app/shared/login-service/entities/employee-biodata.entity';
import { MdaEmployeeBiodataRepository } from '@app/shared/login-service/repositories/mda-employee-biodata.repository';
import { HealthModule } from '@app/shared/health';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { ClientProxyFactory, Transport } from '@nestjs/microservices';
import { APP_GUARD } from '@nestjs/core';
import { CustomUsernameValidator } from '@app/shared/dto/login-access.dto';
import { AuthModule } from '@app/shared/auth/auth.module';
import { AuthGuard } from '@app/shared/auth/guards/auth.guard';
import { AuthLibService } from '@app/shared/auth/services/auth-lib.service';
import { AuditLibModule } from '@app/shared/audit-service/audit-lib.module';
import {
  AUDIT_QUEUE_NAME,
  ECRS_SERVICE,
  ECRS_SERVICE_HOST,
  ECRS_SERVICE_TCP_PORT,
  LOGIN_SERVICE,
  NOTIFICATION_SERVICE_HOST,
  NOTIFICATION_SERVICE_TCP_PORT,
  NOTIFICATIONS_SERVICE,
  USER_SERVICE,
  USER_SERVICE_HOST,
  USER_SERVICE_TCP_PORT,
} from '@app/shared/constants';
import { SecurityQuestionAnswer } from '@app/shared/login-service/entities/security-question-answer.entity';
import { SecurityQuestionsAnswerRepository } from '@app/shared/login-service/repositories/security-questions-answer.repository';
import { SecurityQuestionsRepository } from '@app/shared/login-service/repositories/security-questions.repository';
import { SecurityQuestion } from '@app/shared/login-service/entities/security-question.entity';
import { CobraRole } from '@app/shared/user-service/entities/cobra-role.entity';
import { CobraPrivilege } from '@app/shared/user-service/entities/cobra-priviledge.entity';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { MdaRepository } from '@app/shared/user-service/repositories/mda.repository';
import { Pfa } from '@app/shared/user-service/entities/pfa.entity';
import { Mda } from '@app/shared/user-service/entities/mda.entity';
import { Pfc } from '@app/shared/user-service/entities/pfc.entity';
import { CaptchaService } from 'apps/login-ms/src/services/captcha.service';
import { LdapModule } from '@app/shared/ldap/ldap.module';

@Module({
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([
      CobraUser,
      CobraRole,
      CobraPrivilege,
      MdaEmployeeBiodata,
      SecurityQuestion,
      SecurityQuestionAnswer,
      Pfa,
      Pfc,
      Mda,
    ]),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    HealthModule.registerAsync(
      [
        { name: USER_SERVICE, host: USER_SERVICE_HOST, port: USER_SERVICE_TCP_PORT },
        { name: ECRS_SERVICE, host: ECRS_SERVICE_HOST, port: ECRS_SERVICE_TCP_PORT },
        { name: NOTIFICATIONS_SERVICE, host: NOTIFICATION_SERVICE_HOST, port: NOTIFICATION_SERVICE_TCP_PORT },
      ],
      AUDIT_QUEUE_NAME
    ),
    LoggerModule.forRoot(LOGIN_SERVICE),
    RedisCacheModule.register(),
    SettingMsLibModule,
    AuthModule,
    LdapModule,
    AuditLibModule,
  ],
  controllers: [LoginMsController],
  providers: [
    {
      provide: 'USER_SERVICE_CLIENT',
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: configService.get('USER_SERVICE_HOST', '127.0.0.1'),
            port: configService.get('USER_SERVICE_TCP_PORT', 3013),
          },
        });
      },
    },
    {
      provide: 'ECRS_SERVICE_CLIENT',
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: configService.get('ECRS_SERVICE_HOST', '127.0.0.1'),
            port: configService.get('ECRS_SERVICE_TCP_PORT', 3017),
          },
        });
      },
    },
    {
      provide: 'NOTIFICATION_SERVICE_CLIENT',
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: configService.get('NOTIFICATION_SERVICE_HOST', '127.0.0.1'),
            port: configService.get('NOTIFICATION_SERVICE_TCP_PORT', 3008),
          },
        });
      },
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    LoginService,
    RedisCacheService,
    CaptchaService,
    SettingMsLibService,
    AuthLibService,
    MdaEmployeeBiodataRepository,
    SecurityQuestionsAnswerRepository,
    SecurityQuestionsRepository,
    PfaRepository,
    MdaRepository,
    CustomUsernameValidator,
  ],
})
export class LoginModule {}
