/* eslint-disable */
import { NestFactory } from '@nestjs/core';
import { EnrollmentMsModule } from './enrollment-ms.module';
import { ValidationPipe } from '@nestjs/common';
import { Logger, LoggerErrorInterceptor } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import * as bodyParser from 'body-parser';
import { GlobalExceptionFilter } from '@app/shared/filters/global-exception.filter';

async function bootstrap() {
  const app = await NestFactory.create(EnrollmentMsModule, {
    bufferLogs: true,
  });

  app.setGlobalPrefix('enrollment');
  app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));
  app.useLogger(app.get(Logger));
  app.useGlobalInterceptors(new LoggerErrorInterceptor());
  app.use(bodyParser.json({ limit: '50mb' }));
  app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

  const configService = app.get(ConfigService);
  const port = configService.get('ENROLLMENT_SERVICE_HTTP_PORT') || 3010;
  const tcpPort = configService.get('ENROLLMENT_SERVICE_TCP_PORT') || 3016;

  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.TCP,
    options: {
      host: '0.0.0.0',
      port: tcpPort,
    },
  });

  const config = new DocumentBuilder()
    .setTitle('Enrollment Service')
    .setDescription('Documentation API for Enrollment Service')
    .setVersion(configService.get('ENROLLMENT_SERVICE_VERSION') || '1.0.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('swagger-api', app, document);

  app.useGlobalFilters(new GlobalExceptionFilter());
  await app.startAllMicroservices();
  app.enableCors();
  await app.listen(port);
  console.log(`ENROLLMENT service running on port ${port}, tcp port ${tcpPort}`);
}

bootstrap();
