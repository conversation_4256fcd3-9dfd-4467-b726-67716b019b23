/* eslint-disable */
import { Injectable, Res } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import * as xlsx from 'xlsx';
import { WorkSheet } from 'xlsx';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { firstValueFrom, timeout } from 'rxjs';
import { PinoLogger } from 'nestjs-pino';
import { join } from 'path';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { ResponseCodeEnum, SettingsEnumKey } from '@app/shared/enums';
import { FileUploadResponseDto } from '@app/shared/dto/response/file-upload-response.dto';
import {
  BatchPaymentConfirmationUploadRequestDto,
  FileUploadRequestDto,
} from '@app/shared/dto/request/file-upload-request.dto';
import { ExcelUploadTasksGateway } from '@app/shared/tasks/excel-upload.gateway';
import { GeneralUtils, getStableNumber } from '@app/shared/utils';
import { EnrollmentEngineServiceClientConstant } from '@app/shared/constants/enrollment-engine-service-client.constant';
import { ConfigService } from '@nestjs/config';
import { RabbitMqClient } from '@app/shared/rabbitmq/rabbit-mq-client';
import { FileUploadRepository } from '@app/shared/enrollment-service/repository/file-upload.repository';
import { Response } from 'express';
import { UploadProcessTypeEnum } from '@app/shared/enums/upload-process-type-enum';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { EnrolmentBiodataRepository } from '@app/shared/enrollment-service/repository/enrolment-biodata.repository';
import { BatchRepository } from '@app/shared/enrollment-service/repository/batch.repository';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';

@Injectable()
export class UploadService {
  private client: ClientProxy;

  MDA_PROCESS_TYPES = [
    UploadProcessTypeEnum.MDA_RETIREE_DATA_UPLOAD,
    UploadProcessTypeEnum.MDA_DECEASED_DATA_UPLOAD,
    UploadProcessTypeEnum.MDA_NOMINAL_ROLL_UPLOAD,
  ];

  SIMPLE_UPLOAD_PROCESS_TYPES = [
    UploadProcessTypeEnum.MDA_RETIREE_DATA_UPLOAD,
    UploadProcessTypeEnum.MDA_DECEASED_DATA_UPLOAD,
    UploadProcessTypeEnum.ACCRUED_RIGHTS,
  ];

  constructor(
    private readonly redisCacheService: RedisCacheService,
    private readonly settingsService: SettingMsLibService,
    private readonly logger: PinoLogger,
    private readonly tasksGateway: ExcelUploadTasksGateway,
    private readonly fileUploadRepository: FileUploadRepository,
    private readonly configService: ConfigService,
    private readonly rabbitMqClient: RabbitMqClient,
    private readonly utils: GeneralUtils,
    private readonly enrolmentBiodataRepository: EnrolmentBiodataRepository,
    private readonly batchRepository: BatchRepository,
    @InjectDataSource() private readonly dataSource: DataSource
  ) {
    this.client = this.rabbitMqClient.getClient();
  }

  async handleMdaExcelUpload(
    file: Express.Multer.File,
    fileUploadRequestDto: FileUploadRequestDto,
    req: ICustomRequest
  ): Promise<FileUploadResponseDto> {
    const response = new FileUploadResponseDto(ResponseCodeEnum.ERROR);

    if (!file) {
      response.setDescription('File not attached, please verify and try again.');
      return response;
    }

    const mdaCode = req.user.mdaCode;
    const userEmail = req.user.email;

    if (!mdaCode && this.MDA_PROCESS_TYPES.includes(fileUploadRequestDto.processType)) {
      response.setDescription(`Only MDA users are allowed to perform the process ${fileUploadRequestDto.processType}`);
      return response;
    }

    let queuePattern = '';
    let metaText = '';
    switch (fileUploadRequestDto.processType) {
      case UploadProcessTypeEnum.MDA_DECEASED_DATA_UPLOAD: {
        queuePattern = EnrollmentEngineServiceClientConstant.MDA_DATA_UPLOAD;
        metaText = 'stellarsync-deceased-upload';
        break;
      }
      case UploadProcessTypeEnum.MDA_RETIREE_DATA_UPLOAD: {
        queuePattern = EnrollmentEngineServiceClientConstant.MDA_DATA_UPLOAD;
        metaText = 'stellarsync-retiree-upload';
        break;
      }
      case UploadProcessTypeEnum.MDA_NOMINAL_ROLL_UPLOAD: {
        queuePattern = EnrollmentEngineServiceClientConstant.MDA_NOMINAL_ROLL_UPLOAD;
        metaText = 'stellarsync-nominal-roll';
        break;
      }
      case UploadProcessTypeEnum.ACCRUED_RIGHTS: {
        queuePattern = EnrollmentEngineServiceClientConstant.ACCRUED_RIGHTS_COMPUTATION;
        metaText = 'stellarsync-accrued-rights';
        break;
      }
      default: {
        response.setDescription(
          `Unknown process type: ${fileUploadRequestDto.processType} provided, please verify and try again`
        );
        return response;
      }
    }

    const stableTimer: number = await this.settingsService.getSettingInt(
      SettingsEnumKey.STABLE_UPLOAD_PROCESSING_TIMER_RANGE
    );
    const taskId = `${fileUploadRequestDto.processType}-${mdaCode}-${getStableNumber(stableTimer)}`;
    if ((await this.redisCacheService.get(taskId)) !== null) {
      response.setDescription(`Request already queued, kindly poll for results with id: ${taskId}`);
      return response;
    }

    const workbook = xlsx.read(file.buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];

    const hiddenSheet = workbook.Sheets['meta'];
    if (!hiddenSheet) {
      response.setDescription('Invalid template:Please ensure you are using the provided template.');
      return response;
    }

    const marker = xlsx.utils.sheet_to_json(hiddenSheet, { header: 1 })?.[0]?.[0];
    if (marker !== metaText) {
      response.setDescription(
        'Invalid template: verification failed, please ensure you are using the provided template.'
      );
      return response;
    }

    const rows = this.getRows(sheet, fileUploadRequestDto.processType);
    const allowedRowCount = await this.settingsService.getSettingInt(SettingsEnumKey.EXCEL_SHEET_MAX_ROW_COUNT);
    if (rows.length > allowedRowCount) {
      this.logger.warn(`Worksheet rowcount ${rows.length} is greater than allowed row count  ${allowedRowCount}`);
      response.setDescription(`Kindly ensure that you are not uploading records greater than ${allowedRowCount}`);
      return response;
    }

    const totalRows = rows.length;
    // Store total rows in Redis
    const taskTotalKey = `task:${taskId}:total`;
    console.log(`taskTotalKey: ${taskTotalKey} - totalRows ${totalRows}`);
    await this.redisCacheService.set(`task:${taskId}:total`, totalRows);
    await this.redisCacheService.set(`task:${taskId}:processed`, 0);
    await this.redisCacheService.set(`task:${taskId}:results`, JSON.stringify([]));

    let sendPromises;

    if (this.SIMPLE_UPLOAD_PROCESS_TYPES.includes(fileUploadRequestDto.processType)) {
      sendPromises = rows.map(async (row) => {
        try {
          return await firstValueFrom(
            this.client.emit(queuePattern, JSON.stringify({ taskId, userEmail, row })).pipe(timeout(10000))
          );
        } catch (error) {
          this.logger.error(`Error processing taskId:, ${taskId}, \n error: ${error}`);
          return { error: true }; // Return an error flag
        }
      });
    } else {
      const bandConfig: { year: number; start: number }[] = await this.settingsService.getSettingJson(
        SettingsEnumKey.NOMINAL_ROLL_SHEET_BAND_CONFIG
      );
      sendPromises = rows.map(async (row) => {
        try {
          const bandData = bandConfig
            .map(({ year, start }) => {
              const employer = row[start];
              const salaryStructure = row[start + 1];
              const gl = row[start + 2];
              const step = row[start + 3];

              // Skip if all fields are missing/empty
              if (
                (!employer || employer.toString().trim() === '') &&
                (!salaryStructure || salaryStructure.toString().trim() === '') &&
                (!gl || gl.toString().trim() === '') &&
                (!step || step.toString().trim() === '')
              ) {
                return null;
              }

              return {
                year,
                employer,
                salaryStructure,
                gl,
                step,
              };
            })
            .filter((band) => band !== null);

          const employeeData = {
            rsaPin: row[0], // Column B: RSA PIN
            surname: row[1], // Column C: Surname
            dateOfFirstAppointment: this.getExcelDate(row[2]), // Column F: Date of First Appt
            bandData,
          };

          return await firstValueFrom(
            this.client.emit(queuePattern, JSON.stringify({ taskId, employeeData, userEmail })).pipe(timeout(10000))
          );
        } catch (error) {
          this.logger.error(`Error processing taskId: ${taskId}, \n error: ${error}`);
          return { error: true };
        }
      });
    }

    const results = await Promise.all(sendPromises);
    const hasError = results.some((result) => result && result.error);

    if (hasError) {
      await this.redisCacheService.deleteKeysWithPattern(`${taskId}*`);
      response.setResponseCode(ResponseCodeEnum.ERROR);
      response.description = 'Error occurred while processing request, please try again.';
    } else {
      await this.redisCacheService.set(taskId, 'QUEUED');
      response.setResponseCode(ResponseCodeEnum.SUCCESS);
      response.taskId = taskId;
      response.message = 'File uploaded and queued for processing';
      this.tasksGateway.emitNewTask(taskId);
    }
    return response;
  }

  emitProgressUpdate(taskId: string, percentage: number) {
    this.tasksGateway.emitProgressUpdate(taskId, percentage);
  }

  async getProgress(taskId: string) {
    const total = await this.redisCacheService.get(`task:${taskId}:total`);
    const processed = await this.redisCacheService.get(`task:${taskId}:processed`);
    const results = await this.redisCacheService.get(`task:${taskId}:results`);

    if (!total || !processed || !results) {
      throw new Error('Task not found');
    }

    const percentage = (parseInt(processed) / parseInt(total)) * 100;
    const parsedResults = JSON.parse(results);

    if (percentage === 100) {
      // Generate result file
      const resultFilePath = await this.generateResultFile(taskId, parsedResults);
      return { taskId, percentage: '100%', resultFilePath };
    }

    return { taskId, percentage: percentage.toFixed(2) + '%' };
  }

  async downloadResultFile(taskId: string, @Res() res: Response) {
    console.log(`${taskId} download`);
    if (!taskId) {
      this.logger.error('unknown task id provided');
      return res.status(404).send('Unknown task ID provided');
    }

    const results = await this.fileUploadRepository.find({ where: { taskId } });

    if (!results || results.length === 0) {
      this.logger.warn(`Results not found for task ${taskId}`);
      return res.status(404).send('No results found for the given taskId');
    }

    // Create a new Excel workbook
    const workbook = xlsx.utils.book_new();
    const worksheetData = results.map((result) => ({
      ...JSON.parse(result.rowData),
      'PROCESSING OUTCOME': result.resultDescription,
    }));

    const worksheet = xlsx.utils.json_to_sheet(worksheetData);
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Results');

    // Generate the Excel file
    const buffer = xlsx.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=results_${taskId}.xlsx`);
    res.send(buffer);
  }

  private getExcelDate(rowElement: any) {
    if (typeof rowElement === 'string') {
      return rowElement;
    }

    if (!rowElement || rowElement === 1 || rowElement === 4) {
      return null;
    }

    return this.utils.excelDateToString(rowElement);
  }

  private getRows(sheet: WorkSheet, processType: string) {
    if (processType === 'MDA-NOMINAL-ROLL-UPLOAD') {
      const rawData = xlsx.utils.sheet_to_json(sheet, { header: 1 }) as any[][];
      return rawData.slice(2).filter((row) => row.length > 0);
    } else if (processType === 'ACCRUED-RIGHTS') {
      return xlsx.utils.sheet_to_json(sheet);
    } else {
      return xlsx.utils.sheet_to_json(sheet, {
        range: 2, // Skips the header row, ensuring clean data
        defval: '', // Keeps empty cells
        blankrows: false,
      });
    }
  }

  async generateResultFile(taskId: string, results: any[]) {
    const resultWorkbook = xlsx.utils.book_new();
    const resultSheet = xlsx.utils.json_to_sheet(results);
    xlsx.utils.book_append_sheet(resultWorkbook, resultSheet, 'Results');

    const filePath = join(__dirname, '..', 'uploads', `result_${taskId}.xlsx`);
    xlsx.writeFile(resultWorkbook, filePath);

    return filePath;
  }

  async handlePfaPaymentConfirmation(
    file: Express.Multer.File,
    batchPaymentConfirmationUploadRequestDto: BatchPaymentConfirmationUploadRequestDto,
    req: ICustomRequest
  ) {
    const response: BaseResponseWithContentNoPagination<unknown> = new BaseResponseWithContentNoPagination<unknown>(
      ResponseCodeEnum.ERROR
    );

    if (!file) {
      response.setDescription('File not attached, please verify and try again.');
      return response;
    }

    const workbook = xlsx.read(file.buffer, { type: 'buffer' });
    const hiddenSheetName = 'meta';
    const hiddenSheet = workbook.Sheets[hiddenSheetName];
    if (!hiddenSheet) {
      response.setDescription('Invalid template:Please ensure you are using the provided template.');
      return response;
    }

    const marker = xlsx.utils.sheet_to_json(hiddenSheet, { header: 1 })?.[0]?.[0];
    if (marker !== 'stellarsync') {
      response.setDescription(
        'Invalid template: verification failed, please ensure you are using the provided template.'
      );
      return response;
    }

    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const rows = xlsx.utils.sheet_to_json(sheet);

    if (!rows.length) {
      response.setDescription('No records found in the file.');
      return response;
    }

    const batch = await this.batchRepository.findOne({ batchName: batchPaymentConfirmationUploadRequestDto.batchName });
    if (!batch) {
      response.setDescription('Batch not found with the provided details.');
      return response;
    }

    if (!['AR_AWAITING_PFA_PAYMENT_CONFIRMATION', 'CR_AWAITING_PFA_PAYMENT_CONFIRMATION'].includes(batch.status)) {
      response.setDescription(
        `Batch status: ${batch.status} not expecting a payment confirmation, please verify and try again.`
      );
      return response;
    }

    // Validate and process each row
    const errors: string[] = [];

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    for (const [index, row] of rows.entries()) {
      const rsaPin = row['RSA PIN'];
      const surname = row['SURNAME'];
      const status = row['STATUS'];
      const comment = row['COMMENT'];

      if (!rsaPin || typeof rsaPin !== 'string') {
        errors.push(`RSA PIN: ${rsaPin}: Invalid RSA PIN provided.`);
        continue;
      }

      if (!status || typeof status !== 'string') {
        errors.push(`RSA PIN: ${rsaPin}: Invalid status provided.`);
        continue;
      }

      if (!comment || typeof comment !== 'string') {
        errors.push(`RSA PIN: ${rsaPin}: Invalid comment provided.`);
        continue;
      }

      const enrolmentBiodata = await this.enrolmentBiodataRepository.findOne([
        { rsaPin: rsaPin.trim(), accruedRightsBatchId: batchPaymentConfirmationUploadRequestDto.batchName },
        { rsaPin: rsaPin.trim(), contributionBatchId: batchPaymentConfirmationUploadRequestDto.batchName },
      ]);

      if (!enrolmentBiodata) {
        errors.push(`Row ${rsaPin}: Enrollment Details not found for provided batch.`);
        continue;
      }
      if (enrolmentBiodata.surname !== surname) {
        errors.push(
          `RSA PIN: ${rsaPin}: Surname ${surname} does not match Enrollment data: ${enrolmentBiodata.surname}.`
        );
        continue;
      }

      if (enrolmentBiodata.pfaCode !== req.user.pfaCode) {
        errors.push(
          `RSA PIN: ${rsaPin}: RSA Pin PFA details does not match, verify that RSAHolder belongs to your PFA .`
        );
        continue;
      }

      enrolmentBiodata.paymentStatus = status.trim();
      enrolmentBiodata.paymentComment = comment.trim();
      await this.enrolmentBiodataRepository.saveEntity(enrolmentBiodata, queryRunner);
    }

    if (errors.length) {
      await queryRunner.rollbackTransaction();
      response.setDescription('Validation failed on some rows.');
      response.content = errors;
      return response;
    }
    await queryRunner.commitTransaction();
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    return response;
  }
}
