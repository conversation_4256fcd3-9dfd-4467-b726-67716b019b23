/* eslint-disable */
import { Inject, Injectable } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { <PERSON><PERSON><PERSON>ogger } from 'nestjs-pino';
import { ExcelUploadTasksGateway } from '@app/shared/tasks/excel-upload.gateway';
import { ConfigService } from '@nestjs/config';
import { RabbitMqClient } from '@app/shared/rabbitmq/rabbit-mq-client';
import { ProfessorLess15AccruedRightsService } from '@app/shared/computation/accrued-rights/professor-less-15-accrued-rights.service';
import { Professor15AccruedRightsService } from '@app/shared/computation/accrued-rights/professor-15-accrued-rights.service';
import { OthersAccruedRightsService } from '@app/shared/computation/accrued-rights/others-accrued-rights.service';
import { ICalculationParams } from '@app/shared/computation/accrued-rights/calculation.dto';
import { MdaEmployeeBiodataRepository } from '@app/shared/login-service/repositories/mda-employee-biodata.repository';
import { MdaEmployeeBiodataSearchDto } from '@app/shared/dto/enrollment/mda-data-upload-result.dto';
import {
  BaseResponseListWithContentNoPagination,
  BaseResponseWithContent,
  BaseResponseWithContentNoPagination,
} from '@app/shared/dto/response/base-response-with-content';
import { MdaEmployeeBiodata } from '@app/shared/login-service/entities/employee-biodata.entity';
import { ResponseCodeEnum } from '@app/shared/enums';
import { NominalRollRepository } from '@app/shared/enrollment-service/repository/nominal-roll.repository';
import { NominalRollSearchDto } from '@app/shared/dto/enrollment/nominalroll/nominal-roll-employee.dto';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { RetireeRecordsSearchDto } from '@app/shared/dto/enrollment/retiree-records-search.dto';
import { RegistrationStatusEnum } from '@app/shared/enums/registration-status.enum';
import { EnrolmentBiodataRepository } from '@app/shared/enrollment-service/repository/enrolment-biodata.repository';
import { EnrolmentDraftRepository } from '@app/shared/enrollment-service/repository/enrolment-draft.repository';
import { EnrolmentDraft } from '@app/shared/enrollment-service/entities/enrolment-draft.entity';
import { GetEnrolmentDraftDto, SaveEnrolmentDraftDto } from '@app/shared/dto/enrollment/enrolment-draft.dto';
import { RetrieveUserDto } from '@app/shared/dto/ecrs/retrieve-user.dto';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import { firstValueFrom } from 'rxjs';
import { EcrsServiceClientConstant } from '@app/shared/constants/ecrs-service-client.constant';
import { FormValidatorService } from '@app/shared/enrollment-service/services/form-validator.service';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { WorkflowService } from '@app/shared/workflow/services/workflow.service';
import { ProcessTypeEnum } from '@app/shared/workflow/enums/ProcessTypeEnum';
import { EnrolmentMachineEvent, eventValues } from '@app/shared/workflow/workflow/exit-ar-cr.workflow';

import { AccruedBenefitsWorkflowService } from '@app/shared/workflow/services/accrued-benefits-workflow.service';
import { ProcessBulkEnrolmentDto, ProcessEnrolmentDto } from '@app/shared/dto/enrollment/process-enrollment.dto';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { EnrolmentHistoryView } from '@app/shared/enrollment-service/entities/enrolment-history-view.entity';
import { DataSource, In, Repository } from 'typeorm';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { BatchingService } from './batching.service';
import { EnrolmentBiodata } from '@app/shared/enrollment-service/entities/enrolment-biodata.entity';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { BatchFilterDto } from '@app/shared/dto/enrollment/exit-batching/batch-filter.dto';
import { BatchRepository } from '@app/shared/enrollment-service/repository/batch.repository';
import { TbeOrganisationsRepository } from '@app/shared/enrollment-service/repository/tbe-organisations.repository';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ReassignmentDto } from '@app/shared/dto/enrollment/reassignment.dto';
import { CustomException } from '@app/shared/filters/exception.dto';
import { EnrolmentSummary } from '@app/shared/enrollment-service/entities/enrolment-summary.entity';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { BatchDocumentRepository } from '@app/shared/enrollment-service/repository/batch-document.repository';
import { TbcEnrollmentPfaMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-enrollment-pfa-memo-document-repository.service';
import { TbcEnrollmentPfcMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-enrollment-pfc-memo-document-repository.service';
import { PfaPaymentStats } from '@app/shared/enrollment-service/entities/entity.types';
import { CronJobNamesConstant, EnrollmentEngineServiceClientConstant } from '@app/shared/constants';

@Injectable()
export class EnrollmentMsService {
  private rmqClient: ClientProxy;

  constructor(
    private readonly logger: PinoLogger,
    private readonly tasksGateway: ExcelUploadTasksGateway,
    private readonly professorLess15AccruedRightsService: ProfessorLess15AccruedRightsService,
    private readonly professor15AccruedRightsService: Professor15AccruedRightsService,
    private readonly othersAccruedRightsService: OthersAccruedRightsService,
    private readonly configService: ConfigService,
    private readonly rabbitMqClient: RabbitMqClient,
    private readonly mdaEmployeeBiodataRepository: MdaEmployeeBiodataRepository,
    private readonly tbeOrganisationsRepository: TbeOrganisationsRepository,
    private readonly nominalRollRepository: NominalRollRepository,
    private readonly enrolmentSummaryRepository: EnrolmentSummaryRepository,
    private readonly enrolmentDraftRepository: EnrolmentDraftRepository,
    private readonly enrolmentBiodataRepository: EnrolmentBiodataRepository,
    private readonly formValidatorService: FormValidatorService,
    private readonly workflowService: WorkflowService,
    private readonly batchingService: BatchingService,
    private readonly accruedBenefitsService: AccruedBenefitsWorkflowService,
    private readonly batchRepository: BatchRepository,
    private readonly batchDocumentRepository: BatchDocumentRepository,
    private readonly tbcEnrollmentPfaMemoDocumentRepository: TbcEnrollmentPfaMemoDocumentRepository,
    private readonly tbcEnrollmentPfcMemoDocumentRepository: TbcEnrollmentPfcMemoDocumentRepository,
    private redisService: RedisCacheService,
    @Inject('ECRS_SERVICE_CLIENT') private readonly ecrsClient: ClientProxy,
    @InjectRepository(EnrolmentHistoryView)
    private readonly enrolmentHistoryRepository: Repository<EnrolmentHistoryView>,
    @InjectDataSource() private readonly dataSource: DataSource,
    private readonly cobraUserRepository: CobraUserRepository
  ) {
    this.rmqClient = this.rabbitMqClient.getClient();
  }

  async sendTask(task: any) {
    return this.rmqClient.send('task', task).toPromise();
  }

  calculate(params: ICalculationParams) {
    if (params.type === undefined) {
      return "Unknown Request Type, allowed types are 'OTHERS' | 'PROF15' | 'PROFLESS15'";
    }

    switch (params.type) {
      case 'OTHERS': {
        const resultOthers = this.othersAccruedRightsService.calculateAdditionalFields(
          // const resultOthers = this.othersServiceDss.calculateAdditionalFields(
          params.C11,
          params.C12,
          params.C13,
          params.C14,
          params.C15,
          params.C16
        );
        console.log(`resultOthers ${JSON.stringify(resultOthers)}`);
        return resultOthers;
      }
      case 'PROF15': {
        const result = this.professor15AccruedRightsService.calculateAdditionalFields(
          // const result = this.professor15ServiceDss.calculateAdditionalFields(
          params.C11,
          params.C12,
          params.C13,
          params.C14,
          params.C15,
          params.C16
        );

        console.log(`result ${JSON.stringify(result)}`);
        return result;
      }
      case 'PROFLESS15': {
        // const resultLess = this.professor15ServiceLessDss.calculateAdditionalFields(
        const resultLess = this.professorLess15AccruedRightsService.calculateAdditionalFields(
          params.C11,
          params.C12,
          params.C13,
          params.C14,
          params.C15,
          params.C16
        );
        console.log(`resultLess ${JSON.stringify(resultLess)}`);
        return resultLess;
      }
      default: {
        return "Unknown Request Type, allowed types are 'OTHERS' | 'PROF15' | 'PROFLESS15'";
      }
    }
  }

  async fetchMdaEmployee(
    searchParams: MdaEmployeeBiodataSearchDto,
    req: ICustomRequest
  ): Promise<BaseResponseWithContent<MdaEmployeeBiodata>> {
    if (req.user?.userType === UserTypeEnum.PFA) {
      searchParams.pfaCode = req.user.pfaCode;
    }
    if (req.user?.userType === UserTypeEnum.MDA) {
      searchParams.mdaCode = req.user.mdaCode;
    }

    const paginatedResult = await this.mdaEmployeeBiodataRepository.searchEmployees(searchParams);

    if (!paginatedResult || paginatedResult.results.length === 0) {
      this.logger.warn('No MDA employee biodata found for the provided search criteria.');
      const errorResponse = new BaseResponseWithContent<MdaEmployeeBiodata>(ResponseCodeEnum.ERROR);
      errorResponse.setDescription('No matching MDA Employee biodata found');
      return Promise.resolve(errorResponse);
    }

    const response = new BaseResponseWithContent<MdaEmployeeBiodata>(ResponseCodeEnum.SUCCESS);
    response.content = paginatedResult.results;
    response.total = paginatedResult.total;
    response.page = paginatedResult.page;
    response.limit = paginatedResult.limit;
    return Promise.resolve(response);
  }

  async fetchNominalRoll(searchParams: NominalRollSearchDto) {
    const response = new BaseResponseWithContent<any>(ResponseCodeEnum.SUCCESS);
    try {
      const paginatedResult = await this.nominalRollRepository.getNominalRoll(searchParams);
      response.content = paginatedResult.results;
      response.total = paginatedResult.total;
      response.page = paginatedResult.page;
      response.limit = paginatedResult.limit;
    } catch (error) {
      this.logger.error(`fetchNominalRoll error: ${error instanceof Error ? error.stack : error}`);
      response.setResponseCode(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while retrieving nominal roll, please try again');
    }

    return response;
  }

  async retrieveEnrollmentSummary(filter: PaginatedSearchDto, req: ICustomRequest) {
    if (!filter) {
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Please provide filter details');
      return response;
    }
    try {
      const response = new BaseResponseWithContent(ResponseCodeEnum.SUCCESS);
      const result = await this.enrolmentSummaryRepository.searchPaginatedEnrollmentSummary(filter, req.user.email);
      response.content = result.data;
      response.page = result.page;
      response.limit = result.limit;
      response.total = result.total;
      response.setDescription('Successfully fetched records');
      return response;
    } catch (error) {
      this.logger.error(`retrieveEnrollmentSummary error: ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while processing request, please try again');
      return response;
    }
  }

  async retrieveEnrollmentSummaryDetail(recordsSearchDto: RetireeRecordsSearchDto) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    if (!recordsSearchDto) {
      response.setDescription('Please provide search parameters');
      return response;
    }

    try {
      const enrollmentSummary = await this.enrolmentSummaryRepository.getEnrollmentSummaryByRsaPin(
        recordsSearchDto.rsaPin
      );
      if (!enrollmentSummary || enrollmentSummary.length === 0) {
        response.setDescription('Unable to find records with the provided RSA PIN, please check and try again.');
        return response;
      }

      response.setResponseCode(ResponseCodeEnum.SUCCESS);
      if (
        enrollmentSummary[0].status === RegistrationStatusEnum.REGISTERED ||
        enrollmentSummary[0].status === RegistrationStatusEnum.PENDING_PFA_REVIEW
      ) {
        response.content = { data: enrollmentSummary[0], status: enrollmentSummary[0].status };
        return response;
      }

      const biodata = await this.enrolmentBiodataRepository.fetchEnrolmentBiodataWithDetailsAndDocumentsAndComments(
        recordsSearchDto.rsaPin
      );

      if (biodata) {
        biodata.employmentDetails?.sort((a, b) => Number(a.employmentYear) - Number(b.employmentYear));
        biodata.comments?.sort((a, b) => new Date(a.lastModified).getTime() - new Date(b.lastModified).getTime());
        response.content = { data: biodata, status: enrollmentSummary[0].status };
      } else {
        response.content = { data: enrollmentSummary[0], status: enrollmentSummary[0].status };
      }

      return response;
    } catch (error) {
      this.logger.error(`retrieveEnrollmentSummaryDetail error: ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while retrieving enrollment summary detail, please try again');
      return response;
    }
  }

  async saveEnrolmentDraftProgress(saveEnrolmentDraftDto: SaveEnrolmentDraftDto) {
    let response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
    if (!saveEnrolmentDraftDto) {
      response.setDescription('Please provide Enrolment details');
      return response;
    }

    const rsaPin = saveEnrolmentDraftDto.formData.rsaPin;
    if (!rsaPin) {
      response.setDescription('Please provide RSA PIN');
      return response;
    }

    try {
      let enrollmentDraft;
      if (saveEnrolmentDraftDto.stepNumber !== 1) {
        enrollmentDraft = await this.enrolmentDraftRepository.findOne({
          rsaPin: saveEnrolmentDraftDto.formData.rsaPin,
        });

        if (!enrollmentDraft) {
          response.setDescription('Kindly re-start process from step 1');
          return response;
        }

        saveEnrolmentDraftDto = this.populateFormFields(enrollmentDraft, saveEnrolmentDraftDto);
      }

      response = await this.formValidatorService.validateStep(saveEnrolmentDraftDto);
      if (!response || response.code !== 1) {
        return response;
      }

      if (!enrollmentDraft) {
        const record = new EnrolmentDraft({
          rsaPin: saveEnrolmentDraftDto.formData.rsaPin,
          stepNumber: saveEnrolmentDraftDto.stepNumber,
          formData: JSON.stringify(saveEnrolmentDraftDto.formData),
        });
        await this.enrolmentDraftRepository.upsertEntity(record, ['rsaPin']);
      } else {
        const existingFormData = JSON.parse(enrollmentDraft.formData || '{}');
        const updatedFormData = {
          ...existingFormData,
          ...saveEnrolmentDraftDto.formData, // This will override only fields present in the new formData
        };

        enrollmentDraft.stepNumber = saveEnrolmentDraftDto.stepNumber;
        enrollmentDraft.formData = JSON.stringify(updatedFormData);

        await this.enrolmentDraftRepository.upsertEntity(enrollmentDraft, ['rsaPin']);
      }

      response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
      response.setDescription('Draft Saved successfully');
      return response;
    } catch (error) {
      this.logger.error(`saveEnrolmentDraftProgress error: ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while retrieving enrollment draft detail, please try again');
      return response;
    }
  }

  async getEnrolmentProgress(getEnrolmentDraftDto: GetEnrolmentDraftDto) {
    let response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    if (!getEnrolmentDraftDto) {
      response.setDescription('Please provide RSA PIN details');
      return response;
    }

    try {
      const employeeDetail = await this.mdaEmployeeBiodataRepository.findOne({ rsaPin: getEnrolmentDraftDto.rsaPin });
      if (!employeeDetail) {
        this.logger.error(`Unable to get employee data for pin: ${getEnrolmentDraftDto.rsaPin}`);
        response.setDescription(
          'Unable to retrieve employee details, liaise with your employer or PENCOM for more information'
        );
        return response;
      }

      const cobraUser = await this.cobraUserRepository.findOne({ rsaPin: getEnrolmentDraftDto.rsaPin });
      if (!cobraUser) {
        response.setDescription(
          'Unable to verify RSA PIN registration details, kindly ensure user is registered and try again.'
        );
        return response;
      }

      const requestPayload: RetrieveUserDto = <RetrieveUserDto>{
        rsaPin: getEnrolmentDraftDto.rsaPin,
        surname: employeeDetail.surname,
        userType: employeeDetail.retireeUserType,
      };

      const ecrsResponse = await this.callEcrsService(requestPayload);
      if (!ecrsResponse || ecrsResponse.code !== 1) {
        this.logger.error(`ECRS response unsuccessful for pin: ${getEnrolmentDraftDto.rsaPin}`);
        response.setDescription(ecrsResponse.description);
        return response;
      }

      const employerDetails = await this.tbeOrganisationsRepository.getOrganisationSectorIppisDetails(
        ecrsResponse.employerCode
      );

      const userInformation: Record<string, any> = {};
      userInformation['rsaPin'] = ecrsResponse.rsaPin;
      userInformation['pfaCode'] = ecrsResponse.pfaCode;
      userInformation['employerCode'] = ecrsResponse.employerCode;
      userInformation['employerName'] = ecrsResponse.employerName;
      userInformation['pfaName'] = ecrsResponse.pfaName;
      userInformation['firstName'] = ecrsResponse.firstName;
      userInformation['surname'] = ecrsResponse.surname;
      userInformation['middleName'] = ecrsResponse.middleName;
      userInformation['gender'] = ecrsResponse.gender;
      userInformation['dateOfBirth'] = ecrsResponse.dateOfBirth;
      userInformation['legacy'] = ecrsResponse.legacy;
      userInformation['phoneNo'] = ecrsResponse.phoneNo;
      userInformation['nin'] = ecrsResponse.nin;
      userInformation['dts'] = employeeDetail.dts;
      userInformation['edor'] = employeeDetail.edor;
      userInformation['dofa'] = employeeDetail.dofa;
      userInformation['retireeUserType'] = employeeDetail.retireeUserType;
      userInformation['dateOfDeath'] = employeeDetail.dateOfDeath;
      userInformation['staffId'] = employeeDetail.staffId;
      userInformation['emailAddress'] = cobraUser.emailAddress;
      userInformation['ippisDate'] = employerDetails?.ippisDate;
      userInformation['sectorCode'] = employerDetails?.sectorCode;

      response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
      const draftRecord = await this.enrolmentDraftRepository.findOne({ rsaPin: getEnrolmentDraftDto.rsaPin });
      const result: { draft?: any; userInformation?: any } = {};
      if (draftRecord) {
        result.draft = {
          formData: JSON.parse(draftRecord.formData),
          stepNumber: draftRecord.stepNumber,
          rsaPin: draftRecord.rsaPin,
          enrolmentStatus: draftRecord.enrolmentStatus,
        };
      }

      response.setDescription('ECRS details Retrieved successfully');
      result.userInformation = userInformation;

      response.content = result;
      return response;
    } catch (error) {
      this.logger.error(`getEnrolmentProgress error: ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while retrieving enrollment progress, please try again');
      return response;
    }
  }

  async callEcrsService(requestPayload: RetrieveUserDto): Promise<EcrsUserResponseDto> {
    try {
      return await firstValueFrom(
        this.ecrsClient.send({ cmd: EcrsServiceClientConstant.RETRIEVE_USER }, requestPayload)
      );
    } catch (error) {
      this.logger.error(
        `Error occurred while retrieving COBRA User data from ECRS ms ${error instanceof Error ? error.stack : error}`
      );
      return new EcrsUserResponseDto(ResponseCodeEnum.ERROR);
    }
  }

  isEnrollmentMachineEventType(type: any): type is EnrolmentMachineEvent['type'] {
    return eventValues.includes(type);
  }

  async processEnrolment(processEnrolmentDto: ProcessEnrolmentDto, req: ICustomRequest) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);

    const processEnrolmentDtoLog =
      processEnrolmentDto.action === 'PFA_APPROVAL'
        ? `${processEnrolmentDto.rsaPin}_${processEnrolmentDto.action}`
        : processEnrolmentDto;
    console.log('processEnrolmentDto:::: ', processEnrolmentDtoLog);
    if (!this.isEnrollmentMachineEventType(processEnrolmentDto.action)) {
      response.setDescription(`Invalid action type ${processEnrolmentDto.action} provided.`);
      return response;
    }

    const { action, formData, comment } = processEnrolmentDto;
    const actorEmailAddress = req.user.email;

    const workflowEvent: EnrolmentMachineEvent = this.createWorkflowEvent(action, actorEmailAddress, formData, comment);

    const additionalInfo: Record<string, any> = {
      pfaCode: req.user.pfaCode,
      actorEmailAddress: req.user.email,
    };

    const validationResponse = await this.workflowService.checkIfValidTransition(
      workflowEvent,
      processEnrolmentDto.rsaPin,
      additionalInfo,
      ProcessTypeEnum.RETIREMENT_CONTRIBUTION_ACCRUED_RIGHTS
    );

    if (validationResponse.code !== ResponseCodeEnum.SUCCESS || !validationResponse.actor) {
      this.logger.error(
        `Invalid transition: ${validationResponse.description}:::::: validationResponse.actor ${validationResponse.code}`
      );
      response.setDescription(validationResponse.description);
      return response;
    }

    console.log('workflowEvent:::: ', workflowEvent);

    const result = await this.workflowService.processEvent(
      workflowEvent,
      processEnrolmentDto.rsaPin,
      additionalInfo,
      validationResponse.actor,
      validationResponse.workflowState
    );

    if (result.code === ResponseCodeEnum.SUCCESS && workflowEvent.type === 'PENCOM_VALIDATOR_APPROVAL') {
      const cacheKey = `${CronJobNamesConstant.EXIT_ACCRUED_RIGHTS_CONTRIBUTION_JOB}:${processEnrolmentDto.rsaPin}`;
      // Check if the record exists in Redis cache
      const exists = await this.redisService.get(cacheKey);
      if (!exists) {
        // put in queue immediately after approval
        await this.rmqClient.emit(EnrollmentEngineServiceClientConstant.EXIT_AR_CONTRIBUTION_COMPUTATION, {
          rsaPin: processEnrolmentDto.rsaPin,
        });
        // Store in Redis to prevent re-processing
        await this.redisService.set(cacheKey, 'processing');
      }
    }

    return result;
  }

  async processBulkEnrolment(processEnrolmentDto: ProcessBulkEnrolmentDto, req: ICustomRequest) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    if (!this.isEnrollmentMachineEventType(processEnrolmentDto.action)) {
      response.setDescription(`Invalid action type ${processEnrolmentDto.action} provided.`);
      return response;
    }
    const { action, formData } = processEnrolmentDto;
    const comment = '';

    if (!formData || !formData.batchName) {
      response.setDescription('Please provide Batch Name');
      return response;
    }

    let rsaPins = [];
    if (['AR_BATCHING', 'CR_BATCHING'].includes(processEnrolmentDto.action)) {
      const cachedRecords = await this.batchingService.getBatchById(formData.batchName);
      if (!cachedRecords || !cachedRecords.records || cachedRecords.records.length === 0) {
        response.setDescription('Batch not found or expired, please try again');
        return response;
      }

      const filterDtoDetail = cachedRecords.filterDto as BatchFilterDto;
      if (
        (filterDtoDetail.batchType.toLowerCase() === 'contributions' && !processEnrolmentDto.action.startsWith('CR')) ||
        (filterDtoDetail.batchType.toLowerCase() === 'accrued rights' && !processEnrolmentDto.action.startsWith('AR'))
      ) {
        this.logger.error(
          `Invalid Action: ${processEnrolmentDto.action} provided for Batch Type: ${filterDtoDetail.batchType}`
        );
        response.setDescription(
          `Invalid Action: ${processEnrolmentDto.action} provided for Batch Type: ${filterDtoDetail.batchType}`
        );
        return response;
      }

      rsaPins = cachedRecords.records?.map((record: EnrolmentBiodata) => record.rsaPin) || [];
    } else {
      if (['AR_CONFIRM_PAYMENT', 'CR_CONFIRM_PAYMENT'].includes(processEnrolmentDto.action)) {
        const batch = await this.batchRepository.findOne({ batchName: formData.batchName });
        if (!batch) {
          response.setDescription('Batch not found with the provided details, please check and try again');
          return response;
        }

        const cobraUser = await this.cobraUserRepository.findOne({ emailAddress: req.user.email });
        if (!cobraUser) {
          response.setDescription('Unable to validate provided initiator details');
          return response;
        }

        if (cobraUser.userType !== UserTypeEnum.PFA) {
          response.setDescription('Initiator of this request must be a PFA user ');
          return response;
        }

        const stats: PfaPaymentStats = await this.enrolmentBiodataRepository.findPfaPaymentStatsInBatch(
          action as string,
          formData.batchName,
          req.user.pfaCode
        );
        if (!stats || stats.totalInBatchForPfa === 0) {
          response.setDescription(
            `Your PFA ${req.user.pfaCode} is not part of the provided batch ${formData.batchName}.`
          );
          return response;
        }
        if (stats.pendingInBatchForPfa > 0) {
          response.setDescription(
            'There are pending payments that have not been confirmed by your PFA, please check and try again.'
          );
          return response;
        }

        if (!batch.paymentConfirmedPfa.includes(req.user.pfaCode)) {
          batch.paymentConfirmedPfa = batch.paymentConfirmedPfa
            ? `${batch.paymentConfirmedPfa}, ${req.user.pfaCode}`
            : req.user.pfaCode;
          await this.batchRepository.saveEntity(batch);
          this.logger.debug('Batch payment confirmed pfa updated successfully');
        }

        if (stats.totalPendingPinInBatch > 0) {
          response.setResponseCode(ResponseCodeEnum.SUCCESS);
          return response;
        }
      } else if (['AR_REQUEST_EXCO_APPROVAL', 'CR_REQUEST_EXCO_APPROVAL'].includes(processEnrolmentDto.action)) {
        const batchDocument = await this.batchDocumentRepository.findOne({
          batch: { batchName: formData.batchName },
        });

        if (
          !batchDocument ||
          !batchDocument.excoMemo ||
          (processEnrolmentDto.action.startsWith('CR') && !batchDocument.excoMemoAppendix)
        ) {
          response.setDescription(
            'Batch document not found, please generate EXCO MEMO document for the batch and try again'
          );
          return response;
        }
      } else if (['AR_PAYMENT_REMITTED', 'CR_PAYMENT_REMITTED'].includes(processEnrolmentDto.action)) {
        const batchDocument = await this.batchDocumentRepository.findOne({
          batch: { batchName: formData.batchName },
        });

        if (!batchDocument || !batchDocument.accountConfirmationReceipt) {
          response.setDescription(
            'Batch Account Memo Receipt has not been uploaded, please upload Account Memo Receipt document for the batch and try again'
          );
          return response;
        }
      } else if (['AR_REQUEST_ACCOUNT_PAYMENT', 'CR_REQUEST_ACCOUNT_PAYMENT'].includes(processEnrolmentDto.action)) {
        const batchDocument = await this.batchDocumentRepository.findOne({
          batch: { batchName: formData.batchName },
        });

        if (!batchDocument || !batchDocument.accountMemo) {
          response.setDescription(
            'Batch Account Memo has not been generated, please generate Account Memo document for the batch and try again'
          );
          return response;
        }
      } else if (['AR_NOTIFY_PFA_PFC', 'CR_NOTIFY_PFA_PFC'].includes(processEnrolmentDto.action)) {
        const pfaDocuments = await this.tbcEnrollmentPfaMemoDocumentRepository.findBy({
          batch: { batchName: formData.batchName },
        });

        if (!pfaDocuments || pfaDocuments.length === 0) {
          response.setDescription(
            'Batch PFA Payment document not found, please generate PFA payment Memo document for the batch and try again'
          );
          return response;
        }

        const pfcMemoDocuments = await this.tbcEnrollmentPfcMemoDocumentRepository.findBy({
          batch: { batchName: formData.batchName },
        });
        if (!pfcMemoDocuments || pfcMemoDocuments.length === 0) {
          response.setDescription(
            'Batch PFC Payment document not found, please generate PFC payment Memo document for the batch and try again'
          );
          return response;
        }
      }

      rsaPins = processEnrolmentDto.action.startsWith('AR')
        ? await this.enrolmentBiodataRepository.getPinsByArBatchName(formData.batchName)
        : await this.enrolmentBiodataRepository.getPinsByCrBatchName(formData.batchName);
    }

    if (!rsaPins || rsaPins.length === 0) {
      response.setDescription('No records found for the provided batch ID');
      return response;
    }

    const actorEmailAddress = req.user.email;
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      formData.queryRunner = queryRunner;
      const workflowEvent: EnrolmentMachineEvent = this.createWorkflowEvent(
        action,
        actorEmailAddress,
        formData,
        comment
      );
      const additionalInfo: Record<string, any> = {
        actorEmailAddress: req.user.email,
        batchName: formData.batchName,
      };

      const validationResponse = await this.workflowService.validateBatchTransitions(
        rsaPins,
        workflowEvent,
        additionalInfo,
        ProcessTypeEnum.RETIREMENT_CONTRIBUTION_ACCRUED_RIGHTS
      );

      const invalidRecords = validationResponse.filter((r) => !r.valid);

      console.log('invalidRecord pins: ', invalidRecords);
      if (invalidRecords.length > 0) {
        response.setDescription('One or more records failed validation');
        response.content = invalidRecords;
        return response;
      }

      console.log('workflowEvent:::: ', workflowEvent);

      const result = await this.workflowService.processBatchEvent(
        rsaPins,
        workflowEvent,
        additionalInfo,
        ProcessTypeEnum.RETIREMENT_CONTRIBUTION_ACCRUED_RIGHTS,
        queryRunner
      );

      console.log('Bulk result:::: ', result);
      return result;
    } finally {
      await queryRunner.release();
    }
  }

  private createWorkflowEvent(
    action: string,
    actorEmailAddress: string,
    formData?: Record<string, any>,
    comment?: string
  ): EnrolmentMachineEvent {
    if (action === 'ENROLMENT') {
      return {
        type: action as 'ENROLMENT',
        actorEmailAddress,
        formData,
        rsaPin: formData.rsaPin as string,
      };
    }

    const baseEvent = { type: action, actorEmailAddress };

    switch (action) {
      case 'PFA_APPROVAL':
      case 'PENCOM_VALIDATOR_APPROVAL':
      case 'COMPUTATION_ANALYSIS':
      case 'CR_BATCHING':
      case 'AR_BATCHING':
      case 'AR_DELETE_BATCH':
      case 'CR_DELETE_BATCH':
      case 'AR_REQUEST_EXCO_APPROVAL':
      case 'CR_REQUEST_EXCO_APPROVAL':
      case 'AR_EXCO_PAYMENT_APPROVAL':
      case 'CR_EXCO_PAYMENT_APPROVAL':
      case 'AR_REQUEST_ACCOUNT_PAYMENT':
      case 'CR_REQUEST_ACCOUNT_PAYMENT':
      case 'AR_PAYMENT_REMITTED':
      case 'CR_PAYMENT_REMITTED':
      case 'AR_NOTIFY_PFA_PFC':
      case 'CR_NOTIFY_PFA_PFC':
      case 'AR_NON_CONFIRMATION':
      case 'CR_NON_CONFIRMATION':
      case 'AR_CONFIRM_PAYMENT':
      case 'CR_CONFIRM_PAYMENT':
      case 'AR_COMPLETED':
      case 'CR_COMPLETED':
        return {
          ...baseEvent,
          formData,
        } as EnrolmentMachineEvent;
      case 'PENCOM_VALIDATOR_APPROVE_AMENDMENT_REQUEST':
      case 'PENCOM_VALIDATOR_REJECTION':
      case 'AUDIT_SUPERVISOR_REJECTION':
      case 'AUDIT_VALIDATOR_REJECTION':
        return {
          ...baseEvent,
          comment,
        } as EnrolmentMachineEvent;
      default:
        return baseEvent as EnrolmentMachineEvent;
    }
  }

  async retrieveEnrollmentHistory(uniqueId: string) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    try {
      const cacheKey = `MV-REFRESH:${uniqueId}`;
      const alreadyRefreshed = await this.redisService.get(cacheKey);
      if (!alreadyRefreshed) {
        await this.refreshMaterializedView('ENROLMENT_HISTORY_VIEW');
        await this.redisService.set(cacheKey, uniqueId, 600);
      }
      const result = await this.enrolmentHistoryRepository.find({
        where: { uniqueId: uniqueId },
        order: { eventTimestamp: 'ASC' },
      });
      response.content = result;
      response.setResponseCode(ResponseCodeEnum.SUCCESS);
    } catch (error) {
      this.logger.error(`retrieveEnrollmentHistory error: ${error instanceof Error ? error.stack : error}`);
      response.setDescription('Error occurred while retrieving nominal roll, please try again');
    }

    return response;
  }

  async refreshMaterializedView(viewName: string): Promise<void> {
    const sanitizedViewName = viewName.toUpperCase(); // optional but safe
    await this.dataSource.query(`BEGIN DBMS_MVIEW.REFRESH(:viewName, 'C'); END;`, [sanitizedViewName]);
  }

  private populateFormFields(
    enrollmentDraft: EnrolmentDraft,
    saveEnrolmentDraftDto: SaveEnrolmentDraftDto
  ): SaveEnrolmentDraftDto {
    if (!enrollmentDraft) {
      return saveEnrolmentDraftDto;
    }

    try {
      const draftFormData = JSON.parse(enrollmentDraft.formData || '{}');

      const mergedFormData = {
        ...draftFormData, // Base data from saved draft
        ...saveEnrolmentDraftDto.formData, // New data takes priority
      };

      // Return the merged result
      return {
        ...saveEnrolmentDraftDto,
        formData: mergedFormData,
      };
    } catch (error) {
      console.error('Error parsing enrollment form data:', error);
      return saveEnrolmentDraftDto; // Return original if there's an error
    }
  }

  async reassign(reassignmentDto: ReassignmentDto): Promise<BaseResponseDto> {
    let enrollmentSummary: EnrolmentSummary[] = [];
    if (reassignmentDto.type === 'ENROLLED') {
      enrollmentSummary = await this.enrolmentSummaryRepository.getPendingRecordByAssignedTo(
        reassignmentDto.taskOwnerEmail.toLowerCase()
      );
    } else {
      enrollmentSummary = await this.enrolmentSummaryRepository.getPendingRecordByAuditorAssignedTo(
        reassignmentDto.taskOwnerEmail.toLowerCase()
      );
    }

    if (!enrollmentSummary || enrollmentSummary.length === 0) {
      throw new CustomException('No records found for the provided task owner email');
    }
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const userCountMapping = reassignmentDto.userCountMapping;
      const userEmails = Object.keys(userCountMapping);

      for (const email of userEmails) {
        const count = userCountMapping[email];
        const userEnrollmentSummary = enrollmentSummary.splice(0, count);
        const cobraUser = await this.cobraUserRepository.findOne({ emailAddress: email });

        if (!cobraUser) {
          throw new CustomException(`User not found: ${email}`);
        }
        let partialEntity = {
          assignedTo: cobraUser,
          assignmentDate: new Date(),
        } as Partial<EnrolmentSummary>;

        if (reassignmentDto.type === 'COMPUTED') {
          partialEntity = {
            auditorAssignedTo: email,
            auditorAssignmentDate: new Date(),
          };
        }
        await queryRunner.manager.update(
          EnrolmentSummary,
          { pk: In(userEnrollmentSummary.map((r) => r.pk)), status: reassignmentDto.type },
          partialEntity
        );
      }

      await queryRunner.commitTransaction();
      response.setResponseCode(ResponseCodeEnum.SUCCESS);
      response.setDescription('Reassignment successful');
      return response;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getEnrollmentSummaryCount(emailAddress: string): Promise<BaseResponseListWithContentNoPagination<number>> {
    const count = await this.enrolmentSummaryRepository.getCountOfPendingRecordByAssignedTo(emailAddress.toLowerCase());
    const response = new BaseResponseListWithContentNoPagination<number>(ResponseCodeEnum.SUCCESS);
    response.content = count;
    return response;
  }

  async getEnrollmentSummaryCountByAuditor(emailAddress: string) {
    const count = await this.enrolmentSummaryRepository.getCountOfPendingRecordByAuditorAssignedTo(emailAddress);
    const response = new BaseResponseListWithContentNoPagination<number>(ResponseCodeEnum.SUCCESS);
    response.content = count;
    return response;
  }
}
