import { Injectable } from '@nestjs/common';
import { LegacyRecordRepository } from '@app/shared/enrollment-service/repositories/legacy-record.repository';
import { LegacyRecordDocumentRepository } from '@app/shared/enrollment-service/repositories/legacy-record-document.repository';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { ResponseCodeEnum } from '@app/shared/enums';
import { LegacyRecordSearchDto } from '@app/shared/dto/enrollment/legacy-record-search.dto';
import {
  BaseResponseListWithContentNoPagination,
  BaseResponseWithContent,
  BaseResponseWithContentNoPagination,
  BaseResponseWithNoCountInfo,
} from '@app/shared/dto/response/base-response-with-content';
import { CustomException } from '@app/shared/filters/exception.dto';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { LegacyRecordDto } from '@app/shared/enrollment-service/dtos/legacy-record.dto';

@Injectable()
export class LegacyRecordService {
  constructor(
    private readonly legacyRecordRepository: LegacyRecordRepository,
    private readonly legacyRecordDocumentRepository: LegacyRecordDocumentRepository
  ) {}

  async getLegacyRecords(searchDto: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<LegacyRecordDto>> {
    return await this.legacyRecordRepository.getRecords(searchDto);
  }

  async getLegacyRecordDocument(legacyRecordId: number): Promise<BaseResponseListWithContentNoPagination<any>> {
    const document = await this.legacyRecordDocumentRepository.findByLegacyRecordId(legacyRecordId);

    if (!document) {
      throw new CustomException('Document not found for the specified legacy record');
    }

    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
    response.content = {
      documentData: document.data.toString('base64'),
    };

    response.setDescription('Document retrieved successfully');
    return response;
  }
}
