/* eslint-disable */
import { Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import {
  RetrieveOrganisationDetailsDto,
  RetrieveSectorEmployerDetailsDto,
  RetrieveYearDetailsDto,
  RetrieveYearSectorDetailsDto,
} from '@app/shared/dto/enrollment/enrolment-draft.dto';
import { TbeOrganisationsRepository } from '@app/shared/enrollment-service/repository/tbe-organisations.repository';
import {
  BaseResponseWithContent,
  BaseResponseWithContentNoPagination,
} from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';

@Injectable()
export class EmployerService {
  constructor(
    private readonly logger: PinoLogger,
    private readonly tbeOrganisationsRepository: TbeOrganisationsRepository,
    private readonly redisCacheService: RedisCacheService
  ) {}

  async retrieveEmployerDetails(
    retrieveEmployerDetailsDto: RetrieveSectorEmployerDetailsDto
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    try {
      const cacheKey = `EMP_FULL_DETAIL-${retrieveEmployerDetailsDto.employerCode}-${retrieveEmployerDetailsDto.organizationSector}-${retrieveEmployerDetailsDto.year}`;
      const cacheResult = await this.redisCacheService.get(cacheKey);
      if (cacheResult) {
        return JSON.parse(cacheResult);
      }

      const { employerCode, year, organizationSector } = retrieveEmployerDetailsDto;
      const result = await this.tbeOrganisationsRepository.getEmployerSalaryDetails(
        employerCode,
        year,
        organizationSector
      );
      const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
      response.setDescription('Employer details retrieved successfully');
      response.content = result;
      await this.redisCacheService.set(cacheKey, JSON.stringify(response));
      return response;
    } catch (error) {
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while retrieving employer details, please try again');
      return response;
    }
  }

  async retrieveYearSectorCodeDetails(retrieveEmployerDetailsDto: RetrieveYearDetailsDto) {
    try {
      const cacheKey = `EMP_DETAIL_SECTOR-${retrieveEmployerDetailsDto.year}`;
      const cacheResult = await this.redisCacheService.get(cacheKey);
      if (cacheResult) {
        return JSON.parse(cacheResult);
      }

      const result = await this.tbeOrganisationsRepository.retrieveYearSectorCodeDetails(retrieveEmployerDetailsDto);
      const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
      response.setDescription('Employer sector code retrieved successfully');
      response.content = result;
      await this.redisCacheService.set(cacheKey, JSON.stringify(response));
      return response;
    } catch (error) {
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while retrieving employer details, please try again');
      return response;
    }
  }

  async getOrganisationSectorIppisDetails(retrieveEmployerDetailsDto: RetrieveOrganisationDetailsDto) {
    try {
      const cacheKey = `EMP_ORGANISATION_SECTOR_IPPIS-${retrieveEmployerDetailsDto.employerCode}`;
      const cacheResult = await this.redisCacheService.get(cacheKey);
      if (cacheResult) {
        return JSON.parse(cacheResult);
      }
      const result = await this.tbeOrganisationsRepository.getOrganisationSectorIppisDetails(
        retrieveEmployerDetailsDto.employerCode
      );
      if (!result) {
        const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
        response.setDescription('Employer not found');
        return response;
      }
      const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
      response.setDescription('Employer Details retrieved successfully');
      response.content = result;
      await this.redisCacheService.set(cacheKey, JSON.stringify(response));
      return response;
    } catch (error) {
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while retrieving employer details, please try again');
      return response;
    }
  }

  async retrieveSectorEmployerDetails(retrieveEmployerDetailsDto: RetrieveYearSectorDetailsDto) {
    try {
      const cacheKey = `EMP_DETAIL_YEAR_SECTOR-${retrieveEmployerDetailsDto.organizationSector}-${retrieveEmployerDetailsDto.year}`;
      const cacheResult = await this.redisCacheService.get(cacheKey);
      if (cacheResult) {
        return JSON.parse(cacheResult);
      }
      const result = await this.tbeOrganisationsRepository.retrieveSectorEmployerDetails(retrieveEmployerDetailsDto);
      const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
      response.setDescription('Employer sector details retrieved successfully');
      response.content = result;
      await this.redisCacheService.set(cacheKey, JSON.stringify(response));
      return response;
    } catch (error) {
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while retrieving employer details, please try again');
      return response;
    }
  }
}
