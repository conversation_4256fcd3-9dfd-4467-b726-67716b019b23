import { Injectable } from '@nestjs/common';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import {
  BaseResponseWithContent,
  BaseResponseWithContentNoPagination,
} from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums';
import { TbeOrganisationsRepository } from '@app/shared/enrollment-service/repository/tbe-organisations.repository';
import { PinoLogger } from 'nestjs-pino';
import { TbeOrganisationDto } from '@app/shared/enrollment-service/dtos/tbe-organisation.dto';
import { MdaRepository } from '@app/shared/user-service/repositories/mda.repository';
import { TbeOrganisations } from '@app/shared/enrollment-service/entities/tbe-organisations.entity';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { isNullish } from '@app/shared/utils';

@Injectable()
export class TbeOrganisationService {
  constructor(
    private readonly tbeOrganisationsRepository: TbeOrganisationsRepository,
    private readonly mdaRepository: MdaRepository,
    private readonly logger: PinoLogger
  ) {}

  async fetchTbeOrganisations(filter: PaginatedSearchDto) {
    try {
      const response = new BaseResponseWithContent<any>(ResponseCodeEnum.SUCCESS);
      const paginatedResult = await this.tbeOrganisationsRepository.fetchTbeOrganisations(filter);
      response.content = paginatedResult.result;
      response.total = paginatedResult.total;
      response.page = paginatedResult.page;
      response.limit = paginatedResult.limit;
      return response;
    } catch (error) {
      this.logger.error(`fetchTbeOrganisations \n error:  ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while getting fetchTbeOrganisations List, please try again');
      return response;
    }
  }

  async createTbeOrganisations(tbeOrganisationDto: TbeOrganisationDto, req: ICustomRequest) {
    const response = new BaseResponseWithContentNoPagination<any>(ResponseCodeEnum.ERROR);
    const existingOrganisation = await this.tbeOrganisationsRepository.findOne({
      employerId: tbeOrganisationDto.employerId,
    });
    if (existingOrganisation) {
      this.logger.warn(`Organisation with employer Id ${tbeOrganisationDto.employerId} already exists.`);
      response.setDescription('Organisation with this employer Id already exists.');
      return response;
    }

    const mda = await this.mdaRepository.findOne({ employerId: tbeOrganisationDto.employerId });
    if (!mda) {
      this.logger.warn(`MDA with employer Id ${tbeOrganisationDto.employerId} does not exist.`);
      response.setDescription(
        'MDA with this employer Id does not exist. Kindly create the MDA first before proceeding to perform this action.'
      );
      return response;
    }

    const tbeOrganisation = new TbeOrganisations({
      sectorCode: mda.sectorCode,
      employerId: mda.employerId,
      employerName: mda.employerName,
      ippisDate: mda.ippisDate,
      ecrsEmployerCode: mda.ecrsEmployerCode,
      actorEmail: req.user.email,
    });

    await this.tbeOrganisationsRepository.saveEntity(tbeOrganisation);
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Organisation created successfully.');
    return response;
  }

  async editTbeOrganisations(tbeOrganisationDto: TbeOrganisationDto, req: ICustomRequest) {
    const response = new BaseResponseWithContentNoPagination<any>(ResponseCodeEnum.ERROR);
    const existingOrganisation = await this.tbeOrganisationsRepository.findOne({
      employerId: tbeOrganisationDto.employerId,
    });
    if (!existingOrganisation) {
      this.logger.warn(`Organisation with employer Id ${tbeOrganisationDto.employerId} does not exists.`);
      response.setDescription('Organisation with this employer Id does not exists.');
      return response;
    }

    const mda = await this.mdaRepository.findOne({ employerId: tbeOrganisationDto.employerId });
    if (!mda) {
      this.logger.warn(`MDA with employer Id ${tbeOrganisationDto.employerId} does not exist.`);
      response.setDescription(
        'MDA with this employer Id does not exist. Kindly create the MDA first before proceeding to perform this action.'
      );
      return response;
    }

    existingOrganisation.sectorCode = mda.sectorCode;
    existingOrganisation.employerId = mda.employerId;
    existingOrganisation.employerName = mda.employerName;
    existingOrganisation.ippisDate = mda.ippisDate;
    existingOrganisation.ecrsEmployerCode = mda.ecrsEmployerCode;
    existingOrganisation.active = isNullish(tbeOrganisationDto.active)
      ? existingOrganisation.active
      : tbeOrganisationDto.active;
    existingOrganisation.actorEmail = req.user.email;

    await this.tbeOrganisationsRepository.saveEntity(existingOrganisation);
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Organisation updated successfully.');
    return response;
  }
}
