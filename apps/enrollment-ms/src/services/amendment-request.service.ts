import { Injectable } from '@nestjs/common';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { AmendmentRequestRepository } from '@app/shared/enrollment-service/repositories/amendment-request.repository';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import {
  BaseResponseWithContentNoPagination,
  BaseResponseWithNoCountInfo,
} from '@app/shared/dto/response/base-response-with-content';
import {
  AmendmentRequest,
  AmendmentRequestStatus,
} from '@app/shared/enrollment-service/entities/amendment-request.entity';

import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { ResponseCodeEnum } from '@app/shared/enums/ResponseCodeEnum';
import { AmendmentRequestTableDto } from '@app/shared/enrollment-service/dtos/amendment-request-table.dto';
import { CreateAmendmentRequestDto } from '@app/shared/enrollment-service/dtos/create-amendment-request.dto';
import { UpdateAmendmentRequestDto } from '@app/shared/enrollment-service/dtos/update-amendment-request.dto';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { CustomException } from '@app/shared/filters/exception.dto';
import { ValidateAmendmentPinDto } from '@app/shared/enrollment-service/dtos/validate-amendment-pin.dto';
import { RegistrationStatusEnum } from '@app/shared/enums/registration-status.enum';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { ClientProxy } from '@nestjs/microservices';
import { Inject } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { NotificationServiceClientConstant } from '@app/shared/constants';
import { SendNotificationTemplateDto } from '@app/shared/dto/notification/send-notification-template.dto';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { EnrollmentMsService } from 'apps/enrollment-ms/src/services/enrollment-ms.service';
import { ProcessEnrolmentDto } from '@app/shared/dto/enrollment/process-enrollment.dto';

@Injectable()
export class AmendmentRequestService {
  constructor(
    private readonly amendmentRequestRepository: AmendmentRequestRepository,
    private readonly cobraUserRepository: CobraUserRepository,
    private readonly enrolmentSummaryRepository: EnrolmentSummaryRepository,
    private readonly enrollmentService: EnrollmentMsService,
    @Inject('NOTIFICATION_SERVICE_CLIENT') private readonly notificationClient: ClientProxy
  ) {}

  async createRequest(createDto: CreateAmendmentRequestDto, req: ICustomRequest): Promise<BaseResponseDto> {
    // Check enrollment status first
    // Check for existing pending request
    const pendingRequest = await this.amendmentRequestRepository.findOne({
      rsaPin: createDto.rsaPin,
      status: AmendmentRequestStatus.PENDING,
    });

    if (pendingRequest) {
      throw new CustomException(`A pending amendment request already exists for RSA PIN: ${createDto.rsaPin}`);
    }

    const enrollmentSummary = await this.enrolmentSummaryRepository.findOne({
      rsaPin: createDto.rsaPin,
    });

    if (!enrollmentSummary) {
      throw new CustomException('RSA PIN not found');
    }

    if (enrollmentSummary.status !== RegistrationStatusEnum.ENROLLED) {
      throw new CustomException('RSA PIN is not eligible for amendment. Status must be ENROLLED');
    }

    const request = new AmendmentRequest({});
    request.rsaPin = createDto.rsaPin;
    request.reason = createDto.reason;
    request.pfaCode = req.user.pfaCode;

    const requestor = await this.cobraUserRepository.findOne({
      emailAddress: req.user.email,
    });
    request.requestedBy = requestor;

    await this.amendmentRequestRepository.saveEntity(request);

    // Get the assigned user from enrollment summary
    const assignedTo = (await enrollmentSummary.assignedTo) as unknown as CobraUser;

    // Send notifications
    await this.notifyRequestorAndAssignedUser(request, enrollmentSummary, requestor, assignedTo);

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('Amendment request created successfully');
    return response;
  }

  async getRequests(filter: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<AmendmentRequestTableDto>> {
    return await this.amendmentRequestRepository.getDataTable(filter);
  }

  async updateRequest(updateDto: UpdateAmendmentRequestDto, req: ICustomRequest): Promise<BaseResponseDto> {
    // Find the amendment request
    const request = await this.amendmentRequestRepository.findOne({ pk: updateDto.pk });
    if (!request) {
      throw new CustomException('Amendment request not found');
    }

    // Check if request is already processed
    if (request.status !== AmendmentRequestStatus.PENDING) {
      throw new CustomException('Request has already been processed');
    }
    if (updateDto.status == AmendmentRequestStatus.REJECTED && !updateDto.comment) {
      throw new CustomException('Comment is required to reject a request');
    }

    // Find enrollment summary for the RSA PIN
    const enrollmentSummary = await this.enrolmentSummaryRepository.findOne({
      rsaPin: request.rsaPin,
    });

    if (!enrollmentSummary) {
      throw new CustomException('Enrollment record not found');
    }
    if (enrollmentSummary.status !== RegistrationStatusEnum.ENROLLED) {
      throw new CustomException('Enrollment record is not eligible for amendment. Status must be ENROLLED');
    }

    const assignedTo = (await enrollmentSummary.assignedTo) as unknown as CobraUser;

    if (!assignedTo || assignedTo.emailAddress !== req.user.email) {
      throw new CustomException('Only the user assigned to this enrollment can process this request!');
    }

    // Update the request
    request.status = updateDto.status;
    request.comment = updateDto.comment;

    // Send appropriate notifications based on status

    if (updateDto.status == AmendmentRequestStatus.APPROVED) {
      const processEnrolment = new ProcessEnrolmentDto();

      processEnrolment.rsaPin = request.rsaPin;
      processEnrolment.action = 'PENCOM_VALIDATOR_APPROVE_AMENDMENT_REQUEST';
      processEnrolment.formData = { rsaPin: request.rsaPin, comment: request.reason };
      processEnrolment.comment = request.reason;
      req.user['pfaCode'] = request.pfaCode; // because the workflow needs it

      const response = await this.enrollmentService.processEnrolment(processEnrolment, req);

      if (response.code === ResponseCodeEnum.ERROR) {
        throw new CustomException(response.description);
      }
    }
    await this.amendmentRequestRepository.saveEntity(request);
    const requestedBy = await request.requestedBy;
    switch (updateDto.status) {
      case AmendmentRequestStatus.APPROVED:
        await this.notifyApproval(request, enrollmentSummary, requestedBy);
        break;
      case AmendmentRequestStatus.REJECTED:
        await this.notifyRejection(request, enrollmentSummary, requestedBy);
        break;
      case AmendmentRequestStatus.CANCELLED:
        await this.notifyCancellation(request, enrollmentSummary, requestedBy, assignedTo);
        break;
    }

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('Request processed successfully');
    return response;
  }

  async validatePin(validateDto: ValidateAmendmentPinDto): Promise<BaseResponseWithContentNoPagination<any>> {
    const enrollmentSummary = await this.enrolmentSummaryRepository.findOne({ rsaPin: validateDto.rsaPin });

    if (!enrollmentSummary) {
      throw new CustomException('RSA PIN not found');
    }

    if (enrollmentSummary.status !== RegistrationStatusEnum.ENROLLED) {
      throw new CustomException('RSA PIN is not eligible for amendment. Status must be ENROLLED');
    }

    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.SUCCESS);
    const assignedTo = (await enrollmentSummary.assignedTo) as unknown as CobraUser;

    response.content = {
      firstName: enrollmentSummary.firstName,
      createDate: enrollmentSummary.createDate,
      surname: enrollmentSummary.surname,
      dateOfFirstAppointment: enrollmentSummary.dateOfFirstAppointment,
      submissionDate: enrollmentSummary.enrolmentDate,
      reviewedBy: assignedTo ? `${assignedTo.firstName} ${assignedTo.surname}` : null,
    };
    response.setDescription('RSA PIN is valid for amendment request');
    return response;
  }

  private async notifyRequestorAndAssignedUser(
    request: AmendmentRequest,
    enrollmentSummary: any,
    requestor: CobraUser,
    assignedTo: CobraUser
  ): Promise<void> {
    try {
      // Notify PFA Requestor
      const requestorNotification = new SendNotificationTemplateDto();
      requestorNotification.emailRecipients = [requestor.emailAddress];
      requestorNotification.notificationType = NotificatonTypeEnum.AMENDMENT_REQUEST_INITIATED_PFA;
      requestorNotification.placeholders = {
        pfaRequesterName: requestor.firstName,
        firstName: enrollmentSummary.firstName,
        lastName: enrollmentSummary.surname,
        rsaPin: request.rsaPin,
      };

      // Notify PENCOM Reviewer (assignedTo)
      const reviewerNotification = new SendNotificationTemplateDto();
      reviewerNotification.emailRecipients = [assignedTo.emailAddress];
      reviewerNotification.notificationType = NotificatonTypeEnum.AMENDMENT_REQUEST_REVIEW;
      reviewerNotification.placeholders = {
        reviewerName: assignedTo.firstName,
        pfaName: requestor.firstName,
        firstName: enrollmentSummary.firstName,
        lastName: enrollmentSummary.surname,
        rsaPin: request.rsaPin,
        amendmentReason: request.reason,
      };

      // Notify Retiree
      const retireeNotification = new SendNotificationTemplateDto();
      retireeNotification.emailRecipients = [enrollmentSummary.emailAddress];
      retireeNotification.notificationType = NotificatonTypeEnum.AMENDMENT_REQUEST_INITIATED_RETIREE;
      retireeNotification.placeholders = {
        firstName: enrollmentSummary.firstName,
      };

      await Promise.all([
        firstValueFrom(
          this.notificationClient.emit(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            requestorNotification
          )
        ),
        firstValueFrom(
          this.notificationClient.emit(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            reviewerNotification
          )
        ),
        firstValueFrom(
          this.notificationClient.emit(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            retireeNotification
          )
        ),
      ]);
    } catch (error) {
      // Log error but don't fail the request
      console.error('Failed to send amendment request notifications:', error);
    }
  }

  private async notifyCancellation(
    request: AmendmentRequest,
    enrollmentSummary: any,
    requestor: CobraUser,
    assignedTo: CobraUser
  ): Promise<void> {
    try {
      // Notify PENCOM
      const pencomNotification = new SendNotificationTemplateDto();
      pencomNotification.emailRecipients = [assignedTo.emailAddress];
      pencomNotification.notificationType = NotificatonTypeEnum.AMENDMENT_REQUEST_CANCELLATION_PENCOM;
      pencomNotification.placeholders = {
        assignedToName: assignedTo.firstName,
        firstName: enrollmentSummary.firstName,
        lastName: enrollmentSummary.surname,
        rsaPin: request.rsaPin,
        pfaName: requestor.firstName,
      };

      // Notify PFA
      const pfaNotification = new SendNotificationTemplateDto();
      pfaNotification.emailRecipients = [requestor.emailAddress];
      pfaNotification.notificationType = NotificatonTypeEnum.AMENDMENT_REQUEST_CANCELLATION_PFA;
      pfaNotification.placeholders = {
        pfaRequesterName: requestor.firstName,
        firstName: enrollmentSummary.firstName,
        lastName: enrollmentSummary.surname,
        rsaPin: request.rsaPin,
      };

      // Notify Retiree
      const retireeNotification = new SendNotificationTemplateDto();
      retireeNotification.emailRecipients = [enrollmentSummary.emailAddress];
      retireeNotification.notificationType = NotificatonTypeEnum.AMENDMENT_REQUEST_CANCELLATION_RETIREE;
      retireeNotification.placeholders = {
        firstName: enrollmentSummary.firstName,
      };

      await Promise.all([
        firstValueFrom(
          this.notificationClient.emit(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            pencomNotification
          )
        ),
        firstValueFrom(
          this.notificationClient.emit(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            pfaNotification
          )
        ),
        firstValueFrom(
          this.notificationClient.emit(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            retireeNotification
          )
        ),
      ]);
    } catch (error) {
      // Log error but don't fail the request
      console.error('Failed to send amendment cancellation notifications:', error);
    }
  }

  private async notifyRejection(
    request: AmendmentRequest,
    enrollmentSummary: any,
    requestor: CobraUser
  ): Promise<void> {
    try {
      // Notify PFA
      const pfaNotification = new SendNotificationTemplateDto();
      pfaNotification.emailRecipients = [requestor.emailAddress];
      pfaNotification.notificationType = NotificatonTypeEnum.AMENDMENT_REQUEST_REJECTION_PFA;
      pfaNotification.placeholders = {
        pfaRequesterName: requestor.firstName,
        firstName: enrollmentSummary.firstName,
        lastName: enrollmentSummary.surname,
        rsaPin: request.rsaPin,
      };

      // Notify Retiree
      const retireeNotification = new SendNotificationTemplateDto();
      retireeNotification.emailRecipients = [enrollmentSummary.emailAddress];
      retireeNotification.notificationType = NotificatonTypeEnum.AMENDMENT_REQUEST_REJECTION_RETIREE;
      retireeNotification.placeholders = {
        firstName: enrollmentSummary.firstName,
      };

      await Promise.all([
        firstValueFrom(
          this.notificationClient.emit(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            pfaNotification
          )
        ),
        firstValueFrom(
          this.notificationClient.emit(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            retireeNotification
          )
        ),
      ]);
    } catch (error) {
      console.error('Failed to send amendment rejection notifications:', error);
    }
  }

  private async notifyApproval(request: AmendmentRequest, enrollmentSummary: any, requestor: CobraUser): Promise<void> {
    try {
      // Notify PFA
      const pfaNotification = new SendNotificationTemplateDto();
      pfaNotification.emailRecipients = [requestor.emailAddress];
      pfaNotification.notificationType = NotificatonTypeEnum.AMENDMENT_REQUEST_APPROVAL_PFA;
      pfaNotification.placeholders = {
        pfaRequesterName: requestor.firstName,
        firstName: enrollmentSummary.firstName,
        lastName: enrollmentSummary.surname,
        rsaPin: request.rsaPin,
      };

      // Notify Retiree
      const retireeNotification = new SendNotificationTemplateDto();
      retireeNotification.emailRecipients = [enrollmentSummary.emailAddress];
      retireeNotification.notificationType = NotificatonTypeEnum.AMENDMENT_REQUEST_APPROVAL_RETIREE;
      retireeNotification.placeholders = {
        firstName: enrollmentSummary.firstName,
      };

      await Promise.all([
        firstValueFrom(
          this.notificationClient.emit(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            pfaNotification
          )
        ),
        firstValueFrom(
          this.notificationClient.emit(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            retireeNotification
          )
        ),
      ]);
    } catch (error) {
      console.error('Failed to send amendment approval notifications:', error);
    }
  }
}
