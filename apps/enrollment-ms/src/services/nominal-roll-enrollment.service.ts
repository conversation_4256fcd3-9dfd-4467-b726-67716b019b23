import { Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import {
  BaseResponseWithContent,
  BaseResponseWithContentNoPagination,
} from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums';
import { ProcessNominalRollDto } from '@app/shared/dto/enrollment/process-nominal-roll.dto';
import {
  eventValues,
  NominalRollMachineEvent,
} from '@app/shared/workflow/workflow/norminal-roll-contribution.workflow';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { NominalRollRepository } from '@app/shared/enrollment-service/repository/nominal-roll.repository';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { ProcessTypeEnum } from '@app/shared/workflow/enums/ProcessTypeEnum';
import { NrWorkflowService } from '@app/shared/workflow/services/nr-workflow.service';
import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';
import { NominalRollContributionFilterDto } from '@app/shared/dto/enrollment/nominal-roll-contribution-filter-dto';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { NominalRollDetailSearchDto } from '@app/shared/dto/enrollment/nominalroll/nominal-roll-employee.dto';
import { NominalRollBatchRepository } from '@app/shared/enrollment-service/repository/nominal-roll-batch.repository';
import { BatchDocumentDto } from '@app/shared/dto/enrollment/exit-batching/batch-filter.dto';
import { NrBatchDocumentRepository } from '@app/shared/enrollment-service/repository/nr-batch-document.repository';
import {
  BatchFileUploadRequestDto,
  BatchPaymentConfirmationUploadRequestDto,
} from '@app/shared/dto/request/file-upload-request.dto';
import { BatchUploadProcessTypeEnum } from '@app/shared/enums/batch-upload-process-type-enum';
import * as xlsx from 'xlsx';
import { TbcNominalRollPfaMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-nominal-roll-pfa-memo-document-repository.service';
import { TbcNominalRollPfcMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-nominal-roll-pfc-memo-document-repository.service';
import { PfaPaymentStats } from '@app/shared/enrollment-service/entities/entity.types';
import { NominalRollHistoryRepository } from '@app/shared/enrollment-service/repository/nominal-roll-history.repository';
import { stringToDate } from '@app/shared/utils';

@Injectable()
export class NominalRollEnrollmentService {
  constructor(
    private nrWorkflowService: NrWorkflowService,
    private redisService: RedisCacheService,
    private readonly nominalRollRepository: NominalRollRepository,
    private readonly nominalRollHistoryRepository: NominalRollHistoryRepository,
    private readonly nominalRollBatchRepository: NominalRollBatchRepository,
    private readonly nrBatchDocumentRepository: NrBatchDocumentRepository,
    private readonly tbcNominalRollPfaMemoDetailRepository: TbcNominalRollPfaMemoDocumentRepository,
    private readonly tbcNominalRollPfcMemoDocumentRepository: TbcNominalRollPfcMemoDocumentRepository,
    private readonly logger: PinoLogger,
    @InjectDataSource() private readonly dataSource: DataSource
  ) {}

  isNominalRollMachineEventType(type: any): type is NominalRollMachineEvent['type'] {
    return eventValues.includes(type);
  }

  async processRequest(
    processNominalRollDto: ProcessNominalRollDto,
    req: ICustomRequest
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    const response: BaseResponseWithContentNoPagination<unknown> = new BaseResponseWithContentNoPagination<unknown>(
      ResponseCodeEnum.ERROR
    );

    console.log('processNominalRollDto:::: ', processNominalRollDto);
    if (!this.isNominalRollMachineEventType(processNominalRollDto.action)) {
      response.setDescription(`Invalid action type ${processNominalRollDto.action} provided.`);
      return response;
    }
    const { action, options } = processNominalRollDto;

    let rsaPins = [];
    if ('CR_BATCHING' === action) {
      if (!options || !options.batchName) {
        response.setDescription('Please provide Batch Name');
        return response;
      }
      const batchKey = `nr-cr-batch-filter:${options.batchName}`;
      const cachedRecords = await this.redisService.get(`${batchKey}`);
      if (!cachedRecords) {
        response.setDescription('Batch not found or expired, please try again');
        return response;
      }

      const batchData = JSON.parse(cachedRecords as string);
      if (!batchData.records || batchData.records.length === 0) {
        response.setDescription('Batch not found or expired, please try again');
        return response;
      }
      rsaPins = batchData.records?.map((record: NominalRoll) => record.rsaPin) || [];
    } else if ('CR_REQUEST_EXCO_APPROVAL' === action) {
      // verify if EXCO memo document has been generated for batch
      if (!options || !options.batchName) {
        response.setDescription('Please provide Batch Name');
        return response;
      }

      const batchDocument = await this.nrBatchDocumentRepository.findOne({
        batch: { batchName: options.batchName },
      });
      if (!batchDocument || !batchDocument.excoMemo || !batchDocument.excoMemoAppendix) {
        response.setDescription(
          'Batch document not found, please generate EXCO MEMO document for the batch and try again'
        );
        return response;
      }
      rsaPins = await this.nominalRollRepository.getPinsByBatchName(options.batchName);
    } else if ('CR_NOTIFY_PFA_PFC' === action) {
      if (!options || !options.batchName) {
        response.setDescription('Please provide Batch Name');
        return response;
      }

      const pfaDocuments = await this.tbcNominalRollPfaMemoDetailRepository.findBy({
        batch: { batchName: options.batchName },
      });
      if (!pfaDocuments || pfaDocuments.length === 0) {
        response.setDescription(
          'Batch PFA Payment document not found, please generate PFA payment Memo document for the batch and try again'
        );
        return response;
      }
      const pfcMemoDocuments = await this.tbcNominalRollPfcMemoDocumentRepository.findBy({
        batch: { batchName: options.batchName },
      });
      if (!pfcMemoDocuments || pfcMemoDocuments.length === 0) {
        response.setDescription(
          'Batch PFC Payment document not found, please generate PFC payment Memo document for the batch and try again'
        );
        return response;
      }
      rsaPins = await this.nominalRollRepository.getPinsByBatchName(options.batchName);
    } else if ('CR_REQUEST_ACCOUNT_PAYMENT' === action) {
      if (!options || !options.batchName) {
        response.setDescription('Please provide Batch Name');
        return response;
      }

      const batchDocument = await this.nrBatchDocumentRepository.findOne({
        batch: { batchName: options.batchName },
      });
      if (!batchDocument || !batchDocument.accountMemo) {
        response.setDescription(
          'Batch Account Memo has not been generated, please generate Account Memo document for the batch and try again'
        );
        return response;
      }
      rsaPins = await this.nominalRollRepository.getPinsByBatchName(options.batchName);
    } else if ('CR_PAYMENT_REMITTED' === action) {
      if (!options || !options.batchName) {
        response.setDescription('Please provide Batch Name');
        return response;
      }

      const batchDocument = await this.nrBatchDocumentRepository.findOne({
        batch: { batchName: options.batchName },
      });
      if (!batchDocument || !batchDocument.accountConfirmationReceipt) {
        response.setDescription(
          'Batch Account Memo Receipt has not been uploaded, please upload Account Memo Receipt document for the batch and try again'
        );
        return response;
      }
      rsaPins = await this.nominalRollRepository.getPinsByBatchName(options.batchName);
    } else if ('REQUEST_COMPUTATION' === action) {
      const filterName = options.filterName;
      if (!filterName) {
        response.setDescription(
          'Filter name not provided. Please provide a filter name to proceed with the computation request.'
        );
        return response;
      }
      const filterKey = `nr-filter:${filterName}`;
      const cachedData = await this.redisService.get(`${filterKey}`);
      if (!cachedData) {
        response.setDescription('Filtered records not found or expired, please try again');
        return response;
      }

      const batchData = JSON.parse(cachedData as string);
      rsaPins = batchData.records?.map((record: NominalRoll) => record.rsaPin) || [];
    } else if (['AUDIT_VALIDATOR_REJECTION', 'AUDIT_SUPERVISOR_REJECTION'].includes(action)) {
      if (!options.rsaPin) {
        response.setDescription('RSA Pin not provided, please check and try again');
        return response;
      }
      if (!options || !options.comment) {
        response.setDescription('Comment not provided, please check and try again');
        return response;
      }
      rsaPins = [options.rsaPin];
    } else if (
      [
        'REQUEST_COMPUTATION',
        'AUDIT_VALIDATOR_APPROVED',
        'AUDIT_SUPERVISOR_APPROVAL',
        'COMPUTATION_ANALYSIS',
        'CR_NON_CONFIRMATION',
      ].includes(action)
    ) {
      if (!options.rsaPin) {
        response.setDescription('RSA Pin not provided, please check and try again');
        return response;
      }
      rsaPins = [options.rsaPin];
    } else {
      if (!options || !options.batchName) {
        response.setDescription('Please provide Batch Name');
        return response;
      }

      if ('CR_CONFIRM_PAYMENT' === action) {
        const batch = await this.nominalRollBatchRepository.findOne({ batchName: options.batchName });
        if (!batch) {
          response.setDescription('Nominal Roll Batch not found with the provided details, please check and try again');
          return response;
        }

        const stats: PfaPaymentStats = await this.nominalRollRepository.findPfaPaymentStatsInBatch(
          options.batchName,
          req.user.pfaCode
        );

        if (!stats || stats.totalInBatchForPfa === 0) {
          response.setDescription(
            `Your PFA ${req.user.pfaCode} is not part of the provided batch ${options.batchName}.`
          );
          return response;
        }

        if (stats.pendingInBatchForPfa > 0) {
          response.setDescription(
            'There are pending payments that have not been confirmed by your PFA, please check and try again.'
          );
          return response;
        }

        if (!batch.paymentConfirmedPfa.includes(req.user.pfaCode)) {
          batch.paymentConfirmedPfa = batch.paymentConfirmedPfa
            ? `${batch.paymentConfirmedPfa}, ${req.user.pfaCode}`
            : req.user.pfaCode;
          await this.nominalRollBatchRepository.saveEntity(batch);
          this.logger.debug('Batch payment confirmed pfa updated successfully');
        }

        if (stats.totalPendingPinInBatch > 0) {
          response.setResponseCode(ResponseCodeEnum.SUCCESS);
          return response;
        }
      }

      rsaPins = await this.nominalRollRepository.getPinsByBatchName(options.batchName);
    }

    if (!rsaPins || rsaPins.length === 0) {
      response.setDescription('No records found, please check and try again');
      return response;
    }

    const actorEmailAddress = req.user.email;
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      options.queryRunner = queryRunner;
      options.actorEmailAddress = actorEmailAddress;
      const workflowEvent: NominalRollMachineEvent = this.createWorkflowEvent(action, options);

      const validationResponse = await this.nrWorkflowService.validateBatchTransitions(
        rsaPins,
        workflowEvent,
        ProcessTypeEnum.NOMINAL_ROLL_CONTRIBUTION_ACCRUED_RIGHTS
      );

      const invalidRecords = validationResponse.filter((r) => !r.valid);
      if (invalidRecords.length > 0) {
        response.setDescription('One or more records failed validation');
        response.content = invalidRecords;
        return response;
      }

      workflowEvent.options.queryRunner = queryRunner;
      return await this.nrWorkflowService.processBatchEvent(
        rsaPins,
        workflowEvent,
        ProcessTypeEnum.NOMINAL_ROLL_CONTRIBUTION_ACCRUED_RIGHTS
      );
    } finally {
      await queryRunner.release();
    }
  }

  async applyComputationFilters(
    filterDto: NominalRollContributionFilterDto
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    const response: BaseResponseWithContentNoPagination<unknown> = new BaseResponseWithContentNoPagination<unknown>(
      ResponseCodeEnum.ERROR
    );

    let allRecords;
    try {
      allRecords = await this.nominalRollRepository.fetchNominalRollForComputation(filterDto);
    } catch (error) {
      this.logger.error(
        `Error occurred while attempting to filter records, please try again: ${JSON.stringify(filterDto)} ${error instanceof Error ? error.stack : error}`
      );
      response.setDescription('Error occurred while attempting to filter records, please try again');
      return response;
    }

    // Apply amount cap if specified
    let filteredRecords = [...allRecords];

    const batchData = {
      records: filteredRecords,
      filterDto,
    };

    const batchKey = `nr-filter:${filterDto.filterName}`;
    await this.redisService.set(batchKey, JSON.stringify(batchData));

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.content = {
      records: filteredRecords,
      filterName: filterDto.filterName,
      count: filteredRecords.length,
    };
    return response;
  }

  async applyBatchFilters(
    filterDto: NominalRollContributionFilterDto
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    const response: BaseResponseWithContentNoPagination<unknown> = new BaseResponseWithContentNoPagination<unknown>(
      ResponseCodeEnum.ERROR
    );

    let allRecords;
    try {
      allRecords = await this.nominalRollRepository.fetchNominalRollForBatching(filterDto);
    } catch (error) {
      this.logger.error(
        `Error occurred while attempting to filter records, please try again: ${JSON.stringify(filterDto)} ${error instanceof Error ? error.stack : error}`
      );
      response.setDescription('Error occurred while attempting to filter records, please try again');
      return response;
    }

    // Apply amount cap if specified
    let filteredRecords: typeof allRecords = [];
    let runningTotal = 0;
    const totalAmount = filterDto.totalAmount || 0;

    if (totalAmount > 0) {
      // Cap records based on totalAmount
      for (const record of allRecords) {
        const recordAmount = parseFloat(record.amount || '0');
        if (runningTotal + recordAmount <= totalAmount) {
          runningTotal += recordAmount;
          filteredRecords.push(record);
        } else {
          break;
        }
      }
    } else {
      // No cap — just include all and calculate total
      filteredRecords = allRecords;
      for (const record of allRecords) {
        runningTotal += parseFloat(record.totalPensionPlusInterest || '0');
      }
    }

    const batchData = {
      records: filteredRecords,
      filterDto,
    };

    const batchKey = `nr-cr-batch-filter:${filterDto.filterName}`;
    await this.redisService.set(batchKey, JSON.stringify(batchData));

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.content = {
      records: filteredRecords,
      filterName: filterDto.filterName,
      count: filteredRecords.length,
      runningTotal,
      availableAmount: totalAmount,
    };
    return response;
  }

  private createWorkflowEvent(action: string, options: Record<string, any>): NominalRollMachineEvent {
    return { type: action, options } as NominalRollMachineEvent;
  }

  async retrieveLNominalRollList(searchParams: PaginatedSearchDto): Promise<BaseResponseWithContent<any>> {
    try {
      const paginatedResult = await this.nominalRollRepository.retrieveNominalRollList(searchParams);
      const response = new BaseResponseWithContent<CobraUser>(ResponseCodeEnum.SUCCESS);
      response.content = paginatedResult.result;
      response.total = paginatedResult.total;
      response.page = paginatedResult.page;
      response.limit = paginatedResult.limit;
      return response;
    } catch (error) {
      this.logger.error(`retrieveLNominalRollList error: ${error instanceof Error ? error.stack : error}`);
      const response = new BaseResponseWithContent(ResponseCodeEnum.ERROR);
      response.setDescription('Error occurred while retrieving Nominal roll details, please try again');
      return response;
    }
  }

  async retrieveNominalRollHistory(nominalRollSearchDto: NominalRollDetailSearchDto) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    try {
      response.content = await this.nominalRollHistoryRepository.getNominalRollDetailByRsaPin(
        nominalRollSearchDto.rsaPin
      );
      response.setResponseCode(ResponseCodeEnum.SUCCESS);
    } catch (error) {
      this.logger.error(`retrieveNominalRollHistory error: ${error instanceof Error ? error.stack : error}`);
      response.setDescription('Error occurred while retrieving nominal roll history, please try again');
    }
    return response;
  }

  async retrieveLNominalRollDetail(nominalRollSearchDto: NominalRollDetailSearchDto) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    try {
      response.content = await this.nominalRollRepository.getNominalRollDetailByRsaPin(nominalRollSearchDto.rsaPin);
      response.setResponseCode(ResponseCodeEnum.SUCCESS);
    } catch (error) {
      this.logger.error(`retrieveLNominalRollDetail error: ${error instanceof Error ? error.stack : error}`);
      response.setDescription('Error occurred while retrieving nominal roll detail, please try again');
    }

    return response;
  }

  async retrieveBatch(filterDto: PaginatedSearchDto) {
    return await this.nominalRollBatchRepository.retrieveBatchDetails(filterDto);
  }

  async retrieveBatchDocuments(batchDocumentDto: BatchDocumentDto) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const batchDocument = await this.nrBatchDocumentRepository.findOne({
      batch: { batchName: batchDocumentDto.batchName },
    });
    if (
      !batchDocument ||
      batchDocument.schedule === null ||
      batchDocument.excoMemo === null ||
      batchDocument.excoMemoAppendix === null
    ) {
      response.setDescription('Documents not found for provided batch, please try again');
      return response;
    }

    response.content = {
      schedule: batchDocument.schedule?.toString('base64'),
      excoMemo: batchDocument.excoMemo?.toString('base64'),
      appendix: batchDocument.excoMemoAppendix?.toString('base64'),
      accountMemo: batchDocument.accountMemo?.toString('base64'),
      accountConfirmationReceipt: batchDocument.accountConfirmationReceipt?.toString('base64'),
    };
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    return response;
  }

  async retrieveBatchPaymentDocuments(batchDocumentDto: BatchDocumentDto) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const pfaDocuments = await this.tbcNominalRollPfaMemoDetailRepository.findBy({
      batch: { batchName: batchDocumentDto.batchName },
    });
    const pfcMemoDocuments = await this.tbcNominalRollPfcMemoDocumentRepository.findBy({
      batch: { batchName: batchDocumentDto.batchName },
    });

    if (!pfaDocuments || pfaDocuments.length === 0 || !pfcMemoDocuments || pfcMemoDocuments.length === 0) {
      response.setDescription('Payment Documents not found for provided batch, please try again');
      return response;
    }

    const content: any = {};
    content.pfa = [];
    content.pfc = [];
    for (const pfaDocument of pfaDocuments) {
      const pfaDetail = {
        pfaName: pfaDocument.pfaName,
        pfaCode: pfaDocument.pfaCode,
        pfcName: pfaDocument.pfcName,
        pfcCode: pfaDocument.pfcCode,
        pfaMemo: pfaDocument.pfaMemo.toString('base64'),
        pfaSchedule: pfaDocument.pfaSchedule.toString('base64'),
      };
      content.pfa.push(pfaDetail);
    }

    for (const pfcMemoDocument of pfcMemoDocuments) {
      const pfcDetail = {
        pfcName: pfcMemoDocument.pfcName,
        pfcCode: pfcMemoDocument.pfcCode,
        pfaMemo: pfcMemoDocument.pfcMemo.toString('base64'),
      };

      content.pfc.push(pfcDetail);
    }

    response.content = content;
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    return response;
  }

  async retrievePfaBatchPaymentDocuments(batchDocumentDto: BatchDocumentDto, req: ICustomRequest) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);

    if (!req.user.pfaCode) {
      response.setDescription('Only PFA users are allowed to perform this action, please check and try again');
      return response;
    }
    const pfaDocuments = await this.tbcNominalRollPfaMemoDetailRepository.findBy({
      batch: { batchName: batchDocumentDto.batchName },
      pfaCode: req.user.pfaCode,
    });

    if (!pfaDocuments || pfaDocuments.length === 0) {
      response.setDescription('Payment Documents not found for provided batch, please try again');
      return response;
    }

    const content: any = {};
    content.pfa = [];
    for (const pfaDocument of pfaDocuments) {
      const pfaDetail = {
        pfaName: pfaDocument.pfaName,
        pfaCode: pfaDocument.pfaCode,
        pfcName: pfaDocument.pfcName,
        pfcCode: pfaDocument.pfcCode,
        pfaMemo: pfaDocument.pfaMemo.toString('base64'),
        pfaSchedule: pfaDocument.pfaSchedule.toString('base64'),
      };
      content.pfa.push(pfaDetail);
    }
    response.content = content;
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    return response;
  }

  async uploadBatchDocument(batchFileUploadRequestDto: BatchFileUploadRequestDto, file: Express.Multer.File) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const nrBatchDocument = await this.nrBatchDocumentRepository.findOne({
      batch: { batchName: batchFileUploadRequestDto.batchName },
    });

    if (!nrBatchDocument) {
      response.setDescription('Batch not found for provided batch name, please try again');
      return response;
    }

    const { processType } = batchFileUploadRequestDto;
    if (processType === BatchUploadProcessTypeEnum.ACCOUNT_PAYMENT_RECEIPT) {
      nrBatchDocument.accountConfirmationReceipt = Buffer.from(file.buffer);
    } else {
      response.setDescription('Unknown batch file upload process type, please try again');
      return response;
    }

    await this.nrBatchDocumentRepository.saveEntity(nrBatchDocument);
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Batch document uploaded successfully');
    return response;
  }

  async handlePfaPaymentConfirmation(
    file: Express.Multer.File,
    batchPaymentConfirmationUploadRequestDto: BatchPaymentConfirmationUploadRequestDto,
    req: ICustomRequest
  ) {
    const response: BaseResponseWithContentNoPagination<unknown> = new BaseResponseWithContentNoPagination<unknown>(
      ResponseCodeEnum.ERROR
    );

    if (!file) {
      response.setDescription('File not attached, please verify and try again.');
      return response;
    }

    const workbook = xlsx.read(file.buffer, { type: 'buffer', cellDates: true });
    const hiddenSheetName = 'meta';
    const hiddenSheet = workbook.Sheets[hiddenSheetName];
    if (!hiddenSheet) {
      response.setDescription('Invalid template:Please ensure you are using the provided template.');
      return response;
    }

    const marker = xlsx.utils.sheet_to_json(hiddenSheet, { header: 1 })?.[0]?.[0];
    if (marker !== 'stellarsync') {
      response.setDescription(
        'Invalid template: verification failed, please ensure you are using the provided template.'
      );
      return response;
    }

    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const rows = xlsx.utils.sheet_to_json(sheet, {
      raw: false,
      dateNF: 'mm/dd/yyyy',
    });

    if (!rows.length) {
      response.setDescription('No records found in the file.');
      return response;
    }

    const batch = await this.nominalRollBatchRepository.findOne({
      batchName: batchPaymentConfirmationUploadRequestDto.batchName,
    });
    if (!batch) {
      response.setDescription('Batch not found with the provided details.');
      return response;
    }

    if ('CR_AWAITING_PFA_PAYMENT_CONFIRMATION' !== batch.status) {
      response.setDescription(
        `Batch status: ${batch.status} not expecting a payment confirmation, please verify and try again.`
      );
      return response;
    }

    // Validate and process each row
    const errors: string[] = [];

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    for (const [index, row] of rows.entries()) {
      const rsaPin = row['RSA PIN'];
      const surname = row['SURNAME'];
      const actionDateString = row['ACTION DATE'];
      const status = row['STATUS'];
      const comment = row['COMMENT'];

      if (!rsaPin || typeof rsaPin !== 'string') {
        errors.push(`PIN ${rsaPin}: Invalid RSA PIN provided.`);
        continue;
      }

      if (
        !status ||
        typeof status !== 'string' ||
        !['CREDITED', 'REFUNDED', 'TRANSFERRED'].includes(status.trim().toUpperCase())
      ) {
        errors.push(`PIN ${rsaPin}: Invalid status provided only CREDITED, REFUNDED, TRANSFERRED are allowed.`);
        continue;
      }

      if ((!comment || typeof comment !== 'string') && status !== 'CREDITED') {
        errors.push(`PIN ${rsaPin}: Comment must be provided when status is not CREDITED.`);
        continue;
      }

      const actionDate = stringToDate(actionDateString as string);
      if (!actionDate || isNaN(actionDate.getTime())) {
        errors.push(`PIN ${rsaPin}: Invalid date provided.`);
        continue;
      }

      const nominalRoll = await this.nominalRollRepository.findOne([
        { rsaPin: rsaPin.trim(), batchId: batchPaymentConfirmationUploadRequestDto.batchName },
      ]);

      if (!nominalRoll) {
        errors.push(
          `PIN ${rsaPin}: Nominal Roll Details not found for provided PIN in batch. Confirm if PIN is in this batch`
        );
        continue;
      }

      if (nominalRoll.pfaCode !== req.user.pfaCode) {
        errors.push(`PIN ${rsaPin}: PFA details does not match .`);
        continue;
      }

      if (nominalRoll.surname !== surname) {
        errors.push(`PIN ${rsaPin}: Surname ${surname} does not match Nominal Roll: ${nominalRoll.surname}.`);
        continue;
      }

      // Update the nominal roll record and reconcile the payment status
      nominalRoll.paymentStatus = status.trim();
      nominalRoll.paymentComment = comment?.trim();
      nominalRoll.paymentDate = actionDate;
      await this.nominalRollRepository.saveEntity(nominalRoll, queryRunner);
    }

    if (errors.length) {
      await queryRunner.rollbackTransaction();
      response.setDescription('Validation failed on some rows.');
      response.content = errors;
      return response;
    }
    await queryRunner.commitTransaction();
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    return response;
  }
}
