import { Injectable, Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { PinoLogger } from 'nestjs-pino';
import { RetrieveUserDto, RetrieveUserWithPinDto } from '@app/shared/dto/ecrs/retrieve-user.dto';
import { EcrsServiceClientConstant } from '@app/shared/constants/ecrs-service-client.constant';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import { ResponseCodeEnum } from '@app/shared/enums/ResponseCodeEnum';
import { CustomException } from '@app/shared/filters/exception.dto';
import { LegacyRecordRepository } from '@app/shared/enrollment-service/repositories/legacy-record.repository';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';
import { MultiplePinResolutionPinRepository } from '../../../../libs/shared/src/enrollment-service/repositories/multiple-pin-resolution-pin.repository';

@Injectable()
export class RsaPinValidationService {
  constructor(
    @Inject('ECRS_SERVICE_CLIENT') private readonly ecrsClient: ClientProxy,
    private readonly enrolmentSummaryRepository: EnrolmentSummaryRepository,
    private readonly legacyRecordRepository: LegacyRecordRepository,
    private readonly multiplePinResolutionPinRepository: MultiplePinResolutionPinRepository,
    private readonly logger: PinoLogger
  ) {}

  async validateRsaPin(
    retrieveUserDto: RetrieveUserWithPinDto
  ): Promise<EcrsUserResponseDto & { reconciled: boolean }> {
    if (!retrieveUserDto.rsaPin.startsWith('PEN')) {
      throw new CustomException('Invalid PIN format. PIN must start with "PEN"');
    }
    const rsaPinDto = new RetrieveUserDto();
    rsaPinDto.rsaPin = retrieveUserDto.rsaPin;
    rsaPinDto.skipSurnameCheck = true;
    rsaPinDto.checkCrsStatus = true;
    rsaPinDto.userType = RetireeUserTypeEnum.DECEASED;

    const data = (await firstValueFrom(
      this.ecrsClient.send({ cmd: EcrsServiceClientConstant.RETRIEVE_USER }, rsaPinDto)
    )) as EcrsUserResponseDto;

    if (!data || data.code !== ResponseCodeEnum.SUCCESS) {
      throw new CustomException(data.description || 'Failed to retrieve user data for PIN');
    }
    data.enrolled = await this.isEnrolled(retrieveUserDto.rsaPin);

    const pin = await this.multiplePinResolutionPinRepository.findReconciledPin(retrieveUserDto.rsaPin);

    return Object.assign(data, { reconciled: !!pin });
  }

  private async isEnrolled(rsaPin: string): Promise<boolean> {
    let isEnrolled = await this.enrolmentSummaryRepository.existsByRsaPin(rsaPin);
    if (!isEnrolled) {
      isEnrolled = await this.legacyRecordRepository.existsByRsaPin(rsaPin);
    }
    return isEnrolled;
  }
}
