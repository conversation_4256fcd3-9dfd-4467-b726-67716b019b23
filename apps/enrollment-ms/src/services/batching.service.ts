import { Injectable } from '@nestjs/common';
import { EnrolmentBiodataRepository } from '@app/shared/enrollment-service/repository/enrolment-biodata.repository';
import { PinoLogger } from 'nestjs-pino';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { EnrolmentDetailRepository } from '@app/shared/enrollment-service/repository/enrolment-detail.repository';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { BatchDocumentDto, BatchFilterDto } from '@app/shared/dto/enrollment/exit-batching/batch-filter.dto';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { BatchRepository } from '@app/shared/enrollment-service/repository/batch.repository';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { ResponseCodeEnum } from '@app/shared/enums';
import { TblBatch } from '@app/shared/enrollment-service/entities/tbl-batch';
import { calculateRecordAmount } from '@app/shared/workflow/utils/workflow-utils';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { BatchDocumentRepository } from '@app/shared/enrollment-service/repository/batch-document.repository';
import { BatchFileUploadRequestDto } from '@app/shared/dto/request/file-upload-request.dto';
import { BatchUploadProcessTypeEnum } from '@app/shared/enums/batch-upload-process-type-enum';
import { TbcEnrollmentPfaMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-enrollment-pfa-memo-document-repository.service';
import { TbcEnrollmentPfcMemoDocumentRepository } from '@app/shared/enrollment-service/repository/tbc-enrollment-pfc-memo-document-repository.service';

@Injectable()
export class BatchingService {
  constructor(
    private readonly enrolmentBiodataRepository: EnrolmentBiodataRepository,
    private readonly enrolmentDetailRepository: EnrolmentDetailRepository,
    private readonly enrolmentSummaryRepository: EnrolmentSummaryRepository,
    private readonly settingMsService: SettingMsLibService,
    private readonly logger: PinoLogger,
    private redisService: RedisCacheService,
    private readonly batchRepository: BatchRepository,
    private readonly batchDocumentRepository: BatchDocumentRepository,
    private readonly tbcEnrollmentPfaMemoDocumentRepository: TbcEnrollmentPfaMemoDocumentRepository,
    private readonly tbcEnrollmentPfcMemoDocumentRepository: TbcEnrollmentPfcMemoDocumentRepository
  ) {}

  async applyBatchFilters(filterDto: BatchFilterDto): Promise<BaseResponseWithContentNoPagination<unknown>> {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const { batchType, batchName } = filterDto;

    const savedBatch = await this.batchRepository.findOne({ batchName });
    if (savedBatch) {
      response.setDescription(`Batch with name ${batchName} already exists`);
      return response;
    }

    let allRecords;
    try {
      allRecords = await this.enrolmentBiodataRepository.fetchEnrolmentBiodataBatch(filterDto);
      console.log('allRecords', allRecords);
    } catch (error) {
      this.logger.error(
        `Error occurred while attempting to batch requests, please try again: ${JSON.stringify(filterDto)} ${error instanceof Error ? error.stack : error}`
      );
      response.setDescription('Error occurred while attempting to batch requests, please try again');
      return response;
    }

    // Apply amount cap if specified
    let filteredRecords: typeof allRecords = [];
    let runningTotal = 0;
    const totalAmount = filterDto.totalAmount || 0;

    for (const record of allRecords) {
      const recordAmount = calculateRecordAmount(record, batchType);

      if (totalAmount === 0 || runningTotal + recordAmount <= totalAmount) {
        runningTotal += recordAmount;
        filteredRecords.push(record);
      }

      if (totalAmount > 0 && runningTotal >= totalAmount) {
        break;
      }
    }

    // Generate a batch ID and store the records temporarily in Redis
    const batchData = {
      records: filteredRecords,
      filterDto,
    };

    // Cache the batch data with the records and filter parameters
    if (totalAmount > 0 && filteredRecords && filteredRecords.length > 0) {
      const batchKey = `batch:${batchName}`;
      await this.redisService.set(batchKey, JSON.stringify(batchData));
    } else {
      await this.redisService.del(`batch:${batchName}`);
    }

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.content = {
      records: filteredRecords,
      batchName,
      count: filteredRecords.length,
      runningTotal,
      availableAmount: totalAmount,
    };
    return response;
  }

  // Method to get temporary batch by ID from Redis
  async getBatchById(batchName: string) {
    console.log(`Fetching batch data for batch:${batchName}`);
    const cachedData = await this.redisService.get(`batch:${batchName}`);
    if (!cachedData) {
      return null;
    }

    const batchData = JSON.parse(cachedData as string);
    return batchData;
  }

  async getBatchDetails(batchName: string): Promise<TblBatch> {
    return this.batchRepository.findOneWithRelations({ batchName }, ['records', 'records.enrolment']);
  }

  async retrieveBatch(filterDto: PaginatedSearchDto) {
    return await this.batchRepository.retrieveBatchDetails(filterDto);
  }

  async retrieveBatchDocuments(batchDocumentDto: BatchDocumentDto) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const batchDocument = await this.batchDocumentRepository.findOne({
      batch: { batchName: batchDocumentDto.batchName },
    });
    if (!batchDocument || batchDocument.schedule === null || batchDocument.excoMemo === null) {
      response.setDescription('Documents not found for provided batch, please try again');
      return response;
    }

    response.content = {
      schedule: batchDocument.schedule?.toString('base64'),
      excoMemo: batchDocument.excoMemo?.toString('base64'),
      appendix: batchDocument.excoMemoAppendix?.toString('base64'),
      accountMemo: batchDocument.accountMemo?.toString('base64'),
      accountConfirmationReceipt: batchDocument.accountConfirmationReceipt?.toString('base64'),
    };
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    return response;
  }

  async uploadBatchDocument(batchFileUploadRequestDto: BatchFileUploadRequestDto, file: Express.Multer.File) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const batchDocument = await this.batchDocumentRepository.findOne({
      batch: { batchName: batchFileUploadRequestDto.batchName },
    });

    if (!batchDocument) {
      response.setDescription('Batch not found for provided batch name, please try again');
      return response;
    }

    const { processType } = batchFileUploadRequestDto;
    if (processType === BatchUploadProcessTypeEnum.ACCOUNT_PAYMENT_RECEIPT) {
      batchDocument.accountConfirmationReceipt = Buffer.from(file.buffer);
    } else {
      response.setDescription('Unknown batch file upload process type, please try again');
      return response;
    }

    await this.batchDocumentRepository.saveEntity(batchDocument);
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.setDescription('Batch document uploaded successfully');
    return response;
  }

  async retrieveBatchPaymentDocuments(batchDocumentDto: BatchDocumentDto) {
    const response = new BaseResponseWithContentNoPagination(ResponseCodeEnum.ERROR);
    const pfaDocuments = await this.tbcEnrollmentPfaMemoDocumentRepository.findBy({
      batch: { batchName: batchDocumentDto.batchName },
    });
    const pfcMemoDocuments = await this.tbcEnrollmentPfcMemoDocumentRepository.findBy({
      batch: { batchName: batchDocumentDto.batchName },
    });

    if (!pfaDocuments || pfaDocuments.length === 0 || !pfcMemoDocuments || pfcMemoDocuments.length === 0) {
      response.setDescription('Payment Documents not found for provided batch, please try again');
      return response;
    }

    const content: any = {};
    content.pfa = [];
    content.pfc = [];
    for (const pfaDocument of pfaDocuments) {
      const pfaDetail = {
        pfaName: pfaDocument.pfaName,
        pfaCode: pfaDocument.pfaCode,
        pfcName: pfaDocument.pfcName,
        pfcCode: pfaDocument.pfcCode,
        pfaMemo: pfaDocument.pfaMemo.toString('base64'),
        pfaSchedule: pfaDocument.pfaSchedule.toString('base64'),
      };
      content.pfa.push(pfaDetail);
    }

    for (const pfcMemoDocument of pfcMemoDocuments) {
      const pfcDetail = {
        pfcName: pfcMemoDocument.pfcName,
        pfcCode: pfcMemoDocument.pfcCode,
        pfaMemo: pfcMemoDocument.pfcMemo.toString('base64'),
      };

      content.pfc.push(pfcDetail);
    }

    response.content = content;
    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    return response;
  }
}
