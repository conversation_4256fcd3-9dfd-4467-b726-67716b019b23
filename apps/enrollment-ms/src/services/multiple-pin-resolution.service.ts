import { Inject, Injectable } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { NotificatonTypeEnum } from '@app/shared/enums/NotificatonTypeEnum';
import { SendNotificationTemplateDto } from '@app/shared/dto/notification/send-notification-template.dto';
import { SettingsEnumKey } from '@app/shared/enums';
import * as xlsx from 'xlsx';

import { MultiplePinResolutionRequest } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-req.entity';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { CreateMultiplePinResolutionDto } from '@app/shared/enrollment-service/dtos/create-multiple-pin-resolution.dto';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { MultiplePinResolutionTableDto } from '@app/shared/enrollment-service/dtos/multiple-pin-resolution-table.dto';
import { MultiplePinResolutionRequestRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-req.repository';
import { ResponseCodeEnum } from '@app/shared/enums/ResponseCodeEnum';
import {
  BaseResponseListWithContentNoPagination,
  BaseResponseWithNoCountInfo,
} from '@app/shared/dto/response/base-response-with-content';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { MultiplePinResolutionStatusEnum } from '@app/shared/enrollment-service/enums/multiple-pin-resolution-status.enum';
import { MultiplePinResolutionWorkFlowService } from '@app/shared/workflow/services/multiple-pin-resolution-workflow.service';
import { MultiplePinResolutionMachineEvent } from '@app/shared/workflow/workflow/multi-pin-resolution.workflow';
import { ProcessTypeEnum } from '@app/shared/workflow/enums/ProcessTypeEnum';
import { PinoLogger } from 'nestjs-pino/PinoLogger';
import { UpdateMultiplePinResolutionDto } from '@app/shared/enrollment-service/dtos/update-multiple-pin-resolution.dto';
import { CustomException } from '@app/shared/filters/exception.dto';
import { NotificationServiceClientConstant } from '@app/shared/constants/notification-service-client.constant';
import { firstValueFrom } from 'rxjs';
import { generateDigits } from '@app/shared/utils/general-utils';
import { addDays, format, isValid } from 'date-fns';
import { MultiplePinResolutionPin } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-pin.entity';
import { MultiplePinResolutionTransactionHistoryRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-transaction.repository';
import { MultiplePinResolutionTransactionHistory } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-transaction.entity';

import { RetrieveUserDto } from '@app/shared/dto/ecrs/retrieve-user.dto';
import { RetireeUserTypeEnum } from '@app/shared/enums/RetireeUserTypeEnum';
import { EcrsServiceClientConstant } from '@app/shared/constants/ecrs-service-client.constant';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import {
  MultiplePinResolutionPinDto,
  MultiplePinResolutionPinResponseDto,
} from '@app/shared/enrollment-service/dtos/multiple-pin-resolution-pin.dto';
import * as ejs from 'ejs';
import fs from 'fs';
import { SlipService } from '@app/shared/enrollment-service/services/slip.service';
import { MultiplePinResolutionDocumentRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-document.repository';
import { MultiplePinResolutionDocument } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-document.entity';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { MultiplePinResolutionPinRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-pin.repository';
import { BaseWorkflowService } from '@app/shared/workflow/services/base-workflow.service';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { RabbitMqClient } from '@app/shared/rabbitmq/rabbit-mq-client';
import { CronJobNamesConstant } from '@app/shared/constants/cron.constants';
import { EnrollmentEngineServiceClientConstant } from '@app/shared/constants/enrollment-engine-service-client.constant';
import { TbeOrganisationsRepository } from '@app/shared/enrollment-service/repository/tbe-organisations.repository';
import {
  ConfirmPaymentDto,
  ReconcilePaymentDto,
} from '@app/shared/dto/enrollment/multiple-pin-resolution/confirm-payment.dto';
import { Pfa } from '@app/shared/user-service/entities/pfa.entity';
import { TbcReconcilePay } from '@app/shared/enrollment-service/entities/tbc-reconcile-pay.entity';
import { TbcReconcilePayRepository } from '@app/shared/enrollment-service/repository/tbc-reconcile-pay.repository';

@Injectable()
export class MultiplePinResolutionService extends BaseWorkflowService {
  private readonly CACHE_PREFIX = 'enrollment-ms:mpr:employerCodeCheck:';
  private readonly TRANSACTION_HISTORY_MEMO_PROCESS_TYPE = 'TRANSACTION_HISTORY_MEMO';
  private rmqClient: ClientProxy;

  TRANSACTION_HISTORY_REQUIRED_COLUMNS = [
    { field: '__EMPTY', label: 'RSA Holder Name' },
    { field: '__EMPTY_1', label: 'Invalid PIN' },
  ];

  constructor(
    @Inject('ECRS_SERVICE_CLIENT') private readonly ecrsClient: ClientProxy,
    private readonly multiplePinResolutionRepository: MultiplePinResolutionRequestRepository,
    private readonly workflowService: MultiplePinResolutionWorkFlowService,
    private readonly multiplePinResolutionPinRepository: MultiplePinResolutionPinRepository,
    private readonly multiplePinResolutionTransactionRepository: MultiplePinResolutionTransactionHistoryRepository,
    private readonly multiplePinResolutionDocumentRepository: MultiplePinResolutionDocumentRepository,
    private readonly slipService: SlipService,
    private readonly pfaRepository: PfaRepository,
    private readonly rabbitMqClient: RabbitMqClient,
    private readonly redisService: RedisCacheService,
    private readonly tbeOrganisationsRepository: TbeOrganisationsRepository,
    @Inject('NOTIFICATION_SERVICE_CLIENT') readonly notificationClient: ClientProxy,
    readonly cobraUserRepository: CobraUserRepository,
    readonly logger: PinoLogger,
    readonly settingMsService: SettingMsLibService,
    private readonly reconcilePayRepository: TbcReconcilePayRepository
  ) {
    super(notificationClient, cobraUserRepository, settingMsService, logger);

    this.rmqClient = this.rabbitMqClient.getClient();
  }

  async createRequest(createDto: CreateMultiplePinResolutionDto, req: ICustomRequest): Promise<BaseResponseDto> {
    // Check if any of the PINs have active requests
    const validatePin = [];
    const pfaCodes = new Set<string>(); // Use Set to store unique PFA codes

    let countOfEnrolled = 0;
    let countOfEcrs = 0;
    for (const pin of createDto.pinList) {
      if (!pin.startsWith('PEN')) {
        throw new CustomException(`Invalid PIN format: ${pin}. PIN must start with 'PEN'`);
      }

      const existingRequest = await this.multiplePinResolutionPinRepository.doesPinHaveActiveStatusOrReconciled(pin);

      if (existingRequest) {
        throw new CustomException(`PIN ${pin} already has an active request`);
      }

      const rsaPinDto = new RetrieveUserDto();
      rsaPinDto.rsaPin = pin;
      rsaPinDto.skipSurnameCheck = true;
      rsaPinDto.userType = RetireeUserTypeEnum.DECEASED;
      rsaPinDto.checkCrsStatus = true;

      const data = (await firstValueFrom(
        this.ecrsClient.send({ cmd: EcrsServiceClientConstant.RETRIEVE_USER }, rsaPinDto)
      )) as EcrsUserResponseDto;

      if (!data.crsStatus) {
        throw new CustomException(`PIN ${pin} is not valid on CRS`);
      }

      if (data.ecrsStatus) {
        countOfEcrs++;
      }

      if (data.enrolled) {
        countOfEnrolled++;
      }

      if (!data || data.code !== ResponseCodeEnum.SUCCESS) {
        throw new CustomException(`Failed to retrieve user data for PIN ${pin}`);
      }
      validatePin.push(data);

      pfaCodes.add(data.pfaCode); // Add PFA code to the Set
    }

    if (countOfEcrs != 1) {
      throw new CustomException(`You must only have one pin from ECRS. You have ${countOfEcrs} pins from ECRS`);
    }
    if (countOfEnrolled > 1) {
      throw new CustomException(
        `You can only have at most one enrolled pin. You have ${countOfEnrolled} enrolled pins`
      );
    }

    const request = new MultiplePinResolutionRequest({});
    request.batchId = `MPR_${generateDigits(7)}_${createDto.pinList[0]}_${createDto.pinList[1]}`;
    request.pinCount = createDto.pinList.length;
    request.requestedBy = await this.cobraUserRepository.findOne({ emailAddress: req.user.email });
    request.pfaCodes = Array.from(pfaCodes).join(','); // Convert Set to comma-separated string

    const savedRequest = await this.multiplePinResolutionRepository.saveEntity(request);

    // Create and save the individual PIN records
    const pinEntities = validatePin.map((data) => {
      const pinEntity = new MultiplePinResolutionPin({});
      pinEntity.pin = data.rsaPin;
      pinEntity.isValid = false;
      pinEntity.pfaCode = data.pfaCode;
      pinEntity.mdaCode = data.employerCode;
      pinEntity.pfaName = data.pfaName;
      pinEntity.firstName = data.firstName; // Add first name
      pinEntity.surname = data.surname; // Add surname
      pinEntity.isEnrolled = !!data.enrolled; // Add enrolled status
      pinEntity.crsStatus = !!data.crsStatus; // Add crs status
      pinEntity.ecrsStatus = !!data.ecrsStatus; // Add ecr status
      pinEntity.request = savedRequest;
      return pinEntity;
    });

    await this.multiplePinResolutionPinRepository.saveEntities(pinEntities);

    const workflowEvent = {
      type: 'INITIATE',
      actorEmailAddress: req.user.email,
      formData: { ...request, req: createDto, actorEmailAddress: req.user.email },
    } as MultiplePinResolutionMachineEvent;

    const validationResponse = await this.workflowService.checkIfValidTransition(
      workflowEvent,
      request.pk,
      this,
      ProcessTypeEnum.MULTIPLE_PINS_RESOLUTION
    );

    await this.workflowService.processEvent(workflowEvent, validationResponse.actor, validationResponse.workflowState);

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('Multiple pin resolution request created successfully');
    return response;
  }

  async getRequests(filter: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<MultiplePinResolutionTableDto>> {
    return this.multiplePinResolutionRepository.getDataTable(filter);
  }

  async updateWorkflow(payload: Record<string, string>): Promise<BaseResponseDto> {
    const { batchId, action } = payload;
    const request = await this.multiplePinResolutionRepository.findOne({ batchId });
    if (!request) {
      throw new CustomException(`No request found with ID: ${batchId}`);
    }

    const requestedBy = await request.requestedBy;
    const workflowEvent = {
      type: action,
      actorEmailAddress: requestedBy.emailAddress,
      formData: { data: request, actorEmailAddress: requestedBy.emailAddress, batchId: request.batchId },
    } as MultiplePinResolutionMachineEvent;

    const validationResponse = await this.workflowService.checkIfValidTransition(
      workflowEvent,
      request.pk,
      this,
      ProcessTypeEnum.MULTIPLE_PINS_RESOLUTION
    );

    if (validationResponse.code !== ResponseCodeEnum.SUCCESS || !validationResponse.actor) {
      this.logger.error(`Invalid transition: ${validationResponse.description}`);
      throw new CustomException(validationResponse.description);
    }

    await this.workflowService.processEvent(workflowEvent, validationResponse.actor, validationResponse.workflowState);

    this.logger.info(`Workflow event ${action} processed successfully for request ID: ${request.pk}`);

    return new BaseResponseDto(ResponseCodeEnum.SUCCESS);
  }
  async updateRequest(updateDto: UpdateMultiplePinResolutionDto, req: ICustomRequest): Promise<BaseResponseDto> {
    const { pk, action } = updateDto;

    if (action == 'NDMD_APPROVED' && !updateDto.validPin) {
      throw new CustomException('Valid PIN is required for NDMD_APPROVED action');
    } else if ((action == 'REJECT' || action == 'REJECT_TRANSACTION_HISTORY') && !updateDto.feedback) {
      throw new CustomException('Comment is required for reject action');
    }

    // Find the existing request
    const request = await this.multiplePinResolutionRepository.findOne({ pk });
    if (!request) {
      throw new CustomException(`No request found with ID: ${pk}`);
    }

    const { documents, ...requestWithoutDocuments } = request;
    const pinList = await request.pins;

    if (action == 'NDMD_APPROVED') {
      const validPin = pinList.find((pin) => pin.pin === updateDto.validPin);
      if (!validPin) {
        throw new CustomException(`No PIN found for request pin: ${updateDto.validPin}`);
      }
      if (!validPin.crsStatus || !validPin.ecrsStatus) {
        throw new CustomException(`You are not allowed to reconcile an ECRS Pin that is does not exist on CRS legacy`);
      }
    }

    const workflowEvent: MultiplePinResolutionMachineEvent = {
      type: action,
      actorEmailAddress: req.user.email,
      formData: {
        actorEmailAddress: req.user.email,
        ...requestWithoutDocuments,
        pinList,
        req: updateDto,
        thRejectedPins: updateDto.thRejectedPins,
      },
    };

    // Check if transition is valid
    const validationResponse = await this.workflowService.checkIfValidTransition(
      workflowEvent,
      request.pk,
      this,
      ProcessTypeEnum.MULTIPLE_PINS_RESOLUTION
    );

    if (validationResponse.code !== ResponseCodeEnum.SUCCESS || !validationResponse.actor) {
      this.logger.error(
        `Invalid transition: ${validationResponse.description}:::::: validationResponse.actor ${validationResponse.code}`
      );
      throw new CustomException(validationResponse.description);
    }

    // Process the workflow event
    await this.workflowService.processEvent(workflowEvent, validationResponse.actor, validationResponse.workflowState);

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription(`Multiple pin reconciliation request processed successfully`);
    return response;
  }

  async confirmPencomPayment(confirmPaymentDto: ReconcilePaymentDto, req: ICustomRequest): Promise<BaseResponseDto> {
    const pin = await this.multiplePinResolutionPinRepository.findOne({
      pk: confirmPaymentDto.pk,
      isValid: false,
    });

    if (!pin) {
      throw new CustomException(`No PIN found for this request`);
    }

    if (pin.pencomPaymentConfirmationStatus === 'VERIFIED') {
      throw new CustomException(`Payment for this PIN has already been verified`);
    }

    // Update the PIN record
    pin.pencomPaymentConfirmationStatus = 'VERIFIED';
    pin.pencomPaymentConfirmationDate = new Date();

    await this.multiplePinResolutionPinRepository.saveEntity(pin);

    const request = await pin.request;
    // Check if all PINs have verified payments
    const allPins = await request.pins;
    const unverifiedPins = allPins.filter((p) => p.pencomPaymentConfirmationStatus !== 'VERIFIED' && !p.isValid);

    // If all payments are verified, update the workflow to RECONCILED
    if (unverifiedPins.length !== 0) {
      return new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    }

    const pinList = await request.pins;
    const requestWithDocument = { ...request, documents: [], transactions: [], pins: [] };
    const workflowEvent: MultiplePinResolutionMachineEvent = {
      type: 'RECONCILE_PAYMENT',
      actorEmailAddress: req.user.email,
      formData: {
        ...requestWithDocument,
        pinList,
        actorEmailAddress: req.user.email,
        batchId: request.batchId,
      },
    };

    const validationResponse = await this.workflowService.checkIfValidTransition(
      workflowEvent,
      request.pk,
      this,
      ProcessTypeEnum.MULTIPLE_PINS_RESOLUTION
    );

    if (validationResponse.code !== ResponseCodeEnum.SUCCESS || !validationResponse.actor) {
      this.logger.error(`Invalid transition: ${validationResponse.description}`);
      throw new CustomException(validationResponse.description);
    }

    await this.workflowService.processEvent(workflowEvent, validationResponse.actor, validationResponse.workflowState);

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('Payment verification confirmed successfully');
    return response;
  }

  async updateStatus(pk: number, currentState: string, options?: Record<string, any>): Promise<void> {
    if (!pk) {
      throw new CustomException('Invalid request ID');
    }

    const request = await this.multiplePinResolutionRepository.findOne({ pk });
    if (!request) {
      throw new CustomException(`No request found with ID: ${pk}`);
    }

    const newStatus = this.workflowService.getStatusFromAction(currentState);

    if (currentState === 'REVIEWED') {
      const updatedPin = await this.multiplePinResolutionPinRepository.findAndUpdateValidPin(pk, options.validPin);
      if (!updatedPin) {
        throw new CustomException(`No PIN found for request pin: ${options.validPin}`);
      }
    } else if (currentState === 'PENDING_PAYMENT_CONFIRMATION') {
      const pins = await request.pins;
      for (const pin of pins) {
        pin.thRequestedDate = new Date();
      }
      await this.multiplePinResolutionPinRepository.saveEntities(pins);
    } else if (currentState === 'RECONCILED') {
      const pins = await request.pins;
      const validPin = pins.find((pin) => pin.isValid);

      for (const pin of pins) {
        if (!pin.isValid) {
          const reconcilePay = new TbcReconcilePay({
            pin: validPin.pin,
            amount: pin.cpaBalance,
            name: pin.firstName,
            pfaCode: pin.pfaCode,
            pfaName: pin.pfaName,
            actorEmail: options.actorEmailAddress,
          });
          await this.reconcilePayRepository.saveEntity(reconcilePay);
        }
      }
    } else if (
      currentState === 'REJECT' ||
      currentState === 'REJECT_TRANSACTION_HISTORY' ||
      currentState === 'NDMD_REJECTED'
    ) {
      request.comment = options.comment;
    }

    request.status = newStatus;
    await this.multiplePinResolutionRepository.saveEntity(request);
  }

  async notifyNDMDUsers(formData: Record<string, any>): Promise<void> {
    try {
      // Get NDMD role from settings
      const ndmdRole = await this.settingMsService.getSetting(SettingsEnumKey.NDMD_ROLE_NAME);
      if (!ndmdRole) {
        this.logger.warn('NDMD role setting not found');
        return;
      }

      // Find NDMD users
      const ndmdUsers = await this.cobraUserRepository.findApproversByRoles(ndmdRole.split(','));
      if (!ndmdUsers?.length) {
        this.logger.warn(`No NDMD users found with roles: ${ndmdRole}`);
        return;
      }

      // Get RSA PINs from form data
      const rsaPins = formData.req.pinList;
      // Send notification to each NDMD user
      const notificationPromises = ndmdUsers.map((user) => {
        const notificationDto = new SendNotificationTemplateDto();
        notificationDto.emailRecipients = [user.emailAddress];
        notificationDto.notificationType = NotificatonTypeEnum.NDMD_RSA_PIN_REVIEW;
        notificationDto.placeholders = {
          userName: user.firstName,
          batchId: formData.batchId,
          rsaPins: rsaPins,
        };

        return firstValueFrom(
          this.notificationClient.send(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            notificationDto
          )
        ).catch((error) => {
          this.logger.error(
            `Failed to send notification to NDMD user ${user.emailAddress}: ${error instanceof Error ? error.message : error}`
          );
        });
      });

      await Promise.all(notificationPromises);
      this.logger.info(
        `Successfully sent notifications to ${ndmdUsers.length} NDMD users for batch ${formData.batchId}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to process NDMD notifications for batch ${formData.batchId}: ${error instanceof Error ? error.message : error}`
      );
    }
  }

  async notifyHODOfApproval(formData: Record<string, any>): Promise<void> {
    try {
      // Get HOD role from settings
      const hodRole = await this.settingMsService.getSetting(SettingsEnumKey.HOD_ROLE_NAME);
      if (!hodRole) {
        this.logger.warn('HOD role setting not found');
        return;
      }

      // Find HOD users
      const hodUsers = await this.cobraUserRepository.findApproversByRoles(hodRole.split(','));
      if (!hodUsers?.length) {
        this.logger.warn(`No HOD users found with roles: ${hodRole}`);
        return;
      }

      // Get RSA PINs from form data
      const rsaPins = Array.isArray(formData.pinList) ? formData.pinList.map((pin) => pin.pin) : formData.pinList;

      // Send notification to each HOD
      const notificationPromises = hodUsers.map((user) => {
        const notificationDto = new SendNotificationTemplateDto();
        notificationDto.emailRecipients = [user.emailAddress];
        notificationDto.notificationType = NotificatonTypeEnum.NDMD_RSA_PIN_REVIEW_COMPLETED;
        notificationDto.placeholders = {
          batchId: formData.batchId,
          rsaPins: rsaPins,
          completionDate: format(new Date(), 'dd-MM-yyyy'),
        };

        return firstValueFrom(
          this.notificationClient.send(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            notificationDto
          )
        ).catch((error) => {
          this.logger.error(
            `Failed to send approval notification to HOD ${user.emailAddress}: ${error instanceof Error ? error.message : error}`
          );
        });
      });

      await Promise.all(notificationPromises);
      this.logger.info(
        `Successfully sent approval notifications to ${hodUsers.length} HOD users for batch ${formData.batchId}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to process HOD approval notifications for ID ${formData.pk}: ${error instanceof Error ? error.message : error}`
      );
    }
  }

  async notifyHODOfRejection(formData: Record<string, any>): Promise<void> {
    try {
      const hodRole = await this.settingMsService.getSetting(SettingsEnumKey.HOD_ROLE_NAME);
      if (!hodRole) {
        this.logger.warn('HOD role setting not found');
        return;
      }

      // Find HOD users
      const hodUsers = await this.cobraUserRepository.findApproversByRoles(hodRole.split(','));
      if (!hodUsers?.length) {
        this.logger.warn(`No HOD users found with roles: ${hodRole}`);
        return;
      }

      // Get RSA PINs from form data
      const rsaPins = Array.isArray(formData.pinList) ? formData.pinList : formData.pinList.split(',');

      // Send notification to each HOD
      const notificationPromises = hodUsers.map((user) => {
        const notificationDto = new SendNotificationTemplateDto();
        notificationDto.emailRecipients = [user.emailAddress];
        notificationDto.notificationType = NotificatonTypeEnum.NDMD_RSA_PIN_REVIEW_REJECTED;
        notificationDto.placeholders = {
          batchId: formData.batchId,
          rsaPins: rsaPins,
          rejectionReason: formData.req.feedback || 'No reason provided',
        };

        return firstValueFrom(
          this.notificationClient.send(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            notificationDto
          )
        ).catch((error) => {
          this.logger.error(
            `Failed to send rejection notification to HOD ${user.emailAddress}: ${error instanceof Error ? error.message : error}`
          );
        });
      });

      await Promise.all(notificationPromises);
      this.logger.info(
        `Successfully sent rejection notifications to ${hodUsers.length} HOD users for batch ${formData.batchId}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to process HOD rejection notifications for batch ${formData.batchId}: ${error instanceof Error ? error.message : error}`
      );
    }
  }

  async processPfaUpload(request: MultiplePinResolutionRequest): Promise<void> {
    if (request.status !== MultiplePinResolutionStatusEnum.PENDING_PFA_UPLOAD) {
      throw new CustomException('Request is not in the correct state for document upload');
    }

    // Push to calculation queue and set cache key to prevent duplicate processing
    await this.redisService.set(
      `${CronJobNamesConstant.MULTIPLE_PIN_CALCULATION_JOB}:${request.batchId}`,
      'queued',
      60 * 5
    ); // 5 hour TTL

    const workflowEvent: MultiplePinResolutionMachineEvent = {
      type: 'UPLOAD_TRANSACTION_HISTORY',
      actorEmailAddress: request.requestedBy.emailAddress,
      formData: {
        ...request,
        actorEmailAddress: request.requestedBy.emailAddress,
      },
    };

    // Check if transition is valid
    const validationResponse = await this.workflowService.checkIfValidTransition(
      workflowEvent,
      request.pk,
      this,
      ProcessTypeEnum.MULTIPLE_PINS_RESOLUTION
    );

    if (validationResponse.code !== ResponseCodeEnum.SUCCESS || !validationResponse.actor) {
      this.logger.error(`Invalid transition: ${validationResponse.description}`);
      throw new CustomException(validationResponse.description);
    }

    // Process the workflow event
    await this.workflowService.processEvent(workflowEvent, validationResponse.actor, validationResponse.workflowState);

    await this.rmqClient.emit(EnrollmentEngineServiceClientConstant.MULTIPLE_PIN_CALCULATION_JOB, {
      batchId: request.batchId,
    });
  }

  async uploadPinTransactionHistory(
    batchId: string,
    pinPk: number,
    file: Express.Multer.File,
    req: ICustomRequest
  ): Promise<BaseResponseDto> {
    const workbook = xlsx.read(file.buffer, { type: 'buffer' });

    // Add meta sheet verification
    const hiddenSheetName = 'meta';
    const hiddenSheet = workbook.Sheets[hiddenSheetName];
    if (!hiddenSheet) {
      throw new CustomException('Invalid template: Please ensure you are using the provided template.');
    }

    const marker = xlsx.utils.sheet_to_json(hiddenSheet, { header: 1 })?.[0]?.[0];
    if (marker !== 'stellarsync-multiple-pin-upload') {
      throw new CustomException('Invalid template: please ensure you are using the provided template.');
    }

    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];

    const rows = xlsx.utils.sheet_to_json(sheet, { range: 1, defval: '', blankrows: false });
    const allowedRowCount = await this.settingMsService.getSettingInt(SettingsEnumKey.EXCEL_SHEET_MAX_ROW_COUNT);
    if (rows.length > allowedRowCount) {
      this.logger.warn(`Worksheet row count ${rows.length} is greater than allowed row count  ${allowedRowCount}`);
      throw new CustomException(`Kindly ensure that you are not uploading records greater than ${allowedRowCount}`);
    }

    if (rows.length == 0) {
      throw new CustomException(`No records found in the file.`);
    }

    const request = await this.multiplePinResolutionRepository.findOne({ batchId: batchId });
    if (!request) {
      this.logger.warn(`No request found with ID: ${batchId}`);
      throw new CustomException(`No request found with ID: ${batchId}`);
    }
    const pin = await this.multiplePinResolutionPinRepository.findOne({ pk: pinPk });
    if (!pin) {
      this.logger.warn(`No PIN found with ID: ${pinPk}`);
      throw new CustomException(`No PIN found with ID: ${pinPk}`);
    }
    const validationErrors = await this.validateTransactionHistory(rows, pin.pin);
    if (validationErrors.length > 0) {
      this.logger.warn(`Validation errors: ${validationErrors.join(', ')}`);
      throw new CustomException(validationErrors.join(', '));
    }

    if (request.status !== MultiplePinResolutionStatusEnum.PENDING_PFA_UPLOAD) {
      this.logger.warn(`Request is not in the correct state for document upload`);
      throw new CustomException(`Request is not in the correct state for document upload`);
    }
    if (pin.isValid) {
      throw new CustomException(`PIN ${pin.pin} is valid. No transaction history is required`);
    }

    if (pin.hasTransactionHistory) {
      this.logger.warn(`PIN ${pin.pin} already has transaction history`);
      throw new CustomException(`PIN ${pin.pin} already has transaction history`);
    }

    const rsaPinDto = new RetrieveUserDto();
    rsaPinDto.rsaPin = pin.pin;
    rsaPinDto.skipSurnameCheck = true;
    rsaPinDto.userType = RetireeUserTypeEnum.DECEASED;

    const data = (await firstValueFrom(
      this.ecrsClient.send({ cmd: EcrsServiceClientConstant.RETRIEVE_USER }, rsaPinDto)
    )) as EcrsUserResponseDto;

    if (!data || data.code !== ResponseCodeEnum.SUCCESS) {
      const msg = `Failed to retrieve user data for PIN ${pin.pin}`;
      throw new CustomException(data.description || msg);
    }

    if (data.pfaCode !== req.user.pfaCode) {
      this.logger.warn(`PIN ${pin.pin} does not belong to PFA ${req.user.pfaCode}`);
      throw new CustomException(`PIN ${pin.pin} does not belong to PFA ${req.user.pfaCode}`);
    }
    if (data.surname.toLowerCase().trim() !== rows[0]['__EMPTY'].toLowerCase().trim()) {
      this.logger.debug(
        `Surname provided ${data.surname.toLowerCase().trim()} does not match the surname in the excel sheet ${rows[0]['__EMPTY'].toLowerCase().trim()}`
      );
      throw new CustomException(`Surname provide does not match the surname in the excel sheet`);
    }
    const asynTransactions = rows.map((row) => this.transformTransactionHistoryRow(row, request, data));
    const transactions = await Promise.all(asynTransactions);
    // Save transactions in batches
    await this.multiplePinResolutionTransactionRepository.saveEntities(transactions);

    pin.hasTransactionHistory = true;
    await this.multiplePinResolutionPinRepository.saveEntity(pin);

    // if the request has all pins with transaction history, process the request
    const pinsWithoutTransactionHistory = await this.multiplePinResolutionPinRepository.findOne({
      request: { batchId: batchId },
      hasTransactionHistory: false,
      isValid: false,
    });

    if (!pinsWithoutTransactionHistory) {
      this.logger.info(`All pins have transaction history, processing request`);
      await this.processPfaUpload(request);
    }

    const response = new BaseResponseListWithContentNoPagination(ResponseCodeEnum.SUCCESS);
    response.setDescription('File uploaded successfully');
    response.content = transactions.map((transaction) =>
      this.multiplePinResolutionTransactionRepository.serializeEntity(transaction)
    );
    return response;
  }

  public async notifyHODOfTransactionHistoryUpload(formData: Record<string, any>): Promise<void> {
    try {
      // Get Pencom role from settings
      const pencomRole = await this.settingMsService.getSetting(SettingsEnumKey.HOD_ROLE_NAME);
      if (!pencomRole) {
        this.logger.warn('Pencom role setting not found');
        return;
      }

      // Find Pencom users
      const pencomUsers = await this.cobraUserRepository.findApproversByRoles(pencomRole.split(','));
      if (!pencomUsers?.length) {
        this.logger.warn(`No Pencom users found with roles: ${pencomRole}`);
        return;
      }

      // Get RSA PINs from request
      const rsaPins = formData.pinList.split(',');

      // Send notification to each Pencom user
      const notificationPromises = pencomUsers.map((user) => {
        const notificationDto = new SendNotificationTemplateDto();
        notificationDto.emailRecipients = [user.emailAddress];
        notificationDto.notificationType = NotificatonTypeEnum.MULTIPLE_PIN_RESOLUTION_TRANSACTION_HISTORY;
        notificationDto.placeholders = {
          userName: user.firstName,
          batchId: formData.batchId,
          rsaPins: rsaPins.join(', '),
          loginLink: process.env.COBRA_BASE_URL || '',
          supportEmail: process.env.SUPPORT_EMAIL || '',
        };

        return firstValueFrom(
          this.notificationClient.send(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            notificationDto
          )
        ).catch((error) => {
          this.logger.error(
            `Failed to send notification to Pencom user ${user.emailAddress}: ${error instanceof Error ? error.message : error}`
          );
        });
      });

      await Promise.all(notificationPromises);
      this.logger.info(
        `Successfully sent notifications to ${pencomUsers.length} Pencom users for batch ${formData.batchId}`
      );
    } catch (error) {
      this.logger.error(
        `Failed to process Pencom notifications for batch ${formData.batchId}: ${error instanceof Error ? error.message : error}`
      );
      // Don't throw here to prevent the main transaction from failing
    }
  }

  private async validateTransactionHistory(rows: any[], pin: string): Promise<string[]> {
    const validationErrors: string[] = [];
    for (let index = 0; index < rows.length; index++) {
      const row = rows[index];
      const rowNumber = index + 2;
      // Adding 2 for header row and 1-based index
      const epsilon = 0.01;
      try {
        console.log('validating transaction History row', row);
        this.TRANSACTION_HISTORY_REQUIRED_COLUMNS.forEach(({ field, label }) => {
          const value = row[field]?.toString().trim();
          if (!value) {
            validationErrors.push(`Row ${rowNumber}: ${label} is required`);
          }
        });

        const pencomRemitanceAmount1 = Number(row['AMOUNT']) || 0;
        const investmentIncome1 = Number(row['INVESTMENT INCOME']) || 0;

        if (pencomRemitanceAmount1 > 0 && investmentIncome1 <= 0) {
          validationErrors.push(
            `Row ${rowNumber}: Invalid mandatory contributions - If employer contribution is positive, employee contribution must be positive`
          );
        }

        const shortFallRemitanceAmount = Number(row['2.5% SHORTFALL OF PENSION CONTRIBUTION']) || 0;
        const shortFallInvestmentIncome = Number(row['INVESTMENT INCOME_1']) || 0;

        if (shortFallRemitanceAmount > 0 && shortFallInvestmentIncome <= 0) {
          validationErrors.push(
            `Row ${rowNumber}: Invalid voluntary contributions - If employer contribution is positive, employee contribution must be positive`
          );
        }

        // Contingent contributions (Col 13,14,15)
        const employerContingent = Number(row['AMOUNT_1']) || 0;
        const employeeContingent = Number(row['INVESTMENT INCOME_2']) || 0;

        if (employerContingent > 0 && employeeContingent <= 0) {
          validationErrors.push(
            `Row ${rowNumber}: Invalid contingent contributions - If employer contribution is positive, employee contribution must be positive`
          );
        }
        if (row['__EMPTY_1'] !== pin) {
          validationErrors.push(`Row ${rowNumber}: Invalid PIN - ${row['__EMPTY_1']} does not match ${pin}`);
        }

        if (row['__EMPTY_2'] && row['__EMPTY_2'].length > 100) {
          validationErrors.push(`Row ${rowNumber}: Invalid remark - Remark cannot be greater than 100 characters`);
        }
      } catch (error) {
        validationErrors.push(`Row ${rowNumber}: Invalid data format - ${error.message}`);
      }
    }
    return validationErrors;
  }

  private async transformTransactionHistoryRow(
    row: any,
    request: MultiplePinResolutionRequest,
    data: EcrsUserResponseDto
  ): Promise<MultiplePinResolutionTransactionHistory> {
    const parseAmount = (value: any): number => {
      if (value === '' || value === undefined || value === null) return 0;
      return +value;
    };

    const tbeOrganization = await this.tbeOrganisationsRepository.findOne({ employerId: data.employerCode });

    return new MultiplePinResolutionTransactionHistory({
      request: request,
      rsaHolderFirstName: data.firstName,
      rsaHolderLastName: data.surname,
      employerName: tbeOrganization?.employerName,
      employerCode: data.employerCode,
      invalidPin: data.rsaPin,
      invalidPfaCode: data.pfaCode,
      pencomRemitAmount: parseAmount(row['AMOUNT']),
      pencomInvestmentIncome: parseAmount(row['INVESTMENT INCOME']),
      pencomTotal: parseAmount(row['AMOUNT']) + parseAmount(row['INVESTMENT INCOME']),
      shortfallContribution: parseAmount(row['2.5% SHORTFALL OF PENSION CONTRIBUTION']),
      shortfallInvestmentIncome: parseAmount(row['INVESTMENT INCOME_1']),
      shortfallTotal:
        parseAmount(row['2.5% SHORTFALL OF PENSION CONTRIBUTION']) + parseAmount(row['INVESTMENT INCOME_1']),
      ippisAmount: parseAmount(row['AMOUNT_1']),
      ippisInvestmentIncome: parseAmount(row['INVESTMENT INCOME_2']),
      ippisTotal: parseAmount(row['AMOUNT_1']) + parseAmount(row['INVESTMENT INCOME_2']),
      remark: row['__EMPTY_2'] || null,
    });
  }

  async getPinsForRequest(
    batchId: string,
    req: ICustomRequest
  ): Promise<BaseResponseListWithContentNoPagination<MultiplePinResolutionPinResponseDto>> {
    const pins = await this.multiplePinResolutionPinRepository.findPinsByRequestIdAndPfaCode(batchId, req.user.pfaCode);

    if (!pins.length) {
      throw new CustomException(`No PINs found for request ID: ${batchId}`);
    }

    // Get only transaction history memo documents for this request
    const documents = await this.multiplePinResolutionDocumentRepository.findByRequestIdAndProcessType(
      batchId,
      this.TRANSACTION_HISTORY_MEMO_PROCESS_TYPE
    );

    const transactionHistories = await this.multiplePinResolutionTransactionRepository.findByRequestId(batchId);

    // return pin with the corresponding memo doc
    const refinedData = pins.map((pin) => {
      const pinMemo = documents?.find((doc) => {
        return doc.pin?.pk === pin.pk;
      });

      // Find transaction history for this pin
      const pinTransactionHistory = transactionHistories?.filter((th) => th.invalidPin === pin.pin);

      // avoid the number issue
      pin.isValid = !!pin.isValid;
      pin.hasTransactionHistory = !!pin.hasTransactionHistory;
      pin.isEnrolled = !!pin.isEnrolled;
      pin.crsStatus = !!pin.crsStatus;

      return {
        ...pin,
        memo: pinMemo
          ? {
              documentName: pinMemo.documentName,
              documentData: pinMemo.documentData.toString('base64'),
              documentExt: pinMemo.documentExt,
            }
          : null,
        transactionHistory: pinTransactionHistory
          ? pinTransactionHistory.map((th) => ({
              pk: th.pk,
              pencomRemitAmount: th.pencomRemitAmount,
              pencomInvestmentIncome: th.pencomInvestmentIncome,
              pencomTotal: th.pencomTotal,
              shortfallContribution: th.shortfallContribution,
              shortfallInvestmentIncome: th.shortfallInvestmentIncome,
              shortfallTotal: th.shortfallTotal,
              ippisAmount: th.ippisAmount,
              ippisInvestmentIncome: th.ippisInvestmentIncome,
              ippisTotal: th.ippisTotal,
              remark: th.remark,
            }))
          : [],
      };
    });

    const request = await this.multiplePinResolutionRepository.findOne({ batchId: batchId });

    const response = new BaseResponseListWithContentNoPagination<MultiplePinResolutionPinResponseDto>(
      ResponseCodeEnum.SUCCESS
    );
    response.content = { batchId: request.batchId, status: request.status, pins: refinedData };
    response.setDescription('PINs retrieved successfully');

    return response;
  }

  async generateTransactionHistoryMemos(batchId: string, req: ICustomRequest): Promise<BaseResponseDto> {
    const response = new BaseResponseDto(ResponseCodeEnum.ERROR);

    // Get the MPR request
    const request = await this.multiplePinResolutionRepository.findOne({ batchId: batchId });
    if (!request) {
      throw new CustomException(`No request found with ID: ${batchId}`);
    }

    if (request.status !== MultiplePinResolutionStatusEnum.REVIEWED) {
      throw new CustomException(`Request is not in the correct state for memo generation`);
    }

    // Get PINs requiring transaction history
    const pins = await this.multiplePinResolutionPinRepository.find({
      where: {
        request: { batchId: batchId },
        isValid: false, // only invalid pins needs transaction history.
      },
    });

    if (!pins.length) {
      throw new CustomException(`No PINs requiring transaction history found for request ID: ${batchId}`);
    }

    const currentDate = format(new Date(), 'd MMMM, yyyy');
    const dueDate = format(this.getWorkingDayAfter(new Date(), 3), 'd MMMM, yyyy');
    const memos = [];

    // Generate individual memo for each PIN
    for (const pin of pins) {
      // Get PFA details for the PIN
      const pfaDetails = await this.pfaRepository.getPfaDetails(pin.pfaCode);
      if (!pfaDetails) {
        this.logger.error(`PFA details not found for PIN ${pin.pin}`);
        continue;
      }

      const templateData = {
        headerImage: this.slipService.getBannerImage(),
        requestDate: currentDate,
        dueDate: dueDate,
        rsaPin: pin.pin,
        pfaName: pfaDetails.pfaName,
        pfaAddress: pfaDetails.address1 || 'N/A',
      };

      const htmlContent = fs.readFileSync('apps/enrollment-ms/src/slips/mpr-transaction-history-memo.ejs', 'utf8');
      const message = ejs.render(htmlContent, templateData);
      const pdfBuffer = await this.slipService.generatePdf(message);

      if (!pdfBuffer) {
        throw new CustomException(`Error generating memo for PIN ${pin.pin}. please try again.`);
      }
      // Save the generated memo
      const document = new MultiplePinResolutionDocument({
        request,
        pin,
        documentData: Buffer.from(pdfBuffer),
        documentExt: 'pdf',
        processType: this.TRANSACTION_HISTORY_MEMO_PROCESS_TYPE,
        documentName: `transaction_history_memo_${pin.pin}.pdf`,
      });

      await this.multiplePinResolutionDocumentRepository.saveEntity(document);

      memos.push({
        pin: pin.pin,
        memo: pdfBuffer.toString(),
        pfaCode: pin.pfaCode,
        pfaName: pfaDetails.pfaName,
      });
    }

    await this.processMemoGeneration(request, memos);

    response.setResponseCode(ResponseCodeEnum.SUCCESS);
    response.description = 'Transaction history memos generated successfully';

    return response;
  }

  private getWorkingDayAfter(date: Date, days: number): Date {
    let result = addDays(date, days);
    while (result.getDay() === 0 || result.getDay() === 6) {
      result = addDays(result, 1);
    }
    return result;
  }

  async processMemoGeneration(request: MultiplePinResolutionRequest, memos: any[]): Promise<void> {
    const workflowEvent: MultiplePinResolutionMachineEvent = {
      type: 'MEMO_GENERATED',
      actorEmailAddress: request.requestedBy.emailAddress,
      formData: {
        ...request,
        actorEmailAddress: request.requestedBy.emailAddress,
        memos,
      },
    };

    // Check if transition is valid
    const validationResponse = await this.workflowService.checkIfValidTransition(
      workflowEvent,
      request.pk,
      this,
      ProcessTypeEnum.MULTIPLE_PINS_RESOLUTION
    );

    if (validationResponse.code !== ResponseCodeEnum.SUCCESS || !validationResponse.actor) {
      this.logger.error(`Invalid transition: ${validationResponse.description}`);
      throw new CustomException(validationResponse.description);
    }

    // Process the workflow event
    await this.workflowService.processEvent(workflowEvent, validationResponse.actor, validationResponse.workflowState);
  }

  private async getPfaDetailsFromRequest(
    request: Record<string, any>
  ): Promise<Array<{ pfaName: string; emailAddress: string }>> {
    if (!request.pfaCodes) {
      throw new CustomException('No PFA codes found in the request');
    }

    const pfaCodes = request.pfaCodes.split(',').map((code) => code.trim());
    const pfaDetails = [];

    for (const pfaCode of pfaCodes) {
      const pfaDetail = await this.pfaRepository.getPfaDetails(pfaCode);
      if (pfaDetail && pfaDetail.emailAddress) {
        pfaDetails.push({
          pfaName: pfaDetail.pfaName,
          emailAddress: pfaDetail.emailAddress,
        });
      } else {
        this.logger.warn(`PFA details not found or missing email for PFA code: ${pfaCode}`);
      }
    }

    if (pfaDetails.length === 0) {
      throw new CustomException('No valid PFA details found');
    }

    return pfaDetails;
  }

  async notifyPencomOfComputation(formData: Record<string, any>) {
    const placeholders: Record<string, string> = {};
    placeholders['batchId'] = formData.data.batchId;
    placeholders['computationDate'] = format(new Date(), 'dd-MM-yyyy');

    await this.sendBulkNotification(
      SettingsEnumKey.HOD_ROLE_NAME,
      placeholders,
      NotificatonTypeEnum.MULTIPLE_PIN_RESOLUTION_COMPUTATION
    );

    this.logger.info(`Successfully sent computation notification for batch ${formData.data.batchId}`);
  }

  public async notifyPfaOfMemoGeneration(formData: Record<string, any>): Promise<void> {
    try {
      // using the memo array because I only want to send email to PFAs that have memos
      for (const pfa of formData.memos) {
        const pfaDetails = await this.pfaRepository.getPfaDetails(pfa.pfaCode);
        const notificationDto = new SendNotificationTemplateDto();
        notificationDto.emailRecipients = [pfaDetails.emailAddress];
        notificationDto.notificationType = NotificatonTypeEnum.TRANSACTION_HISTORY_MEMO_NOTIFICATION;
        notificationDto.placeholders = {
          pfaName: pfa.pfaName,
          batchId: formData.batchId,
        };

        await firstValueFrom(
          this.notificationClient.send(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            notificationDto
          )
        );

        this.logger.info(`Successfully sent memo notification to PFA ${pfa.pfaName}`);
      }
    } catch (error) {
      this.logger.error(`Failed to send memo notifications: ${error instanceof Error ? error.message : error}`);
    }
  }

  async deleteTransactionHistory(pk: number, req: ICustomRequest): Promise<BaseResponseDto> {
    const transaction = await this.multiplePinResolutionTransactionRepository.findOne({ pk });

    if (!transaction) {
      throw new CustomException('Transaction history record not found');
    }

    const request = await transaction.request;

    if (request.status !== MultiplePinResolutionStatusEnum.PENDING_PFA_UPLOAD) {
      throw new CustomException('Transaction history can only be deleted when request is in PENDING_PFA_UPLOAD status');
    }

    // Check if user has permission (PFA can only delete their own uploads)
    if (transaction.invalidPfaCode === req.user.pfaCode) {
      throw new CustomException('You are not authorized to delete this transaction history');
    }

    // Get and update the associated pin
    const pin = await this.multiplePinResolutionPinRepository.findOne({
      request: { pk: request.pk },
      pin: transaction.invalidPin,
    });

    if (pin) {
      pin.hasTransactionHistory = false;
      pin.thReceivedDate = null;
      await this.multiplePinResolutionPinRepository.saveEntity(pin);
    }

    await this.multiplePinResolutionTransactionRepository.removeEntity(transaction);

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('Transaction history deleted successfully');
    return response;
  }
  async getPinRequests(filter: PaginatedSearchDto): Promise<BaseResponseWithNoCountInfo<MultiplePinResolutionPinDto>> {
    return await this.multiplePinResolutionPinRepository.getDataTable(filter);
  }
  async confirmPayment(
    batchId: string,
    confirmPaymentDto: ConfirmPaymentDto,
    req: ICustomRequest
  ): Promise<BaseResponseDto> {
    const request = await this.multiplePinResolutionRepository.findOne({ batchId });
    if (!request) {
      throw new CustomException(`No request found with ID: ${batchId}`);
    }

    if (request.status !== MultiplePinResolutionStatusEnum.PENDING_PAYMENT_CONFIRMATION) {
      throw new CustomException('Request is not in the correct state for payment confirmation');
    }

    const pin = await this.multiplePinResolutionPinRepository.findOne({
      request: { batchId },
      pin: confirmPaymentDto.pin,
    });

    if (!pin) {
      throw new CustomException(`No PIN found with ID: ${confirmPaymentDto.pin} for this request`);
    }

    // Check if the user's PFA matches the PIN's PFA
    if (pin.pfaCode !== req.user.pfaCode) {
      throw new CustomException('You are not authorized to confirm payment for this PIN');
    }

    if (!pin.cpaBalance && !confirmPaymentDto.paymentRef) {
      throw new CustomException('Payment reference is required for CPA payment.');
    }
    if (!pin.validPinBalance && !confirmPaymentDto.validPinPaymentRef) {
      throw new CustomException('Valid pin payment reference is required for this payment to be confirmed.');
    }

    // Update the PIN record
    pin.paymentConfirmationStatus = 'CONFIRMED';
    pin.paymentConfirmedBy = req.user.email;
    pin.paymentConfirmedAt = new Date();
    pin.paymentRef = confirmPaymentDto.paymentRef;
    pin.validPinPaymentRef = confirmPaymentDto.validPinPaymentRef;

    await this.multiplePinResolutionPinRepository.saveEntity(pin);

    // Check if all PINs have confirmed payments
    const allPins = await request.pins;

    const unconfirmedPins = allPins.filter((p) => p.paymentConfirmationStatus !== 'CONFIRMED');
    const isAnyEligible = allPins.find((p) => !p.isValid && p.eligibilityStatus === true);


    // If all payments are confirmed, update the workflow and notify PENCOM
    if (unconfirmedPins.length === 0 || (unconfirmedPins.length === 1 && !isAnyEligible)) {
      const pinList = await request.pins;
      const requestWithDocument = { ...request, documents: [], transactions: [], pins: [] };
      const workflowEvent: MultiplePinResolutionMachineEvent = {
        type: 'PAYMENT_CONFIRMED',
        actorEmailAddress: req.user.email,
        formData: {
          ...requestWithDocument,
          pinList,
          actorEmailAddress: req.user.email,
          batchId: request.batchId,
        },
      };

      const validationResponse = await this.workflowService.checkIfValidTransition(
        workflowEvent,
        request.pk,
        this,
        ProcessTypeEnum.MULTIPLE_PINS_RESOLUTION
      );

      if (validationResponse.code !== ResponseCodeEnum.SUCCESS || !validationResponse.actor) {
        this.logger.error(`Invalid transition: ${validationResponse.description}`);
        throw new CustomException(validationResponse.description);
      }

      await this.workflowService.processEvent(
        workflowEvent,
        validationResponse.actor,
        validationResponse.workflowState
      );
    }

    const response = new BaseResponseDto(ResponseCodeEnum.SUCCESS);
    response.setDescription('Payment confirmed successfully');
    return response;
  }

  public async notifyPencomOfAllPaymentsConfirmed(formData: Record<string, any>): Promise<void> {
    const invalidPins = formData.pinList.filter((p) => !p.isValid);
    // Get unique sending PFAs\
    const pfaInvolved = [];
    let totalAmount = 0;
    for (const pin of invalidPins) {
      const pfaDetails = await this.pfaRepository.getPfaDetails(pin.pfaCode);
      if (pfaDetails) {
        pfaInvolved.push(pfaDetails.pfaName);
      }
      if (pin.validPinBalance && !pin.isValid) {
        totalAmount += pin.validPinBalance;
      }
    }

    const placeholders = {
      batchId: formData.batchId,
      pfaInvolved: pfaInvolved.join(', '),

      paymentDate: format(new Date(), 'dd-MM-yyyy'),
      totalAmount: '' + totalAmount,
    };

    await this.sendBulkNotification(
      SettingsEnumKey.HOD_ROLE_NAME,
      placeholders,
      NotificatonTypeEnum.MULTIPLE_PIN_PAYMENT_CONFIRMATION_PENCOM
    );
  }

  async notifyPfaOfPaymentRequest(formData: Record<string, any>): Promise<void> {
    try {
      const request = formData;
      if (!request || !request.batchId) {
        this.logger.error('Invalid request data for payment notification');
        return;
      }

      const pins = formData.pinList;
      const validPin = pins.find((pin) => pin.isValid);
      if (!validPin) {
        this.logger.error(`No valid PIN found for batch ${request.batchId}`);
        return;
      }

      const receivingPfaDetails = await this.pfaRepository.getPfaDetails(validPin.pfaCode);
      if (!receivingPfaDetails) {
        this.logger.error(`ValidPin PFA details not found for code ${validPin.pfaCode}`);
        return;
      }
      const invalidPins = [];
      let totalAmount = 0;
      for (const pin of pins) {
        if (!pin.isValid && pin.isEligibilityStatus) {
          invalidPins.push(pin);
          totalAmount += pin.validPinBalance;
        }
      }
      // only send message if there are pins that are elgible
      if (invalidPins.length > 0) {
        await this.notifyPaymentReciever(receivingPfaDetails, validPin, totalAmount, invalidPins);
      }

      // reset it here
      invalidPins.length = 0;
      invalidPins.push(...pins.filter((pin) => !pin.isValid));

      for (const pin of invalidPins) {
        // Get sending PFA details
        const sendingPfaDetails = await this.pfaRepository.getPfaDetails(pin.pfaCode);
        if (!sendingPfaDetails || !sendingPfaDetails.emailAddress) {
          this.logger.warn(`PFA details not found or missing email for PFA code: ${pin.pfaCode}`);
          continue;
        }

        // Create notification
        const notificationDto = new SendNotificationTemplateDto();
        notificationDto.emailRecipients = [sendingPfaDetails.emailAddress];
        notificationDto.notificationType = NotificatonTypeEnum.MPR_PAYMENT_REQUEST_PFA;

        notificationDto.placeholders = {
          pfaName: sendingPfaDetails.pfaName,
          rsaPin: pin.pin,
          receivingPin: validPin.pin,
          receivingPfaName: receivingPfaDetails.pfaName,
          amountDue: pin.eligibilityStatus ? pin.validPinBalance.toString() : 'NA',
          cpaBalance: pin.cpaBalance == 0 ? 'NA' : pin.cpaBalance.toString(),
        };

        // Send notification
        await firstValueFrom(
          this.notificationClient.send(
            NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE,
            notificationDto
          )
        );

        this.logger.info(`Payment request notification sent to PFA ${sendingPfaDetails.pfaName} for PIN ${pin.pin}`);
      }
    } catch (error) {
      this.logger.error(
        `Failed to send payment request notifications: ${error instanceof Error ? error.message : error}`
      );
    }
  }
  async notifyPaymentReciever(receivingPfaDetails: Pfa, validPin: any, totalAmount: number, invalidPins: any[]) {
    const pfaInvolved = await this.pfaRepository.getPfaNameByCode(invalidPins.map((pin) => pin.pfaCode));
    const notificationDto = new SendNotificationTemplateDto();
    notificationDto.emailRecipients = [receivingPfaDetails.emailAddress];
    notificationDto.notificationType = NotificatonTypeEnum.MULTIPLE_PIN_VALID_PIN_PAYMENT_CONFIRMATION;

    notificationDto.placeholders = {
      pfaName: receivingPfaDetails.pfaName,
      rsaPin: validPin.pin,
      amountDue: '' + totalAmount,
      pfaInvolved: pfaInvolved.join(', '),
    };

    this.logger.info(`Payment request notification sent to PFA ${receivingPfaDetails.pfaName} for PIN ${validPin.pin}`);

    return firstValueFrom(
      this.notificationClient.send(NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE, notificationDto)
    ).catch((error) => {
      this.logger.error(
        `Failed to send payment request notification to PFA ${receivingPfaDetails.pfaName}: ${error instanceof Error ? error.message : error}`
      );
    });
  }

  async rejectTransactionHistory(formData: Record<string, any>) {
    const pins = formData.thRejectedPins;
    for (const pin of pins) {
      const pinEntity = await this.multiplePinResolutionPinRepository.findOne({
        pk: pin.pk,
      });

      if (!pinEntity) {
        throw new CustomException(`No PIN found for pk: ${pin.pk}`);
      }

      const transaction = await this.multiplePinResolutionTransactionRepository.findOne({
        invalidPin: pinEntity.pin,
        request: { batchId: formData.batchId },
      });

      if (transaction) {
        await this.multiplePinResolutionTransactionRepository.removeEntity(transaction);
      }
      if (pinEntity) {
        pinEntity.hasTransactionHistory = false;
        pinEntity.thRequestedDate = new Date(); // should we request a new date
        pinEntity.thReceivedDate = null;
        pinEntity.cpaBalance = 0;
        pinEntity.validPinBalance = 0;
        await this.multiplePinResolutionPinRepository.saveEntity(pinEntity);
      }
      await this.notifyPfaOfTransactionHistoryRejection(pinEntity, pin.reason);
    }
  }
  async notifyPfaOfTransactionHistoryRejection(pin: any, reason: string) {
    const pfaDetails = await this.pfaRepository.getPfaDetails(pin.pfaCode);
    if (!pfaDetails) {
      this.logger.error(`PFA details not found for code ${pin.pfaCode}`);
      return;
    }

    const notificationDto = new SendNotificationTemplateDto();
    notificationDto.emailRecipients = [pfaDetails.emailAddress];
    notificationDto.notificationType = NotificatonTypeEnum.MULTIPLE_PIN_TRANSACTION_HISTORY_REJECTION;
    notificationDto.placeholders = {
      pfaName: pfaDetails.pfaName,
      rsaPin: pin.pin,
      reason: reason,
    };

    await firstValueFrom(
      this.notificationClient.send(NotificationServiceClientConstant.SEND_EMAIL_NOTIFICATION_TEMPLATE, notificationDto)
    ).catch((error) => {
      this.logger.error(
        `Failed to send transaction history rejection notification to PFA ${pfaDetails.pfaName}: ${error instanceof Error ? error.message : error}`
      );
    });
  }
}
