import { Module } from '@nestjs/common';
import { EnrollmentMsController } from './controllers/enrollment-ms.controller';
import { EnrollmentMsService } from './services/enrollment-ms.service';
import { DatabaseModule, LoggerModule, RedisCacheModule } from '@app/shared';
import { HealthModule } from '@app/shared/health';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { SettingMsLibModule } from '@app/shared/setting-service/setting-ms-lib.module';
import { ExcelUploadTasksGateway } from '@app/shared/tasks/excel-upload.gateway';
import { TasksModule } from '@app/shared/tasks/tasks.module';
import { AccruedRightComputationModule } from '@app/shared/computation';
import { UploadController } from './controllers/upload.controller';
import { UploadService } from './services/upload.service';
import { RabbitmqModule } from '@app/shared/rabbitmq/rabbitmq.module';
import { FileUploadRepository } from '@app/shared/enrollment-service/repository/file-upload.repository';
import { FileUploadProcess } from '@app/shared/enrollment-service/entities/file-upload.entity';
import {
  ECRS_SERVICE,
  ECRS_SERVICE_HOST,
  ECRS_SERVICE_TCP_PORT,
  ENROLLMENT_MS_SERVICE,
  ENROLLMENT_QUEUE,
} from '@app/shared/constants';
import { GeneralUtils } from '@app/shared/utils';
import { MdaEmployeeBiodataRepository } from '@app/shared/login-service/repositories/mda-employee-biodata.repository';
import { MdaEmployeeBiodata } from '@app/shared/login-service/entities/employee-biodata.entity';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { NominalRollRepository } from '@app/shared/enrollment-service/repository/nominal-roll.repository';
import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';
import { NominalRollBand } from '@app/shared/enrollment-service/entities/nominal-roll-band.entity';
import { NominalRollBandDetails } from '@app/shared/enrollment-service/entities/nominal-roll-band-details.entity';
import { CobraRole } from '@app/shared/user-service/entities/cobra-role.entity';
import { CobraPrivilege } from '@app/shared/user-service/entities/cobra-priviledge.entity';
import { APP_FILTER, APP_GUARD } from '@nestjs/core';
import { GlobalExceptionFilter } from '@app/shared/filters/global-exception.filter';
import { AuthGuard } from '@app/shared/auth/guards/auth.guard';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { EnrolmentSummary } from '@app/shared/enrollment-service/entities/enrolment-summary.entity';
import { EnrolmentBiodataRepository } from '@app/shared/enrollment-service/repository/enrolment-biodata.repository';
import { EnrolmentBiodata } from '@app/shared/enrollment-service/entities/enrolment-biodata.entity';
import { EmploymentDetail } from '@app/shared/enrollment-service/entities/employment-detail.entity';
import { EmploymentDocument } from '@app/shared/enrollment-service/entities/employment-document.entity';
import { ClientProxyFactory, Transport } from '@nestjs/microservices';
import { EnrolmentDraftRepository } from '@app/shared/enrollment-service/repository/enrolment-draft.repository';
import { EnrolmentDraft } from '@app/shared/enrollment-service/entities/enrolment-draft.entity';
import { FormValidatorService } from '@app/shared/enrollment-service/services/form-validator.service';
import { PfaRepository } from '@app/shared/user-service/repositories/pfa.repository';
import { MdaRepository } from '@app/shared/user-service/repositories/mda.repository';
import { Mda } from '@app/shared/user-service/entities/mda.entity';
import { Pfa } from '@app/shared/user-service/entities/pfa.entity';
import { EmployerService } from './services/employer.service';
import { TbeOrganisationsRepository } from '@app/shared/enrollment-service/repository/tbe-organisations.repository';
import { TbeOrganisations } from '@app/shared/enrollment-service/entities/tbe-organisations.entity';
import { TbeSectorSalMap } from '@app/shared/enrollment-service/entities/tbe-sector-sal-map.entity';
import { TbeSalStructure } from '@app/shared/enrollment-service/entities/tbe-sal-structure.entity';
import { WorkflowModule } from '@app/shared/workflow/workflow.module';
import { WorkflowService } from '@app/shared/workflow/services/workflow.service';
import { AuthModule } from '@app/shared/auth/auth.module';
import { SlipController } from './controllers/slip.controller';
import { SlipService } from '@app/shared/enrollment-service/services/slip.service';
import { EnrolmentDetailRepository } from '@app/shared/enrollment-service/repository/enrolment-detail.repository';
import { MultiplePinResolutionController } from './controllers/multiple-pin-resolution.controller';
import { MultiplePinResolutionService } from './services/multiple-pin-resolution.service';
import { MultiplePinResolutionRequest } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-req.entity';
import { MultiplePinResolutionRequestRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-req.repository';
import { MultiplePinResolutionDocument } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-document.entity';
import { MultiplePinResolutionWorkFlowService } from '@app/shared/workflow/services/multiple-pin-resolution-workflow.service';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { EnrollmentMsMessageController } from './controllers/enrollment-ms-message.controller';
import { EnrolmentHistoryView } from '@app/shared/enrollment-service/entities/enrolment-history-view.entity';
import { BatchRepository } from '@app/shared/enrollment-service/repository/batch.repository';
import { TblBatch } from '@app/shared/enrollment-service/entities/tbl-batch';
import { BatchController } from './controllers/batch.controller';
import { BatchRecord } from '@app/shared/enrollment-service/entities/batch-record';
import { BatchingService } from './services/batching.service';
import { BatchRecordRepository } from '@app/shared/enrollment-service/repository/batch-record.repository';
import { AmendmentRequest } from '@app/shared/enrollment-service/entities/amendment-request.entity';
import { AmendmentRequestController } from './controllers/amendment-request.controller';
import { AmendmentRequestService } from 'apps/enrollment-ms/src/services/amendment-request.service';
import { MemoService } from '@app/shared/enrollment-service/services/memo.service';
import { BatchDocuments } from '@app/shared/enrollment-service/entities/batch-documents.entity';
import { BatchDocumentRepository } from '@app/shared/enrollment-service/repository/batch-document.repository';
import { SerialNumberService } from '@app/shared/enrollment-service/services/serial-number.service';
import { SerialNumber } from '@app/shared/enrollment-service/entities/serial-number.entity';
import { AccruedRightsResultRepository } from '@app/shared/dto/enrollment/repositories/accrued-rights-result.repository';
import { AccruedRightsResult } from '@app/shared/dto/enrollment/entities/accrued-rights-result.entity';
import { MultiplePinResolutionDocumentRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-document.repository';
import { PfcRepository } from '@app/shared/user-service/repositories/pfc.repository';
import { Pfc } from '@app/shared/user-service/entities/pfc.entity';
import { TbcBatchAccountMemoDetailsRepository } from '@app/shared/enrollment-service/repository/tbc-batch-account-memo-details.repository';
import { TbcBatchAccountMemoDetails } from '@app/shared/enrollment-service/entities/tbc-batch-account-memo-details.entity';
import { NominalRollController } from './controllers/nominal-roll.controller';
import { NominalRollEnrollmentService } from './services/nominal-roll-enrollment.service';
import { MultiplePinResolutionTransactionHistory } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-transaction.entity';
import { MultiplePinResolutionPin } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-pin.entity';
import { MultiplePinResolutionPinRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-pin.repository';
import { MultiplePinResolutionTransactionHistoryRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-transaction.repository';
import { AuditLibModule } from '@app/shared/audit-service/audit-lib.module';
import { LegacyRecordController } from './controllers/legacy-record.controller';
import { LegacyRecordService } from './services/legacy-record.service';
import { LegacyRecordRepository } from '@app/shared/enrollment-service/repositories/legacy-record.repository';
import { LegacyRecordDocumentRepository } from '@app/shared/enrollment-service/repositories/legacy-record-document.repository';
import { LegacyRecordDocument } from '@app/shared/enrollment-service/entities/legacy-record-document.entity';
import { LegacyRecord } from '@app/shared/enrollment-service/entities/legacy-record.entity';
import { NominalRollHistoryRepository } from '@app/shared/enrollment-service/repository/nominal-roll-history.repository';
import { NominalRollHistory } from '@app/shared/enrollment-service/entities/nominal-roll-history.entity';
import { NominalRollBandHistory } from '@app/shared/enrollment-service/entities/nominal-roll-band-history.entity';
import { NominalRollBandDetailsHistory } from '@app/shared/enrollment-service/entities/nominal-roll-band-details-history.entity';
import { RsaPinValidationService } from './services/rsapin-validation.service';
import { RsaPinValidationController } from 'apps/enrollment-ms/src/controllers/rsapin-validation.controller';
import { TbeOrganisationsController } from './controllers/setup/tbe-organisations.controller';
import { TbeOrganisationService } from './services/setup/tbe-organisation.service';
import { TbcReconcilePay } from '@app/shared/enrollment-service/entities/tbc-reconcile-pay.entity';
import { TbcReconcilePayRepository } from '@app/shared/enrollment-service/repository/tbc-reconcile-pay.repository';

@Module({
  imports: [
    DatabaseModule,
    DatabaseModule.forFeature([
      SerialNumber,
      FileUploadProcess,
      CobraUser,
      CobraRole,
      CobraPrivilege,
      MdaEmployeeBiodata,
      NominalRoll,
      NominalRollBand,
      NominalRollBandDetails,
      EnrolmentSummary,
      EnrolmentBiodata,
      EmploymentDetail,
      EmploymentDocument,
      EnrolmentDraft,
      AccruedRightsResult,
      Mda,
      Pfa,
      Pfc,
      TblBatch,
      BatchRecord,
      BatchDocuments,
      TbeSectorSalMap,
      TbeSalStructure,
      TbeOrganisations,
      AmendmentRequest,
      NominalRollHistory,
      NominalRollBandHistory,
      NominalRollBandDetailsHistory,
      EnrolmentHistoryView,
      TbcBatchAccountMemoDetails,
      MultiplePinResolutionTransactionHistory,
      MultiplePinResolutionRequest,
      MultiplePinResolutionPin,
      MultiplePinResolutionDocument,
      LegacyRecord,
      LegacyRecordDocument,
      TbcReconcilePay,
    ]),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    LoggerModule.forRoot(ENROLLMENT_MS_SERVICE),
    RedisCacheModule.register(),
    HealthModule.registerAsync(
      [{ name: ECRS_SERVICE, host: ECRS_SERVICE_HOST, port: ECRS_SERVICE_TCP_PORT }],
      ENROLLMENT_QUEUE
    ),
    AuthModule,
    SettingMsLibModule,
    WorkflowModule,
    TasksModule,
    AccruedRightComputationModule,
    RabbitmqModule.register(ENROLLMENT_QUEUE),
    EnrollmentMsModule,
    AuditLibModule,
  ],
  controllers: [
    EnrollmentMsController,
    UploadController,
    SlipController,
    MultiplePinResolutionController,
    EnrollmentMsMessageController,
    BatchController,
    AmendmentRequestController,
    NominalRollController,
    LegacyRecordController,
    RsaPinValidationController,
    TbeOrganisationsController,
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: 'ECRS_SERVICE_CLIENT',
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: configService.get('ECRS_SERVICE_HOST', '0.0.0.0'),
            port: configService.get('ECRS_SERVICE_TCP_PORT', 3017),
          },
        });
      },
    },
    {
      provide: 'NOTIFICATION_SERVICE_CLIENT',
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: configService.get('NOTIFICATION_SERVICE_HOST', '127.0.0.1'),
            port: configService.get('NOTIFICATION_SERVICE_TCP_PORT', 3008),
          },
        });
      },
    },
    TbeOrganisationService,
    BatchingService,
    SlipService,
    MemoService,
    UploadService,
    WorkflowService,
    EmployerService,
    SerialNumberService,
    FormValidatorService,
    EnrollmentMsService,
    AmendmentRequestService,
    MultiplePinResolutionService,
    NominalRollEnrollmentService,
    MultiplePinResolutionWorkFlowService,
    ExcelUploadTasksGateway,
    FileUploadRepository,
    GeneralUtils,
    MdaRepository,
    PfaRepository,
    PfcRepository,
    BatchRepository,
    CobraUserRepository,
    BatchRecordRepository,
    NominalRollRepository,
    BatchDocumentRepository,
    EnrolmentDraftRepository,
    EnrolmentDetailRepository,
    NominalRollHistoryRepository,
    TbeOrganisationsRepository,
    MultiplePinResolutionService,
    MultiplePinResolutionRequestRepository,
    MultiplePinResolutionDocumentRepository,
    MultiplePinResolutionWorkFlowService,
    CobraUserRepository,
    AmendmentRequestService,
    EnrolmentBiodataRepository,
    EnrolmentSummaryRepository,
    MdaEmployeeBiodataRepository,
    AccruedRightsResultRepository,
    TbcBatchAccountMemoDetailsRepository,
    MultiplePinResolutionPinRepository,
    MultiplePinResolutionTransactionHistoryRepository,
    LegacyRecordService,
    LegacyRecordRepository,
    LegacyRecordDocumentRepository,
    RsaPinValidationService,
    TbcReconcilePayRepository,
  ],
})
export class EnrollmentMsModule {}
