<!DOCTYPE html>
<html>
<head>
  <style>
    body {
      font-family: Arial, sans-serif;
      font-size: 11px;
    }

    .header {
      text-align: right;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .title {
      text-align: center;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .second-title {
      text-align: center;
      font-weight: bold;
      margin-bottom: 10px;
      margin-top: 100px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th, td {
      border: 1px solid #000;
      padding: 4px;
      text-align: center;
    }

    th {
      background-color: #f2f2f2;
    }

    .nowrap {
      white-space: nowrap;
    }
  </style>
</head>
<body>
<div class="header">Certification Schedule</div>
<%
function fullName(record) {
  return `${record.surname} ${record.firstName}`;
}
let contributionPaidToDateSum = 0;
%>

<!-- ACCRUED RIGHTS SECTION -->
<h3 style="text-align: center;">ACCRUED RIGHTS FOR (<%= accruedRights[0]?.batchName || '' %>)</h3>
<table border="1" cellspacing="0" cellpadding="5">
  <thead>
  <tr>
    <th>S/N</th>
    <th>PIN</th>
    <th>NAME</th>
    <th>MDA</th>
    <th>MDA CODE</th>
    <th>PFA</th>
    <th>PFA CODE</th>
    <th>BATCH NAME</th>
    <th>ACCRUED RIGHTS</th>
  </tr>
  </thead>
  <tbody>
  <% accruedRights.forEach((record, index) => {
    const total = parseFloat(record.totalPension || '0');
    contributionPaidToDateSum += total;
  %>
  <tr>
    <td><%= index + 1 %></td>
    <td><%= record.rsaPin %></td>
    <td><%= fullName(record) %></td>
    <td><%= record.mdaName %></td>
    <td><%= record.mdaCode %></td>
    <td><%= record.pfaName %></td>
    <td><%= record.pfaCode %></td>
    <td><%= record.batchName %></td>
    <td><%= total.toLocaleString(undefined, { minimumFractionDigits: 2 }) %></td>
  </tr>
  <% }); %>
  <tr>
    <td colspan="8" style="text-align: right;"><strong>GRAND TOTAL</strong></td>
    <td><strong><%= contributionPaidToDateSum.toLocaleString(undefined, { minimumFractionDigits: 2 }) %></strong></td>
  </tr>
  </tbody>
</table>

</body>
</html>
