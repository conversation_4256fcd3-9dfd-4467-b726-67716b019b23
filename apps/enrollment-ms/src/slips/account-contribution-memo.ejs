<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Internal Memo</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans&display=swap" rel="stylesheet">
  <style>
    body {
      background: #fff;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      /*font-family: 'Noto Sans', sans-serif;*/
      padding: 2rem 1rem;
    }
    .naira {
      font-family: 'Noto Sans', sans-serif;
    }
    .memo-wrapper {
      max-width: 900px;
      margin: auto;
      border: 1px solid #ccc;
      padding: 3rem;
    }
    .logo {
      display: block;
      margin: 0 auto 1rem;
      max-height: 100px;
    }
    .memo-header {
      text-align: center;
      margin-bottom: 2rem;
    }
    .memo-header h4 {
      font-weight: bold;
    }
    .memo-section h6 {
      font-weight: bold;
      margin-top: 2rem;
    }
    .highlight-yellow {
      font-weight: bold;
    }
    .highlight-red {
      font-weight: bold;
      padding: 0 4px;
    }
    .section-content {
      padding-left: 2rem;
      text-align: justify;
    }
    .memo-table {
      margin-top: 1rem;
    }
    .memo-table th, .memo-table td {
      vertical-align: middle;
    }
    .confidential {
      text-align: center;
      margin-top: 2rem;
      font-size: small;
      color: #9D9D9D;
    }
    .memo-footer {
      margin-top: 1.5rem;
    }
  </style>
</head>
<body>
<div class="memo-wrapper">
  <img src="data:image/jpeg;base64,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"
       alt="Logo" class="logo mb-2">
  <div class="memo-header">
    <h4>NATIONAL PENSION COMMISSION</h4>
    <p class="fw-bold">INTERNAL MEMO</p>
  </div>

  <div class="row mb-2">
    <div class="col-2 fw-bold">To:</div>
    <div class="col">Head, Accounts Department</div>
  </div>
  <div class="row mb-2">
    <div class="col-2 fw-bold">From:</div>
    <div class="col">Head, Contribution and Bond Redemption Department</div>
  </div>
  <div class="row mb-2">
    <div class="col-2 fw-bold">Date:</div>
    <div class="col"><%= requestDate; %></div>
  </div>
  <div class="row mb-2">
    <div class="col-2 fw-bold">Ref:</div>
    <div class="col"><%= referenceNo; %></div>
  </div>
  <div class="row mb-3">
    <div class="col-2 fw-bold">Subject:</div>
    <div class="col">
        <span class="fw-bold text-uppercase">
          RE: PAYMENT OF OUTSTANDING PENSION CONTRIBUTIONS OF FEDERAL GOVERNMENT EMPLOYEES UNDER (<span class="highlight-yellow"><%= batchName; %></span>)
        </span>
    </div>
  </div>

  <div class="memo-section">
    <p>
      Please be informed that approval had been granted for the payment of  <span class="fw-bold naira"><%=batchAmount;%></span>  <strong>(<%= batchAmountWords; %> Naira only)</strong> outstanding pension contributions to Federal Government employees who retired under <span class="highlight-yellow"><%= batchName; %></span>.
    </p>
    <p>Kindly debit the Transit Account and credit the accounts of the underlisted PFCs/PFAs with the amounts indicated in the Table below, and forward the duly signed e-payment schedule to us for further action
    </p>

    <div class="table-responsive memo-table">
      <table class="table table-bordered text-center">
        <thead class="table-secondary">
        <tr>
          <th>S/N</th>
          <th>PFA</th>
          <th>ACCOUNT NAME</th>
          <th>ACCOUNT NUMBER</th>
          <th>AMOUNT <span class="naira">(₦)</span></th>
        </tr>
        </thead>
        <tbody>
        <%
        let totalAmount = 0;
        if (Array.isArray(pfaBreakdown) && pfaBreakdown.length > 0) {
        pfaBreakdown.forEach((item, index) => {
          const amountStr = String(item.amount).replace(/[^\d.-]/g, '');
          const amount = parseFloat(amountStr) || 0;
          totalAmount += amount;
        %>
        <tr>
          <td><%= index + 1 %></td>
          <td style="text-align:left"><%= item.pfaName %></td>
          <td style="text-align:left"><%= item.pfaAccountName %></td>
          <td><%= item.pfaAccountNumber %></td>
          <td class="naira" style="text-align:right"><%= amount.toLocaleString('en-NG', { minimumFractionDigits: 2 }) %></td>
        </tr>
        <% }); } %>
        <tr class="table-secondary fw-bold">
          <td colspan="4">TOTAL</td>
          <td class="naira" style="text-align:right"><%= totalAmount.toLocaleString('en-NG', { minimumFractionDigits: 2 }) %></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>

  <p>Thank you.</p>
  <div class="memo-footer">
    <div class='row '>
      <img style="max-height: 50px;margin-top: 10px;width: max-content;" class='signature-img col-6' src='<%= authorizerSignature; %>'
           alt='Signature'>
    </div>
    <b><%= authorizerName; %></b>
  </div>
</div>
</body>
</html>
