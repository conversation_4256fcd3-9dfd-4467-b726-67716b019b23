<!DOCTYPE html>
<html>
<head>
  <style>
    body {
      font-family: Arial, sans-serif;
      font-size: 11px;
    }

    .header {
      text-align: right;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .title {
      text-align: center;
      font-weight: bold;
      margin-bottom: 10px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th, td {
      border: 1px solid #000;
      padding: 4px;
      text-align: center;
    }

    th {
      background-color: #f2f2f2;
    }

    .nowrap {
      white-space: nowrap;
    }
  </style>
</head>
<body>
<div class="header">Appendix 1</div>
<div class="title">SCHEDULE OF REMITTANCE/REFUND FOR (<%= batchName %>)</div>

<table>
  <thead>
  <tr>
    <th rowspan="2">S/N</th>
    <th rowspan="2">PFA</th>
    <th colspan="4">REMITTANCE</th>
    <th colspan="4">REFUND</th>
  </tr>
  <tr>
    <th>RSA Count</th>
    <th>Total Due</th>
    <th>Paid To Date</th>
    <th>Additional Pension</th>
    <th>RSA Count</th>
    <th>Total Due</th>
    <th>Paid To Date</th>
    <th>Refund</th>
  </tr>
  </thead>
  <tbody>
  <% let total = {
    ocCount: 0, ocDue: 0, ocPaid: 0, ocPension: 0,
    rcCount: 0, rcDue: 0, rcPaid: 0, rcRefund: 0
  }; %>

  <% pfaBreakdown.forEach((pfa, index) => { %>
    <tr>
      <td><%= index + 1 %></td>
      <td class="company-name"><%= pfa.pfa %></td>
      <td><%= pfa.outstanding.count %></td>
      <td class="numeric"><%= pfa.outstanding.totalDue.toLocaleString() %></td>
      <td class="numeric"><%= pfa.outstanding.paidToDate.toLocaleString() %></td>
      <td class="numeric"><%= pfa.outstanding.additionalPension.toLocaleString() %></td>
      <td><%= pfa.refund.count %></td>
      <td class="numeric"><%= pfa.refund.totalDue.toLocaleString() %></td>
      <td class="numeric"><%= pfa.refund.paidToDate.toLocaleString() %></td>
      <td class="numeric"><%= pfa.refund.refund.toLocaleString() %></td>
    </tr>

    <%
      total.ocCount += pfa.outstanding.count;
      total.ocDue += pfa.outstanding.totalDue;
      total.ocPaid += pfa.outstanding.paidToDate;
      total.ocPension += pfa.outstanding.additionalPension;
      total.rcCount += pfa.refund.count;
      total.rcDue += pfa.refund.totalDue;
      total.rcPaid += pfa.refund.paidToDate;
      total.rcRefund += pfa.refund.refund;
    %>
  <% }) %>

  <tr class="total-row">
    <td colspan="2" class="company-name">GRAND TOTAL</td>
    <td><%= total.ocCount %></td>
    <td class="numeric"><%= total.ocDue.toLocaleString() %></td>
    <td class="numeric"><%= total.ocPaid.toLocaleString() %></td>
    <td class="numeric"><%= total.ocPension.toLocaleString() %></td>
    <td><%= total.rcCount %></td>
    <td class="numeric"><%= total.rcDue.toLocaleString() %></td>
    <td class="numeric"><%= total.rcPaid.toLocaleString() %></td>
    <td class="numeric"><%= total.rcRefund.toLocaleString() %></td>
  </tr>
  </tbody>
</table>
</body>
</html>
