<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>National Pension Commission</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
        }

        .header {
            background: #2d572c;
            color: white;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
            min-height: 60px;
        }

        .header img {
            width: 100%;
            height: 100%;
        }

        .detail-section {
            padding: 20px 40px;
        }

        .header h1 {
            font-size: 24px;
            margin: 0;
            padding-left: 10px;
        }

        .content-section {
            background: #F4F4F4;
        }

        .container {
            padding: 20px;
            max-width: 900px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            margin: 20px 0;
            text-transform: uppercase;
        }

        .info-row {
            display: flex;
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: bold;
            width: 200px;
        }

        .info-value {
            flex: 1;
        }

        .section-header {
            background-color: #cccccc;
            padding: 10px;
            font-weight: bold;
            margin: 30px 0;
            text-transform: uppercase;
        }

        .photo {
            float: right;
            border: 1px solid #ccc;
            max-width: 150px;
            max-height: 180px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }

        table th {
            background-color: #f5f5f5;
            text-align: left;
            padding: 8px;
            border-bottom: 1px solid #ddd;
        }

        table td {
            padding: 8px;
            border-bottom: 1px solid #ddd;
        }

        .deceased-instruction {
            margin-top: 30px;
        }

        .signature-img {
            max-height: 50px;
            width: fit-content;
        }

        .footer {
            background-color: #0a2701;
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
        }
    </style>
</head>
<body>
<div class='container'>
    <div class='content-section'>
        <div class='header'>
            <img src='data:image/png;base64, <%= bannerImage; %>'
                 alt='Contribution & Bond Redemption Application' style='vertical-align: middle; color: black'>
        </div>
        <div class='detail-section'>
            <div class='title'>
                <%= isDeceased ? 'DEATH BENEFIT - APPLICATION SLIP' : 'Retirement Benefit Enrolment Slip' %>
            </div>

            <div class='row'>
                <div class='col-md-8'>
                    <p><strong><%= rsaPin.startsWith('PEN') ? 'RSA PIN:' : 'TPIN:' %></strong> <%= rsaPin %></p>
                    <p><strong>PFA:</strong> <%= pfaName; %></p>
                </div>
                <div class='col-md-4 text-end'>
                    <img class='photo' src='data:image/png;base64, <%= passportBase64; %>' alt='ID Photo'>
                </div>
            </div>

            <div class='section-header'>Personal Information</div>

            <div class='info-row'>
                <div class='info-label'>Full Name</div>
                <div class='info-value'> <%= fullName; %></div>
                <div class='info-label' style='margin-left: 40px'>
                    Date of First Appointment
                </div>
                <div class='info-value'> <%= dofa; %></div>
            </div>

            <div class='info-row'>
                <div class='info-label'>Gender</div>
                <div class='info-value'> <%= gender; %></div>
                <div class='info-label' style='margin-left: 40px'>
                    Date of Retirement
                </div>
                <div class='info-value'> <%= edor; %></div>
            </div>

            <div class='info-row'>
                <div class='info-label'>Date of Birth</div>
                <div class='info-value'> <%= dob; %></div>
            </div>

            <div class='section-header'>Employment Information</div>

            <table>
                <thead>
                <tr>
                    <th>YEAR</th>
                    <th>SALARY STRUCTURE</th>
                    <th>G/L</th>
                    <th>STEP</th>
                    <th>EMPLOYER NAME</th>
                </tr>
                </thead>
                <tbody>
                <% employmentHistory.forEach(record => { %>
                    <tr>
                        <td><%= record.year; %></td>
                        <td><%= record.salaryStructure; %></td>
                        <td><%= record.gradeLevel; %></td>
                        <td><%= record.step; %></td>
                        <td><%= record.employerName; %></td>
                    </tr>
                <% }); %>
                </tbody>
            </table>

            <div class='section-header'>Registration Information</div>

            <div class='row'>
                <div class='col-6'>
                    <div class='row'>
                        <div class='col-5'>Enrolment Date</div>
                        <div class='col-7'><%= registrationDate; %></div>
                    </div>

                    <div class='row mt-3'>
                        <div class='col-5'>Location (state)</div>
                        <div class='col-7'><%= location; %></div>
                    </div>

                    <div class='row mt-3'>
                        <div class='col-5'>Officer</div>
                        <div class='col-7'><%= enrollmentOfficer; %></div>
                    </div>
                </div>
                <div class='col-6'>
                    <div class='row' style='max-height: 50px;'>
                        <div class='signature-label col-6'>Employee Signature</div>
                        <img class='signature-img col-6' src='data:image/png;base64, <%= retireeSignatureBase64; %>'
                             alt='Retiree Signature'>
                    </div>
                    <div class='row' style='max-height: 50px;'>
                        <div class='signature-label col-6'>Officer Signature</div>
                        <img class='signature-img col-6' src='<%= officerSignatureBase64; %>'
                             alt='Officer Signature'>
                    </div>
                </div>
            </div>

            <% if (pdoSurname) { %>
                <div class='pdo-section'>
                    <div class='section-header'>PDO Information</div>
                    <div class='row'>
                        <div class='col-6'>
                            <div class='row'>
                                <div class='col-5'>Surname</div>
                                <div class='col-7'><%= pdoSurname; %></div>
                            </div>
                            <div class='row mt-3'>
                                <div class='col-5'>User ID</div>
                                <div class='col-7'><%= pdoUserId; %></div>
                            </div>
                        </div>
                        <div class='col-6'>

                            <div class='row '>
                                <div class='col-5'>First Name</div>
                                <div class='col-7'><%= pdoFirstName; %></div>
                            </div>

                            <div class='row mt-3'>
                                <div class='col-5'>Email Address</div>
                                <div class='col-7'><%= pdoEmailAddress; %></div>
                            </div>
                        </div>
                    </div>
                </div>
            <% } %>

            <% if (nokSurname) { %>
                <div class='nok-section'>
                    <div class='section-header'>NOK Information</div>
                    <div class='row'>
                        <div class='col-6'>
                            <div class='row'>
                                <div class='col-5'>Surname</div>
                                <div class='col-7'><%= nokSurname; %></div>
                            </div>
                            <div class='row mt-3'>
                                <div class='col-5'>Phone Number</div>
                                <div class='col-7'><%= nokPhoneNumber; %></div>
                            </div>
                        </div>
                        <div class='col-6'>

                            <div class='row '>
                                <div class='col-5'>First Name</div>
                                <div class='col-7'><%= nokFirstName; %></div>
                            </div>

                            <div class='row mt-3'>
                                <div class='col-5'>Email Address</div>
                                <div class='col-7'><%= nokEmailAddress; %></div>
                            </div>
                        </div>
                    </div>
                </div>
            <% } %>

            <% if (isDeceased) { %>
                <div class='row deceased-instruction'>
                    <hr>
                    <p>
                        You have successfully submitted the application for the death benefit of the deceased employee
                        named above.
                    </p>
                    <p>
                        Thank you.
                    </p>
                    <p>
                        <strong>Note: The PFA would contact you as soon as the payment is made.</strong>
                    </p>
                </div>
            <% } %>
        </div>

        <div class='footer'>
            <div>www.pencom.gov.ng</div>
            <div><EMAIL></div>
        </div>
    </div>
</div>


</body>
</html>
