<!DOCTYPE html>
<html>
<head>
  <style>
    body {
      font-family: Arial, sans-serif;
      font-size: 11px;
    }

    .header {
      text-align: right;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .title {
      text-align: center;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .second-title {
      text-align: center;
      font-weight: bold;
      margin-bottom: 10px;
      margin-top: 100px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th, td {
      border: 1px solid #000;
      padding: 4px;
      text-align: center;
    }

    th {
      background-color: #f2f2f2;
    }

    .nowrap {
      white-space: nowrap;
    }
  </style>
</head>
<body>
<div class="header">Certification Schedule</div>
<div class="title">OUTSTANDING CONTRIBUTIONS FOR (<%= batchName %>)</div>

<%
let contributionTotalDueSum = 0;
let contributionPaidToDateSum = 0;
let contributionAdditionalPensionSum = 0;
%>

<table>
  <thead>
  <tr>
    <th>S/N</th>
    <th>PIN</th>
    <th>NAME</th>
    <th>GENDER</th>
    <th>DOB</th>
    <th>DOFA</th>
    <th>EXIT DATE</th>
    <th>PFA</th>
    <th>TOTAL DUE</th>
    <th>PAID TO DATE</th>
    <th>ADDITIONAL PENSION</th>
  </tr>
  </thead>
  <tbody>
  <% contributionRecords.forEach((record, index) => {
    const totalDue = parseFloat(record.totalDue || 0);
    const paidToDate = parseFloat(record.paidToDate || 0);
    const additionalPension = parseFloat(record.additionalPension || 0);

    contributionTotalDueSum += totalDue;
    contributionPaidToDateSum += paidToDate;
    contributionAdditionalPensionSum += additionalPension;
  %>
  <tr>
    <td><%= index + 1 %></td>
    <td><%= record.rsaPin %></td>
    <td><%= record.name %></td>
    <td><%= record.gender %></td>
    <td><%= record.dob %></td>
    <td><%= record.dofa %></td>
    <td><%= record.edor %></td>
    <td><%= record.pfa %></td>
    <td><%= totalDue.toLocaleString() %></td>
    <td><%= paidToDate.toLocaleString() %></td>
    <td><%= additionalPension.toLocaleString() %></td>
  </tr>
  <% }); %>
  </tbody>
  <tfoot>
  <tr>
    <td colspan="8"><strong>GRAND TOTAL</strong></td>
    <td><%= contributionTotalDueSum.toLocaleString() %></td>
    <td><%= contributionPaidToDateSum.toLocaleString() %></td>
    <td><%= contributionAdditionalPensionSum.toLocaleString() %></td>
  </tr>
  </tfoot>
</table>

<%
let refundPaidToDateSum = 0;
let refundSum = 0;
%>

<div class="second-title">OUTSTANDING CONTRIBUTIONS (REFUNDS) FOR (<%= batchName %>)</div>

<table border="1" cellspacing="0" cellpadding="5">
  <thead>
  <tr>
    <th>S/N</th>
    <th>PIN</th>
    <th>NAME</th>
    <th>SEX</th>
    <th>DATE OF BIRTH</th>
    <th>DATE FIRST APPOINTED</th>
    <th>DATE RETIRED</th>
    <th>PFA</th>
    <th>TOTAL DUE</th>
    <th>PAID TO DATE</th>
    <th>REFUND</th>
  </tr>
  </thead>
  <tbody>
  <% refundRecords.forEach((record, index) => {
    const totalDue = parseFloat(record.totalDue || 0);
    const paidToDate = parseFloat(record.paidToDate || 0);
    const refund = parseFloat(record.refund || 0);

    refundPaidToDateSum += paidToDate;
    refundSum += refund;
  %>
  <tr>
    <td><%= index + 1 %></td>
    <td><%= record.rsaPin %></td>
    <td><%= record.name %></td>
    <td><%= record.gender %></td>
    <td><%= record.dob %></td>
    <td><%= record.dofa %></td>
    <td><%= record.edor %></td>
    <td><%= record.pfa %></td>
    <td><%= totalDue.toLocaleString(undefined, {minimumFractionDigits: 2}) %></td>
    <td><%= paidToDate.toLocaleString(undefined, {minimumFractionDigits: 2}) %></td>
    <td>(<%= Math.abs(refund).toLocaleString(undefined, {minimumFractionDigits: 2}) %>)</td>
  </tr>
  <% }); %>
  </tbody>
  <tfoot>
  <tr>
    <td colspan="8"><strong>GRAND TOTAL</strong></td>
    <td><strong><%= refundRecords.reduce((sum, r) => sum + parseFloat(r.totalDue || 0), 0).toLocaleString(undefined, {minimumFractionDigits: 2}) %></strong></td>
    <td><strong><%= refundPaidToDateSum.toLocaleString(undefined, {minimumFractionDigits: 2}) %></strong></td>
    <td><strong>(<%= Math.abs(refundSum).toLocaleString(undefined, {minimumFractionDigits: 2}) %>)</strong></td>
  </tr>
  </tfoot>
</table>

</body>
</html>
