/* eslint-disable */
import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { UpdateProgressDto } from '@app/shared/dto/enrollment/update-progress.dto';
import { UploadService } from '../services/upload.service';
import { EnrollmentServiceClientConstant } from '@app/shared/constants/enrollment-service-client.constant';
import { EnrollmentMsService } from '../services/enrollment-ms.service';
import { ProcessEnrolmentDto } from '@app/shared/dto/enrollment/process-enrollment.dto';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { EnrolmentMachineEventType } from '@app/shared/workflow/workflow/exit-ar-cr.workflow';
import { NominalRollEnrollmentService } from '../services/nominal-roll-enrollment.service';
import { ProcessNominalRollDto } from '@app/shared/dto/enrollment/process-nominal-roll.dto';
import { NominalRollMachineEventType } from '@app/shared/workflow/workflow/norminal-roll-contribution.workflow';
import { MultiplePinResolutionService } from 'apps/enrollment-ms/src/services/multiple-pin-resolution.service';

@Controller('')
export class EnrollmentMsMessageController {
  constructor(
    private readonly uploadService: UploadService,
    private readonly enrollmentMsService: EnrollmentMsService,
    private readonly nominalRollEnrollmentService: NominalRollEnrollmentService,
    private readonly multiplePinResolutionService: MultiplePinResolutionService
  ) {}

  @MessagePattern(EnrollmentServiceClientConstant.UPDATE_PROGRESS)
  async retrieveUserDetails(@Payload() updateProgressDto: UpdateProgressDto) {
    this.uploadService.emitProgressUpdate(updateProgressDto.taskId, updateProgressDto.percentage);
    return {
      message: `Progress update emitted for task ${updateProgressDto.taskId}: ${updateProgressDto.percentage}%`,
    };
  }

  @MessagePattern(EnrollmentServiceClientConstant.UPDATE_EXIT_ACCRUED_COMPUTATION)
  async handleExitComptationUpdate(@Payload() payload: Record<string, string>) {
    const req: ICustomRequest = {
      user: {
        email: payload.email,
      },
    } as ICustomRequest;

    const processEnrolmentDto: ProcessEnrolmentDto = new ProcessEnrolmentDto();
    processEnrolmentDto.action = payload.action as EnrolmentMachineEventType;
    processEnrolmentDto.rsaPin = payload.rsaPin;
    processEnrolmentDto.formData = payload;

    return await this.enrollmentMsService.processEnrolment(processEnrolmentDto, req);
  }

  @MessagePattern(EnrollmentServiceClientConstant.PFA_NON_CONFIRMATION_ESCALATION_JOB)
  async handleNonConfirmationEscaltion(@Payload() payload: Record<string, string>) {
    const req: ICustomRequest = {
      user: {
        email: payload.email,
      },
    } as ICustomRequest;

    if (payload.requestType === 'NOMINAL_ROLL') {
      const options: Record<string, string> = {};
      options['rsaPin'] = payload.rsaPin;
      options['pfaCode'] = payload.pfaCode;
      const processEnrolmentDto: ProcessNominalRollDto = new ProcessNominalRollDto();
      processEnrolmentDto.action = payload.action as NominalRollMachineEventType;
      processEnrolmentDto.options = options;
      return await this.nominalRollEnrollmentService.processRequest(processEnrolmentDto, req);
    } else {
      // todo implement for enrollment
    }

    return await this.multiplePinResolutionService.updateWorkflow(payload);
  }

  @MessagePattern(EnrollmentServiceClientConstant.UPDATE_NOMINAL_ROLL_COMPUTATION)
  async handleNominalRollContributionComptationUpdate(@Payload() payload: Record<string, string>) {
    const req: ICustomRequest = {
      user: {
        email: payload.email,
      },
    } as ICustomRequest;

    const options: Record<string, string> = {};
    options['rsaPin'] = payload.rsaPin;
    options['actorEmailAddress'] = 'SYSTEM_COMPUTATION';
    const processEnrolmentDto: ProcessNominalRollDto = new ProcessNominalRollDto();
    processEnrolmentDto.action = payload.action as NominalRollMachineEventType;
    processEnrolmentDto.options = options;

    return await this.nominalRollEnrollmentService.processRequest(processEnrolmentDto, req);
  }

  @MessagePattern(EnrollmentServiceClientConstant.PROCESS_MULTIPLE_PIN_WORKFLOW)
  async handleMultiplePinComptationUpdate(@Payload() payload: Record<string, string>) {
    return await this.multiplePinResolutionService.updateWorkflow(payload);
  }
}
