/* eslint-disable */
import { Body, Controller, Get, Param, Post, Query, Req, Res, UseInterceptors } from '@nestjs/common';
import { EnrollmentMsService } from '../services/enrollment-ms.service';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ICalculationParams } from '@app/shared/computation/accrued-rights/calculation.dto';
import { MdaEmployeeBiodataSearchDto } from '@app/shared/dto/enrollment/mda-data-upload-result.dto';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { RetireeRecordsSearchDto } from '@app/shared/dto/enrollment/retiree-records-search.dto';
import {
  BandCalculation,
  GetEnrolmentDraftDto,
  RetrieveOrganisationDetailsDto,
  RetrieveSectorEmployerDetailsDto,
  RetrieveYearDetailsDto,
  RetrieveYearSectorDetailsDto,
  SaveEnrolmentDraftDto,
} from '@app/shared/dto/enrollment/enrolment-draft.dto';
import { EmployerService } from '../services/employer.service';
import { filterBands } from '@app/shared/utils';
import { Public } from '@app/shared/decorators/public.decorator';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { ProcessBulkEnrolmentDto, ProcessEnrolmentDto } from '@app/shared/dto/enrollment/process-enrollment.dto';
import { BufferToBase64Interceptor } from '@app/shared/interceptors/buffer-to-base64-interceptor';
import { Response } from 'express';
import { AppendUserFilters } from '@app/shared/interceptors/user-filters.interceptor';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { BaseResponseListWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { ReassignmentDto } from '@app/shared/dto/enrollment/reassignment.dto';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { Audit } from '@app/shared/decorators/audit.decorator';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';

@ApiBearerAuth()
@UseInterceptors(AuditInterceptor)
@Controller('/')
export class EnrollmentMsController {
  constructor(
    private readonly enrollmentMsService: EnrollmentMsService,
    private readonly employerService: EmployerService
  ) {}

  @Post('/handle-enrollment')
  async handleEnrollment(task: any): Promise<BaseResponseDto> {
    return this.enrollmentMsService.sendTask(task);
  }

  @Post('/accrued-right')
  calculate(@Body() input: ICalculationParams) {
    return this.enrollmentMsService.calculate(input);
  }

  @RequirePrivileges(CobraPrivileges.EMPLOYEE_RECORD_PAGE)
  @Get('mda-employee-biodata')
  async getMdaEmployeeBiodata(@Query() query: MdaEmployeeBiodataSearchDto, @Req() req: ICustomRequest) {
    return await this.enrollmentMsService.fetchMdaEmployee(query, req);
  }

  @RequirePrivileges(
    CobraPrivileges.EMPLOYEE_RECORD_PAGE,
    CobraPrivileges.RETIREE_RECORD_PAGE,
    CobraPrivileges.ENROLLMENT_PAGE,
    CobraPrivileges.PENDING_APPROVAL_PAGE,
    CobraPrivileges.CERTIFICATE_REVIEW_PAGE,
    CobraPrivileges.AUDIT_VALIDATION_PAGE,
    CobraPrivileges.RECORD_VALIDATION_PAGE
  )
  @Post('retrieve-enrollment-summary')
  async retrieveEnrollmentSummary(
    @Body() filter: PaginatedSearchDto,
    @AppendUserFilters([]) filters: any,
    @Req() req: ICustomRequest
  ) {
    return await this.enrollmentMsService.retrieveEnrollmentSummary(filter, req);
  }

  @Get('retrieve-enrollment-history')
  async retrieveEnrollmentHistory(@Query('uniqueId') uniqueId: string, @Res() res: Response) {
    if (!uniqueId) {
      res.status(400).send('Please provide valid Unique ID');
      return;
    }
    const result = await this.enrollmentMsService.retrieveEnrollmentHistory(uniqueId);
    return res.status(200).json(result);
  }

  @Post('retrieve-enrollment-summary-detail')
  @UseInterceptors(BufferToBase64Interceptor)
  async retrieveEnrollmentSummaryDetail(@Body() recordsSearchDto: RetireeRecordsSearchDto) {
    return await this.enrollmentMsService.retrieveEnrollmentSummaryDetail(recordsSearchDto);
  }

  @Post('save-enrollment-progress')
  async saveEnrolmentProgress(@Body() saveEnrolmentDraftDto: SaveEnrolmentDraftDto) {
    return await this.enrollmentMsService.saveEnrolmentDraftProgress(saveEnrolmentDraftDto);
  }

  @Post('process-enrolment')
  async processEnrolment(@Body() processEnrolmentDto: ProcessEnrolmentDto, @Req() req: ICustomRequest) {
    return await this.enrollmentMsService.processEnrolment(processEnrolmentDto, req);
  }

  @Post('process-bulk-enrolment')
  async processBulkEnrolment(@Body() processEnrolmentDto: ProcessBulkEnrolmentDto, @Req() req: ICustomRequest) {
    return await this.enrollmentMsService.processBulkEnrolment(processEnrolmentDto, req);
  }

  @Post('get-enrollment-progress')
  async getEnrolmentProgress(@Body() getEnrolmentDraftDto: GetEnrolmentDraftDto) {
    return await this.enrollmentMsService.getEnrolmentProgress(getEnrolmentDraftDto);
  }

  @Post('retrieve-employer-details')
  async retrieveEmployerDetails(@Body() retrieveEmployerDetailsDto: RetrieveSectorEmployerDetailsDto) {
    return await this.employerService.retrieveEmployerDetails(retrieveEmployerDetailsDto);
  }

  @Post('retrieve-sector-employer-details')
  async retrieveSectorEmployerDetails(@Body() retrieveEmployerDetailsDto: RetrieveYearSectorDetailsDto) {
    return await this.employerService.retrieveSectorEmployerDetails(retrieveEmployerDetailsDto);
  }

  @Post('retrieve-year-sector-details')
  async retrieveYearSectorCodeDetails(@Body() retrieveEmployerDetailsDto: RetrieveYearDetailsDto) {
    return await this.employerService.retrieveYearSectorCodeDetails(retrieveEmployerDetailsDto);
  }

  @Post('retrieve-organisation-details')
  async retrieveOrganisationSectorIppisDetails(@Body() retrieveEmployerDetailsDto: RetrieveOrganisationDetailsDto) {
    return await this.employerService.getOrganisationSectorIppisDetails(retrieveEmployerDetailsDto);
  }

  @RequirePrivileges(CobraPrivileges.REASSIGN_TASK)
  @ApiOperation({
    summary: 'Get count of pending records  assigned to a user ',
    description: 'Get count of pending records  assigned to a user ',
  })
  @ApiResponse({
    status: 200,
    description: 'Count of pending records by assigned to a user ',
    type: BaseResponseListWithContentNoPagination,
  })
  @Get('/:emailAddress/enrolled/count')
  async getEnrollmentSummaryCount(@Param('emailAddress') emailAddress: string) {
    return await this.enrollmentMsService.getEnrollmentSummaryCount(emailAddress);
  }

  @RequirePrivileges(CobraPrivileges.REASSIGN_TASK)
  @ApiOperation({
    summary: 'Get count of pending records assigned to an auditor',
    description: 'Get count of pending records assigned to an auditor',
  })
  @ApiResponse({
    status: 200,
    description: 'Count of pending records assigned to an auditor',
    type: BaseResponseListWithContentNoPagination,
  })
  @Get('/:emailAddress/validated/count')
  async getEnrollmentSummaryCountByAuditor(@Param('emailAddress') emailAddress: string) {
    return await this.enrollmentMsService.getEnrollmentSummaryCountByAuditor(emailAddress);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.REASSIGN_TASK)
  @Audit(AuditEventTypeEnum.REASSIGN_TASK)
  @ApiOperation({
    summary: 'Reassign pending records to users',
    description: 'Reassign pending records to users',
  })
  @ApiResponse({
    status: 200,
    description: 'Reassignment successful',
    type: BaseResponseDto,
  })
  @Post('/reassign')
  async reassign(@Body() reassignmentDto: ReassignmentDto, @Req() req: ICustomRequest) {
    req.metadata.extra = {
      reassignmentDto,
    };
    return await this.enrollmentMsService.reassign(reassignmentDto);
  }

  @Public()
  @Post('calculate-bands')
  async filterBands(@Body() bandCalculation: BandCalculation) {
    console.log(`bandCalculation, ${JSON.stringify(bandCalculation)}`);
    const filter = await filterBands(bandCalculation.startDate, bandCalculation.endDate, bandCalculation.ippisDate);

    console.log(`bandCalculation, ${JSON.stringify(filter)}`);
    return {
      message: 'Bands filtered successfully',
      data: filter,
    };
  }
}
