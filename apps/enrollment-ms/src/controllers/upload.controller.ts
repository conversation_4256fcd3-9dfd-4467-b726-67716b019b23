/* eslint-disable */
import {
  Body,
  Controller,
  Get,
  NestInterceptor,
  Post,
  Query,
  Req,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  BatchPaymentConfirmationUploadRequestDto,
  FileUploadRequestDto,
} from '@app/shared/dto/request/file-upload-request.dto';
import { FileUploadResponseDto } from '@app/shared/dto/response/file-upload-response.dto';
import { UploadService } from '../services/upload.service';
import { Response } from 'express';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiOperation } from '@nestjs/swagger';
import { multerExcelOptions } from '@app/shared/decorators/multer-xlsx.config';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';

@ApiBearerAuth()
@Controller('/upload')
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @RequirePrivileges()
  @ApiOperation({ summary: 'Upload MDA Excel file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Excel file to upload',
        },
        processType: {
          type: 'string',
          description: 'Type of upload process',
        },
      },
    },
  })
  @Post('/mda-excel-upload')
  @UseInterceptors(FileInterceptor('file', multerExcelOptions) as unknown as NestInterceptor)
  async handleMdaBiodataUpload(
    @UploadedFile() file: Express.Multer.File,
    @Body() fileUploadRequestDto: FileUploadRequestDto,
    @Req() req: ICustomRequest
  ): Promise<FileUploadResponseDto> {
    return this.uploadService.handleMdaExcelUpload(file, fileUploadRequestDto, req);
  }

  @AllowedUserTypes(UserTypeEnum.PFA)
  @RequirePrivileges(CobraPrivileges.NOMINAL_ROLL_UPLOAD, CobraPrivileges.ACCRUED_RIGHTS_UPLOAD)
  @Post('/payment-confirmation-excel-upload')
  @UseInterceptors(FileInterceptor('file', multerExcelOptions) as unknown as NestInterceptor)
  async handlePfaPaymentConfirmation(
    @UploadedFile() file: Express.Multer.File,
    @Body() batchPaymentConfirmationUploadRequestDto: BatchPaymentConfirmationUploadRequestDto,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    return this.uploadService.handlePfaPaymentConfirmation(file, batchPaymentConfirmationUploadRequestDto, req);
  }

  @Get('/download-result')
  async downloadTask(@Query('taskId') taskId: string, @Res() res: Response) {
    return this.uploadService.downloadResultFile(taskId, res);
  }

  @Get('update-progress')
  async updateProgress(@Query('taskId') taskId: string, @Query('percentage') percentage: number) {
    this.uploadService.emitProgressUpdate(taskId, percentage);
    return {
      message: `Progress update emitted for task ${taskId}: ${percentage}%`,
    };
  }
}
