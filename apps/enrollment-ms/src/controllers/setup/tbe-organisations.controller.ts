/* eslint-disable */
import { Body, Controller, Post, Req, UseInterceptors } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { TbeOrganisationService } from '../../services/setup/tbe-organisation.service';
import { TbeOrganisationDto } from '@app/shared/enrollment-service/dtos/tbe-organisation.dto';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';

@ApiBearerAuth()
@ApiTags('Tbe Organisations Controller')
@Controller('')
@UseInterceptors(AuditInterceptor)
export class TbeOrganisationsController {
  constructor(private readonly tbeOrganisationService: TbeOrganisationService) {}

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.FETCH_TBE_ORGANISATIONS)
  @Post('fetch-organisations')
  @ApiOperation({
    summary: 'Retrieve Organisations',
    description: 'Retrieve Organisations',
  })
  @ApiResponse({
    status: 201,
    description: 'Success',
    schema: {
      example: {
        code: 1,
        description: 'Success',
        content: [
          {
            sectorCode: 'UNIV_PROF',
            employerId: 'PU000032D023_P',
            employerName: 'Nnamdi Azikiwe University, Awka  (Prof)',
            ippisDate: '2020-02-01',
            ecrsEmployerCode: ' ',
            active: 1,
            deleted: 0,
            createDate: '2025-03-06',
            lastModified: '2025-03-06',
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
      },
    },
  })
  async fetchTbeOrganisations(@Body() filter: PaginatedSearchDto) {
    return await this.tbeOrganisationService.fetchTbeOrganisations(filter);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.CREATE_TBE_ORGANISATIONS)
  @Post('create-organisation')
  @ApiOperation({
    summary: 'Create Organisations',
    description: 'Create Organisations',
  })
  @ApiResponse({
    status: 201,
    description: 'Success',
    schema: {
      example: {
        code: 1,
        description: 'Organisation created successfully.',
        content: null,
      },
    },
  })
  async createTbeOrganisations(@Body() tbeOrganisationDto: TbeOrganisationDto, @Req() req: ICustomRequest) {
    return await this.tbeOrganisationService.createTbeOrganisations(tbeOrganisationDto, req);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.EDIT_TBE_ORGANISATIONS)
  @Post('edit-organisation')
  @ApiOperation({
    summary: 'Edit Organisations',
    description: 'Edit Organisations',
  })
  @ApiResponse({
    status: 201,
    description: 'Success',
    schema: {
      example: {
        code: 1,
        description: 'Organisation updated successfully.',
        content: null,
      },
    },
  })
  async editTbeOrganisations(@Body() tbeOrganisationDto: TbeOrganisationDto, @Req() req: ICustomRequest) {
    return await this.tbeOrganisationService.editTbeOrganisations(tbeOrganisationDto, req);
  }
}
