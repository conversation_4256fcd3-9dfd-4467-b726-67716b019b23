import {
  Body,
  Controller,
  Post,
  Req,
  Patch,
  Param,
  UseInterceptors,
  UploadedFile,
  ParseIntPipe,
  Get,
  NestInterceptor,
  Delete,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { MultiplePinResolutionService } from '../services/multiple-pin-resolution.service';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { MultiplePinResolutionTableDto } from '@app/shared/enrollment-service/dtos/multiple-pin-resolution-table.dto';
import { CreateMultiplePinResolutionDto } from '@app/shared/enrollment-service/dtos/create-multiple-pin-resolution.dto';
import {
  BaseResponseListWithContentNoPagination,
  BaseResponseWithNoCountInfo,
} from '@app/shared/dto/response/base-response-with-content';
import { UpdateMultiplePinResolutionDto } from '@app/shared/enrollment-service/dtos/update-multiple-pin-resolution.dto';
import { multerExcelOptions } from '@app/shared/decorators/multer-xlsx.config';
import {
  MultiplePinResolutionPinDto,
  MultiplePinResolutionPinResponseDto,
} from '@app/shared/enrollment-service/dtos/multiple-pin-resolution-pin.dto';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import {
  ConfirmPaymentDto,
  ReconcilePaymentDto,
} from '@app/shared/dto/enrollment/multiple-pin-resolution/confirm-payment.dto';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { Audit } from '@app/shared/decorators/audit.decorator';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';

@ApiBearerAuth()
@ApiTags('Multiple Pin Resolution')
@UseInterceptors(AuditInterceptor)
@Controller('multiple-pin-resolution')
export class MultiplePinResolutionController {
  constructor(private readonly multiplePinResolutionService: MultiplePinResolutionService) {}

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.MULTIPLE_PIN_RESOLUTION_CREATE)
  @Audit(AuditEventTypeEnum.CREATE_MULTIPLE_PIN_RESOLUTION)
  @Post()
  @ApiOperation({ summary: 'Create a multiple pin resolution request' })
  @ApiResponse({
    status: 201,
    description: 'Multiple pin resolution request created successfully',
    type: BaseResponseDto,
  })
  async createRequest(
    @Body() createDto: CreateMultiplePinResolutionDto,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseDto> {
    return this.multiplePinResolutionService.createRequest(createDto, req);
  }

  @RequirePrivileges(CobraPrivileges.MULTIPLE_PIN_RESOLUTION_PAGE)
  @AllowedUserTypes(UserTypeEnum.PENCOM, UserTypeEnum.PFA)
  @Post('get-requests')
  @ApiOperation({ summary: 'Get all multiple pin resolution requests' })
  @ApiResponse({
    status: 200,
    description: 'List of multiple pin resolution requests',
    type: BaseResponseWithNoCountInfo<MultiplePinResolutionTableDto>,
  })
  async getRequests(
    @Body() filter: PaginatedSearchDto,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseWithNoCountInfo<MultiplePinResolutionTableDto>> {
    filter.filters = {
      ...filter.filters,
      pfaCode: req.user.pfaCode,
    };
    return this.multiplePinResolutionService.getRequests(filter);
  }

  @RequirePrivileges(CobraPrivileges.MULTIPLE_PIN_RESOLUTION_PAGE)
  @AllowedUserTypes(UserTypeEnum.PENCOM, UserTypeEnum.PFA)
  @Post('pin/get-requests')
  @ApiOperation({ summary: 'Get all multiple pin resolution requests' })
  @ApiResponse({
    status: 200,
    description: 'List of multiple pin resolution requests',
    type: BaseResponseWithNoCountInfo<MultiplePinResolutionPinDto>,
  })
  async getPinRequests(
    @Body() filter: PaginatedSearchDto,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseWithNoCountInfo<MultiplePinResolutionPinDto>> {
    filter.filters = {
      ...filter.filters,
      pfaCode: req.user.pfaCode,
    };
    return this.multiplePinResolutionService.getPinRequests(filter);
  }

  @Patch()
  @ApiOperation({ summary: 'Update a multiple pin resolution request status' })
  @ApiResponse({
    status: 200,
    description: 'Multiple pin resolution request updated successfully',
    type: BaseResponseDto,
  })
  async updateRequest(
    @Body() updateDto: UpdateMultiplePinResolutionDto,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseDto> {
    return this.multiplePinResolutionService.updateRequest(updateDto, req);
  }

  @Audit(AuditEventTypeEnum.MULTIPLE_PIN_TRANSACTION_HISTORY_UPLOAD, (req, params) => ({
    extra: {
      batchId: params.batchId,
      pinPk: params.pinPk,
    },
  }))
  @RequirePrivileges(CobraPrivileges.MULTIPLE_PIN_TRANSACTION_HISTORY)
  @AllowedUserTypes(UserTypeEnum.PFA)
  @Post(':batchId/pin/:pinPk/transaction-history')
  @ApiOperation({ summary: 'Upload transaction history for a specific PIN' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Excel file to upload',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file', multerExcelOptions) as unknown as NestInterceptor)
  async uploadPinTransactionHistory(
    @Param('batchId') batchId: string,
    @Param('pinPk') pinPk: number,
    @UploadedFile() file: Express.Multer.File,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseDto> {
    return await this.multiplePinResolutionService.uploadPinTransactionHistory(batchId, pinPk, file, req);
  }

  @Get(':batchId/pins')
  @ApiOperation({
    summary: 'Get all PINs with their memo documents and transaction history for a multiple pin resolution request',
  })
  @ApiResponse({
    status: 200,
    description: 'List of PINs with their memo documents and transaction history for the request',
    type: BaseResponseListWithContentNoPagination<MultiplePinResolutionPinDto>,
  })
  async getPinsForRequest(
    @Param('batchId') batchId: string,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseListWithContentNoPagination<MultiplePinResolutionPinResponseDto>> {
    return this.multiplePinResolutionService.getPinsForRequest(batchId, req);
  }

  @RequirePrivileges(CobraPrivileges.MULTIPLE_PIN_GENERATE_DOCUMENT)
  @Audit(AuditEventTypeEnum.MULTIPLE_PIN_MEMO_GENERATION, (req) => ({
    extra: {
      batchId: req.params?.batchId,
    },
  }))
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Post(':batchId/generate-memo')
  @ApiOperation({ summary: 'Generate memos for invalid PINs in an MPR request' })
  @ApiResponse({ status: 200, description: 'Memos generated successfully' })
  async generateInvalidPinMemos(@Param('batchId') batchId: string, @Req() req: ICustomRequest) {
    return await this.multiplePinResolutionService.generateTransactionHistoryMemos(batchId, req);
  }

  @Audit(AuditEventTypeEnum.DELETE_MULTIPLE_PIN_REQUEST_TRANSACTION_HISTORY, (req, params) => ({
    extra: {
      pk: params.pk,
    },
  }))
  @RequirePrivileges(CobraPrivileges.MULTIPLE_PIN_TRANSACTION_HISTORY)
  @Delete('transaction-history/:pk')
  @ApiOperation({ summary: 'Delete a specific transaction history record' })
  @ApiResponse({
    status: 200,
    description: 'Transaction history record deleted successfully',
    type: BaseResponseDto,
  })
  async deleteTransactionHistory(
    @Param('pk', ParseIntPipe) pk: number,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseDto> {
    return this.multiplePinResolutionService.deleteTransactionHistory(pk, req);
  }

  @RequirePrivileges(CobraPrivileges.MULTIPLE_PIN_CONFIRM_PAYMENT)
  @Audit(AuditEventTypeEnum.MULTIPLE_PIN_PAYMENT_CONFIRMATION_PENCOM, (req) => ({
    extra: {
      batchId: req.params?.batchId,
      pin: req.body.pin,
    },
  }))
  @AllowedUserTypes(UserTypeEnum.PFA)
  @Patch(':batchId/confirm-payment')
  @ApiOperation({ summary: 'Confirm payment for an invalid PIN in a multiple pin resolution request' })
  @ApiResponse({
    status: 200,
    description: 'Payment confirmed successfully',
    type: BaseResponseDto,
  })
  async confirmPayment(
    @Param('batchId') batchId: string,
    @Body() confirmPaymentDto: ConfirmPaymentDto,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseDto> {
    return this.multiplePinResolutionService.confirmPayment(batchId, confirmPaymentDto, req);
  }

  @RequirePrivileges(CobraPrivileges.MULTIPLE_PIN_RESOLUTION_CONFIRM_PAYMENT)
  @Audit(AuditEventTypeEnum.MULTIPLE_PIN_PAYMENT_CONFIRMATION_PENCOM, (req) => ({
    extra: {
      pk: req.params?.pk,
    },
  }))
  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Patch('/confirm-pencom-payment')
  @ApiOperation({ summary: 'Confirm PENCOM payment verification for a PIN in a multiple pin resolution request' })
  @ApiResponse({
    status: 200,
    description: 'Payment verification confirmed successfully',
    type: BaseResponseDto,
  })
  async confirmPencomPayment(
    @Body() confirmPaymentDto: ReconcilePaymentDto,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseDto> {
    return this.multiplePinResolutionService.confirmPencomPayment(confirmPaymentDto, req);
  }
}
