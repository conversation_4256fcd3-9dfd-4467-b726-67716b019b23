import {
  BadRequestException,
  Body,
  Controller,
  HttpException,
  HttpStatus,
  NestInterceptor,
  Post,
  Req,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { BatchingService } from '../services/batching.service';
import {
  BatchDocumentDto,
  BatchFilterDto,
  BatchMemoGenerationDto,
  StampScheduleDto,
} from '@app/shared/dto/enrollment/exit-batching/batch-filter.dto';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { MemoService } from '@app/shared/enrollment-service/services/memo.service';
import { Response } from 'express';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { ResponseCodeEnum } from '@app/shared/enums';
import { BatchFileUploadRequestDto } from '@app/shared/dto/request/file-upload-request.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { multerPdfOptions } from '@app/shared/decorators/multer-pdf.config';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { Audit } from '@app/shared/decorators/audit.decorator';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';

@UseInterceptors(AuditInterceptor)
@Controller('/batch')
export class BatchController {
  constructor(
    private readonly batchingService: BatchingService,
    private readonly memoService: MemoService
  ) {}

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Post('apply-exit-filters')
  async applyFilters(@Body() filterDto: BatchFilterDto) {
    return this.batchingService.applyBatchFilters(filterDto);
  }

  @Post('retrieve-batch-documents')
  async retrieveBatchDocument(@Body() batchDocumentDto: BatchDocumentDto) {
    return this.batchingService.retrieveBatchDocuments(batchDocumentDto);
  }

  @RequirePrivileges(CobraPrivileges.BATCH_DOCUMENT_UPLOAD)
  @Audit(AuditEventTypeEnum.BATCH_DOCUMENT_UPLOAD)
  @Post('upload-batch-document')
  @UseInterceptors(FileInterceptor('file', multerPdfOptions) as unknown as NestInterceptor)
  async uploadBatchDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() batchFileUploadRequestDto: BatchFileUploadRequestDto
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }
    return this.batchingService.uploadBatchDocument(batchFileUploadRequestDto, file);
  }

  @RequirePrivileges(CobraPrivileges.BATCH_RECORDS_PAGE)
  @Post('retrieve-batch-details')
  async retrieveBatch(@Body() filterDto: PaginatedSearchDto) {
    return this.batchingService.retrieveBatch(filterDto);
  }

  @RequirePrivileges(CobraPrivileges.BATCH_STAMP_SCHEDULE)
  @Audit(AuditEventTypeEnum.BATCH_STAMP_SCHEDULE)
  @Post('stamp-schedule')
  async stampSchedule(@Body() batchData: BatchMemoGenerationDto, @Req() req: ICustomRequest, @Res() res: Response) {
    try {
      // Generate the PDF
      const stampScheduleDto = new StampScheduleDto();
      stampScheduleDto.batchName = batchData.batchName;
      stampScheduleDto.actorEmailAddress = req.user.email;
      const response = await this.memoService.stampDocument(stampScheduleDto);

      const { schedule, excoMemo } = response.content as { schedule: Buffer; excoMemo: Buffer };
      if (
        response.code !== ResponseCodeEnum.SUCCESS ||
        !schedule ||
        !schedule.length ||
        !excoMemo ||
        !excoMemo.length
      ) {
        return res.status(404).send(response.description);
      }

      res.json({
        schedule: schedule.toString('base64'),
        excoMemo: excoMemo.toString('base64'),
        description: response.description,
      });
    } catch (error) {
      throw new HttpException(`Failed to generate document: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @RequirePrivileges(CobraPrivileges.BATCH_MEMO_GENERATION)
  @Audit(AuditEventTypeEnum.BATCH_MEMO_GENERATION)
  @Post('generate-batch-memo')
  async generateDocument(@Body() batchData: BatchMemoGenerationDto, @Req() req: ICustomRequest, @Res() res: Response) {
    try {
      // Generate the PDF
      const response = await this.memoService.generateExcoMemoPdf(batchData, req);
      const { schedule, excoMemo, appendix } = response.content as {
        schedule: Buffer;
        excoMemo: Buffer;
        appendix?: Buffer;
      };
      if (
        response.code !== ResponseCodeEnum.SUCCESS ||
        !schedule ||
        !schedule.length ||
        !excoMemo ||
        !excoMemo.length
      ) {
        return res.status(404).send(response.description);
      }

      res.json({
        schedule: schedule.toString('base64'),
        excoMemo: excoMemo.toString('base64'),
        appendix: appendix?.toString('base64'),
        description: response.description,
      });
    } catch (error) {
      throw new HttpException(`Failed to generate document: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Post('generate-batch-pfa-memo')
  async generateCrBatchPfaDocument(
    @Body() batchData: BatchDocumentDto,
    @Req() req: ICustomRequest,
    @Res() res: Response
  ) {
    try {
      const response = await this.memoService.generateExitBatchPfaDocument(batchData, req);
      res.json({
        content: response.content,
        description: response.description,
      });
    } catch (error) {
      throw new HttpException(`Failed to generate document: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('retrieve-batch-payment-documents')
  async retrieveBatchPaymentDocuments(@Body() batchDocumentDto: BatchDocumentDto) {
    return this.batchingService.retrieveBatchPaymentDocuments(batchDocumentDto);
  }

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @RequirePrivileges(CobraPrivileges.BATCH_ACCOUNT_MEMO_GENERATION)
  @Audit(AuditEventTypeEnum.BATCH_ACCOUNT_MEMO_GENERATION)
  @Post('generate-account-memo')
  async generateAccountDocument(@Body() batchData: BatchDocumentDto, @Req() req: ICustomRequest, @Res() res: Response) {
    try {
      // Generate the PDF
      const response = await this.memoService.generateAccountMemoPdf(batchData, req);
      if (!response || response.code !== ResponseCodeEnum.SUCCESS || !response.content) {
        return res.status(404).send(response.description);
      }

      const { accountMemo } = response.content as { accountMemo: Buffer };
      if (!accountMemo || !accountMemo.length) {
        return res.status(404).send(response.description);
      }

      res.json({
        accountMemo: accountMemo.toString('base64'),
        description: response.description,
      });
    } catch (error) {
      throw new HttpException(`Failed to generate document: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
