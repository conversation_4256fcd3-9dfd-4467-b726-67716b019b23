import { Body, Controller, Post, Req, UseInterceptors } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { LegacyRecordService } from '../services/legacy-record.service';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';
import {
  BaseResponseListWithContentNoPagination,
  BaseResponseWithContent,
  BaseResponseWithNoCountInfo,
} from '@app/shared/dto/response/base-response-with-content';
import { LegacyRecordDocumentDto, LegacyRecordSearchDto } from '@app/shared/dto/enrollment/legacy-record-search.dto';
import { LegacyRecordDto } from '@app/shared/enrollment-service/dtos/legacy-record.dto';

@ApiBearerAuth()
@ApiTags('Legacy Records')
@UseInterceptors(AuditInterceptor)
@Controller('legacy-records')
export class LegacyRecordController {
  constructor(private readonly legacyRecordService: LegacyRecordService) {}

  @RequirePrivileges(CobraPrivileges.LEGACY_RECORD_PAGE)
  @Post('get-records')
  @ApiOperation({ summary: 'Get legacy records with pagination and filtering' })
  @ApiResponse({
    status: 200,
    description: 'Returns legacy records based on search criteria',
    type: BaseResponseWithContent,
  })
  async getLegacyRecords(
    @Body() searchDto: LegacyRecordSearchDto
  ): Promise<BaseResponseWithNoCountInfo<LegacyRecordDto>> {
    return this.legacyRecordService.getLegacyRecords(searchDto);
  }

  @RequirePrivileges(CobraPrivileges.LEGACY_RECORD_PAGE)
  @Post('get-document')
  @ApiOperation({ summary: 'Get document by legacy record ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns the document associated with the legacy record',
    type: BaseResponseListWithContentNoPagination,
  })
  async getLegacyRecordDocument(
    @Body() documentDto: LegacyRecordDocumentDto,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseListWithContentNoPagination<any>> {
    return this.legacyRecordService.getLegacyRecordDocument(documentDto.legacyRecordId);
  }
}
