import { Body, Controller, Post, Req, Patch, UseInterceptors } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { BaseResponseDto } from '@app/shared/dto/response/base-response.dto';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import {
  BaseResponseWithContentNoPagination,
  BaseResponseWithNoCountInfo,
} from '@app/shared/dto/response/base-response-with-content';
import { AmendmentRequestTableDto } from '@app/shared/enrollment-service/dtos/amendment-request-table.dto';
import { AmendmentRequestService } from 'apps/enrollment-ms/src/services/amendment-request.service';
import { CreateAmendmentRequestDto } from '@app/shared/enrollment-service/dtos/create-amendment-request.dto';
import { UpdateAmendmentRequestDto } from '@app/shared/enrollment-service/dtos/update-amendment-request.dto';
import { ValidateAmendmentPinDto } from '@app/shared/enrollment-service/dtos/validate-amendment-pin.dto';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { Audit } from '@app/shared/decorators/audit.decorator';
import { AuditEventTypeEnum } from '@app/shared/enums/AuditEventTypeEnum';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { AuditInterceptor } from '@app/shared/audit-service/interceptors/audit.interceptor';

@UseInterceptors(AuditInterceptor)
@ApiBearerAuth()
@ApiTags('Amendment Requests')
@Controller('amendment-requests')
export class AmendmentRequestController {
  constructor(private readonly amendmentRequestService: AmendmentRequestService) {}

  @Audit(AuditEventTypeEnum.CREATE_AMENDMENT_REQUEST)
  @RequirePrivileges(CobraPrivileges.AMENDMENT_REQUEST_CREATION)
  @AllowedUserTypes(UserTypeEnum.PFA)
  @Post()
  @ApiOperation({ summary: 'Create an amendment request' })
  @ApiResponse({
    status: 201,
    description: 'Amendment request created successfully',
    type: BaseResponseDto,
  })
  async createRequest(
    @Body() createDto: CreateAmendmentRequestDto,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseDto> {
    return this.amendmentRequestService.createRequest(createDto, req);
  }

  @RequirePrivileges(CobraPrivileges.AMENDMENT_REQUEST_PAGE)
  @AllowedUserTypes(UserTypeEnum.PENCOM, UserTypeEnum.PFA)
  @Post('get-requests')
  @ApiOperation({ summary: 'Get all amendment requests' })
  @ApiResponse({
    status: 200,
    description: 'List of amendment requests',
    type: BaseResponseWithNoCountInfo<AmendmentRequestTableDto>,
  })
  async getRequests(
    @Body() filter: PaginatedSearchDto
  ): Promise<BaseResponseWithNoCountInfo<AmendmentRequestTableDto>> {
    return await this.amendmentRequestService.getRequests(filter);
  }

  @Audit(AuditEventTypeEnum.UPDATE_AMENDMENT_REQUEST, (req) => {
    return {
      extra: {
        pk: req.body.pk,
        status: req.body.status,
      },
    };
  })
  @RequirePrivileges()
  @AllowedUserTypes(UserTypeEnum.PENCOM, UserTypeEnum.PFA)
  @Patch('status')
  @ApiOperation({ summary: 'Update amendment request status' })
  @ApiResponse({
    status: 200,
    description: 'Amendment request updated successfully',
    type: BaseResponseDto,
  })
  async updateRequest(
    @Body() updateDto: UpdateAmendmentRequestDto,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseDto> {
    return await this.amendmentRequestService.updateRequest(updateDto, req);
  }

  @Post('validate-pin')
  @ApiOperation({ summary: 'Validate if RSA PIN is eligible for amendment request' })
  @ApiResponse({
    status: 200,
    description: 'RSA PIN validation result',
    type: BaseResponseDto,
  })
  async validatePin(@Body() validateDto: ValidateAmendmentPinDto): Promise<BaseResponseWithContentNoPagination<any>> {
    return await this.amendmentRequestService.validatePin(validateDto);
  }
}
