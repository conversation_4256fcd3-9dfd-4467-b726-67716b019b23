import {
  BadRequestException,
  Body,
  Controller,
  HttpException,
  HttpStatus,
  NestInterceptor,
  Post,
  Req,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { NominalRollEnrollmentService } from '../services/nominal-roll-enrollment.service';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { ProcessNominalRollDto } from '@app/shared/dto/enrollment/process-nominal-roll.dto';
import { ICustomRequest } from '@app/shared/utils/custom-request-type';
import { NominalRollContributionFilterDto } from '@app/shared/dto/enrollment/nominal-roll-contribution-filter-dto';
import { PaginatedSearchDto } from '@app/shared/dto/paginated-search.dto';
import { AppendUserFilters } from '@app/shared/interceptors/user-filters.interceptor';
import { NominalRollDetailSearchDto } from '@app/shared/dto/enrollment/nominalroll/nominal-roll-employee.dto';
import { BatchDocumentDto, BatchMemoGenerationDto } from '@app/shared/dto/enrollment/exit-batching/batch-filter.dto';
import { Response } from 'express';
import { ResponseCodeEnum } from '@app/shared/enums';
import { MemoService } from '@app/shared/enrollment-service/services/memo.service';
import { RequirePrivileges } from '@app/shared/decorators/require-privileges.decorator';
import { CobraPrivileges } from '@app/shared/user-service/entities/enums/cobra-privilege-enum';
import { FileInterceptor } from '@nestjs/platform-express';
import { multerPdfOptions } from '@app/shared/decorators/multer-pdf.config';
import {
  BatchFileUploadRequestDto,
  BatchPaymentConfirmationUploadRequestDto,
} from '@app/shared/dto/request/file-upload-request.dto';
import { multerExcelOptions } from '@app/shared/decorators/multer-xlsx.config';

@Controller('/nominal-roll')
export class NominalRollController {
  constructor(
    private readonly nominalRollEnrollmentService: NominalRollEnrollmentService,
    private readonly memoService: MemoService
  ) {}

  @Post('apply-computation-filter')
  async applyFilters(@Body() filterDto: NominalRollContributionFilterDto) {
    return this.nominalRollEnrollmentService.applyComputationFilters(filterDto);
  }

  @Post('apply-contribution-batch-filters')
  async applyBatchFilters(@Body() filterDto: NominalRollContributionFilterDto) {
    return this.nominalRollEnrollmentService.applyBatchFilters(filterDto);
  }

  @RequirePrivileges(CobraPrivileges.NOMINAL_ROLL_PAGE)
  @Post('retrieve-roll-records')
  async retrieveLNominalRollList(@Body() query: PaginatedSearchDto, @AppendUserFilters() filters: any) {
    return await this.nominalRollEnrollmentService.retrieveLNominalRollList(query);
  }

  @Post('retrieve-roll-detail')
  async retrieveLNominalRollDetail(@Body() nominalRollSearchDto: NominalRollDetailSearchDto) {
    return await this.nominalRollEnrollmentService.retrieveLNominalRollDetail(nominalRollSearchDto);
  }

  @Post('retrieve-roll-history')
  async retrieveLNominalRollHistory(@Body() nominalRollSearchDto: NominalRollDetailSearchDto) {
    return await this.nominalRollEnrollmentService.retrieveNominalRollHistory(nominalRollSearchDto);
  }

  @Post('process-request')
  async processRequest(
    @Body() processNominalRollDto: ProcessNominalRollDto,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    return this.nominalRollEnrollmentService.processRequest(processNominalRollDto, req);
  }

  @Post('retrieve-batch-details')
  async retrieveBatch(@Body() filterDto: PaginatedSearchDto) {
    return this.nominalRollEnrollmentService.retrieveBatch(filterDto);
  }

  @Post('generate-cr-account-memo')
  async generateCrAccountDocument(
    @Body() batchData: BatchDocumentDto,
    @Req() req: ICustomRequest,
    @Res() res: Response
  ) {
    try {
      const response = await this.memoService.generateCrAccountMemoPdf(batchData, req);
      if (response.code !== ResponseCodeEnum.SUCCESS || !response.content) {
        return res.status(404).send(response.description);
      }

      const { accountMemo } = response.content as { accountMemo: Buffer };
      if (!accountMemo || !accountMemo.length) {
        return res.status(404).send(response.description);
      }

      res.json({
        accountMemo: accountMemo.toString('base64'),
        description: response.description,
      });
    } catch (error) {
      throw new HttpException(`Failed to generate document: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('retrieve-batch-documents')
  async retrieveBatchDocument(@Body() batchDocumentDto: BatchDocumentDto) {
    return this.nominalRollEnrollmentService.retrieveBatchDocuments(batchDocumentDto);
  }

  @Post('retrieve-batch-payment-documents')
  async retrieveBatchPaymentDocuments(@Body() batchDocumentDto: BatchDocumentDto) {
    return this.nominalRollEnrollmentService.retrieveBatchPaymentDocuments(batchDocumentDto);
  }

  @Post('retrieve-pfa-batch-payment-documents')
  async retrievePfaBatchPaymentDocuments(@Body() batchDocumentDto: BatchDocumentDto, @Req() req: ICustomRequest) {
    return this.nominalRollEnrollmentService.retrievePfaBatchPaymentDocuments(batchDocumentDto, req);
  }

  @Post('upload-payment-document')
  @UseInterceptors(FileInterceptor('file', multerPdfOptions) as unknown as NestInterceptor)
  async uploadBatchDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() batchFileUploadRequestDto: BatchFileUploadRequestDto
  ) {
    if (!file) {
      throw new BadRequestException('No file attached to upload, please check and try again.');
    }
    return this.nominalRollEnrollmentService.uploadBatchDocument(batchFileUploadRequestDto, file);
  }

  @Post('generate-cr-batch-memo')
  async generateCrBatchDocument(
    @Body() batchData: BatchMemoGenerationDto,
    @Req() req: ICustomRequest,
    @Res() res: Response
  ) {
    try {
      const response = await this.memoService.generateCrExcoMemoPdf(batchData, req);
      const { schedule, excoMemo, appendix } = response.content as {
        schedule: Buffer;
        excoMemo: Buffer;
        appendix: Buffer;
      };
      if (
        response.code !== ResponseCodeEnum.SUCCESS ||
        !schedule ||
        !schedule.length ||
        !excoMemo ||
        !excoMemo.length ||
        !appendix ||
        !appendix.length
      ) {
        return res.status(404).send(response.description);
      }

      res.json({
        schedule: schedule.toString('base64'),
        excoMemo: excoMemo.toString('base64'),
        appendix: appendix.toString('base64'),
        description: response.description,
      });
    } catch (error) {
      throw new HttpException(`Failed to generate document: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('generate-cr-batch-pfa-memo')
  async generateCrBatchPfaDocument(
    @Body() batchData: BatchDocumentDto,
    @Req() req: ICustomRequest,
    @Res() res: Response
  ) {
    try {
      const response = await this.memoService.generateNrBatchPfaDocument(batchData, req);

      res.json({
        content: response.content,
        description: response.description,
      });
    } catch (error) {
      throw new HttpException(`Failed to generate document: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('/payment-confirmation-excel-upload')
  @UseInterceptors(FileInterceptor('file', multerExcelOptions) as unknown as NestInterceptor)
  async handlePfaPaymentConfirmation(
    @UploadedFile() file: Express.Multer.File,
    @Body() batchPaymentConfirmationUploadRequestDto: BatchPaymentConfirmationUploadRequestDto,
    @Req() req: ICustomRequest
  ): Promise<BaseResponseWithContentNoPagination<unknown>> {
    return this.nominalRollEnrollmentService.handlePfaPaymentConfirmation(
      file,
      batchPaymentConfirmationUploadRequestDto,
      req
    );
  }
}
