/* eslint-disable */
import { Controller, Get, Query, Res } from '@nestjs/common';
import { Public } from '@app/shared/decorators/public.decorator';
import { Response } from 'express';
import { SlipService } from '@app/shared/enrollment-service/services/slip.service';

@Controller('/slip')
export class SlipController {
  constructor(private readonly slipService: SlipService) {}

  @Public()
  @Get('download')
  async generatePdf(@Query('rsaPin') rsaPin: string, @Query('slipType') slipType: string, @Res() res: Response) {
    try {
      if (!rsaPin) {
        res.status(404).send('Please provide valid RSA Pin');
        return;
      }

      if (!slipType) {
        res.status(404).send('Please provide valid Slip Type');
        return;
      }
      const pdfBuffer = await this.slipService.retrieveSlipDetails(rsaPin, slipType);

      if (!pdfBuffer || pdfBuffer.length === 0) {
        res.status(404).send('No slip found for provided RSA Pin');
        return;
      }

      res.set({
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="document.pdf"',
        'Content-Length': pdfBuffer.length,
      });

      res.end(pdfBuffer);
    } catch (error) {
      console.error('Error generating PDF:', error);
      res.status(500).send('Error generating PDF');
    }
  }
}
