import { Body, Controller, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { BaseResponseWithContentNoPagination } from '@app/shared/dto/response/base-response-with-content';
import { RsaPinValidationService } from '../services/rsapin-validation.service';
import { EcrsUserResponseDto } from '@app/shared/dto/ecrs/ecrs-user.dto';
import { AllowedUserTypes } from '@app/shared/decorators/allowed-user-types.decorator';
import { UserTypeEnum } from '@app/shared/enums/UserTypeEnum';
import { RetrieveUserWithPinDto } from '@app/shared/dto/ecrs/retrieve-user.dto';

@ApiBearerAuth()
@ApiTags('RSA PIN Validation')
@Controller('rsapin-validation')
export class RsaPinValidationController {
  constructor(private readonly rsaPinValidationService: RsaPinValidationService) {}

  @AllowedUserTypes(UserTypeEnum.PENCOM)
  @Post('validate')
  @ApiOperation({ summary: 'Validate if RSA PIN exists in the system' })
  @ApiResponse({
    status: 200,
    description: 'RSA PIN validation result',
    type: BaseResponseWithContentNoPagination,
  })
  async validateRsaPin(@Body() validateDto: RetrieveUserWithPinDto): Promise<EcrsUserResponseDto> {
    return await this.rsaPinValidationService.validateRsaPin(validateDto);
  }
}
