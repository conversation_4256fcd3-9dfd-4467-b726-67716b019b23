import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { PinoLogger } from 'nestjs-pino';
import { InactiveUsersService } from './services/inactive-users.service';
import { ConfigService } from '@nestjs/config';
import { CronJob } from 'cron';
import { CronJobNamesConstant } from '@app/shared/constants/cron.constants';
import { EnrolmentDistributionService } from '@app/shared/enrollment-service/services/enrolment-distribution.service';
import { CronJobLogRepository } from './repositories/cron-job-log-repository';
import { CronJobLog } from './entities/cron-job-log.entity';
import { v4 as uuidv4 } from 'uuid';
import { ExitArContributionCalculationService } from './services/exit-ar-contribution-calculation.service';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { NominalRollContributionCalculationService } from './services/nominal-roll-contribution-calculation.service';
import { MultiplePinCalculationService } from './services/multiple-pin-calculation.service';
import { NonConfirmationEscalationService } from './services/non-confirmation-escalation.service';

@Injectable()
export class CronMsService implements OnModuleInit {
  constructor(
    private readonly logger: PinoLogger,
    private readonly inactiveUsersService: InactiveUsersService,
    private readonly enrolmentDistributionService: EnrolmentDistributionService,
    private readonly exitArContributionCalculationService: ExitArContributionCalculationService,
    private readonly nominalRollContributionCalculationService: NominalRollContributionCalculationService,
    private readonly nonConfirmationEscalationService: NonConfirmationEscalationService,
    private readonly configService: ConfigService,
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly cronJobLogRepository: CronJobLogRepository,
    private redisService: RedisCacheService,
    private readonly multiplePinCalculationService: MultiplePinCalculationService
  ) {}

  async onModuleInit() {
    const cronExpression = this.configService.get<string>('VALIDATOR_JOB_CRON_EXPRESSION', '*/10 * * * *');
    await this.initializeValidatorAssignmentJob(CronJobNamesConstant.VALIDATOR_ASSIGNMENT_JOB, cronExpression);

    const auditorCronExpression = this.configService.get<string>('AUDITOR_JOB_CRON_EXPRESSION', '*/10 * * * *');
    await this.initializeAuditorAssignmentJob(CronJobNamesConstant.AUDITOR_ASSIGNMENT_JOB, auditorCronExpression);

    const arContributionCronExpression = this.configService.get<string>(
      CronJobNamesConstant.EXIT_ACCRUED_RIGHTS_CONTRIBUTION_JOB,
      '*/1 * * * *'
    );
    await this.initializeExitAccruedRightsContributionJob(
      CronJobNamesConstant.EXIT_ACCRUED_RIGHTS_CONTRIBUTION_JOB,
      arContributionCronExpression
    );

    const nrContributionCronExpression = this.configService.get<string>(
      CronJobNamesConstant.NOMINAL_ROLL_CONTRIBUTION_JOB,
      '*/1 * * * *'
    );
    await this.initializeNominalRollContributionJob(
      CronJobNamesConstant.NOMINAL_ROLL_CONTRIBUTION_JOB,
      nrContributionCronExpression
    );

    const multiplePinCronExpression = this.configService.get<string>(
      CronJobNamesConstant.MULTIPLE_PIN_CALCULATION_JOB,
      '*/1 * * * *'
    );
    await this.initializeMultiplePinCalculationJob(
      CronJobNamesConstant.MULTIPLE_PIN_CALCULATION_JOB,
      multiplePinCronExpression
    );
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async checkInactiveUsers() {
    await this.initializeCheckInactiveUsers(
      CronJobNamesConstant.INACTIVE_USER_DEACTIVATION_JOB,
      CronExpression.EVERY_DAY_AT_MIDNIGHT
    );
  }

  async initializeCheckInactiveUsers(jobName: string, cronExpression: string) {
    this.logger.debug(`initializeCheckInactiveUsers cron job with schedule: ${cronExpression}`);
    const lock = await this.redisService.setNx(jobName, 'running');
    if (!lock) {
      this.logger.warn('Skipping initializeCheckInactiveUsers as previous instance is still running');
      await this.cronJobLogRepository.saveEntity(
        new CronJobLog({
          jobName,
          status: 'SKIPPED',
          jobId: uuidv4(),
          details: 'Skipping initializeCheckInactiveUsers as previous instance is still running',
        })
      );
      return;
    }
    this.logger.info('Starting daily inactive users check');
    const job = new CronJob(cronExpression, async () => {
      const cronJobLog = await this.cronJobLogRepository.saveEntity(
        new CronJobLog({
          jobName,
          status: 'IN_PROGRESS',
          jobId: uuidv4(),
        })
      );

      try {
        const result = await this.inactiveUsersService.deactivateInactiveUsers();

        cronJobLog.details = JSON.stringify(result);
        cronJobLog.status = 'SUCCESS';
        this.logger.info('Completed daily inactive users check');
      } catch (error) {
        console.log(error);
        this.logger.error(
          `Failed to complete inactive users check: \n error: ${error instanceof Error ? error.stack : error.message}`
        );

        cronJobLog.details = error instanceof Error ? error.message.split('\n')[0] : String(error);
        cronJobLog.status = 'FAILED';
      } finally {
        await this.redisService.del(jobName); // Release lock
      }

      cronJobLog.nextRun = this.calculateNextRun(CronExpression.EVERY_DAY_AT_MIDNIGHT);
      await this.cronJobLogRepository.saveEntity(cronJobLog);
    });

    this.schedulerRegistry.addCronJob(jobName, job);
    job.start();
  }

  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async checkNonConfirmationEscalation() {
    await this.initializeNonConfirmationEscalationJob(
      CronJobNamesConstant.PFA_NON_CONFIRMATION_ESCALATION_JOB,
      CronExpression.EVERY_DAY_AT_9AM
    );
  }

  /**
   * Calculates the next run date for a given cron expression.
   */
  private calculateNextRun(cronExpression: string): Date {
    return new CronJob(cronExpression, () => {}).nextDate().toJSDate();
  }

  private async initializeValidatorAssignmentJob(jobName: string, cronExpression: string) {
    this.logger.debug(`Initializing ValidatorAssignmentJob with schedule: ${cronExpression}`);
    const lock = await this.redisService.setNx(jobName, 'running');
    if (!lock) {
      this.logger.warn('Skipping ValidatorAssignmentJob as previous instance is still running');
      await this.cronJobLogRepository.saveEntity(
        new CronJobLog({
          jobName,
          status: 'SKIPPED',
          jobId: uuidv4(),
          details: 'Skipping ValidatorAssignmentJob as previous instance is still running',
        })
      );
      return;
    }

    const job = new CronJob(cronExpression, async () => {
      this.logger.debug('Running initializeValidatorAssignmentJob');
      const cronJobLog = await this.cronJobLogRepository.saveEntity(
        new CronJobLog({
          jobName,
          status: 'IN_PROGRESS',
          jobId: uuidv4(),
        })
      );
      try {
        const result = await this.enrolmentDistributionService.distributeEnrolments();
        cronJobLog.details = JSON.stringify(result);
        cronJobLog.status = 'SUCCESS';
        this.logger.info(`Completed initializeValidatorAssignmentJob, Next Run at: ${job.nextDate()}`);
      } catch (error) {
        this.logger.error(
          `'Failed to complete validator assignment: \n error: ${error instanceof Error ? error.stack : error.message} `
        );
        cronJobLog.details = error instanceof Error ? error.message.split('\n')[0] : String(error);
        cronJobLog.status = 'FAILED';
      } finally {
        await this.redisService.del(jobName); // Release lock
      }
      cronJobLog.nextRun = job.nextDate().toJSDate();
      await this.cronJobLogRepository.saveEntity(cronJobLog);
    });

    this.schedulerRegistry.addCronJob(jobName, job);
    job.start();
  }

  private async initializeExitAccruedRightsContributionJob(jobName: string, cronExpression: string) {
    this.logger.debug(`initializeExitAccruedRightsContributionJob cron job with schedule: ${cronExpression}`);
    const lock = await this.redisService.setNx(jobName, 'running');
    if (!lock) {
      this.logger.warn('Skipping initializeExitAccruedRightsContributionJob as previous instance is still running');
      await this.cronJobLogRepository.saveEntity(
        new CronJobLog({
          jobName,
          status: 'SKIPPED',
          jobId: uuidv4(),
          details: 'Skipping initializeExitAccruedRightsContributionJob as previous instance is still running',
        })
      );
      return;
    }

    const job = new CronJob(cronExpression, async () => {
      this.logger.debug('Running initializeExitAccruedRightsContributionJob');
      const cronJobLog = await this.cronJobLogRepository.saveEntity(
        new CronJobLog({
          jobName,
          status: 'IN_PROGRESS',
          jobId: uuidv4(),
        })
      );

      try {
        const result = await this.exitArContributionCalculationService.handleExitArContributionCalculation();
        cronJobLog.details = JSON.stringify(result);
        cronJobLog.status = 'SUCCESS';
        this.logger.info(`Completed initializeExitAccruedRightsContributionJob, Next Run at: ${job.nextDate()}`);
      } catch (error) {
        this.logger.error(
          `Failed to complete initializeExitAccruedRightsContributionJob: \n error: ${error instanceof Error ? error.stack : error.message} `
        );
        cronJobLog.details = error instanceof Error ? error.message.split('\n')[0] : String(error);
        cronJobLog.status = 'FAILED';
      } finally {
        await this.redisService.del(jobName); // Release lock
      }
      cronJobLog.nextRun = job.nextDate().toJSDate();
      await this.cronJobLogRepository.saveEntity(cronJobLog);
    });

    this.schedulerRegistry.addCronJob(jobName, job);
    job.start();
  }

  private async initializeNominalRollContributionJob(jobName: string, cronExpression: string) {
    this.logger.debug(`initializeNominalRollContributionJob cron job with schedule: ${cronExpression}`);
    const lock = await this.redisService.setNx(jobName, 'running');
    if (!lock) {
      this.logger.warn('Skipping initializeNominalRollContributionJob as previous instance is still running');
      await this.cronJobLogRepository.saveEntity(
        new CronJobLog({
          jobName,
          status: 'SKIPPED',
          jobId: uuidv4(),
          details: 'Skipping initializeNominalRollContributionJob as previous instance is still running',
        })
      );
      return;
    }

    const job = new CronJob(cronExpression, async () => {
      this.logger.debug('Running initializeNominalRollContributionJob');
      const cronJobLog = await this.cronJobLogRepository.saveEntity(
        new CronJobLog({
          jobName,
          status: 'IN_PROGRESS',
          jobId: uuidv4(),
        })
      );

      try {
        const result = await this.nominalRollContributionCalculationService.handleNominalRollContributionCalculation();
        cronJobLog.details = JSON.stringify(result);
        cronJobLog.status = 'SUCCESS';
        this.logger.info(`Completed initializeNominalRollContributionJob, Next Run at: ${job.nextDate()}`);
      } catch (error) {
        this.logger.error(
          `Failed to complete initializeNominalRollContributionJob: \n error: ${error instanceof Error ? error.stack : error.message} `
        );
        cronJobLog.details = error instanceof Error ? error.message.split('\n')[0] : String(error);
        cronJobLog.status = 'FAILED';
      } finally {
        await this.redisService.del(jobName); // Release lock
      }
      cronJobLog.nextRun = job.nextDate().toJSDate();
      await this.cronJobLogRepository.saveEntity(cronJobLog);
    });

    this.schedulerRegistry.addCronJob(jobName, job);
    job.start();
  }

  private async initializeNonConfirmationEscalationJob(jobName: string, cronExpression: string) {
    this.logger.debug(`initializeNonConfirmationEscalationJob cron job with schedule: ${cronExpression}`);
    const lock = await this.redisService.setNx(jobName, 'running');
    if (!lock) {
      this.logger.warn('Skipping initializeNonConfirmationEscalationJob as previous instance is still running');
      await this.cronJobLogRepository.saveEntity(
        new CronJobLog({
          jobName,
          status: 'SKIPPED',
          jobId: uuidv4(),
          details: 'Skipping initializeNonConfirmationEscalationJob as previous instance is still running',
        })
      );
      return;
    }

    const job = new CronJob(cronExpression, async () => {
      this.logger.debug('Running initializeNonConfirmationEscalationJob');
      const cronJobLog = await this.cronJobLogRepository.saveEntity(
        new CronJobLog({
          jobName,
          status: 'IN_PROGRESS',
          jobId: uuidv4(),
        })
      );

      try {
        const result = await this.nonConfirmationEscalationService.handleNonConfirmationEscaltion();
        cronJobLog.details = JSON.stringify(result);
        cronJobLog.status = 'SUCCESS';
        this.logger.info(`Completed initializeNonConfirmationEscalationJob, Next Run at: ${job.nextDate()}`);
      } catch (error) {
        this.logger.error(
          `Failed to complete initializeNonConfirmationEscalationJob: \n error: ${error instanceof Error ? error.stack : error.message} `
        );
        cronJobLog.details = error instanceof Error ? error.message.split('\n')[0] : String(error);
        cronJobLog.status = 'FAILED';
      } finally {
        await this.redisService.del(jobName); // Release lock
      }
      cronJobLog.nextRun = job.nextDate().toJSDate();
      await this.cronJobLogRepository.saveEntity(cronJobLog);
    });

    this.schedulerRegistry.addCronJob(jobName, job);
    job.start();
  }

  private async initializeMultiplePinCalculationJob(jobName: string, cronExpression: string) {
    this.logger.debug(`initializeMultiplePinCalculationJob cron job with schedule: ${cronExpression}`);
    const lock = await this.redisService.setNx(jobName, 'running');
    if (!lock) {
      this.logger.warn('Skipping initializeMultiplePinCalculationJob as previous instance is still running');
      await this.cronJobLogRepository.saveEntity(
        new CronJobLog({
          jobName,
          status: 'SKIPPED',
          jobId: uuidv4(),
          details: 'Skipping initializeMultiplePinCalculationJob as previous instance is still running',
        })
      );
      return;
    }

    const job = new CronJob(cronExpression, async () => {
      this.logger.debug('Running initializeMultiplePinCalculationJob');
      const cronJobLog = await this.cronJobLogRepository.saveEntity(
        new CronJobLog({
          jobName,
          status: 'IN_PROGRESS',
          jobId: uuidv4(),
        })
      );

      try {
        const result = await this.multiplePinCalculationService.handleMultiplePinCalculation();
        cronJobLog.details = JSON.stringify(result);
        cronJobLog.status = 'SUCCESS';
        this.logger.info(`Completed initializeMultiplePinCalculationJob, Next Run at: ${job.nextDate()}`);
      } catch (error) {
        this.logger.error(
          `Failed to complete initializeMultiplePinCalculationJob: \n error: ${error instanceof Error ? error.stack : error.message} `
        );
        cronJobLog.details = error instanceof Error ? error.message.split('\n')[0] : String(error);
        cronJobLog.status = 'FAILED';
      } finally {
        await this.redisService.del(jobName); // Release lock
      }
      cronJobLog.nextRun = job.nextDate().toJSDate();
      await this.cronJobLogRepository.saveEntity(cronJobLog);
    });

    this.schedulerRegistry.addCronJob(jobName, job);
    job.start();
  }

  private async initializeAuditorAssignmentJob(jobName: string, cronExpression: string) {
    this.logger.debug(`Initializing AuditorAssignmentJob with schedule: ${cronExpression}`);
    const lock = await this.redisService.setNx(jobName, 'running');
    if (!lock) {
      this.logger.warn('Skipping AuditorAssignmentJob as previous instance is still running');
      await this.cronJobLogRepository.saveEntity(
        new CronJobLog({
          jobName,
          status: 'SKIPPED',
          jobId: uuidv4(),
          details: 'Skipping AuditorAssignmentJob as previous instance is still running',
        })
      );
      return;
    }

    const job = new CronJob(cronExpression, async () => {
      this.logger.debug('Running initializeAuditorAssignmentJob');
      const cronJobLog = await this.cronJobLogRepository.saveEntity(
        new CronJobLog({
          jobName,
          status: 'IN_PROGRESS',
          jobId: uuidv4(),
        })
      );
      try {
        const result = await this.enrolmentDistributionService.distributeComputedEnrolments();
        cronJobLog.details = JSON.stringify(result);
        cronJobLog.status = 'SUCCESS';
        this.logger.info(`Completed initializeAuditorAssignmentJob, Next Run at: ${job.nextDate()}`);
      } catch (error) {
        this.logger.error(
          `Failed to complete auditor assignment: \n error: ${error instanceof Error ? error.stack : error.message} `
        );
        cronJobLog.details = error instanceof Error ? error.message.split('\n')[0] : String(error);
        cronJobLog.status = 'FAILED';
      } finally {
        await this.redisService.del(jobName); // Release lock
      }
      cronJobLog.nextRun = job.nextDate().toJSDate();
      await this.cronJobLogRepository.saveEntity(cronJobLog);
    });

    this.schedulerRegistry.addCronJob(jobName, job);
    job.start();
  }

  async updateCronJob(jobName: string, newCronExpression: string) {
    if (this.schedulerRegistry.doesExist('cron', jobName)) {
      const existingJob = this.schedulerRegistry.getCronJob(jobName);
      existingJob.stop();
      this.schedulerRegistry.deleteCronJob(jobName);
      this.logger.debug(`Removed existing cron job: ${jobName}`);
    }

    if (CronJobNamesConstant.VALIDATOR_ASSIGNMENT_JOB === jobName) {
      await this.initializeValidatorAssignmentJob(jobName, newCronExpression);
    } else if (CronJobNamesConstant.AUDITOR_ASSIGNMENT_JOB === jobName) {
      await this.initializeAuditorAssignmentJob(jobName, newCronExpression);
    } else if (CronJobNamesConstant.EXIT_ACCRUED_RIGHTS_CONTRIBUTION_JOB === jobName) {
      await this.initializeExitAccruedRightsContributionJob(jobName, newCronExpression);
    } else if (CronJobNamesConstant.NOMINAL_ROLL_CONTRIBUTION_JOB === jobName) {
      await this.initializeNominalRollContributionJob(jobName, newCronExpression);
    } else if (CronJobNamesConstant.MULTIPLE_PIN_CALCULATION_JOB === jobName) {
      await this.initializeMultiplePinCalculationJob(jobName, newCronExpression);
    } else if (CronJobNamesConstant.PFA_NON_CONFIRMATION_ESCALATION_JOB === jobName) {
      await this.initializeNonConfirmationEscalationJob(jobName, newCronExpression);
    } else if (CronJobNamesConstant.INACTIVE_USER_DEACTIVATION_JOB === jobName) {
      await this.initializeCheckInactiveUsers(jobName, newCronExpression);
    }

    this.logger.debug(`Cron job "${jobName}" updated to: ${newCronExpression}`);
  }
}
