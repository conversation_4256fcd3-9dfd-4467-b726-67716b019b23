import { Column, Entity } from 'typeorm';
import { AbstractEntity } from '@app/shared/database/abstract.entity';

@Entity('CRON_JOB_LOG')
export class CronJobLog extends AbstractEntity<CronJobLog> {
  @Column({ name: 'JOB_ID' })
  jobId: string;

  @Column({ name: 'JOB_NAME' })
  jobName: string;

  @Column({ name: 'NEXT_RUN_DATE', type: 'timestamp', nullable: true })
  nextRun: Date;

  @Column({ name: 'JOB_STATUS' })
  status: 'SUCCESS' | 'FAILED' | 'IN_PROGRESS' | 'SKIPPED';

  @Column({ name: 'JOB_DETAILS', nullable: true, type: 'clob' })
  details: string;
}
