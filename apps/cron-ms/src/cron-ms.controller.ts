import { Body, Controller, Post } from '@nestjs/common';
import { CronMsService } from './cron-ms.service';

@Controller()
export class CronMsController {
  constructor(private readonly cronMsService: CronMsService) {}

  @Post('update')
  async updateCron(@Body('jobName') jobName: string, @Body('cronExpression') cronExpression: string) {
    await this.cronMsService.updateCronJob(jobName, cronExpression);
    return { message: `Cron job "${jobName}" updated to: ${cronExpression}` };
  }
}
