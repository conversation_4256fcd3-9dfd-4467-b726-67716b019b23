import { Injectable } from '@nestjs/common';
import { AbstractRepository } from '@app/shared/database/abstract.repository';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { PinoLogger } from 'nestjs-pino';
import { CronJobLog } from '../entities/cron-job-log.entity';

@Injectable()
export class CronJobLogRepository extends AbstractRepository<CronJobLog> {
  constructor(
    @InjectRepository(CronJobLog)
    cronJobLogRepository: Repository<CronJobLog>,
    entityManager: EntityManager,
    readonly logger: PinoLogger
  ) {
    super(cronJobLogRepository, entityManager);
  }
}
