import { Injectable } from '@nestjs/common';
import { Pi<PERSON>Logger } from 'nestjs-pino';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { RegistrationStatusEnum } from '@app/shared/enums/registration-status.enum';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { RabbitMqClient } from '@app/shared/rabbitmq/rabbit-mq-client';
import { ClientProxy } from '@nestjs/microservices';
import { CronJobNamesConstant } from '@app/shared/constants/cron.constants';
import { EnrollmentEngineServiceClientConstant } from '@app/shared/constants/enrollment-engine-service-client.constant';

@Injectable()
export class ExitArContributionCalculationService {
  private rmqClient: ClientProxy;

  constructor(
    private readonly enrolmentSummaryRepository: EnrolmentSummaryRepository,
    private redisService: RedisCacheService,
    private readonly rabbitMqClient: RabbitMqClient,
    private readonly logger: PinoLogger
  ) {
    this.rmqClient = this.rabbitMqClient.getClient();
  }

  async handleExitArContributionCalculation(): Promise<string> {
    const validatedRecords = await this.enrolmentSummaryRepository.getRecordsByStatus(RegistrationStatusEnum.VALIDATED);

    if (!validatedRecords || validatedRecords.length === 0) {
      return 'NO RECORD TO PROCESS';
    }

    let queuedCount = 0;
    let skippedCount = 0;

    for (const record of validatedRecords) {
      const cacheKey = `${CronJobNamesConstant.EXIT_ACCRUED_RIGHTS_CONTRIBUTION_JOB}:${record.rsaPin}`;

      // Check if the record exists in Redis cache
      const exists = await this.redisService.get(cacheKey);
      if (exists) {
        skippedCount++;
        this.logger.info(`Skipping ${record.rsaPin}, already still in cache`);
        continue;
      }

      // Add record to RabbitMQ queue
      await this.rmqClient.emit(EnrollmentEngineServiceClientConstant.EXIT_AR_CONTRIBUTION_COMPUTATION, {
        rsaPin: record.rsaPin,
      });

      // Store in Redis to prevent re-processing
      await this.redisService.set(cacheKey, 'processing'); // Expires in 1 hour

      queuedCount++;
      this.logger.info(`Queued ${record.rsaPin} for processing`);
    }

    return `QUEUED: ${queuedCount}; SKIPPED: ${skippedCount}`;
  }
}
