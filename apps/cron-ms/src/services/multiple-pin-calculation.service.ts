import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON>Logger } from 'nestjs-pino';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { RabbitMqClient } from '@app/shared/rabbitmq/rabbit-mq-client';
import { ClientProxy } from '@nestjs/microservices';
import { CronJobNamesConstant } from '@app/shared/constants/cron.constants';
import { EnrollmentEngineServiceClientConstant } from '@app/shared/constants/enrollment-engine-service-client.constant';
import { MultiplePinResolutionRequestRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-req.repository';
import { MultiplePinResolutionStatusEnum } from '@app/shared/enrollment-service/enums/multiple-pin-resolution-status.enum';

@Injectable()
export class MultiplePinCalculationService {
  private rmqClient: ClientProxy;

  constructor(
    private readonly multiplePinRequestRepository: MultiplePinResolutionRequestRepository,
    private redisService: RedisCacheService,
    private readonly rabbitMqClient: RabbitMqClient,
    private readonly logger: PinoLogger
  ) {
    this.rmqClient = this.rabbitMqClient.getClient();
  }

  async handleMultiplePinCalculation() {
    const filteredRecords = await this.multiplePinRequestRepository.find({
      where: { status: MultiplePinResolutionStatusEnum.AWAITING_COMPUTATION },
    });

    if (!filteredRecords || filteredRecords.length === 0) {
      return 'NO MULTIPLE PIN RECORDS TO PROCESS';
    }

    let queuedCount = 0;
    let skippedCount = 0;

    for (const record of filteredRecords) {
      const cacheKey = `${CronJobNamesConstant.MULTIPLE_PIN_CALCULATION_JOB}:${record.batchId}`;

      // Check if the record exists in Redis cache
      const exists = await this.redisService.get(cacheKey);
      if (exists) {
        skippedCount++;
        this.logger.info(`Skipping ${record.batchId}, already in multiple pin cache`);
        continue;
      }

      // Add record to RabbitMQ queue
      await this.rmqClient.emit(EnrollmentEngineServiceClientConstant.MULTIPLE_PIN_CALCULATION_JOB, {
        batchId: record.batchId,
      });

      // Store in Redis to prevent re-processing
      await this.redisService.set(cacheKey, 'processing'); // Expires in 1 hour

      queuedCount++;
      this.logger.info(`Queued ${record.batchId} for processing`);
    }

    return `QUEUED: ${queuedCount}; SKIPPED: ${skippedCount}`;
  }
}
