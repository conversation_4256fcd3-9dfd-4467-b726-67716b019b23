import { Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { RabbitMqClient } from '@app/shared/rabbitmq/rabbit-mq-client';
import { ClientProxy } from '@nestjs/microservices';
import { CronJobNamesConstant } from '@app/shared/constants/cron.constants';
import { EnrollmentEngineServiceClientConstant } from '@app/shared/constants/enrollment-engine-service-client.constant';
import { NominalRollRepository } from '@app/shared/enrollment-service/repository/nominal-roll.repository';
import { NominalRollStatusEnum } from '@app/shared/enums/nominal-roll-status.enum';

@Injectable()
export class NominalRollContributionCalculationService {
  private rmqClient: ClientProxy;

  constructor(
    private readonly nominalRollRepository: NominalRollRepository,
    private redisService: RedisCacheService,
    private readonly rabbitMqClient: RabbitMqClient,
    private readonly logger: PinoLogger
  ) {
    this.rmqClient = this.rabbitMqClient.getClient();
  }

  async handleNominalRollContributionCalculation(): Promise<string> {
    const filteredRecords = await this.nominalRollRepository.find({
      where: { status: NominalRollStatusEnum.AWAITING_COMPUTATION },
    });

    if (!filteredRecords || filteredRecords.length === 0) {
      return 'NO NOMINAL ROLL RECORD TO PROCESS';
    }

    let queuedCount = 0;
    let skippedCount = 0;

    for (const record of filteredRecords) {
      const cacheKey = `${CronJobNamesConstant.NOMINAL_ROLL_CONTRIBUTION_JOB}:${record.rsaPin}`;

      // Check if the record exists in Redis cache
      const exists = await this.redisService.get(cacheKey);
      if (exists) {
        skippedCount++;
        this.logger.info(`Skipping ${record.rsaPin}, already still in nr cache`);
        continue;
      }

      // Add record to RabbitMQ queue
      await this.rmqClient.emit(EnrollmentEngineServiceClientConstant.NOMINAL_ROLL_CONTRIBUTION_JOB, {
        rsaPin: record.rsaPin,
      });

      // Store in Redis to prevent re-processing
      await this.redisService.set(cacheKey, 'processing'); // Expires in 1 hour

      queuedCount++;
      this.logger.info(`Queued ${record.rsaPin} for processing`);
    }

    return `QUEUED: ${queuedCount}; SKIPPED: ${skippedCount}`;
  }
}
