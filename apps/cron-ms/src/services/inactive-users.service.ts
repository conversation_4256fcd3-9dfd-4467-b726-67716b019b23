import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ogger } from 'nestjs-pino';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { SettingsEnumKey } from '@app/shared/enums/SettingsEnum';

@Injectable()
export class InactiveUsersService {
  constructor(
    private readonly cobraUserRepository: CobraUserRepository,
    private readonly settingMsLibService: SettingMsLibService,
    private readonly logger: PinoLogger
  ) {}

  async deactivateInactiveUsers(): Promise<void> {
    const batchSize = await this.settingMsLibService.getSettingInt(SettingsEnumKey.CRON_INACTIVE_USERS_BATCH_SIZE);

    let totalDeactivated = 0;

    const inActiveDays = await this.settingMsLibService.getSettingInt(
      SettingsEnumKey.DAYS_TO_DEACTIVATE_INACTIVE_USERS
    );
    while (true) {
      const { deactivatedUsers } = await this.cobraUserRepository.findAndDeactivateInactiveUsers(
        batchSize,
        inActiveDays
      );

      if (deactivatedUsers.length === 0) {
        break;
      }

      totalDeactivated += deactivatedUsers.length;

      this.logger.info(
        `Processed batch: ${deactivatedUsers.length} users deactivated. ` +
          `Total processed so far: ${totalDeactivated}`
      );

      // If we got less than batch size, we're done
      if (deactivatedUsers.length < batchSize) {
        break;
      }

      // Add a small delay between batches because it is event driven
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }

    this.logger.info(`Completed processing inactive users. Total deactivated: ${totalDeactivated}`);
  }
}
