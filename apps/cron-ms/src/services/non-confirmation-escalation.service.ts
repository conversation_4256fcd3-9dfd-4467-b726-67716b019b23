import { Injectable } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { RabbitMqClient } from '@app/shared/rabbitmq/rabbit-mq-client';
import { ClientProxy } from '@nestjs/microservices';
import { CronJobNamesConstant } from '@app/shared/constants/cron.constants';
import { EnrollmentEngineServiceClientConstant } from '@app/shared/constants/enrollment-engine-service-client.constant';
import { NominalRollStatusEnum } from '@app/shared/enums/nominal-roll-status.enum';
import { NominalRollBatchRepository } from '@app/shared/enrollment-service/repository/nominal-roll-batch.repository';
import { SettingMsLibService } from '@app/shared/setting-service/setting-ms-lib.service';
import { SettingsEnumKey } from '@app/shared/enums';
import { NominalRollRepository } from '@app/shared/enrollment-service/repository/nominal-roll.repository';

@Injectable()
export class NonConfirmationEscalationService {
  private rmqClient: ClientProxy;

  constructor(
    private readonly nominalRollBatchRepository: NominalRollBatchRepository,
    private readonly nominalRollRepository: NominalRollRepository,
    private readonly settingMsLibService: SettingMsLibService,
    private redisService: RedisCacheService,
    private readonly rabbitMqClient: RabbitMqClient,
    private readonly logger: PinoLogger
  ) {
    this.rmqClient = this.rabbitMqClient.getClient();
  }

  async handleNonConfirmationEscaltion(): Promise<string> {
    const filteredBatchRecords = await this.nominalRollBatchRepository.find({
      where: { status: NominalRollStatusEnum.CR_AWAITING_PFA_PAYMENT_CONFIRMATION },
    });

    if (!filteredBatchRecords || filteredBatchRecords.length === 0) {
      return 'NO NOMINAL ROLL BATCH RECORD TO PROCESS';
    }

    let queuedCount = 0;
    let skippedCount = 0;
    const escalationWaitPeriod = await this.settingMsLibService.getSettingInt(
      SettingsEnumKey.PFA_AWAITING_CONFIRMATION_WAIT_PERIOD
    );
    for (const record of filteredBatchRecords) {
      if (!record.pfaNotificationDate) {
        this.logger.info(`Skipping ${record.batchName}, no pfaNotificationDate`); // todo send email to support to investigate why(low priority)
        continue;
      }

      const escalationDate = this.addWorkingDays(record.pfaNotificationDate, escalationWaitPeriod);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      escalationDate.setHours(0, 0, 0, 0);
      if (escalationDate > today) {
        this.logger.info(
          `Escalation Date: ${escalationDate} is after today: ${today}, skipping this record with batch name: , ${record.batchName}`
        );
        continue;
      }

      // fetch all PFAs that are non-compliant
      const nominalRolls = await this.nominalRollRepository.find({
        where: { batchId: record.batchName, paymentStatus: null },
      });
      if (!nominalRolls || nominalRolls.length === 0) {
        this.logger.info(`Skipping ${record.batchName}, no batch records found`);
        continue;
      }

      for (const nominalRoll of nominalRolls) {
        //then send them emails to pfas, call CR_NON_CONFIRMATION event
        const cacheKey = `${CronJobNamesConstant.PFA_NON_CONFIRMATION_ESCALATION_JOB}:${nominalRoll.rsaPin}`;

        // Check if the record exists in Redis cache
        const exists = await this.redisService.get(cacheKey);
        if (exists) {
          skippedCount++;
          this.logger.info(`Skipping ${record.batchName}, already still in escalation cache`);
          continue;
        }

        // Add record to RabbitMQ queue
        await this.rmqClient.emit(EnrollmentEngineServiceClientConstant.PFA_NON_CONFIRMATION_ESCALATION_JOB, {
          rsaPin: nominalRoll.rsaPin,
          pfaCode: nominalRoll.pfaCode,
          action: 'CR_NON_CONFIRMATION',
          requestType: 'NOMINAL_ROLL',
        });

        // Store in Redis to prevent re-processing
        await this.redisService.set(cacheKey, 'processing'); // Expires in 1 hour

        queuedCount++;
        this.logger.info(`Queued ${record.batchName} for processing`);
      }
    }

    return `QUEUED: ${queuedCount}; SKIPPED: ${skippedCount}`;
  }

  addWorkingDays(startDate: Date, days: number): Date {
    const result = new Date(startDate); // Clone to avoid mutating the original date
    let addedDays = 0;

    while (addedDays < days) {
      result.setDate(result.getDate() + 1);
      const day = result.getDay();
      if (day !== 0 && day !== 6) {
        // Skip Sunday (0) and Saturday (6)
        addedDays++;
      }
    }

    return result;
  }
}
