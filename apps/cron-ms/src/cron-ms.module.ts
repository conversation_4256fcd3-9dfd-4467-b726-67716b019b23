import { Module } from '@nestjs/common';
import { CronMsController } from './cron-ms.controller';
import { DatabaseModule, LoggerModule, RedisCacheModule } from '@app/shared';
import {
  CRON_SERVICE,
  ENROLLMENT_QUEUE,
  NOTIFICATION_SERVICE_HOST,
  NOTIFICATION_SERVICE_TCP_PORT,
  NOTIFICATIONS_SERVICE,
} from '@app/shared/constants';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HealthModule } from '@app/shared/health';
import { ScheduleModule } from '@nestjs/schedule';
import { CobraUser } from '@app/shared/user-service/entities/cobra-user.entity';
import { InactiveUsersService } from './services/inactive-users.service';
import { SettingMsLibModule } from '@app/shared/setting-service/setting-ms-lib.module';
import { CobraUserRepository } from '@app/shared/user-service/repositories/cobra-user.repository';
import { AuditLog } from '@app/shared/audit-service/entities/audit-logs.entity';
import { ClientProxyFactory, Transport } from '@nestjs/microservices';
import { CronMsService } from './cron-ms.service';
import { CobraRole } from '@app/shared/user-service/entities/cobra-role.entity';
import { CobraPrivilege } from '@app/shared/user-service/entities/cobra-priviledge.entity';
import { CronJobLogRepository } from './repositories/cron-job-log-repository';
import { CronJobLog } from './entities/cron-job-log.entity';
import { Setting } from '@app/shared/setting-service/entities/setting.entity';
import { SettingMsLibRepository } from '@app/shared/setting-service/setting-ms-lib.repository';
import { CobraRoleRepository } from '@app/shared/user-service/repositories/cobra-role.repository';
import { TbeOrganisationsRepository } from '@app/shared/enrollment-service/repository/tbe-organisations.repository';
import { TbeOrganisations } from '@app/shared/enrollment-service/entities/tbe-organisations.entity';
import { EnrolmentSummaryRepository } from '@app/shared/enrollment-service/repository/enrolment-summary.repository';
import { EnrolmentDistributionService } from '@app/shared/enrollment-service/services/enrolment-distribution.service';
import { EnrolmentSummary } from '@app/shared/enrollment-service/entities/enrolment-summary.entity';
import { ExitArContributionCalculationService } from './services/exit-ar-contribution-calculation.service';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { RabbitmqModule } from '@app/shared/rabbitmq/rabbitmq.module';
import { NominalRollContributionCalculationService } from './services/nominal-roll-contribution-calculation.service';
import { NominalRollRepository } from '@app/shared/enrollment-service/repository/nominal-roll.repository';
import { NominalRoll } from '@app/shared/enrollment-service/entities/nominal-roll.entity';
import { NominalRollBand } from '@app/shared/enrollment-service/entities/nominal-roll-band.entity';
import { NominalRollBandDetails } from '@app/shared/enrollment-service/entities/nominal-roll-band-details.entity';
import { MultiplePinCalculationService } from './services/multiple-pin-calculation.service';
import { MultiplePinResolutionRequestRepository } from '@app/shared/enrollment-service/repositories/multiple-pin-resolution-req.repository';
import { MultiplePinResolutionRequest } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-req.entity';
import { MultiplePinResolutionDocument } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-document.entity';
import { MultiplePinResolutionPin } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-pin.entity';
import { MultiplePinResolutionTransactionHistory } from '@app/shared/enrollment-service/entities/multiple-pin-resolution-transaction.entity';
import { NominalRollComment } from '@app/shared/enrollment-service/entities/nominal-roll-comment.entity';
import { NonConfirmationEscalationService } from './services/non-confirmation-escalation.service';
import { NominalRollBatchRepository } from '@app/shared/enrollment-service/repository/nominal-roll-batch.repository';
import { TbcNominalRollBatch } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch';
import { TbcNominalRollBatchRecord } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch-record';
import { NominalRollBatchDocuments } from '@app/shared/enrollment-service/entities/tbc-nominal-roll-batch-documents.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    DatabaseModule,
    DatabaseModule.forFeature([
      AuditLog,
      CobraUser,
      CobraRole,
      NominalRoll,
      NominalRollComment,
      TbcNominalRollBatch,
      TbcNominalRollBatchRecord,
      NominalRollBatchDocuments,
      NominalRollBand,
      NominalRollBandDetails,
      CobraPrivilege,
      EnrolmentSummary,
      TbeOrganisations,
      Setting,
      CronJobLog,
      MultiplePinResolutionRequest,
      MultiplePinResolutionDocument,
      MultiplePinResolutionPin,
      MultiplePinResolutionTransactionHistory,
    ]),
    RabbitmqModule.register(ENROLLMENT_QUEUE),
    RedisCacheModule.register(),
    ScheduleModule.forRoot(),
    LoggerModule.forRoot(CRON_SERVICE),
    SettingMsLibModule,
    HealthModule.registerAsync([
      { name: NOTIFICATIONS_SERVICE, host: NOTIFICATION_SERVICE_HOST, port: NOTIFICATION_SERVICE_TCP_PORT },
    ]),
  ],
  controllers: [CronMsController],
  providers: [
    {
      provide: 'NOTIFICATION_SERVICE_CLIENT',
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: configService.get('NOTIFICATION_SERVICE_HOST', '127.0.0.1'),
            port: configService.get('NOTIFICATION_SERVICE_TCP_PORT', 3008),
          },
        });
      },
    },
    RedisCacheService,
    CronMsService,
    InactiveUsersService,
    NonConfirmationEscalationService,
    ExitArContributionCalculationService,
    NominalRollContributionCalculationService,
    EnrolmentDistributionService,
    CobraUserRepository,
    NominalRollRepository,
    EnrolmentSummaryRepository,
    TbeOrganisationsRepository,
    SettingMsLibRepository,
    CobraRoleRepository,
    CronJobLogRepository,
    NominalRollBatchRepository,
    MultiplePinResolutionRequestRepository,
    MultiplePinCalculationService,
  ],
})
export class CronMsModule {}
