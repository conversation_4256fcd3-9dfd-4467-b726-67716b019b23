import { NestFactory } from '@nestjs/core';
import { CronMsModule } from './cron-ms.module';
import { Logger, LoggerErrorInterceptor } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(CronMsModule);

  app.setGlobalPrefix('cron');
  app.useLogger(app.get(Logger));
  app.useGlobalInterceptors(new LoggerErrorInterceptor());

  const configService = app.get(ConfigService);
  const port = configService.get('CRON_SERVICE_PORT') || 3021;

  const config = new DocumentBuilder()
    .setTitle('Cron Service')
    .setDescription('Documentation API for Cron Service')
    .setVersion(configService.get('CRON_SERVICE_VERSION') || '1.0.0')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('swagger-api', app, document);

  app.enableCors();
  await app.listen(port);
  console.log(`Cron service running on port ${port}`);
}

bootstrap();
