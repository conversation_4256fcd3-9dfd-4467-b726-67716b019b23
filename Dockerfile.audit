FROM node:20-alpine as builder

WORKDIR /usr/src/app

# Copy only the root package.json and the microservice-specific package.json
COPY package*.json ./
COPY apps/audit-ms/package.json apps/audit-ms/
COPY tsconfig*.json ./
COPY nest-cli.json ./

# Ensure the apps directory exists
RUN mkdir -p apps

COPY apps/audit-ms apps/audit-ms
COPY libs libs

# Install dependencies (including shared and microservice-specific)
RUN npm install
RUN npm run build audit-ms

FROM node:20-alpine

ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}
ENV TZ=Africa/Lagos

WORKDIR /usr/src/app

# Install logrotate (Alpine Linux uses apk)
RUN apk add --no-cache logrotate tzdata

# Create log directory (mapped to host via docker-compose)
RUN mkdir -p /usr/src/app/logs

# Copy logrotate configuration
COPY ./logrotate.conf /etc/logrotate.d/applogs

# Set logrotate to run daily (Alpine uses crond)
RUN echo "0 0 * * * /usr/sbin/logrotate -f /etc/logrotate.d/applogs" >> /etc/crontabs/root

# Start cron in the background (Alpine)
RUN crond

# Copy only production dependencies
COPY package*.json ./
COPY apps/audit-ms/package.json apps/audit-ms/
RUN npm install --production

# Copy the built code from the builder stage
COPY --from=builder /usr/src/app/dist ./dist

CMD ["node", "dist/apps/audit-ms/main"]
