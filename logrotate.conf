/usr/src/app/logs/*/*.log {
    daily              # Rotate daily
    missingok          # Skip if missing
    rotate 7           # Keep 7 days of logs
    compress           # Gzip old logs
    delaycompress      # Don't compress the most recent file
    notifempty         # Skip empty files
    copytruncate       # Safe for running apps
    dateext            # Append date to rotated logs
    dateformat -%Y%m%d # Format: service-YYYYMMDD.log
}