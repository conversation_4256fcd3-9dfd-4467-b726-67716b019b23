services:
  cobra-audit-service:
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    env_file:
      - .env
    build:
      context: .
      dockerfile: Dockerfile.audit
    command: npm run start:debug:audit-ms
    ports:
      - '${AUDIT_SERVICE_PORT}:${AUDIT_SERVICE_PORT}'
      - '9235:9235'
    volumes:
      - .:/usr/src/app
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - AUDIT_SERVICE_PORT=${AUDIT_SERVICE_PORT}
      - RABBIT_MQ_URL=${RABBIT_MQ_URL}
      - AUDIT_QUEUE_NAME=${AUDIT_QUEUE_NAME}
      - JWT_SECRET=${JWT_SECRET}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
    networks:
      - microservices-network

  cobra-login-service:
    build:
      context: .
      dockerfile: Dockerfile.login
    command: npm run start:debug:login-ms
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    ports:
      - '${LOGIN_SERVICE_PORT}:${LOGIN_SERVICE_PORT}'
      - '9232:9232'
    volumes:
      - .:/usr/src/app
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - LOGIN_SERVICE_PORT=${LOGIN_SERVICE_PORT}
      - USER_SERVICE_HOST=${USER_SERVICE_HOST}
      - USER_SERVICE_PORT=${USER_SERVICE_PORT}
      - USER_SERVICE_TCP_PORT=${USER_SERVICE_TCP_PORT}
      - JWT_SECRET=${JWT_SECRET}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
      - CLIENT_TIME_TOLERANCE_MINUTES=${CLIENT_TIME_TOLERANCE_MINUTES}
      - ECRS_SERVICE_HOST=${ECRS_SERVICE_HOST}
      - ECRS_SERVICE_TCP_PORT=${ECRS_SERVICE_TCP_PORT}
      - NOTIFICATION_SERVICE_TCP_PORT=${NOTIFICATION_SERVICE_TCP_PORT}
      - NOTIFICATION_SERVICE_HOST=${NOTIFICATION_SERVICE_HOST}
    depends_on:
      - cobra-user-service
    networks:
      - microservices-network

  cobra-user-service:
    build:
      context: .
      dockerfile: Dockerfile.user
    command: npm run start:debug:user-ms
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    ports:
      - '${USER_SERVICE_PORT}:${USER_SERVICE_PORT}'
      - '${USER_SERVICE_TCP_PORT}:${USER_SERVICE_TCP_PORT}'
      - '9933:9933'
    volumes:
      - .:/usr/src/app
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - USER_SERVICE_PORT=${USER_SERVICE_PORT}
      - USER_SERVICE_TCP_PORT=${USER_SERVICE_TCP_PORT}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
      - ECRS_SERVICE_HOST=${ECRS_SERVICE_HOST}
      - ECRS_SERVICE_TCP_PORT=${ECRS_SERVICE_TCP_PORT}
      - NOTIFICATION_SERVICE_TCP_PORT=${NOTIFICATION_SERVICE_TCP_PORT}
      - NOTIFICATION_SERVICE_HOST=${NOTIFICATION_SERVICE_HOST}
    networks:
      - microservices-network

  cobra-setting-service:
    build:
      context: .
      dockerfile: Dockerfile.setting
    command: npm run start:dev setting-ms
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    ports:
      - '${SETTINGS_SERVICE_PORT}:${SETTINGS_SERVICE_PORT}'
    volumes:
      - .:/usr/src/app
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - SETTINGS_SERVICE_PORT=${SETTINGS_SERVICE_PORT}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
    networks:
      - microservices-network

  cobra-notification-service:
    build:
      context: .
      dockerfile: Dockerfile.notification
    command: npm run start:debug:notification-ms
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    ports:
      - '${NOTIFICATION_SERVICE_PORT}:${NOTIFICATION_SERVICE_PORT}'
      - '${NOTIFICATION_SERVICE_TCP_PORT}:${NOTIFICATION_SERVICE_TCP_PORT}'
      - '9234:9234'
    volumes:
      - .:/usr/src/app
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - NOTIFICATION_SERVICE_PORT=${NOTIFICATION_SERVICE_PORT}
      - NOTIFICATION_SERVICE_TCP_PORT=${NOTIFICATION_SERVICE_TCP_PORT}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
    networks:
      - microservices-network

  cobra-ecrs-service:
    build:
      context: .
      dockerfile: Dockerfile.ecrs
    command: npm run start:debug:ecrs-ms
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    ports:
      - '${ECRS_SERVICE_PORT}:${ECRS_SERVICE_PORT}'
      - '${ECRS_SERVICE_TCP_PORT}:${ECRS_SERVICE_TCP_PORT}'
      - '9231:9231'
    volumes:
      - .:/usr/src/app
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - ECRS_SERVICE_PORT=${ECRS_SERVICE_PORT}
      - ECRS_SERVICE_TCP_PORT=${ECRS_SERVICE_TCP_PORT}
      - ECRS_SERVICE_HOST=${ECRS_SERVICE_HOST}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
    networks:
      - microservices-network

  cobra-enrollment-service:
    build:
      context: .
      dockerfile: Dockerfile.enrollmentms
    command: npm run start:debug:enrollment-ms
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    ports:
      - '${ENROLLMENT_SERVICE_HTTP_PORT}:${ENROLLMENT_SERVICE_HTTP_PORT}'
      - '${ENROLLMENT_SERVICE_TCP_PORT}:${ENROLLMENT_SERVICE_TCP_PORT}'
      - '9229:9229'
    volumes:
      - .:/usr/src/app
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - ENROLLMENT_SERVICE_HTTP_PORT=${ENROLLMENT_SERVICE_HTTP_PORT}
      - ENROLLMENT_SERVICE_TCP_PORT=${ENROLLMENT_SERVICE_TCP_PORT}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
      - RABBIT_MQ_URL=${RABBIT_MQ_URL}
      - ECRS_SERVICE_HOST=${ECRS_SERVICE_HOST}
      - ECRS_SERVICE_TCP_PORT=${ECRS_SERVICE_TCP_PORT}
      - NOTIFICATION_SERVICE_TCP_PORT=${NOTIFICATION_SERVICE_TCP_PORT}
      - NOTIFICATION_SERVICE_HOST=${NOTIFICATION_SERVICE_HOST}
    networks:
      - microservices-network

  cobra-enrollment-engine-service:
    build:
      context: .
      dockerfile: Dockerfile.enrollmentengine
    command: npm run start:debug:enrollment-engine
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    ports:
      - '${ENROLLMENT_ENGINE_SERVICE_PORT}:${ENROLLMENT_ENGINE_SERVICE_PORT}'
      - '9230:9230'
    volumes:
      - .:/usr/src/app
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - ENROLLMENT_ENGINE_SERVICE_PORT=${ENROLLMENT_ENGINE_SERVICE_PORT}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=${REDIS_URL}
      - REDIS_TTL=${REDIS_TTL}
      - RABBIT_MQ_URL=${RABBIT_MQ_URL}
      - ECRS_SERVICE_HOST=${ECRS_SERVICE_HOST}
      - ECRS_SERVICE_TCP_PORT=${ECRS_SERVICE_TCP_PORT}
      - ENROLLMENT_SERVICE_TCP_PORT=${ENROLLMENT_SERVICE_TCP_PORT}
      - ENROLLMENT_SERVICE_HOST=${ENROLLMENT_SERVICE_HOST}
    networks:
      - microservices-network

  cobra-cron-service:
    restart: always
    build:
      context: .
      dockerfile: Dockerfile.cron
    command: npm run start:debug:cron-ms
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    ports:
      - '${CRON_SERVICE_PORT}:${CRON_SERVICE_PORT}'
      - '9228:9228'
    volumes:
      - .:/usr/src/app
      - ./logs:/usr/src/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - CRON_SERVICE_PORT=${CRON_SERVICE_PORT}
      - NOTIFICATION_SERVICE_TCP_PORT=${NOTIFICATION_SERVICE_TCP_PORT}
      - NOTIFICATION_SERVICE_HOST=${NOTIFICATION_SERVICE_HOST}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_USERNAME=${DATABASE_USERNAME}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SYNCHRONIZE=${DATABASE_SYNCHRONIZE}
      - DATABASE_LOGGING=${DATABASE_LOGGING}
      - DATABASE_AUTOLOAD_ENTITIES=${DATABASE_AUTOLOAD_ENTITIES}
      - RABBIT_MQ_URL=${RABBIT_MQ_URL}
    networks:
      - microservices-network

  pdf-service:
    image: pdf-generator-ms:latest
    container_name: pdf-generator
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    build:
      context: .
      dockerfile: Dockerfile.pdfgen
    command: npm run start:debug:pdf-generator-ms
    ports:
      - '${PDF_SERVICE_PORT}:${PDF_SERVICE_PORT}'
      - '9248:9248'
    volumes:
      - .:/usr/src/app
      - ./pdf-service/logs:/app/logs
    working_dir: /usr/src/app
    environment:
      - TZ=Africa/Lagos
      - PDF_SERVICE_PORT=${PDF_SERVICE_PORT}
    networks:
      - microservices-network

  cobra-web:
    image: ghcr.io/keanhive/cobra-web:latest
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    ports:
      - '3000:3000'
    env_file:
      - .env
    container_name: cobra-web
    networks:
      - microservices-network

networks:
  microservices-network:
    driver: bridge
