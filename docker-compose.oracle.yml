services:
  oraclecobradb:
    image: oracle/database:21.3.0-ee # image_name:tag
    container_name: oraclecobradb
    hostname: oraclecobradb
    domainname: mydocker.com
    environment:
      ORACLE_SID: cobra # Container database name
      ORACLE_PDB: MYPDB # Pluggable database name
      ORACLE_PWD: asdf
      ORACLE_CHARACTERSET: AL32UTF8
    ports:
      - "1522:1521"
      - "5501:5500"
    tty: true
    #    extra_hosts:
    #      - "hostname:192.xxx.xxx.xxx" # change to your own PC's hostname & IP Address
    privileged: true
    networks:
      - microservices-network

networks:
  microservices-network:
    driver: bridge
